# 本次更新缺失的单元测试清单

## 概述

本次 feat/Jerry/20250702-迁移 srt-editor 分支主要实现了字幕编辑器的完整迁移，但测试覆盖率较低。以下是缺失的测试列表：

## 1. 客户端字幕编辑器组件测试 (高优先级)

### 1.1 字幕编辑器核心组件

- `apps/client/components/subtitle-editor/SubtitleEditor.vue`

  - 字幕文件导入功能测试
  - 不同格式字幕解析测试 (SRT, VTT, LRC)
  - 导出功能测试
  - 空状态展示测试

- `apps/client/components/subtitle-editor/SubtitleList.vue`

  - 字幕列表渲染测试
  - 活动字幕高亮测试
  - 自动滚动功能测试
  - 合并高亮效果测试

- `apps/client/components/subtitle-editor/SubtitleItem.vue`

  - 字幕条目编辑功能测试
  - 时间输入验证测试
  - 循环播放功能测试
  - 练习播放功能测试
  - 字幕合并功能测试

- `apps/client/components/subtitle-editor/SubtitleToolbar.vue`
  - 撤销/重做功能测试
  - 工具栏状态管理测试

### 1.2 对话框组件

- `apps/client/components/subtitle-editor/FindReplaceDialog.vue`

  - 查找替换逻辑测试
  - 正则表达式支持测试
  - 批量替换功能测试

- `apps/client/components/subtitle-editor/SubtitleFilterDialog.vue`

  - 筛选条件测试
  - 筛选结果预览测试

- `apps/client/components/subtitle-editor/SubtitleJumpDialog.vue`
  - 跳转功能测试
  - 输入验证测试

### 1.3 时间输入组件

- `apps/client/components/subtitle-editor/TimeInput.vue`
  - 时间格式验证测试
  - 时间转换功能测试

## 2. 音频/视频播放器测试 (高优先级)

### 2.1 音频播放器

- `apps/client/components/subtitle-editor/AudioPlayer.vue`

  - 文件选择和验证测试
  - 播放控制功能测试
  - 进度条交互测试
  - 播放速率切换测试

- `apps/client/components/subtitle-editor/AudioWaveform.vue`
  - WaveSurfer 初始化测试
  - 波形显示测试
  - 区域管理测试

### 2.2 视频播放器

- `apps/client/components/subtitle-editor/VideoPlayer.vue`
  - Video.js 初始化测试
  - VTT 字幕轨道管理测试
  - 播放状态同步测试

## 3. 字幕处理逻辑测试 (高优先级)

### 3.1 字幕格式处理

- `apps/client/composables/subtitle/useFormatProcessing.ts`
  - SRT 格式解析和生成测试
  - VTT 格式解析和生成测试
  - LRC 格式解析和生成测试
  - 格式转换功能测试

### 3.2 字幕处理和验证

- `apps/client/composables/subtitle/useSubtitleProcessing.ts`
  - 字幕验证逻辑测试
  - 批量处理功能测试
  - 变更处理测试

### 3.3 时间工具函数

- `apps/client/composables/subtitle/useTimeUtils.ts`
  - 时间格式解析测试
  - 时间验证功能测试
  - 时间转换功能测试

### 3.4 字幕 UI 逻辑

- `apps/client/composables/subtitle/useSubtitleUI.ts`
  - 字幕操作功能测试
  - 字幕编辑逻辑测试
  - 顶部栏逻辑测试

### 3.5 音频处理

- `apps/client/composables/subtitle/useAudio.ts`
  - WaveSurfer 集成测试
  - 音频同步测试
  - 区域管理测试

## 4. 状态管理测试 (中优先级)

### 4.1 字幕存储

- `apps/client/stores/subtitle/subtitleStore.ts`
  - 字幕 CRUD 操作测试
  - 历史记录管理测试
  - 筛选功能测试

### 4.2 播放器存储

- `apps/client/stores/subtitle/playerStore.ts`
  - 播放状态管理测试
  - 媒体文件处理测试
  - 循环播放功能测试
  - 练习播放功能测试

### 4.3 LRC 存储

- `apps/client/stores/subtitle/lrcStore.ts`
  - LRC 内容管理测试
  - 文件上传处理测试

## 6. 课程管理扩展测试 (中优先级)

### 6.1 课程类型支持

- 课程类型枚举测试
- 课程类型徽章组件测试
- 课程类型路由逻辑测试

### 6.2 课程详情处理

- `apps/client/composables/subtitle/useCourseDetail.ts`
  - 课程详情获取测试
  - 字幕编辑器初始化测试

## 7. 工具函数和辅助模块测试 (低优先级)

### 7.1 文件处理

- `apps/client/utils/file.ts`
  - 文件上传功能测试
  - 文件哈希计算测试

### 7.2 验证逻辑

- `apps/client/composables/subtitle/useValidation.ts`
  - 完整性验证测试

### 7.3 变更检测

- `apps/client/composables/subtitle/useChangeDetection.ts`
  - 变更检测逻辑测试

## 8. 集成测试 (低优先级)

### 8.1 端到端功能测试

- 字幕编辑器完整工作流测试
- 课程发布和更新流程测试
- 媒体文件处理流程测试

### 8.2 组件集成测试

- 字幕编辑器与播放器集成测试
- 存储状态同步测试

## 9. 性能和边界测试 (低优先级)

### 9.1 性能测试

- 大文件处理性能测试
- 长时间播放内存泄漏测试

### 9.2 边界情况测试

- 异常输入处理测试
- 网络错误处理测试

## 测试优先级说明

### 高优先级

- 核心字幕编辑功能
- 音频/视频播放器
- 字幕格式处理
- 关键业务逻辑

### 中优先级

- 状态管理
- Admin API
- 课程管理扩展

### 低优先级

- 工具函数
- 集成测试
- 性能测试

## 建议的测试实施策略

1. **先实现高优先级测试**：专注于核心功能的单元测试
2. **使用测试驱动开发**：对新功能先写测试再实现
3. **Mock 外部依赖**：使用 Vitest 的 mock 功能隔离测试
4. **设置测试覆盖率目标**：核心模块至少 80%覆盖率
5. **CI/CD 集成**：将测试集成到持续集成流程

## 预估测试工作量

- **高优先级测试**：约 40-60 小时
- **中优先级测试**：约 20-30 小时
- **低优先级测试**：约 10-20 小时

**总计**：约 70-110 小时的测试开发工作量

---

_清单生成时间: 2025-01-03_
_基于分支: feat/Jerry/20250702-迁移 srt-editor_
