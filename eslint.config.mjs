import antfu from '@antfu/eslint-config'
import tailwind from 'eslint-plugin-tailwindcss'

export default antfu(
  {
    // 启用 Vue 支持（项目使用 Nuxt）
    vue: true,

    // 启用 TypeScript 类型感知规则
    typescript: {
      tsconfigPath: 'tsconfig.json',
    },

    // 启用格式化器
    formatters: {
      css: true,
      html: true,
      markdown: 'prettier',
    },

    // 编辑器特定设置
    isInEditor: false,
  },
  {
    ignores: [
      // Drizzle 生成的文件
      'apps/server/drizzle/**',
      '.gitignore',
      '.env*',
      '.*rc*',
      'dist/**',
      'build/**',
      // 忽略 markdown 文件
      '**/*.md',
      // 忽略测试文件
      '**/*.test.ts',
      '**/*.test.js',
      '**/*.spec.ts',
      '**/*.spec.js',
      '**/tests/**',
      '**/test/**',
      '**/cypress/**',
      '**/__tests__/**',
    ],
  },
  {
    // Vue 单文件组件块顺序规则
    files: ['**/*.vue'],
    rules: {
      'vue/block-order': [
        'error',
        {
          order: ['script', 'template', 'style'],
        },
      ],
    },
  },
  {
    files: ['**/*.ts'],
    languageOptions: {
      parserOptions: {
        project: './tsconfig.json',
      },
    },
    rules: {
      // 将 console 语句从错误降级为警告
      'no-console': 'warn',
      '@typescript-eslint/no-unsafe-assignment': 'warn',
      // 设置大括号风格为 1tbs，else 与前面的 } 在同一行
      'style/brace-style': ['error', '1tbs', { allowSingleLine: true }],
      // 禁用 Node.js 相关规则，允许在配置文件中使用 process
      'node/prefer-global/process': 'off',
      // 禁用 TypeScript 严格类型检查，允许在配置文件中使用 any 类型
      'ts/no-unsafe-call': 'off',
      'ts/no-unsafe-member-access': 'off',
      'ts/no-unsafe-assignment': 'off',
      'ts/no-unsafe-return': 'off',
      'ts/strict-boolean-expressions': 'off',
      'jsdoc/check-param-names': 'off',
      "ts/return-await": "off",
    },
  },
  ...tailwind.configs['flat/recommended'],
)
