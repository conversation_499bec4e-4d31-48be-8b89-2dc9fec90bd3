import { exec as childProcessExec } from 'node:child_process'
import path from 'node:path'
import { chdir } from 'node:process'

import archiver from 'archiver'
import fs from 'fs-extra'
import inquirer from 'inquirer'
import semver from 'semver'

// 执行命令的通用函数
async function executeCommand(command: string): Promise<string> {
  return new Promise((resolve, reject) => {
    console.log(`执行命令: ${command}`)
    console.log('当前目录:', process.cwd())
    childProcessExec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`错误: ${error.message}`)
        return reject(error)
      }
      if (stderr) {
        console.log(`${stderr}`)
      }
      console.log(`标准输出: ${stdout}`)
      resolve(stdout)
    })
  })
}

// 模拟上传到服务器的函数
async function uploadToServer(filePath: string, serverPath: string): Promise<void> {
  return new Promise((resolve, reject) => {
    console.log(`正在上传 ${filePath} 到 ${serverPath}`)
    const uploadCommand = `scp ${filePath} julebu-server:${serverPath}`
    executeCommand(uploadCommand)
      .then(() => {
        console.log(`文件成功上传到服务器`)
        resolve()
      })
      .catch((error) => {
        console.error(`上传失败: ${error.message}`)
        reject(error)
      })
  })
}

// 模拟在服务器上解压文件的函数
async function unzipOnServer(filePath: string, serverPath: string): Promise<void> {
  return new Promise((resolve, reject) => {
    console.log(`正在服务器上解压 ${filePath} 到 ${serverPath}`)
    const unzipCommand = `ssh julebu-server "cd ${serverPath} && unzip -o ${path.basename(filePath)}"`
    executeCommand(unzipCommand)
      .then(() => {
        console.log(`文件成功在服务器上解压`)
        resolve()
      })
      .catch((error) => {
        console.error(`解压失败: ${error.message}`)
        reject(error)
      })
  })
}

async function buildAndPackage(
  type: 'client' | 'server' | 'all',
): Promise<void> {
  if (type === 'client' || type === 'all') {
    await buildClient()
  }
  if (type === 'server' || type === 'all') {
    await buildServer()
  }
}

async function askForGitHubCommit(
  version: string,
  packageName: string,
): Promise<void> {
  const { shouldCommit } = await inquirer.prompt<{ shouldCommit: boolean }>([
    { type: 'confirm', name: 'shouldCommit', message: '是否要提交到 GitHub？' },
  ])

  if (shouldCommit) {
    try {
      const commitMessage = `release(${packageName}): v${version}`
      await executeCommand(`git add .`)
      await executeCommand(`git commit -m "${commitMessage}"`)
      console.log(`已创建 commit: ${commitMessage}`)

      await executeCommand(`git push origin main`)
      console.log('已成功推送到 GitHub')
    } catch (error) {
      console.error('GitHub 操作过程中出错:', error)
    }
  }
}

async function buildClient(): Promise<void> {
  console.log('正在构建 Client...')
  await executeCommand('npm run build:client')

  const clientPackageJson = await fs.readJson(
    path.resolve(__dirname, '../apps/client/package.json'),
  )
  let version = clientPackageJson.version as string

  const outputPath = path.resolve(__dirname, '../apps/client/.output/public')

  const { shouldUpgradeVersion } = await inquirer.prompt<{
    shouldUpgradeVersion: boolean
  }>([
    {
      type: 'confirm',
      name: 'shouldUpgradeVersion',
      message: '是否要升级版本？',
    },
  ])

  if (shouldUpgradeVersion) {
    const newVersion = semver.inc(version, 'patch')
    if (newVersion) {
      version = newVersion
      clientPackageJson.version = version
      await fs.writeJson('apps/client/package.json', clientPackageJson, {
        spaces: 2,
      })
    }
  }

  console.log('正在部署到腾讯云静态托管...')
  try {
    await executeCommand(
      `sudo tcb hosting deploy -e ${process.env.GAME_CLIENT_TCB_ENV} ${outputPath}`,
    )
    console.log('已成功部署到腾讯云静态托管')
  } catch (error) {
    console.error('部署到腾讯云静态托管时出错:', error)
  }

  await askForGitHubCommit(version, 'client')
}

// 对于 workspace 的依赖来讲 目前只有 julebu/shared 一个
// 这个会直接打包到 server 构建产物中的 所以需要在依赖中删除
async function resolveWorkspaceDependencyVersions(
  dependencies: Record<string, string>,
): Promise<Record<string, string>> {
  const newDependencies = { ...dependencies }

  for (const [pkgName, pkgVersion] of Object.entries(dependencies)) {
    if (typeof pkgVersion === 'string' && pkgVersion.startsWith('workspace:')) {
      delete newDependencies[pkgName]
      console.log(`已删除工作区依赖: ${pkgName}`)
    }
  }

  return newDependencies
}

async function buildServer(): Promise<void> {
  console.log('正在构建 Api...')
  await executeCommand('npm run build:server')

  const serverPackageJson = await fs.readJson(
    path.resolve(__dirname, '../apps/server/package.json'),
  )
  let version = serverPackageJson.version as string

  // 创建用于打包的 package.json 副本
  const packageJsonForZip = JSON.parse(JSON.stringify(serverPackageJson))
  packageJsonForZip.dependencies = await resolveWorkspaceDependencyVersions(
    packageJsonForZip.dependencies as Record<string, string> ?? {},
  )
  // 同时处理 devDependencies
  packageJsonForZip.devDependencies = await resolveWorkspaceDependencyVersions(
    packageJsonForZip.devDependencies as Record<string, string> ?? {},
  )

  // 创建临时目录用于存放修改后的 package.json
  const tempDir = path.resolve(__dirname, '../temp')
  await fs.ensureDir(tempDir)
  const tempPackageJsonPath = path.resolve(tempDir, 'package.json')
  await fs.writeJson(tempPackageJsonPath, packageJsonForZip, { spaces: 2 })

  const getZipFileName = (v: string) => path.resolve(`server-v${v}.zip`)
  let zipFileName = getZipFileName(version)

  const filesToZip = [
    path.resolve(__dirname, '../apps/server/dist'),
    path.resolve(__dirname, '../apps/server/.npmrc'),
    path.resolve(__dirname, '../apps/server/.env.prod'),
    tempPackageJsonPath, // 使用临时的 package.json
  ]

  await createZip({
    type: 'server',
    source: filesToZip,
    output: zipFileName,
    includeBaseFolder: false,
  })

  const { shouldUpgradeVersion } = await inquirer.prompt<{
    shouldUpgradeVersion: boolean
  }>([
    {
      type: 'confirm',
      name: 'shouldUpgradeVersion',
      message: '是否要升级版本？',
    },
  ])

  if (shouldUpgradeVersion) {
    // 更新版本号
    const newVersion = semver.inc(version, 'patch')
    if (newVersion) {
      version = newVersion
      // 更新原始 package.json
      serverPackageJson.version = version
      await fs.writeJson(
        path.resolve(__dirname, '../apps/server/package.json'),
        serverPackageJson,
        { spaces: 2 },
      )

      // 更新临时目录中的 package.json
      packageJsonForZip.version = version
      await fs.writeJson(tempPackageJsonPath, packageJsonForZip, { spaces: 2 })

      // 重新创建 zip 文件
      const newZipFileName = getZipFileName(version)
      await createZip({
        type: 'server',
        source: filesToZip,
        output: newZipFileName,
        includeBaseFolder: false,
      })

      // 删除旧的 zip 文件
      await fs.remove(zipFileName)
      zipFileName = newZipFileName
    }
  }

  // 清理临时文件
  await fs.remove(tempDir)

  const { shouldUpload } = await inquirer.prompt<{ shouldUpload: boolean }>([
    { type: 'confirm', name: 'shouldUpload', message: '是否要上传到服务器？' },
  ])

  if (shouldUpload) {
    const serverPath = process.env.SERVER_SERVER_PATH
    if (!serverPath) {
      console.error('错误：未设置 SERVER_SERVER_PATH 环境变量')
      return
    }

    if (await fs.pathExists(zipFileName)) {
      try {
        await uploadToServer(zipFileName, serverPath)
        await unzipOnServer(zipFileName, serverPath)
        // 在服务器端解压成功后，删除本地压缩包
        await fs.remove(zipFileName)
        console.log(`已删除本地压缩包: ${zipFileName}`)
      } catch (error) {
        console.error('上传或解压过程中出错:', error)
      }
    } else {
      console.log(`警告：未找到压缩包 ${zipFileName}`)
    }
  }

  await askForGitHubCommit(version, 'server')
}

interface ZipOptions {
  type: 'client' | 'server'
  source: string | string[]
  output: string
  includeBaseFolder: boolean
}

async function createZip(options: ZipOptions): Promise<void> {
  const { type, source, output, includeBaseFolder } = options

  return new Promise((resolve, reject) => {
    const archive = archiver('zip', { zlib: { level: 9 } })
    const stream = fs.createWriteStream(output)

    archive.on('error', err => reject(err))
    stream.on('close', () => resolve())

    archive.pipe(stream)

    if (type === 'client') {
      if (typeof source !== 'string') {
        throw new TypeError('Client source must be a string')
      }
      if (includeBaseFolder) {
        archive.directory(source, path.basename(source))
        console.log(`正在添加目录到压缩包: ${path.basename(source)}`)
      } else {
        archive.directory(source, false)
        console.log(`正在添加目录内容到压缩包: ${source}`)
      }
    } else if (type === 'server') {
      if (!Array.isArray(source)) {
        throw new TypeError('Server source must be an array of strings')
      }
      source.forEach((file) => {
        const name = path.basename(file)
        const stats = fs.statSync(file)
        if (stats.isDirectory()) {
          archive.directory(file, name)
          console.log(`正在添加目录到压缩包: ${name}`)
        } else {
          archive.file(file, { name })
          console.log(`正在添加文件到压缩包: ${name}`)
        }
      })
    }

    void archive.finalize()
  })
}

async function main(): Promise<void> {
  changeToProjectRoot()
  const { buildType } = await inquirer.prompt<{
    buildType:
      | 'client'
      | 'server'
      | 'all'
  }>([
    {
      type: 'list',
      name: 'buildType',
      message: '您想要构建什么？',
      choices: ['client', 'server', 'all'],
    },
  ])

  await buildAndPackage(buildType)
}

function changeToProjectRoot() {
  // 假设脚本位于项目根目录的 scripts 文件夹中
  chdir(path.resolve(__dirname, '..'))
}

main().catch(console.error)
