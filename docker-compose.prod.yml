version: '3.8'

services:
  # 后端服务
  server:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    env_file:
      - apps/server/.env.prod
    environment:
      - NODE_ENV=production
      - PORT=3400
    ports:
      - '3400:3400'
    restart: unless-stopped
    healthcheck:
      test: [CMD-SHELL, 'curl -f http://localhost:3400/health || exit 1']
      interval: 30s
      timeout: 5s
      retries: 3
