import { useLogto } from '@logto/vue'

let logto: ReturnType<typeof useLogto>
let runtimeConfig: ReturnType<typeof useRuntimeConfig>
export async function setupAuth() {
  logto = useLogto()
  runtimeConfig = useRuntimeConfig()
}

export async function signIn() {
  void logto.signIn(runtimeConfig.public.logto.signInRedirectURI)
}

export async function signOut() {
  return logto.signOut(runtimeConfig.public.logto.signOutRedirectURI)
}

export function isAuthenticated() {
  return logto.isAuthenticated.value
}

export async function getToken() {
  const accessToken = await logto.getAccessToken(
    runtimeConfig.public.logto.resources[0],
  )

  return accessToken
}
