<script setup lang="ts">
import { useAsyncData } from '#imports'
import { useLogto } from '@logto/vue'
import {
  darkTheme,
  NConfigProvider,
  NDialogProvider,
  NLoadingBarProvider,
  NMessageProvider,
} from 'naive-ui'
import { Toaster } from 'vue-sonner'
import HttpErrorProvider from './components/common/HttpErrorProvider.vue'
import LoadingScreen from './components/common/LoadingScreen.vue'
import LoadingSpinner from './components/common/LoadingSpinner.vue'
import { isAuthenticated } from './services/auth'
import { useUserStore } from './stores/user'

const userStore = useUserStore()

const { status } = useAsyncData(
  'initApplication',
  async () => {
    if (isAuthenticated()) {
      const logto = useLogto()
      const res = await logto.fetchUserInfo()
      await userStore.initUser(res!)
    }
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <HttpErrorProvider>
    <template v-if="status !== 'pending'">
      <NConfigProvider :theme="darkTheme">
        <NMessageProvider>
          <NDialogProvider>
            <NLoadingBarProvider>
              <NuxtLayout>
                <NuxtPage />
              </NuxtLayout>
            </NLoadingBarProvider>
          </NDialogProvider>
        </NMessageProvider>
      </NConfigProvider>
    </template>
    <UModals />
    <Toaster
      theme="dark"
      position="top-center"
      :expand="true"
      :toast-options="{
        style: {
          background: '#c084fc',
          color: '#000',
        },
      }"
    />
    <LoadingSpinner />
    <LoadingScreen :show="isAuthenticated() && status === 'pending'" />
  </HttpErrorProvider>
</template>
