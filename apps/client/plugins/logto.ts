import type { LogtoConfig } from '@logto/vue'

import { createLogto, UserScope } from '@logto/vue'
import { setupAuth } from '~/services/auth'

export default defineNuxtPlugin((nuxtApp) => {
  const runtimeConfig = useRuntimeConfig()

  const config: LogtoConfig = {
    endpoint: runtimeConfig.public.logto.endpoint,
    appId: runtimeConfig.public.logto.appId,

    scopes: [
      UserScope.Email,
      UserScope.Phone,
      UserScope.CustomData,
      UserScope.Identities,
      UserScope.Organizations,
    ],
    resources: [runtimeConfig.public.logto.resources[0]],
  }

  console.log(config)

  nuxtApp.vueApp.use(createLogto, config)
  void setupAuth()
})
