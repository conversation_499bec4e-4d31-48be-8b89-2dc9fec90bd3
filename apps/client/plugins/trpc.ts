import type { AppRouter } from '@julebu/shared'

import type { TRPCLink } from '@trpc/client'
import { createTRPCProxyClient, httpBatchLink } from '@trpc/client'
import { observable } from '@trpc/server/observable'
import { defineNuxtPlugin } from 'nuxt/app'

import * as superjson from 'superjson'
import { getToken, isAuthenticated } from '~/services/auth'

export type TrpcClient = ReturnType<typeof createTRPCProxyClient<AppRouter>>

type HttpStatusErrorHandler = (message: string, statusCode: number) => void
let httpStatusErrorHandler: HttpStatusErrorHandler

export function injectHttpTRPCStatusErrorHandler(handler: HttpStatusErrorHandler) {
  httpStatusErrorHandler = handler
}

export const errorHandlingLink: TRPCLink<AppRouter> = () => {
  return ({ next, op }) => {
    // 每个 link 需要返回一个传播结果的 observable
    return observable((observer) => {
      const unsubscribe = next(op).subscribe({
        next(value) {
          observer.next(value)
        },
        error(err) {
          httpStatusErrorHandler?.(err.message, err.data?.httpStatus ?? 0)
          observer.error(err)
        },
        complete() {
          observer.complete()
        },
      })
      return unsubscribe
    })
  }
}
export default defineNuxtPlugin(async () => {
  const config = useRuntimeConfig()
  const baseURL = config.public.apiBase

  const client = createTRPCProxyClient<AppRouter>({
    links: [
      errorHandlingLink,
      httpBatchLink({
        url: `${baseURL}/trpc`,
        async headers() {
          if (isAuthenticated()) {
            const token = await getToken()
            return {
              authorization: `Bearer ${token}`,
            }
          }
          return {}
        },
      }),
    ],
    transformer: superjson,
  })

  return {
    provide: {
      trpc: client,
    },
  }
})
