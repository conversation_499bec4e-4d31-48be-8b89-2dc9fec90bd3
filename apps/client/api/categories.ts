export interface Category {
  id: string
  parentId?: string // 父级分类ID，根分类为null
  label: string
  value: string
  description: string
  sortOrder: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export const categoriesApi = {
  // 获取所有分类
  async fetchCategories(): Promise<Category[]> {
    const { $trpc } = useNuxtApp()
    try {
      const data = await $trpc.category.list.query()
      return data
    } catch (error) {
      console.error('获取分类失败:', error)
      throw error
    }
  },
}
