import type { GetPresignedUrlDto, MediaUploadDto } from '@julebu/shared'

export const s3Api = {
  getImagePresignedUrl: async (input: GetPresignedUrlDto) => {
    const { $trpc } = useNuxtApp()
    return $trpc.s3.getPresignedUrl.mutate({
      ...input,
      key: `/${input.key}`,
    })
  },

  getAudioPresignedUrl: async (input: GetPresignedUrlDto) => {
    const { $trpc } = useNuxtApp()
    return $trpc.s3.getPresignedUrl.mutate({
      ...input,
      key: `/audios/${input.key}`,
    })
  },

  getMediaPresignedUrl: async (input: MediaUploadDto) => {
    const { $trpc } = useNuxtApp()
    return $trpc.s3.getMediaPresignedUrl.mutate({
      ...input,
      key: input.key,
    })
  },
}
