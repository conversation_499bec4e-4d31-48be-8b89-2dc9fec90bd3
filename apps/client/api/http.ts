import type { $Fetch } from 'ofetch'

import { useRuntimeConfig } from '#app'
import { ofetch } from 'ofetch'

import { getToken } from '~/services/auth'

type HttpStatusErrorHandler = (message: string, statusCode: number) => void
let httpStatusErrorHandler: HttpStatusErrorHandler

export function injectHttpAPIStatusErrorHandler(
  handler: HttpStatusErrorHandler,
) {
  httpStatusErrorHandler = handler
}

let http: $Fetch
export function setupHttp() {
  if (http)
    return http

  const config = useRuntimeConfig()
  const baseURL = config.public.apiBase

  http = ofetch.create({
    baseURL,
    headers: { 'Content-Type': 'application/json' },
    async onRequest({ options }) {
      const token = await getToken()

      options.headers = new Headers({
        ...options.headers,
        Authorization: `Bearer ${token}`,
      })
    },
    async onResponseError({ response }) {
      const { message } = response._data
      httpStatusErrorHandler?.(message as string, response.status)
      return Promise.reject(response._data)
    },
    retry: 3,
    retryDelay: 1000,
  })
}

export function getHttp() {
  if (!http) {
    throw new Error('HTTP client not initialized. Call setupHttp first.')
  }
  return http
}
