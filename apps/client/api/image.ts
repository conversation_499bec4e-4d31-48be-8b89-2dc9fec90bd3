import type { AiG<PERSON>ateImageDto, CreateImageWithTextLinkDto } from '@julebu/shared'

export async function createImageWithTextLink(input: CreateImageWithTextLinkDto): Promise<string> {
  const { $trpc } = useNuxtApp()
  return $trpc.image.createImageWithTextLink.mutate(input)
}

export async function generateImage(input: AiGenerateImageDto) {
  const { $trpc } = useNuxtApp()
  return $trpc.image.generateImage.mutate(input)
}
