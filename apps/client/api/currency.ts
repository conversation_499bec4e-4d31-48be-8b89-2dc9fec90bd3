import { useNuxtApp } from '#app'

/**
 * 虚拟货币余额数据接口
 */
export interface CurrencyBalance {
  balance: number
}

/**
 * 虚拟货币交易记录接口
 */
export interface CurrencyTransaction {
  id: string
  userId: string
  currencyType: string
  amount: number
  transactionType: string
  description?: string | null
  createdAt: string | Date
}

/**
 * 当前账单周期交易信息接口
 */
export interface CurrentCycleResult {
  transactions: CurrencyTransaction[]
  cycleStartDate: string | null
  cycleEndDate: string | null
}

/**
 * 虚拟货币API封装
 */
export const currencyApi = {
  /**
   * 获取用户钻石余额
   * @returns 包含余额的对象
   */
  async getBalance(): Promise<{ balance: number }> {
    const { $trpc } = useNuxtApp()
    try {
      const result = await $trpc.currency.getBalance.query()
      return {
        balance: result.balance || 0,
      }
    } catch (error) {
      console.error('获取钻石余额失败:', error)
      // 出错时返回默认值
      return { balance: 0 }
    }
  },

  /**
   * 获取当前账单周期的交易记录
   * @returns 当前账单周期的交易记录及日期信息
   */
  async getCurrentCycleTransactions(): Promise<CurrentCycleResult> {
    const { $trpc } = useNuxtApp()
    try {
      const result = await $trpc.currency.getCurrentCycleTransactions.query()
      return {
        transactions: result.transactions || [],
        cycleStartDate: result.cycleStartDate,
        cycleEndDate: result.cycleEndDate,
      }
    } catch (error) {
      console.error('获取当前账单周期交易记录失败:', error)
      // 出错时返回空结果
      return {
        transactions: [],
        cycleStartDate: null,
        cycleEndDate: null,
      }
    }
  },
}
