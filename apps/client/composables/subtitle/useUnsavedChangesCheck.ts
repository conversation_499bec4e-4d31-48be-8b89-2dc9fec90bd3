import Dialog from '~/components/common/Dialog.vue'
import { checkAndProcessUnprocessedSentences, useChangeDetection } from './useChangeDetection'
import { useTopBarLogic } from './useSubtitleUI'

type ButtonColor = 'primary' | 'gray' | 'red' | 'orange' | 'amber' | 'yellow' | 'lime' | 'green' | 'emerald' | 'teal' | 'cyan' | 'sky' | 'blue' | 'indigo' | 'violet' | 'purple' | 'fuchsia' | 'pink' | 'rose'

/**
 * 显示未保存更改确认弹窗
 * @param action 要执行的操作名称
 * @param options 选项配置
 * @returns Promise<'cancel' | 'continue' | 'saveAndContinue'> 用户选择的操作
 */
export async function showUnsavedChangesDialog(
  action: string,
  options?: {
    showThirdButton?: boolean
    thirdButtonText?: string
    thirdButtonColor?: ButtonColor
  },
): Promise<'cancel' | 'continue' | 'saveAndContinue'> {
  const modal = useModal()
  return new Promise((resolve) => {
    modal.open(Dialog, {
      title: '未保存的更改',
      content: `检测到有未保存的内容，直接${action}不会包含这些更改。`,
      showCancel: true,
      showConfirm: true,
      confirmText: `直接${action}`,
      showThirdButton: options?.showThirdButton ?? false,
      thirdButtonText: options?.thirdButtonText ?? `保存并${action}`,
      thirdButtonColor: options?.thirdButtonColor ?? 'amber',
      onCancel() {
        resolve('cancel')
      },
      onConfirm() {
        resolve('continue')
      },
      onThirdButton() {
        resolve('saveAndContinue')
      },
    })
  })
}

/**
 * 保存字幕并继续操作
 * @returns Promise<boolean> 返回保存是否成功
 */
async function saveSubtitlesAndContinue(): Promise<boolean> {
  const { handleFinish } = useTopBarLogic()
  const loadingSpinner = useLoadingSpinner()

  try {
    loadingSpinner.startLoading([
      '正在保存字幕...',
      '正在验证字幕数据...',
      '正在保存句子信息...',
      '正在上传媒体文件...',
      '即将完成，请稍候...',
    ])

    await handleFinish()
    return true
  } catch (error) {
    console.error('保存字幕失败:', error)
    return false
  } finally {
    loadingSpinner.finishLoading()
  }
}

/**
 * 检查是否有未保存的更改
 * 复用 useTopBarLogic 中的 canSaveChanges 逻辑
 */
export function useUnsavedChangesCheck() {
  const { hasUnsavedChanges } = useChangeDetection()

  /**
   * 检查未保存更改并显示确认弹窗
   * @param action 要执行的操作名称（如 '发布' 或 '更新'）
   * @param onConfirm 用户确认后执行的回调函数
   * @returns Promise<boolean> 返回是否继续执行操作
   */
  const checkUnsavedChangesBeforeAction = async (
    action: string,
    onConfirm: () => Promise<void> | void,
  ): Promise<boolean> => {
    // 如果没有未保存的更改，直接执行操作
    if (!hasUnsavedChanges.value) {
      await onConfirm()
      return true
    }

    // 显示确认弹窗
    const shouldContinue = await showUnsavedChangesDialog(action)
    if (shouldContinue === 'continue') {
      await onConfirm()
    }
    return shouldContinue === 'continue'
  }

  return {
    hasUnsavedChanges,
    checkUnsavedChangesBeforeAction,
  }
}

/**
 * 简化的检查未保存更改函数，用于回调中使用
 * 只检查并询问用户，不执行任何操作
 */
export function createUnsavedChangesChecker() {
  const { hasUnsavedChanges } = useUnsavedChangesCheck()

  return async (action: string): Promise<boolean> => {
    // 如果没有未保存的更改，直接返回 true
    if (!hasUnsavedChanges.value) {
      return true
    }

    // 使用共享的弹窗逻辑
    const result = await showUnsavedChangesDialog(action)
    return result === 'continue'
  }
}

/**
 * 组合的检查函数：先检查未保存更改，再检查并处理未加工句子
 * @param action 要执行的操作名称（如 '发布' 或 '更新'）
 * @returns Promise<boolean> 返回是否继续执行操作
 */
export async function checkUnsavedChangesAndProcessSentences(action: string): Promise<boolean> {
  const { hasUnsavedChanges } = useUnsavedChangesCheck()

  // 1. 先检查未保存的更改
  if (hasUnsavedChanges.value) {
    const userChoice = await showUnsavedChangesDialog(action, {
      showThirdButton: true,
    })

    if (userChoice === 'cancel') {
      return false
    }

    if (userChoice === 'saveAndContinue') {
      // 先保存字幕
      const saveSuccess = await saveSubtitlesAndContinue()
      if (!saveSuccess) {
        return false
      }
    }
    // 如果是 'continue'，直接继续不保存
  }

  // 2. 检查并加工未处理的句子
  return checkAndProcessUnprocessedSentences()
}
