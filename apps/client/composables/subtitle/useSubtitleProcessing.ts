import type { Sentence } from '@julebu/shared'
import type { Subtitle } from '~/types/subtitle/subtitle'
import { filter, find, isEmpty, map, some } from 'lodash-es'
import { parseSrtTime } from './useTimeUtils'

// ============================================================================
// 字幕基础处理函数
// ============================================================================

/**
 * 重新编号字幕 id 字段
 */
export function reindexSubtitles(subtitles: Subtitle[]): Subtitle[] {
  return map(subtitles, (sub, idx) => ({ ...sub, id: idx + 1 }))
}

// ============================================================================
// 字幕变更处理
// ============================================================================

/**
 * 字幕变更处理结果
 */
export interface SubtitleChangeResult {
  newSubtitles: Subtitle[] // 新增的句子数组（按顺序）
  updatedSubtitles: Subtitle[] // 更新的句子数组（按顺序）
  unchangedSubtitles: Subtitle[] // 没变的句子数组（按顺序）
  deletedSentences: Subtitle[] // 被删除的句子数组（按顺序）
}

/**
 * 处理字幕句子变更分类
 *
 * @param currentSubtitles 当前字幕数据
 * @param originalSubtitles 原始字幕数据
 * @returns 分类结果对象
 *
 * 分类规则：
 * 1. 新增的句子：没有 sentenceId 或 sentenceId 为空
 * 2. 更新的句子：内容（text/translationText）发生变化
 * 3. 没变的句子：其他情况
 */
export function processSubtitleChanges(
  currentSubtitles: Subtitle[],
  originalSubtitles: Subtitle[] = [],
): SubtitleChangeResult {
  const newSubtitles: Subtitle[] = [] // 新增的句子
  const updatedSubtitles: Subtitle[] = [] // 更新的句子
  const unchangedSubtitles: Subtitle[] = [] // 没变的句子
  const deletedSentences: Subtitle[] = [] // 被删除的句子

  // 创建原始字幕的映射表，以sentenceId为key
  const originalMap = new Map<string, Subtitle>()
  originalSubtitles.forEach((subtitle) => {
    const sentenceId = subtitle.sentenceId
    if (sentenceId != null && sentenceId.trim() !== '') {
      originalMap.set(sentenceId, subtitle)
    }
  })

  // 创建当前字幕的 sentenceId 集合
  const currentSentenceIdSet = new Set<string>()
  currentSubtitles.forEach((subtitle) => {
    if (subtitle.sentenceId != null && subtitle.sentenceId.trim() !== '') {
      currentSentenceIdSet.add(subtitle.sentenceId)
    }
  })

  // 遍历当前字幕进行分类
  currentSubtitles.forEach((subtitle) => {
    // 1. 新增的句子：没有sentenceId
    const sentenceId = subtitle.sentenceId
    if (sentenceId == null || sentenceId.trim() === '') {
      newSubtitles.push(subtitle)
      return
    }

    // 2. 检查是否是更新的句子
    const originalSubtitle = originalMap.get(sentenceId)
    if (!originalSubtitle) {
      // 如果有sentenceId但找不到原始记录，也算作新增
      newSubtitles.push(subtitle)
      return
    }

    // 只比较内容是否发生变化
    const contentChanged
      = subtitle.text !== originalSubtitle.text
        || subtitle.translationText !== originalSubtitle.translationText

    // 3. 更新的句子：content跟之前不同
    if (contentChanged) {
      updatedSubtitles.push(subtitle)
    } else {
      // 4. 没变的句子
      unchangedSubtitles.push(subtitle)
    }
  })

  // 遍历原始字幕，找出被删除的句子
  originalSubtitles.forEach((subtitle) => {
    const sentenceId = subtitle.sentenceId
    if (sentenceId != null && sentenceId.trim() !== '' && !currentSentenceIdSet.has(sentenceId)) {
      deletedSentences.push(subtitle)
    }
  })

  return {
    newSubtitles, // 新增的句子数组（按顺序）
    updatedSubtitles, // 更新的句子数组（按顺序）
    unchangedSubtitles, // 没变的句子数组（按顺序）
    deletedSentences, // 被删除的句子数组（按顺序）
  }
}

// ============================================================================
// 句子处理状态检查
// ============================================================================

/**
 * 判断句子是否需要加工
 * @param sentence 句子对象
 * @returns 是否需要加工
 */
function needsProcessing(sentence: Sentence): boolean {
  if (isEmpty(sentence)) {
    return true
  }
  // 没有元素加工会直接把句子的 content 作为练习元素
  // 或者 有元素没有中文 或者 有元素没有音标
  return (
    isEmpty(sentence.elements)
    || some(sentence.elements, element => !element.chinese || !element.phonetic)
  )
}

/**
 * 获取未加工的句子列表
 * @param sentences 句子数组
 * @returns 未加工的句子数组，如果全部加工过则返回空数组
 */
export function getUnprocessedSentences(subtitles: Subtitle[], sentences: Sentence[]): string[] {
  // 过滤出未加工的句子 - 逻辑是：subtitles 中包含 sentences 的过滤掉，然后剩下的句子就是未加工的句子
  const unprocessedSubtitlesIds = map(filter(subtitles, (subtitle: Subtitle) => isEmpty(find(sentences, sentence => sentence.id === subtitle.sentenceId))), 'sentenceId')

  // 过滤出接口获取到的数据中未加工的句子
  const unprocessedSentencesIds = map(filter(sentences, sentence => needsProcessing(sentence)), 'id')
  return [...unprocessedSubtitlesIds, ...unprocessedSentencesIds] as string[]
}

// ============================================================================
// 字幕时间验证
// ============================================================================

export function useSubtitleTimeValidation(
  startTime: Ref<string>,
  endTime: Ref<string>,
  nextSubtitleStartTime?: Ref<string | null>,
  prevSubtitleEndTime?: Ref<string | null>,
) {
  const isTimeOrderValid = computed(() => {
    const startSeconds = parseSrtTime(startTime.value)
    const endSeconds = parseSrtTime(endTime.value)

    if (startSeconds === null || endSeconds === null)
      return true
    return startSeconds <= endSeconds
  })

  // 验证开始时间必须早于结束时间（不能相等）
  const isStartTimeValid = computed(() => {
    const startSeconds = parseSrtTime(startTime.value)
    const endSeconds = parseSrtTime(endTime.value)

    if (startSeconds === null || endSeconds === null)
      return true
    return startSeconds < endSeconds
  })

  // 验证结束时间必须晚于开始时间（不能相等）
  const isEndTimeValid = computed(() => {
    const startSeconds = parseSrtTime(startTime.value)
    const endSeconds = parseSrtTime(endTime.value)

    if (startSeconds === null || endSeconds === null)
      return true
    return endSeconds > startSeconds
  })

  const isStartTimeOrderValid = computed(() => {
    // 歌词模式优化：允许开始时间早于上一行结束时间
    return true

    /* 原逻辑保留以备恢复
    if (prevSubtitleEndTime?.value == null) {
      return true
    }

    const startSeconds = parseSrtTime(startTime.value)
    const prevEndSeconds = parseSrtTime(prevSubtitleEndTime.value)
    if (startSeconds === null || prevEndSeconds === null)
      return true
    return startSeconds >= prevEndSeconds
    */
  })

  const isEndTimeOrderValid = computed(() => {
    // 歌词模式优化：允许结束时间晚于下一行开始时间
    return true

    /* 原逻辑保留以备恢复
    if (nextSubtitleStartTime?.value == null) {
      return true
    }

    const endSeconds = parseSrtTime(endTime.value)
    const nextStartSeconds = parseSrtTime(nextSubtitleStartTime.value)

    if (endSeconds === null || nextStartSeconds === null)
      return true
    return endSeconds <= nextStartSeconds
    */
  })

  const isAddDisabled = computed(() => {
    if (nextSubtitleStartTime?.value == null) {
      return false
    }

    const currentEndTimeSec = parseSrtTime(endTime.value)
    const nextStartTimeSec = parseSrtTime(nextSubtitleStartTime.value)

    if (currentEndTimeSec === null || nextStartTimeSec === null)
      return false

    const timeDiff = nextStartTimeSec - currentEndTimeSec
    return timeDiff <= 0.001
  })

  return {
    isTimeOrderValid,
    isAddDisabled,
    isStartTimeOrderValid,
    isEndTimeOrderValid,
    isStartTimeValid,
    isEndTimeValid,
  }
}
