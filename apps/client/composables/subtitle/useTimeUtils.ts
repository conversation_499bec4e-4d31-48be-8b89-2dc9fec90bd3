import type { Subtitle } from '~/types/subtitle/subtitle'
import Decimal from 'decimal.js-light'

// ============================================================================
// 时间格式化函数
// ============================================================================

/**
 * 通用时间格式化函数
 * @param seconds 秒数
 * @param separator 秒与毫秒的分隔符
 * @param format 时间格式：HH:MM:SS 或 MM:SS
 * @returns 格式化后的时间字符串
 */
export function formatTimeToString(seconds: number, separator: ',' | '.' = ',', format: 'HH:MM:SS' | 'MM:SS' = 'HH:MM:SS'): string {
  if (seconds == null) {
    return '00:00:00,000'
  }
  const dSeconds = new Decimal(seconds)
  const totalMs = dSeconds.times(1000).toDecimalPlaces(0)
  const ms = totalMs.modulo(1000)
  const totalIntSeconds = totalMs.minus(ms).dividedBy(1000).toDecimalPlaces(0)
  const secs = totalIntSeconds.modulo(60)
  const minutes = totalIntSeconds.dividedToIntegerBy(60).modulo(60)
  const hours = totalIntSeconds.dividedToIntegerBy(3600)

  const msStr = ms.toNumber().toString().padStart(3, '0')
  const secsStr = secs.toNumber().toString().padStart(2, '0')
  const minsStr = minutes.toNumber().toString().padStart(2, '0')
  const hoursStr = hours.toNumber().toString().padStart(2, '0')

  if (format === 'MM:SS') {
    const totalMinutes = totalIntSeconds.dividedToIntegerBy(60)
    return `${totalMinutes.toNumber().toString().padStart(2, '0')}:${secsStr}${separator}${msStr}`
  }

  return `${hoursStr}:${minsStr}:${secsStr}${separator}${msStr}`
}

/**
 * 格式化时间为VTT格式 (HH:MM:SS.mmm)
 * @param timeString 时间字符串，支持多种格式
 * @returns VTT格式的时间字符串
 */
export function formatTimeForVtt(timeString: string): string {
  // 如果已经是VTT格式，直接返回
  if (/^\d{2}:\d{2}:\d{2}\.\d{3}$/.test(timeString)) {
    return timeString
  }

  // 先标准化为带逗号的格式，然后转换为VTT格式
  const normalized = normalizeVttTime(timeString, ',')
  return normalized.replace(',', '.')
}

/**
 * 格式化时间为SRT格式显示 (HH:MM:SS,mmm)
 * @param seconds 秒数
 * @returns SRT格式的时间字符串
 */
export function formatTimeForSRT(seconds: number): string {
  const dSeconds = new Decimal(seconds)
  const totalMs = dSeconds.times(1000).toDecimalPlaces(0)
  const ms = totalMs.modulo(1000)
  const totalIntSeconds = totalMs.minus(ms).dividedBy(1000).toDecimalPlaces(0)
  const secs = totalIntSeconds.modulo(60)
  const minutes = totalIntSeconds.dividedToIntegerBy(60).modulo(60)
  const hours = totalIntSeconds.dividedToIntegerBy(3600)

  const msStr = ms.toNumber().toString().padStart(3, '0')
  const secsStr = secs.toNumber().toString().padStart(2, '0')
  const minsStr = minutes.toNumber().toString().padStart(2, '0')
  const hoursStr = hours.toNumber().toString().padStart(2, '0')

  return `${hoursStr}:${minsStr}:${secsStr},${msStr}`
}

/**
 * 格式化持续时间（毫秒 → "分:秒" 字符串） 返回 mm:ss
 */
export function formatDurationToMinute(ms: number): string {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

/**
 * 毫秒转秒
 * @param ms 毫秒数
 * @returns 秒数
 */
export function millisecondsToSeconds(ms: number): number {
  return new Decimal(ms).dividedBy(1000).toNumber()
}

/**
 * 标准化时间字符串为 HH:MM:SS,mmm 或 HH:MM:SS.mmm 格式
 * 支持各种宽松格式：mm:ss, mm:ss.xxx, HH:MM:SS, HH:MM:SS.xxx 等
 * @param raw 原始时间字符串，如 "3:5", "3:5.1", "3:5:1.1", "03:05:01.123", "12:34:56" 等
 * @param separator 秒与毫秒的分隔符，默认为逗号
 * @returns 标准化后的时间字符串 "HH:MM:SS,mmm" 或 "HH:MM:SS.mmm"
 */
export function normalizeVttTime(raw: string, separator: ',' | '.' = ','): string {
  // 匹配 mm:ss 或 mm:ss.xxx 格式（2级时间）
  let match = raw.match(/^(\d{1,2}):(\d{1,2})(?:[.,](\d{1,4}))?$/)
  if (match) {
    let [, m, s, ms] = match
    m = m.padStart(2, '0')
    s = s.padStart(2, '0')
    ms = ms ? ms.slice(0, 3).padEnd(3, '0') : '000'
    return `00:${m}:${s}${separator}${ms}`
  }

  // 匹配 HH:MM:SS 或 HH:MM:SS.xxx 格式（3级时间）
  // eslint-disable-next-line regexp/no-unused-capturing-group
  match = raw.match(/^(\d{1,2}):(\d{1,2}):(\d{1,2})([.,](\d+))?$/)
  if (match) {
    let [, h, m, s, , ms] = match
    h = h.padStart(2, '0')
    m = m.padStart(2, '0')
    s = s.padStart(2, '0')
    ms = ms ? ms.slice(0, 3).padEnd(3, '0') : '000'
    return `${h}:${m}:${s}${separator}${ms}`
  }

  return raw // 不合法则原样返回
}

// ============================================================================
// 时间解析函数
// ============================================================================

/**
 * 解析 SRT 时间字符串 "HH:MM:SS,mmm" 为秒数（浮点数）
 */
export function parseSrtTime(timeString: string): number | null {
  const normalized = normalizeVttTime(timeString, ',')
  if (normalized === timeString && !timeString.match(/^(?:\d{2}:){2}\d{2},\d{3}$/)) {
    return null // 严格验证SRT格式，不能标准化说明格式不正确
  }
  const result = parseNormalizedTimeToSeconds(normalized)
  return result === null ? null : result
}

/**
 * 将时间字符串解析为秒数
 * @param timeString 格式为 "mm:ss" 或 "mm:ss.xxx" 的时间字符串
 * @returns 转换后的秒数，如果格式无效则返回 0
 */
export function parseTimeStringToSeconds(timeString: string): number {
  const normalized = normalizeVttTime(timeString, ',')
  const result = parseNormalizedTimeToSeconds(normalized)
  return result === null ? 0 : result
}

/**
 * 通用解析函数：将标准化的时间字符串转换为秒数
 * @param normalizedTime 标准化的时间字符串 "HH:MM:SS,mmm" 或 "HH:MM:SS.mmm"
 * @returns 转换后的秒数，如果格式无效则返回 0
 */
export function parseNormalizedTimeToSeconds(normalizedTime: string): number | null {
  const match = normalizedTime.match(/^(\d{2}):(\d{2}):(\d{2})[,.](\d{3})$/)
  if (!match)
    return null

  const [, hh, mm, ss, ms] = match
  const hours = new Decimal(hh)
  const minutes = new Decimal(mm)
  const seconds = new Decimal(ss)
  const milliseconds = new Decimal(ms)

  // 验证范围
  if (minutes.gte(60) || seconds.gte(60) || hours.gte(100) || milliseconds.gte(1000))
    return null

  return hours.times(3600).plus(minutes.times(60)).plus(seconds).plus(milliseconds.dividedBy(1000)).toNumber()
}

/**
 * 通用时间戳转秒数函数
 */
export function timestampToSeconds(timestamp: string | number): number {
  if (typeof timestamp === 'number') {
    return timestamp
  }

  const timeString = timestamp.toString()

  // 先尝试使用 parseTimeStringToSeconds，因为它有完整的格式验证
  const result = parseTimeStringToSeconds(timeString)
  if (result !== 0) {
    return result
  }

  // 如果 parseTimeStringToSeconds 返回 0，再尝试简单的秒数解析
  if (/^\d+(?:\.\d+)?$/.test(timeString)) {
    const seconds = Number.parseFloat(timeString)
    return !Number.isNaN(seconds) && seconds >= 0 ? seconds : 0
  }

  return 0
}

// ============================================================================
// 时间验证函数
// ============================================================================

/**
 * 校验 SRT 时间字符串格式 "HH:MM:SS,mmm"
 */
export function validateSrtTimeFormat(timeString: string): boolean {
  const normalized = normalizeVttTime(timeString, ',')
  const result = parseNormalizedTimeToSeconds(normalized)
  return normalized === timeString && result !== null
}

/**
 * 校验开始时间不能晚于结束时间
 */
export function validateTimeLogic(startTime: string, endTime: string): boolean {
  const startNormalized = normalizeVttTime(startTime, ',')
  const endNormalized = normalizeVttTime(endTime, ',')
  const start = parseNormalizedTimeToSeconds(startNormalized)
  const end = parseNormalizedTimeToSeconds(endNormalized)
  if (start === null || end === null)
    return false
  return start <= end
}

/**
 * 检测字幕时间戳是否有错误
 * @param subtitle 当前字幕
 * @param prevSubtitle 前一个字幕（可选）
 * @param nextSubtitle 下一个字幕（可选）
 * @returns 如果时间戳有错误返回true，否则返回false
 */
export function hasTimestampError(subtitle: Subtitle, prevSubtitle?: Subtitle, nextSubtitle?: Subtitle): boolean {
  // 1. 格式验证
  if (!validateSrtTimeFormat(subtitle.startTime) || !validateSrtTimeFormat(subtitle.endTime)) {
    return true
  }

  // 2. 时间逻辑验证
  if (!validateTimeLogic(subtitle.startTime, subtitle.endTime)) {
    return true
  }

  // 3. 与前一字幕的顺序验证
  if (prevSubtitle) {
    const currentStart = parseSrtTime(subtitle.startTime)
    const prevEnd = parseSrtTime(prevSubtitle.endTime)
    if (currentStart !== null && prevEnd !== null && currentStart < prevEnd) {
      return true
    }
  }

  // 4. 与下一字幕的顺序验证
  if (nextSubtitle) {
    const currentEnd = parseSrtTime(subtitle.endTime)
    const nextStart = parseSrtTime(nextSubtitle.startTime)
    if (currentEnd !== null && nextStart !== null && currentEnd > nextStart) {
      return true
    }
  }

  return false
}

/**
 * 验证时间戳格式
 */
export function isValidTimestamp(timestamp: string | number): boolean {
  if (typeof timestamp === 'number') {
    return timestamp >= 0
  }

  const timeString = timestamp.toString()

  // SRT格式
  if (/^\d{2}:\d{2}:\d{2}[,.]\d{3}$/.test(timeString)) {
    return parseSrtTime(timeString) !== null
  }

  // 秒数格式
  if (/^\d+(?:\.\d+)?$/.test(timeString)) {
    const seconds = Number.parseFloat(timeString)
    return !Number.isNaN(seconds) && seconds >= 0
  }

  return false
}
