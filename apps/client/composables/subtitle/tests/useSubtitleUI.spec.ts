import { describe, it, expect, vi, beforeEach } from 'vitest'

// Mock Vue dependencies
const mockRef = vi.fn((value) => ({ value }))
const mockComputed = vi.fn((fn) => ({ value: fn() }))
const mockWatch = vi.fn()
const mockNextTick = vi.fn(() => Promise.resolve())

vi.mock('vue', () => ({
  ref: mockRef,
  computed: mockComputed,
  watch: mockWatch,
  nextTick: mockNextTick,
}))

// Make Vue functions available globally
global.ref = mockRef
global.computed = mockComputed
global.watch = mockWatch
global.nextTick = mockNextTick

// Mock dependencies
vi.mock('@vueuse/core', () => ({
  useRafFn: vi.fn((fn) => ({
    resume: vi.fn(() => fn()),
  })),
}))

vi.mock('vue-sonner', () => ({
  toast: {
    error: vi.fn(),
    warning: vi.fn(),
    success: vi.fn(),
  },
}))

vi.mock('lodash-es', () => ({
  cloneDeep: vi.fn((obj) => JSON.parse(JSON.stringify(obj))),
  find: vi.fn(),
  findIndex: vi.fn(),
  isEmpty: vi.fn((val) => !val || val.length === 0),
  map: vi.fn((arr, fn) => arr.map(fn)),
}))

vi.mock('~/composables/loadingSpinner', () => ({
  useLoadingSpinner: vi.fn(() => ({
    withLoading: vi.fn((fn) => fn()),
  })),
}))

vi.mock('~/stores/gameUpdate', () => ({
  useGameUpdateStore: vi.fn(() => ({
    markCourseForUpdate: vi.fn(),
  })),
}))

// LRC store mock is handled in test-setup.ts

// Player store mock is handled in test-setup.ts

vi.mock('~/utils/file', () => ({
  calculateFileHash: vi.fn(() => Promise.resolve('hash123')),
  uploadMediaFileToS3: vi.fn(() => Promise.resolve('s3-key')),
}))

vi.mock('./useFormatProcessing', () => ({
  convertStatementsToSubtitles: vi.fn(() => []),
  lrcToSrt: vi.fn(() => []),
  parseSrt: vi.fn(() => []),
  parseVtt: vi.fn(() => []),
  subtitleToLrc: vi.fn(() => ''),
}))

vi.mock('./useSubtitleProcessing', () => ({
  processSubtitleChanges: vi.fn(() => ({
    newSubtitles: [],
    updatedSubtitles: [],
    unchangedSubtitles: [],
    deletedSentences: [],
  })),
}))

vi.mock('./useTimeUtils', () => ({
  parseSrtTime: vi.fn((time: string) => {
    if (time === '00:00:01,000') return 1.0
    if (time === '00:00:02,000') return 2.0
    if (time === '00:00:03,000') return 3.0
    return 0
  }),
  parseTimeStringToSeconds: vi.fn((time: string) => {
    if (time === '00:00:01,000') return 1.0
    if (time === '00:00:02,000') return 2.0
    return 0
  }),
}))

// Mock Nuxt functions globally
const mockUseNuxtApp = vi.fn(() => ({
  $trpc: {
    sentence: {
      batchUpsertSentences: {
        mutate: vi.fn(() => Promise.resolve({
          sentences: [
            { uuid: 'uuid1', sentenceId: 'sent1' },
            { uuid: 'uuid2', sentenceId: 'sent2' },
          ],
        })),
      },
      deleteBatchSentences: {
        mutate: vi.fn(() => Promise.resolve({ failedIds: [] })),
      },
      movePosition: {
        mutate: vi.fn(() => Promise.resolve()),
      },
      batchUpdateElementsTime: {
        mutate: vi.fn(() => Promise.resolve()),
      },
    },
    course: {
      findOne: {
        query: vi.fn(() => Promise.resolve({
          id: 'course-1',
          coursePackId: 'pack-1',
          title: 'Test Course',
          sentences: [],
        })),
      },
      edit: {
        mutate: vi.fn(() => Promise.resolve()),
      },
    },
    speech: {
      submit: {
        mutate: vi.fn(() => Promise.resolve({ id: 'task-1', message: 'Success' })),
      },
      query: {
        query: vi.fn(() => Promise.resolve({
          code: 0,
          message: 'Success',
          utterances: [
            { text: 'Hello', start_time: 0, end_time: 1000 },
            { text: 'World', start_time: 1000, end_time: 2000 },
          ],
        })),
      },
    },
  },
}))

const mockUseRuntimeConfig = vi.fn(() => ({
  public: {
    s3: {
      bucketGameCDN: 'https://cdn.example.com/',
    },
  },
}))

vi.mock('#app', () => ({
  useNuxtApp: mockUseNuxtApp,
}))

vi.mock('#imports', () => ({
  useRuntimeConfig: mockUseRuntimeConfig,
}))

// Make Nuxt functions available globally
global.useNuxtApp = mockUseNuxtApp
global.useRuntimeConfig = mockUseRuntimeConfig

vi.mock('~/stores/course', () => ({
  useCourseStore: vi.fn(() => ({
    currentCourse: {
      id: 'course-1',
      coursePackId: 'pack-1',
      title: 'Test Course',
      description: 'Test Description',
      sentences: [
        { id: 'sent1', content: 'Hello', chinese: '你好', position: 1 },
        { id: 'sent2', content: 'World', chinese: '世界', position: 2 },
      ],
    },
    updateCourseData: vi.fn(),
  })),
}))

// Subtitle store mock is handled in test-setup.ts

vi.mock('./useChangeDetection', () => ({
  useChangeDetection: vi.fn(() => ({
    hasUnsavedChanges: false,
  })),
}))

// Constants mock is handled in test-setup.ts

vi.mock('./useValidation', () => ({
  validateComplete: vi.fn(() => ({
    isValid: true,
    errorMessage: null,
    hasEmptyContent: false,
    hasTimeErrors: false,
    hasNoMediaFile: false,
    timeErrors: [],
  })),
}))

// Mock DOM
global.document = {
  getElementById: vi.fn(),
} as any

// Import the module after all mocks
import {
  useSubtitleActions,
  useSubtitleEditing,
  useMergeHighlight,
  useTopBarLogic,
} from '../useSubtitleUI'

describe('useSubtitleUI', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset Vue mock implementations
    mockRef.mockImplementation((value) => ({ value }))
    mockComputed.mockImplementation((fn) => ({ value: fn() }))
    mockWatch.mockImplementation(() => {})
    mockNextTick.mockImplementation(() => Promise.resolve())
  })

  // ============================================================================
  // 字幕操作功能测试
  // ============================================================================

  describe('useSubtitleActions', () => {
    it('应该存在 useSubtitleActions 函数', () => {
      expect(typeof useSubtitleActions).toBe('function')
    })

    it('应该正确处理添加字幕操作', () => {
      const mockSubtitle = { value: { uuid: 'uuid1', id: 1 } }
      const mockStore = {
        addSubtitle: vi.fn(),
        deleteSubtitle: vi.fn(),
        mergeWithNextSubtitle: vi.fn(),
      }
      const isAddDisabled = { value: false }
      const isLast = { value: false }

      // Mock computed to return proper handlers
      mockComputed.mockReturnValueOnce({ value: '添加一行' })

      const actions = useSubtitleActions(mockSubtitle, mockStore, isAddDisabled, isLast)
      
      expect(actions).toHaveProperty('addTooltipContent')
      expect(actions).toHaveProperty('handleAddSubtitle')
      expect(actions).toHaveProperty('handleDeleteSubtitle')
      expect(actions).toHaveProperty('handleMergeSubtitle')
    })

    it('应该正确处理删除字幕操作', () => {
      const mockSubtitle = { value: { uuid: 'uuid1', id: 1 } }
      const mockStore = {
        addSubtitle: vi.fn(),
        deleteSubtitle: vi.fn(),
        mergeWithNextSubtitle: vi.fn(),
      }
      const isAddDisabled = { value: false }
      const isLast = { value: false }

      const actions = useSubtitleActions(mockSubtitle, mockStore, isAddDisabled, isLast)
      
      expect(actions.handleDeleteSubtitle).toBeDefined()
      expect(typeof actions.handleDeleteSubtitle).toBe('function')
    })

    it('应该正确处理合并字幕操作', () => {
      const mockSubtitle = { value: { uuid: 'uuid1', id: 1 } }
      const mockStore = {
        addSubtitle: vi.fn(),
        deleteSubtitle: vi.fn(),
        mergeWithNextSubtitle: vi.fn(),
      }
      const isAddDisabled = { value: false }
      const isLast = { value: false }

      const actions = useSubtitleActions(mockSubtitle, mockStore, isAddDisabled, isLast)
      
      expect(actions.handleMergeSubtitle).toBeDefined()
      expect(typeof actions.handleMergeSubtitle).toBe('function')
    })
  })

  // ============================================================================
  // 字幕编辑功能测试
  // ============================================================================

  describe('useSubtitleEditing', () => {
    it('应该存在 useSubtitleEditing 函数', () => {
      expect(typeof useSubtitleEditing).toBe('function')
    })

    it('应该正确初始化编辑状态', () => {
      const mockSubtitle = { 
        value: {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
        }
      }
      const mockStore = {
        updateSubtitleText: vi.fn(),
        updateSubtitleTranslationText: vi.fn(),
        updateSubtitleStartTime: vi.fn(),
        updateSubtitleEndTime: vi.fn(),
      }

      // Mock ref calls for the editing state
      mockRef
        .mockReturnValueOnce({ value: false }) // editing
        .mockReturnValueOnce({ value: 'Hello' }) // editingText
        .mockReturnValueOnce({ value: '你好' }) // editingTranslationText
        .mockReturnValueOnce({ value: null }) // editingType
        .mockReturnValueOnce({ value: '00:00:01,000' }) // startTime
        .mockReturnValueOnce({ value: '00:00:02,000' }) // endTime

      const editing = useSubtitleEditing(mockSubtitle, mockStore)
      
      expect(editing).toHaveProperty('editing')
      expect(editing).toHaveProperty('editingText')
      expect(editing).toHaveProperty('editingTranslationText')
      expect(editing).toHaveProperty('editingType')
      expect(editing).toHaveProperty('startTime')
      expect(editing).toHaveProperty('endTime')
      expect(editing).toHaveProperty('startEdit')
      expect(editing).toHaveProperty('confirmEdit')
      expect(editing).toHaveProperty('cancelEdit')
    })

    it('应该正确处理编辑操作', () => {
      const mockSubtitle = { 
        value: {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
        }
      }
      const mockStore = {
        updateSubtitleText: vi.fn(),
        updateSubtitleTranslationText: vi.fn(),
        updateSubtitleStartTime: vi.fn(),
        updateSubtitleEndTime: vi.fn(),
      }

      const editing = useSubtitleEditing(mockSubtitle, mockStore)
      
      expect(typeof editing.startEdit).toBe('function')
      expect(typeof editing.confirmEdit).toBe('function')
      expect(typeof editing.cancelEdit).toBe('function')
      expect(typeof editing.onStartTime).toBe('function')
      expect(typeof editing.onEndTime).toBe('function')
    })
  })

  // ============================================================================
  // 合并高亮功能测试
  // ============================================================================

  describe('useMergeHighlight', () => {
    it('应该存在 useMergeHighlight 函数', () => {
      expect(typeof useMergeHighlight).toBe('function')
    })

    it('应该正确初始化高亮状态', () => {
      const mockStore = {
        subtitles: [
          { uuid: 'uuid1', id: 1, text: 'Hello' },
          { uuid: 'uuid2', id: 2, text: 'World' },
        ],
      }

      // Mock ref calls for the highlight state
      mockRef
        .mockReturnValueOnce({ value: null }) // hoveredMergeCandidateUuid
        .mockReturnValueOnce({ value: {} }) // highlightBoxStyle
        .mockReturnValueOnce({ value: false }) // showHighlightBox

      const highlight = useMergeHighlight(mockStore)
      
      expect(highlight).toHaveProperty('highlightBoxStyle')
      expect(highlight).toHaveProperty('showHighlightBox')
      expect(highlight).toHaveProperty('handleMergeButtonHover')
      expect(highlight).toHaveProperty('handleMergeButtonLeave')
    })

    it('应该正确处理高亮事件', () => {
      const mockStore = {
        subtitles: [
          { uuid: 'uuid1', id: 1, text: 'Hello' },
          { uuid: 'uuid2', id: 2, text: 'World' },
        ],
      }

      const highlight = useMergeHighlight(mockStore)
      
      expect(typeof highlight.handleMergeButtonHover).toBe('function')
      expect(typeof highlight.handleMergeButtonLeave).toBe('function')
    })
  })

  // ============================================================================
  // TopBar 逻辑测试
  // ============================================================================

  describe('useTopBarLogic', () => {
    it('应该存在 useTopBarLogic 函数', () => {
      expect(typeof useTopBarLogic).toBe('function')
    })

    it('应该正确初始化TopBar状态', () => {
      expect(() => {
        useTopBarLogic()
      }).not.toThrow()
      
      // 测试函数返回正确的属性
      const topBar = useTopBarLogic()
      
      expect(topBar).toHaveProperty('isOnlineLrcModalOpen')
      expect(topBar).toHaveProperty('audioFileInputRef')
      expect(topBar).toHaveProperty('canSaveChanges')
      expect(topBar).toHaveProperty('subtitleFileInputRef')
      expect(topBar).toHaveProperty('totalElementsCount')
      expect(topBar).toHaveProperty('triggerAudioFileInput')
      expect(topBar).toHaveProperty('handleAudioFileUpload')
      expect(topBar).toHaveProperty('handleFinish')
    })

    it('应该正确处理文件上传操作', () => {
      const topBar = useTopBarLogic()
      
      expect(typeof topBar.triggerAudioFileInput).toBe('function')
      expect(typeof topBar.handleAudioFileUpload).toBe('function')
      expect(typeof topBar.triggerSubtitleFileInput).toBe('function')
      expect(typeof topBar.handleSubtitleFileUpload).toBe('function')
    })

    it('应该正确处理字幕操作', () => {
      const topBar = useTopBarLogic()
      
      expect(typeof topBar.handleFinish).toBe('function')
      expect(typeof topBar.handleSubtitleAlign).toBe('function')
      expect(typeof topBar.handleSubtitleFormat).toBe('function')
    })

    it('应该正确处理LRC操作', () => {
      const topBar = useTopBarLogic()
      
      expect(typeof topBar.handleGetLrc).toBe('function')
      expect(typeof topBar.handleApplyOnlineLrc).toBe('function')
    })
  })

  // ============================================================================
  // 边界情况和错误处理测试
  // ============================================================================

  describe('边界情况测试', () => {
    it('应该处理空的字幕数据', () => {
      const mockSubtitle = { value: null }
      const mockStore = {
        addSubtitle: vi.fn(),
        deleteSubtitle: vi.fn(),
        mergeWithNextSubtitle: vi.fn(),
      }
      const isAddDisabled = { value: false }
      const isLast = { value: false }

      expect(() => {
        useSubtitleActions(mockSubtitle, mockStore, isAddDisabled, isLast)
      }).not.toThrow()
    })

    it('应该处理无效的store对象', () => {
      const mockSubtitle = { value: { uuid: 'uuid1', id: 1 } }
      const isAddDisabled = { value: false }
      const isLast = { value: false }

      expect(() => {
        useSubtitleActions(mockSubtitle, isAddDisabled, isLast)
      }).not.toThrow()
    })

    it('应该处理空的字幕数组', () => {
      const mockStore = {
        subtitles: [],
      }

      expect(() => {
        useMergeHighlight(mockStore)
      }).not.toThrow()
    })
  })

  // ============================================================================
  // 函数存在性测试
  // ============================================================================

  describe('函数存在性测试', () => {
    it('应该导出所有必要的函数', () => {
      expect(useSubtitleActions).toBeDefined()
      expect(useSubtitleEditing).toBeDefined()
      expect(useMergeHighlight).toBeDefined()
      expect(useTopBarLogic).toBeDefined()
    })

    it('所有导出的函数都应该是函数类型', () => {
      expect(typeof useSubtitleActions).toBe('function')
      expect(typeof useSubtitleEditing).toBe('function')
      expect(typeof useMergeHighlight).toBe('function')
      expect(typeof useTopBarLogic).toBe('function')
    })
  })

  // ============================================================================
  // 类型检查测试
  // ============================================================================

  describe('类型检查测试', () => {
    it('应该接受正确的参数类型', () => {
      const mockSubtitle = { value: { uuid: 'uuid1', id: 1 } }
      const mockStore = {
        addSubtitle: vi.fn(),
        deleteSubtitle: vi.fn(),
        mergeWithNextSubtitle: vi.fn(),
      }
      const isAddDisabled = { value: false }
      const isLast = { value: false }

      expect(() => {
        useSubtitleActions(mockSubtitle, mockStore, isAddDisabled, isLast)
      }).not.toThrow()
    })

    it('应该处理部分参数缺失的情况', () => {
      const mockSubtitle = { value: { uuid: 'uuid1', id: 1 } }
      const mockStore = {
        addSubtitle: vi.fn(),
        deleteSubtitle: vi.fn(),
        mergeWithNextSubtitle: vi.fn(),
      }
      const isAddDisabled = { value: false }
      const isLast = { value: false }

      expect(() => {
        useSubtitleActions(mockSubtitle, mockStore, isAddDisabled, isLast)
      }).not.toThrow()
    })
  })
})