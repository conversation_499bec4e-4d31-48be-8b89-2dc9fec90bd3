import { describe, test, expect, vi, beforeEach } from 'vitest'
import type { Course, Sentence } from '@julebu/shared'
import type { Subtitle } from '~/types/subtitle/subtitle'

// Mock the parseSrtTime function
const mockParseSrtTime = vi.fn()
vi.mock('../useTimeUtils', () => ({
  parseSrtTime: mockParseSrtTime,
}))

// Mock constants
vi.mock('~/utils/shared/constants', () => ({
  AUDIO_MIME_PREFIX: 'audio/',
  VIDEO_MIME_PREFIX: 'video/',
}))

describe('useSubtitleUI - canSaveChanges 逻辑测试', () => {
  // Mock data
  let mockPlayerStore: {
    mediaFile: File | null
    mediaFileHash: string | null
    originalMediaFileHash: string | null
  }

  let mockLrcStore: {
    hasLrc: boolean
  }

  let mockSubtitleStore: {
    subtitles: Subtitle[]
  }

  let mockCourseStore: {
    currentCourse: Course | null
  }

  // Helper function to implement the canSaveChanges logic
  const checkHasValidChanges = () => {
    // 检查是否有媒体文件
    if (!mockPlayerStore.mediaFile) {
      return false
    }

    const { type } = mockPlayerStore.mediaFile
    // 检查媒体文件格式
    if (!type.startsWith('audio/') && !type.startsWith('video/')) {
      return false
    }

    // 检查是否有LRC内容
    if (!mockLrcStore.hasLrc) {
      return false
    }

    // 检查字幕内容是否有效
    const hasValidSubtitles = mockSubtitleStore.subtitles.some(
      subtitle => subtitle.text.trim() !== '' || subtitle.translationText.trim() !== '',
    )

    if (!hasValidSubtitles) {
      return false
    }

    // 检查内容是否有变更（时间/原文/翻译）
    const originalSentences = mockCourseStore.currentCourse?.sentences ?? []
    const hasContentChanges = mockSubtitleStore.subtitles.some((subtitle) => {
      // 对于新增的字幕（没有sentenceId），如果有内容就算有变更
      if (!subtitle.sentenceId) {
        return subtitle.text.trim() !== '' || subtitle.translationText.trim() !== ''
      }

      // 对于已存在的句子，检查是否有变更
      const originalSentence = originalSentences.find(s => s.id === subtitle.sentenceId)
      if (!originalSentence) {
        return true // 原始句子不存在，算作有变更
      }

      // 检查内容变更
      const textChanged = subtitle.text.trim() !== (originalSentence.content || '').trim()
      const translationChanged = subtitle.translationText.trim() !== (originalSentence.chinese || '').trim()
      
      // 检查时间变更
      const currentStartTime = mockParseSrtTime(subtitle.startTime)
      const currentEndTime = mockParseSrtTime(subtitle.endTime)
      const timeChanged = currentStartTime !== originalSentence.startTime || currentEndTime !== originalSentence.endTime

      return textChanged || translationChanged || timeChanged
    })

    // 检查是否有原始句子被删除
    const currentSentenceIds = new Set(mockSubtitleStore.subtitles.map(s => s.sentenceId).filter(Boolean))
    const hasDeletedSentences = originalSentences.some(originalSentence => 
      originalSentence.id && !currentSentenceIds.has(originalSentence.id)
    )

    // 检查媒体文件是否有变更
    const hasMediaFileChanges = mockPlayerStore.mediaFileHash && mockPlayerStore.originalMediaFileHash && 
      mockPlayerStore.mediaFileHash !== mockPlayerStore.originalMediaFileHash

    if (!hasContentChanges && !hasDeletedSentences && !hasMediaFileChanges) {
      return false
    }

    return true
  }

  beforeEach(() => {
    // Reset all mocks and data
    mockPlayerStore = {
      mediaFile: null,
      mediaFileHash: null,
      originalMediaFileHash: null,
    }
    mockLrcStore = {
      hasLrc: false,
    }
    mockSubtitleStore = {
      subtitles: [],
    }
    mockCourseStore = {
      currentCourse: null,
    }
    vi.clearAllMocks()
  })

  describe('基础验证', () => {
    test('没有媒体文件时返回false', () => {
      const result = checkHasValidChanges()
      expect(result).toBe(false)
    })

    test('媒体文件不是音频或视频时返回false', () => {
      mockPlayerStore.mediaFile = new File([''], 'test.txt', { type: 'text/plain' })
      const result = checkHasValidChanges()
      expect(result).toBe(false)
    })

    test('没有LRC内容时返回false', () => {
      mockPlayerStore.mediaFile = new File([''], 'test.mp3', { type: 'audio/mpeg' })
      mockLrcStore.hasLrc = false
      const result = checkHasValidChanges()
      expect(result).toBe(false)
    })

    test('没有有效字幕内容时返回false', () => {
      mockPlayerStore.mediaFile = new File([''], 'test.mp3', { type: 'audio/mpeg' })
      mockLrcStore.hasLrc = true
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: '',
          translationText: '',
          sentenceId: 'sent1',
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(false)
    })
  })

  describe('新增字幕检测', () => {
    test('新增字幕有原文内容时返回true', () => {
      mockPlayerStore.mediaFile = new File([''], 'test.mp3', { type: 'audio/mpeg' })
      mockLrcStore.hasLrc = true
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: 'Hello world',
          translationText: '',
          // 没有sentenceId，表示是新增的
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(true)
    })

    test('新增字幕有翻译内容时返回true', () => {
      mockPlayerStore.mediaFile = new File([''], 'test.mp3', { type: 'audio/mpeg' })
      mockLrcStore.hasLrc = true
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: '',
          translationText: '你好世界',
          // 没有sentenceId，表示是新增的
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(true)
    })
  })

  describe('已存在字幕变更检测', () => {
    beforeEach(() => {
      mockPlayerStore.mediaFile = new File([''], 'test.mp3', { type: 'audio/mpeg' })
      mockLrcStore.hasLrc = true
      mockCourseStore.currentCourse = {
        id: 'course1',
        sentences: [
          {
            id: 'sent1',
            content: 'Original text',
            chinese: 'Original translation',
            startTime: 0,
            endTime: 2000,
          } as Sentence,
        ],
      } as Course
      mockParseSrtTime.mockImplementation((time: string) => {
        if (time === '00:00:00,000') return 0
        if (time === '00:00:02,000') return 2000
        if (time === '00:00:03,000') return 3000
        return 0
      })
    })

    test('原文内容变更时返回true', () => {
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: 'Modified text', // 原文变更
          translationText: 'Original translation',
          sentenceId: 'sent1',
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(true)
    })

    test('翻译内容变更时返回true', () => {
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: 'Original text',
          translationText: 'Modified translation', // 翻译变更
          sentenceId: 'sent1',
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(true)
    })

    test('开始时间变更时返回true', () => {
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:03,000', // 时间变更
          endTime: '00:00:02,000',
          text: 'Original text',
          translationText: 'Original translation',
          sentenceId: 'sent1',
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(true)
    })

    test('结束时间变更时返回true', () => {
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:03,000', // 时间变更
          text: 'Original text',
          translationText: 'Original translation',
          sentenceId: 'sent1',
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(true)
    })

    test('内容完全相同时返回false', () => {
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: 'Original text',
          translationText: 'Original translation',
          sentenceId: 'sent1',
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(false)
    })

    test('原始句子不存在时返回true', () => {
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: 'Some text',
          translationText: 'Some translation',
          sentenceId: 'nonexistent', // 原始句子不存在
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(true)
    })

    test('删除了原始句子时返回true', () => {
      // 原始课程有两个句子
      mockCourseStore.currentCourse = {
        id: 'course1',
        sentences: [
          {
            id: 'sent1',
            content: 'Original text 1',
            chinese: 'Original translation 1',
            startTime: 0,
            endTime: 2000,
          } as Sentence,
          {
            id: 'sent2',
            content: 'Original text 2',
            chinese: 'Original translation 2',
            startTime: 2000,
            endTime: 4000,
          } as Sentence,
        ],
      } as Course
      
      // 当前字幕只有一个句子，删除了 sent2
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: 'Original text 1',
          translationText: 'Original translation 1',
          sentenceId: 'sent1',
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(true)
    })

    test('删除了所有原始句子时返回false（因为没有有效字幕内容）', () => {
      // 原始课程有句子
      mockCourseStore.currentCourse = {
        id: 'course1',
        sentences: [
          {
            id: 'sent1',
            content: 'Original text 1',
            chinese: 'Original translation 1',
            startTime: 0,
            endTime: 2000,
          } as Sentence,
        ],
      } as Course
      
      // 当前字幕为空（删除了所有内容）
      mockSubtitleStore.subtitles = []
      const result = checkHasValidChanges()
      expect(result).toBe(false) // 因为没有有效字幕内容，在检查删除之前就返回false
    })
  })

  describe('空白字符处理', () => {
    beforeEach(() => {
      mockPlayerStore.mediaFile = new File([''], 'test.mp3', { type: 'audio/mpeg' })
      mockLrcStore.hasLrc = true
      mockCourseStore.currentCourse = {
        id: 'course1',
        sentences: [
          {
            id: 'sent1',
            content: 'Original text',
            chinese: 'Original translation',
            startTime: 0,
            endTime: 2000,
          } as Sentence,
        ],
      } as Course
      mockParseSrtTime.mockImplementation((time: string) => {
        if (time === '00:00:00,000') return 0
        if (time === '00:00:02,000') return 2000
        return 0
      })
    })

    test('应该正确处理前后空格', () => {
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: '  Original text  ', // 前后有空格但内容相同
          translationText: '  Original translation  ',
          sentenceId: 'sent1',
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(false)
    })

    test('空字符串应该被正确处理', () => {
      mockCourseStore.currentCourse = {
        id: 'course1',
        sentences: [
          {
            id: 'sent1',
            content: '',
            chinese: '',
            startTime: 0,
            endTime: 2000,
          } as Sentence,
        ],
      } as Course

      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: '   ', // 只有空格
          translationText: '',
          sentenceId: 'sent1',
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(false)
    })
  })

  describe('多媒体格式支持', () => {
    test('支持音频格式', () => {
      mockPlayerStore.mediaFile = new File([''], 'test.mp3', { type: 'audio/mpeg' })
      mockLrcStore.hasLrc = true
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: 'Hello world',
          translationText: '',
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(true)
    })

    test('支持视频格式', () => {
      mockPlayerStore.mediaFile = new File([''], 'test.mp4', { type: 'video/mp4' })
      mockLrcStore.hasLrc = true
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: 'Hello world',
          translationText: '',
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(true)
    })
  })

  describe('媒体文件变更检测', () => {
    beforeEach(() => {
      mockPlayerStore.mediaFile = new File([''], 'test.mp3', { type: 'audio/mpeg' })
      mockLrcStore.hasLrc = true
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: 'Hello world',
          translationText: '',
        },
      ]
    })

    test('媒体文件hash变更时返回true', () => {
      mockPlayerStore.mediaFileHash = 'new-hash-value'
      mockPlayerStore.originalMediaFileHash = 'original-hash-value'
      const result = checkHasValidChanges()
      expect(result).toBe(true)
    })

    test('媒体文件hash相同时按其他变更逻辑处理', () => {
      mockPlayerStore.mediaFileHash = 'same-hash-value'
      mockPlayerStore.originalMediaFileHash = 'same-hash-value'
      const result = checkHasValidChanges()
      expect(result).toBe(true) // 因为有新增字幕内容
    })

    test('没有原始hash时不算作媒体文件变更', () => {
      mockPlayerStore.mediaFileHash = 'some-hash-value'
      mockPlayerStore.originalMediaFileHash = null
      mockSubtitleStore.subtitles = [] // 清空字幕以测试纯媒体文件变更逻辑
      const result = checkHasValidChanges()
      expect(result).toBe(false)
    })

    test('没有当前hash时不算作媒体文件变更', () => {
      mockPlayerStore.mediaFileHash = null
      mockPlayerStore.originalMediaFileHash = 'original-hash-value'
      mockSubtitleStore.subtitles = [] // 清空字幕以测试纯媒体文件变更逻辑
      const result = checkHasValidChanges()
      expect(result).toBe(false)
    })

    test('仅媒体文件变更且有有效字幕时返回true', () => {
      // 设置媒体文件变更
      mockPlayerStore.mediaFileHash = 'new-hash-value'
      mockPlayerStore.originalMediaFileHash = 'original-hash-value'
      
      // 设置字幕内容与原始内容完全相同（无其他变更）
      mockCourseStore.currentCourse = {
        id: 'course1',
        sentences: [
          {
            id: 'sent1',
            content: 'Hello world',
            chinese: '',
            startTime: 0,
            endTime: 2000,
          } as Sentence,
        ],
      } as Course
      
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: 'Hello world',
          translationText: '',
          sentenceId: 'sent1',
        },
      ]
      
      mockParseSrtTime.mockImplementation((time: string) => {
        if (time === '00:00:00,000') return 0
        if (time === '00:00:02,000') return 2000
        return 0
      })
      
      const result = checkHasValidChanges()
      expect(result).toBe(true) // 仅因为媒体文件变更而返回true
    })
  })

  describe('边界情况', () => {
    test('字幕列表为空时返回false', () => {
      mockPlayerStore.mediaFile = new File([''], 'test.mp3', { type: 'audio/mpeg' })
      mockLrcStore.hasLrc = true
      mockSubtitleStore.subtitles = []
      const result = checkHasValidChanges()
      expect(result).toBe(false)
    })

    test('原始句子列表为空时，有字幕内容返回true', () => {
      mockPlayerStore.mediaFile = new File([''], 'test.mp3', { type: 'audio/mpeg' })
      mockLrcStore.hasLrc = true
      mockCourseStore.currentCourse = {
        id: 'course1',
        sentences: [],
      } as unknown as Course
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: 'Hello world',
          translationText: '',
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(true)
    })

    test('课程为null时，有字幕内容返回true', () => {
      mockPlayerStore.mediaFile = new File([''], 'test.mp3', { type: 'audio/mpeg' })
      mockLrcStore.hasLrc = true
      mockCourseStore.currentCourse = null
      mockSubtitleStore.subtitles = [
        {
          uuid: '1',
          id: 1,
          startTime: '00:00:00,000',
          endTime: '00:00:02,000',
          text: 'Hello world',
          translationText: '',
        },
      ]
      const result = checkHasValidChanges()
      expect(result).toBe(true)
    })
  })
})