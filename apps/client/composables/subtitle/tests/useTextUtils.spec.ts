import { describe, it, expect, vi } from 'vitest'
import type { RecognitionResult } from '~/composables/subtitle/useSpeechRecognition'
import {
  detectTextLanguage,
  classifySubtitleLines,
  mergeRecognitionText,
  mergeRecognitionToSentences,
  cleanText,
  truncateText,
  isTextEmpty,
  countTextCharacters,
  countWords,
  containsChinese,
  containsEnglish,
  splitIntoLines,
  splitIntoSentences,
  formatTextForDisplay,
  extractTimeMarkers,
  removeTimeMarkers,
} from '../useTextUtils'

// Constants mock is handled in test-setup.ts

describe('useTextUtils', () => {
  // ============================================================================
  // 文本语言检测测试
  // ============================================================================

  describe('detectTextLanguage', () => {
    it('应该正确识别英文文本', () => {
      expect(detectTextLanguage('Hello world')).toBe('english')
      expect(detectTextLanguage('This is a test')).toBe('english')
      expect(detectTextLanguage('English text with some numbers 123')).toBe('english')
    })

    it('应该正确识别中文文本', () => {
      expect(detectTextLanguage('你好世界')).toBe('chinese')
      expect(detectTextLanguage('这是一个测试')).toBe('chinese')
      expect(detectTextLanguage('中文文本包含一些数字123')).toBe('chinese')
    })

    it('应该正确识别混合文本', () => {
      expect(detectTextLanguage('Hello 世界')).toBe('english') // 英文字符比例≥50%
      expect(detectTextLanguage('你好 world')).toBe('english') // 英文字符比例=50%
      expect(detectTextLanguage('Hi你好')).toBe('english') // 英文字符比例=50%
    })

    it('应该处理特殊字符', () => {
      expect(detectTextLanguage('Hello, world!')).toBe('english')
      expect(detectTextLanguage('你好，世界！')).toBe('chinese')
      expect(detectTextLanguage('123456')).toBe('chinese') // 无字母字符默认为中文
      expect(detectTextLanguage('!@#$%')).toBe('chinese') // 特殊字符默认为中文
    })

    it('应该处理空字符串', () => {
      expect(detectTextLanguage('')).toBe('chinese')
      expect(detectTextLanguage('   ')).toBe('chinese')
    })

    it('应该处理包含日文假名的文本', () => {
      expect(detectTextLanguage('こんにちは')).toBe('chinese') // 日文假名归类为中文
      expect(detectTextLanguage('Hello こんにちは')).toBe('english')
    })
  })

  describe('classifySubtitleLines', () => {
    it('应该正确分类英文和中文行', () => {
      const lines = ['Hello world', '你好世界', 'Good morning', '早上好']
      const result = classifySubtitleLines(lines)
      
      expect(result.text).toBe('Hello world\nGood morning')
      expect(result.translationText).toBe('你好世界\n早上好')
    })

    it('应该处理只有英文的情况', () => {
      const lines = ['Hello world', 'Good morning']
      const result = classifySubtitleLines(lines)
      
      expect(result.text).toBe('Hello world\nGood morning')
      expect(result.translationText).toBe('')
    })

    it('应该处理只有中文的情况', () => {
      const lines = ['你好世界', '早上好']
      const result = classifySubtitleLines(lines)
      
      expect(result.text).toBe('')
      expect(result.translationText).toBe('你好世界\n早上好')
    })

    it('应该处理空数组', () => {
      const result = classifySubtitleLines([])
      
      expect(result.text).toBe('')
      expect(result.translationText).toBe('')
    })

    it('应该处理混合语言行', () => {
      const lines = ['Hello 世界', '你好 world']
      const result = classifySubtitleLines(lines)
      
      expect(result.text).toBe('Hello 世界\n你好 world')
      expect(result.translationText).toBe('')
    })

    it('应该处理包含特殊字符的行', () => {
      const lines = ['Hello!', '你好！', '123', '']
      const result = classifySubtitleLines(lines)
      
      expect(result.text).toBe('Hello!')
      expect(result.translationText).toBe('你好！\n123\n')
    })
  })

  // ============================================================================
  // 语音识别结果处理测试
  // ============================================================================

  describe('mergeRecognitionText', () => {
    it('应该合并识别结果中的文本片段', () => {
      const recognitionResult: RecognitionResult = {
        utterances: [
          { text: 'Hello world', start_time: 0, end_time: 2000 },
          { text: 'How are you', start_time: 2000, end_time: 4000 },
          { text: 'I am fine', start_time: 4000, end_time: 6000 },
        ],
      }

      const result = mergeRecognitionText(recognitionResult)
      expect(result).toBe('Hello world How are you I am fine')
    })

    it('应该处理空的识别结果', () => {
      const recognitionResult: RecognitionResult = {
        utterances: [],
      }

      const result = mergeRecognitionText(recognitionResult)
      expect(result).toBe('')
    })

    it('应该处理 null 或 undefined 的识别结果', () => {
      expect(mergeRecognitionText(null as any)).toBe('')
      expect(mergeRecognitionText(undefined as any)).toBe('')
      expect(mergeRecognitionText({} as any)).toBe('')
    })

    it('应该限制文本长度为 SUMMARY_LENGTH', () => {
      const longText = 'A'.repeat(10000)
      const recognitionResult: RecognitionResult = {
        utterances: [
          { text: longText, start_time: 0, end_time: 2000 },
        ],
      }

      const result = mergeRecognitionText(recognitionResult)
      expect(result).toHaveLength(8000) // SUMMARY_LENGTH
      expect(result).toBe('A'.repeat(8000))
    })

    it('应该处理包含空白文本的识别结果', () => {
      const recognitionResult: RecognitionResult = {
        utterances: [
          { text: 'Hello', start_time: 0, end_time: 1000 },
          { text: '', start_time: 1000, end_time: 2000 },
          { text: 'world', start_time: 2000, end_time: 3000 },
        ],
      }

      const result = mergeRecognitionText(recognitionResult)
      expect(result).toBe('Hello  world')
    })
  })

  describe('mergeRecognitionToSentences', () => {
    it('应该将识别结果转换为句子数组', () => {
      const recognitionResult: RecognitionResult = {
        utterances: [
          { text: 'Hello world', start_time: 0, end_time: 2000 },
          { text: 'How are you', start_time: 2000, end_time: 4000 },
          { text: 'I am fine', start_time: 4000, end_time: 6000 },
        ],
      }

      const result = mergeRecognitionToSentences(recognitionResult)
      expect(result).toEqual(['Hello world', 'How are you', 'I am fine'])
    })

    it('应该过滤空白句子', () => {
      const recognitionResult: RecognitionResult = {
        utterances: [
          { text: 'Hello', start_time: 0, end_time: 1000 },
          { text: '   ', start_time: 1000, end_time: 2000 },
          { text: '', start_time: 2000, end_time: 3000 },
          { text: 'world', start_time: 3000, end_time: 4000 },
        ],
      }

      const result = mergeRecognitionToSentences(recognitionResult)
      expect(result).toEqual(['Hello', 'world'])
    })

    it('应该处理空的识别结果', () => {
      const recognitionResult: RecognitionResult = {
        utterances: [],
      }

      const result = mergeRecognitionToSentences(recognitionResult)
      expect(result).toEqual([])
    })

    it('应该处理 null 或 undefined 的识别结果', () => {
      expect(mergeRecognitionToSentences(null as any)).toEqual([])
      expect(mergeRecognitionToSentences(undefined as any)).toEqual([])
      expect(mergeRecognitionToSentences({} as any)).toEqual([])
    })
  })

  // ============================================================================
  // 通用文本处理工具测试
  // ============================================================================

  describe('cleanText', () => {
    it('应该清理多余的空格', () => {
      expect(cleanText('Hello    world')).toBe('Hello world')
      expect(cleanText('  Hello  world  ')).toBe('Hello world')
      expect(cleanText('Hello\t\tworld')).toBe('Hello world')
    })

    it('应该清理多余的换行符', () => {
      expect(cleanText('Hello\n\n\nworld')).toBe('Hello world')
      expect(cleanText('Hello\r\n\r\nworld')).toBe('Hello world')
    })

    it('应该去除首尾空格', () => {
      expect(cleanText('  Hello world  ')).toBe('Hello world')
      expect(cleanText('\n\nHello world\n\n')).toBe('Hello world')
    })

    it('应该处理空字符串', () => {
      expect(cleanText('')).toBe('')
      expect(cleanText('   ')).toBe('')
    })
  })

  describe('truncateText', () => {
    it('应该截取文本到指定长度', () => {
      expect(truncateText('Hello world', 5)).toBe('Hello...')
      expect(truncateText('Hello world', 5, false)).toBe('Hello')
    })

    it('应该保持短于最大长度的文本不变', () => {
      expect(truncateText('Hello', 10)).toBe('Hello')
      expect(truncateText('Hello', 10, false)).toBe('Hello')
    })

    it('应该处理空字符串', () => {
      expect(truncateText('', 5)).toBe('')
      expect(truncateText('', 5, false)).toBe('')
    })

    it('应该处理边界情况', () => {
      expect(truncateText('Hello', 5)).toBe('Hello')
      expect(truncateText('Hello', 0)).toBe('...')
      expect(truncateText('Hello', 0, false)).toBe('')
    })
  })

  describe('isTextEmpty', () => {
    it('应该检测空文本', () => {
      expect(isTextEmpty('')).toBe(true)
      expect(isTextEmpty('   ')).toBe(true)
      expect(isTextEmpty('\n\t')).toBe(true)
      expect(isTextEmpty(null)).toBe(true)
      expect(isTextEmpty(undefined)).toBe(true)
    })

    it('应该检测非空文本', () => {
      expect(isTextEmpty('Hello')).toBe(false)
      expect(isTextEmpty('  Hello  ')).toBe(false)
      expect(isTextEmpty('0')).toBe(false)
    })
  })

  describe('countTextCharacters', () => {
    it('应该正确统计字符数（不包括空格）', () => {
      expect(countTextCharacters('Hello world')).toBe(10)
      expect(countTextCharacters('Hello  world')).toBe(10)
      expect(countTextCharacters('你好世界')).toBe(4)
      expect(countTextCharacters('Hello 世界')).toBe(7)
    })

    it('应该处理空字符串', () => {
      expect(countTextCharacters('')).toBe(0)
      expect(countTextCharacters('   ')).toBe(0)
    })

    it('应该处理特殊字符', () => {
      expect(countTextCharacters('Hello, world!')).toBe(12)
      expect(countTextCharacters('123 456')).toBe(6)
    })
  })

  describe('countWords', () => {
    it('应该正确统计单词数', () => {
      expect(countWords('Hello world')).toBe(2)
      expect(countWords('Hello  world  test')).toBe(3)
      expect(countWords('你好 世界')).toBe(2)
    })

    it('应该处理空字符串', () => {
      expect(countWords('')).toBe(0)
      expect(countWords('   ')).toBe(0)
    })

    it('应该处理单个单词', () => {
      expect(countWords('Hello')).toBe(1)
      expect(countWords('  Hello  ')).toBe(1)
    })

    it('应该处理特殊分隔符', () => {
      expect(countWords('Hello\tworld\ntest')).toBe(3)
      expect(countWords('Hello,world.test')).toBe(1) // 没有空格分隔
    })
  })

  describe('containsChinese', () => {
    it('应该检测包含中文字符的文本', () => {
      expect(containsChinese('你好')).toBe(true)
      expect(containsChinese('Hello 世界')).toBe(true)
      expect(containsChinese('测试123')).toBe(true)
      expect(containsChinese('，。！？')).toBe(true) // 中文标点
    })

    it('应该检测不包含中文字符的文本', () => {
      expect(containsChinese('Hello world')).toBe(false)
      expect(containsChinese('123456')).toBe(false)
      expect(containsChinese('!@#$%')).toBe(false)
    })

    it('应该处理空字符串', () => {
      expect(containsChinese('')).toBe(false)
      expect(containsChinese('   ')).toBe(false)
    })
  })

  describe('containsEnglish', () => {
    it('应该检测包含英文字符的文本', () => {
      expect(containsEnglish('Hello')).toBe(true)
      expect(containsEnglish('你好 world')).toBe(true)
      expect(containsEnglish('test123')).toBe(true)
      expect(containsEnglish('A')).toBe(true)
    })

    it('应该检测不包含英文字符的文本', () => {
      expect(containsEnglish('你好世界')).toBe(false)
      expect(containsEnglish('123456')).toBe(false)
      expect(containsEnglish('!@#$%')).toBe(false)
    })

    it('应该处理空字符串', () => {
      expect(containsEnglish('')).toBe(false)
      expect(containsEnglish('   ')).toBe(false)
    })
  })

  describe('splitIntoLines', () => {
    it('应该将文本分割为非空行', () => {
      const text = 'Line 1\nLine 2\nLine 3'
      expect(splitIntoLines(text)).toEqual(['Line 1', 'Line 2', 'Line 3'])
    })

    it('应该过滤空行', () => {
      const text = 'Line 1\n\nLine 2\n   \nLine 3'
      expect(splitIntoLines(text)).toEqual(['Line 1', 'Line 2', 'Line 3'])
    })

    it('应该处理不同的换行符', () => {
      const text = 'Line 1\r\nLine 2\rLine 3'
      expect(splitIntoLines(text)).toEqual(['Line 1\r', 'Line 2\rLine 3'])
    })

    it('应该处理空字符串', () => {
      expect(splitIntoLines('')).toEqual([])
      expect(splitIntoLines('\n\n\n')).toEqual([])
    })
  })

  describe('splitIntoSentences', () => {
    it('应该按中文标点分割句子', () => {
      const text = '你好世界。这是测试！真的很好？'
      expect(splitIntoSentences(text)).toEqual(['你好世界', '这是测试', '真的很好'])
    })

    it('应该按英文标点分割句子', () => {
      const text = 'Hello world. This is a test! Really good?'
      expect(splitIntoSentences(text)).toEqual(['Hello world', 'This is a test', 'Really good'])
    })

    it('应该处理混合标点', () => {
      const text = 'Hello世界。This is测试！Really好？'
      expect(splitIntoSentences(text)).toEqual(['Hello世界', 'This is测试', 'Really好'])
    })

    it('应该过滤空句子', () => {
      const text = 'Hello..world!!test??'
      expect(splitIntoSentences(text)).toEqual(['Hello', 'world', 'test'])
    })

    it('应该处理空字符串', () => {
      expect(splitIntoSentences('')).toEqual([])
      expect(splitIntoSentences('...')).toEqual([])
    })
  })

  describe('formatTextForDisplay', () => {
    it('应该按指定长度格式化文本', () => {
      const text = 'This is a long text that needs to be formatted for display'
      const result = formatTextForDisplay(text, 20)
      const lines = result.split('\n')
      
      expect(lines.length).toBeGreaterThan(1)
      lines.forEach(line => {
        expect(line.length).toBeLessThanOrEqual(20)
      })
    })

    it('应该处理短文本', () => {
      const text = 'Short text'
      const result = formatTextForDisplay(text, 50)
      expect(result).toBe('Short text')
    })

    it('应该处理空字符串', () => {
      expect(formatTextForDisplay('')).toBe('')
      expect(formatTextForDisplay('', 50)).toBe('')
    })

    it('应该处理单个长单词', () => {
      const text = 'verylongwordthatexceedsmaxlength'
      const result = formatTextForDisplay(text, 10)
      expect(result).toBe('verylongwordthatexceedsmaxlength')
    })

    it('应该使用默认行长度', () => {
      const text = 'This is a test text that should be formatted with default line length'
      const result = formatTextForDisplay(text)
      expect(result).toContain('\n')
    })
  })

  describe('extractTimeMarkers', () => {
    it('应该提取各种时间格式', () => {
      const text = 'Start at 1:23, then 12:34:56.789, and finally 0:05.123'
      const result = extractTimeMarkers(text)
      expect(result).toEqual(['1:23', '12:34:56.789', '0:05.123'])
    })

    it('应该提取带逗号的时间格式', () => {
      const text = 'Time markers: 1:23,456 and 12:34:56,789'
      const result = extractTimeMarkers(text)
      expect(result).toEqual(['1:23,456', '12:34:56,789'])
    })

    it('应该处理没有时间标记的文本', () => {
      const text = 'No time markers here'
      const result = extractTimeMarkers(text)
      expect(result).toEqual([])
    })

    it('应该处理空字符串', () => {
      expect(extractTimeMarkers('')).toEqual([])
    })

    it('应该处理边界情况', () => {
      const text = '99:59:59.999 and 0:00.000'
      const result = extractTimeMarkers(text)
      expect(result).toEqual(['99:59:59.999', '0:00.000'])
    })
  })

  describe('removeTimeMarkers', () => {
    it('应该移除时间标记', () => {
      const text = 'Start at 1:23, then continue'
      const result = removeTimeMarkers(text)
      expect(result).toBe('Start at , then continue')
    })

    it('应该移除多个时间标记', () => {
      const text = 'From 1:23 to 4:56.789 and 12:34:56,123'
      const result = removeTimeMarkers(text)
      expect(result).toBe('From  to  and')
    })

    it('应该处理没有时间标记的文本', () => {
      const text = 'No time markers here'
      const result = removeTimeMarkers(text)
      expect(result).toBe('No time markers here')
    })

    it('应该处理空字符串', () => {
      expect(removeTimeMarkers('')).toBe('')
    })

    it('应该清理多余的空格', () => {
      const text = '  Start 1:23 end  '
      const result = removeTimeMarkers(text)
      expect(result).toBe('Start  end')
    })
  })

  // ============================================================================
  // 边界情况和错误处理测试
  // ============================================================================

  describe('边界情况测试', () => {
    it('应该处理极长的文本', () => {
      const longText = 'A'.repeat(100000)
      
      expect(detectTextLanguage(longText)).toBe('english')
      expect(cleanText(longText)).toBe(longText)
      expect(truncateText(longText, 100).length).toBeLessThanOrEqual(103) // 100 + '...'
    })

    it('应该处理包含特殊 Unicode 字符的文本', () => {
      const specialText = '🚀💫⭐你好Hello🌟'
      
      expect(detectTextLanguage(specialText)).toBe('english')
      expect(containsChinese(specialText)).toBe(true)
      expect(containsEnglish(specialText)).toBe(true)
    })

    it('应该处理混合换行符的文本', () => {
      const mixedText = 'Line1\nLine2\r\nLine3\rLine4'
      
      expect(splitIntoLines(mixedText)).toEqual(['Line1', 'Line2\r', 'Line3\rLine4'])
      expect(cleanText(mixedText)).toBe('Line1 Line2 Line3 Line4')
    })

    it('应该处理包含制表符的文本', () => {
      const tabText = 'Hello\tworld\ttest'
      
      expect(cleanText(tabText)).toBe('Hello world test')
      expect(countWords(tabText)).toBe(3)
    })
  })

  // ============================================================================
  // 性能测试
  // ============================================================================

  describe('性能测试', () => {
    it('应该快速处理大量文本语言检测', () => {
      const texts = []
      for (let i = 0; i < 1000; i++) {
        texts.push(`Test text ${i} 测试文本 ${i}`)
      }

      const start = Date.now()
      texts.forEach(text => detectTextLanguage(text))
      const end = Date.now()

      expect(end - start).toBeLessThan(1000) // 应该在1秒内完成
    })

    it('应该快速处理大量文本清理', () => {
      const texts = []
      for (let i = 0; i < 1000; i++) {
        texts.push(`  Test   text   ${i}   with   spaces  `)
      }

      const start = Date.now()
      texts.forEach(text => cleanText(text))
      const end = Date.now()

      expect(end - start).toBeLessThan(500) // 应该在0.5秒内完成
    })

    it('应该快速处理大量语音识别结果', () => {
      const recognitionResults: RecognitionResult[] = []
      for (let i = 0; i < 100; i++) {
        recognitionResults.push({
          utterances: [
            { text: `Utterance ${i}`, start_time: i * 1000, end_time: (i + 1) * 1000 },
            { text: `Another ${i}`, start_time: (i + 1) * 1000, end_time: (i + 2) * 1000 },
          ],
        })
      }

      const start = Date.now()
      recognitionResults.forEach(result => {
        mergeRecognitionText(result)
        mergeRecognitionToSentences(result)
      })
      const end = Date.now()

      expect(end - start).toBeLessThan(100) // 应该在0.1秒内完成
    })
  })

  // ============================================================================
  // 集成测试
  // ============================================================================

  describe('集成测试', () => {
    it('应该正确处理完整的文本处理流程', () => {
      const rawText = '  Hello   world！  \n\n  你好世界。  \n  This is a test.  '
      
      // 清理文本
      const cleanedText = cleanText(rawText)
      expect(cleanedText).toBe('Hello world！ 你好世界。 This is a test.')
      
      // 分割为句子
      const sentences = splitIntoSentences(cleanedText)
      expect(sentences).toEqual(['Hello world', '你好世界', 'This is a test'])
      
      // 检测每个句子的语言
      const languages = sentences.map(sentence => detectTextLanguage(sentence))
      expect(languages).toEqual(['english', 'chinese', 'english'])
      
      // 分类为字幕行
      const classified = classifySubtitleLines(sentences)
      expect(classified.text).toBe('Hello world\nThis is a test')
      expect(classified.translationText).toBe('你好世界')
    })

    it('应该正确处理语音识别结果的完整流程', () => {
      const recognitionResult: RecognitionResult = {
        utterances: [
          { text: 'Hello world', start_time: 0, end_time: 2000 },
          { text: '你好世界', start_time: 2000, end_time: 4000 },
          { text: 'This is a test', start_time: 4000, end_time: 6000 },
        ],
      }
      
      // 合并为句子
      const sentences = mergeRecognitionToSentences(recognitionResult)
      expect(sentences).toEqual(['Hello world', '你好世界', 'This is a test'])
      
      // 分类字幕行
      const classified = classifySubtitleLines(sentences)
      expect(classified.text).toBe('Hello world\nThis is a test')
      expect(classified.translationText).toBe('你好世界')
      
      // 合并为长文本
      const mergedText = mergeRecognitionText(recognitionResult)
      expect(mergedText).toBe('Hello world 你好世界 This is a test')
    })
  })
})