import { describe, it, expect, vi, beforeEach } from 'vitest'
import type { ApiConfig, RecognitionResult, RecognitionUtterance } from '../useSpeechRecognition'

// Create mock functions
const mockRef = vi.fn((value) => ({ value }))
const mockComputed = vi.fn((fn) => ({ value: fn() }))

// Mock Vue dependencies
vi.mock('vue', () => ({
  ref: mockRef,
  computed: mockComputed,
}))

// Make Vue functions available globally
;(globalThis as any).ref = mockRef
;(globalThis as any).computed = mockComputed

// Mock text utils
vi.mock('../useTextUtils', () => ({
  mergeRecognitionText: vi.fn((result) => {
    if (!result || !result.utterances) return ''
    return result.utterances.map((u: RecognitionUtterance) => u.text).join(' ')
  }),
}))

// Mock global fetch
global.fetch = vi.fn()

// Mock setTimeout and clearTimeout
let timeoutId = 0
const timeoutQueue: Array<{ id: number; fn: Function; delay: number }> = []

global.setTimeout = vi.fn((fn, delay = 0) => {
  const id = ++timeoutId
  if (typeof fn === 'function') {
    // 对于测试，我们需要控制何时执行setTimeout的回调
    // 不立即执行，而是添加到队列中
    timeoutQueue.push({ id, fn, delay })
  }
  return id as any
}) as any

global.clearTimeout = vi.fn((id) => {
  const index = timeoutQueue.findIndex(item => item.id === id)
  if (index !== -1) {
    timeoutQueue.splice(index, 1)
  }
}) as any

// 辅助函数：执行一个待处理的setTimeout回调
const flushNextTimeout = () => {
  if (timeoutQueue.length > 0) {
    const { fn } = timeoutQueue.shift()!
    fn()
  }
}

// 辅助函数：执行所有待处理的setTimeout回调
const flushAllTimeouts = () => {
  while (timeoutQueue.length > 0) {
    const { fn } = timeoutQueue.shift()!
    fn()
  }
}

describe('useSpeechRecognition', () => {
  let mockRef: any
  let mockComputed: any
  let mockFetch: any
  let mergeRecognitionText: any

  beforeEach(async () => {
    vi.clearAllMocks()

    // Clear timeout queue
    timeoutQueue.length = 0
    timeoutId = 0

    // Import Vue mocks
    const vueMocks = await import('vue')
    mockRef = vueMocks.ref as any
    mockComputed = vueMocks.computed as any

    // Import text utils mock
    const textUtilsMocks = await import('../useTextUtils')
    mergeRecognitionText = textUtilsMocks.mergeRecognitionText as any

    // Setup fetch mock
    mockFetch = global.fetch as any

    // Reset implementations
    mockRef.mockImplementation((value: any) => ({ value }))
    mockComputed.mockImplementation((fn: any) => ({ value: fn() }))

    // Reset fetch mock
    mockFetch.mockResolvedValue({
      ok: true,
      json: vi.fn().mockResolvedValue({}),
      text: vi.fn().mockResolvedValue(''),
    })
  })

  // ============================================================================
  // 基本功能测试
  // ============================================================================

  describe('基本功能', () => {
    it('应该存在 useSpeechRecognition 函数', async () => {
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      expect(typeof useSpeechRecognition).toBe('function')
    })

    it('应该正确初始化状态', async () => {
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const result = useSpeechRecognition()
      
      expect(result).toHaveProperty('isProcessing')
      expect(result).toHaveProperty('recognitionResult')
      expect(result).toHaveProperty('jobId')
      expect(result).toHaveProperty('errorMessage')
      expect(result).toHaveProperty('canStartRecognition')
      expect(result).toHaveProperty('startRecognition')
      expect(result).toHaveProperty('resetRecognition')
      expect(result).toHaveProperty('mergeRecognitionResult')
    })

    it('应该正确设置初始值', async () => {
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const result = useSpeechRecognition()
      
      expect(result.isProcessing.value).toBe(false)
      expect(result.recognitionResult.value).toBeUndefined()
      expect(result.jobId.value).toBe('')
      expect(result.errorMessage.value).toBe('')
    })

    it('应该正确计算 canStartRecognition', async () => {
      mockComputed.mockImplementation((fn: any) => {
        if (fn.toString().includes('isProcessing')) {
          return { value: true } // 模拟 !isProcessing.value
        }
        return { value: fn() }
      })
      
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const result = useSpeechRecognition()
      
      expect(result.canStartRecognition.value).toBe(true)
    })
  })

  // ============================================================================
  // 类型定义测试
  // ============================================================================

  describe('类型定义', () => {
    it('应该导出正确的类型', async () => {
      const types = await import('../useSpeechRecognition')
      
      // 验证类型导出存在（通过 TypeScript 编译验证）
      expect(types).toBeDefined()
    })

    it('ApiConfig 应该包含必要的字段', () => {
      const apiConfig: ApiConfig = {
        appid: 'test-app-id',
        token: 'test-token',
      }
      
      expect(apiConfig.appid).toBe('test-app-id')
      expect(apiConfig.token).toBe('test-token')
    })

    it('RecognitionUtterance 应该包含必要的字段', () => {
      const utterance: RecognitionUtterance = {
        start_time: 0,
        end_time: 1000,
        text: 'Hello world',
      }
      
      expect(utterance.start_time).toBe(0)
      expect(utterance.end_time).toBe(1000)
      expect(utterance.text).toBe('Hello world')
    })

    it('RecognitionResult 应该包含必要的字段', () => {
      const result: RecognitionResult = {
        code: 0,
        duration: 5000,
        utterances: [],
      }
      
      expect(result.code).toBe(0)
      expect(result.duration).toBe(5000)
      expect(result.utterances).toEqual([])
    })
  })

  // ============================================================================
  // 提交识别任务测试
  // ============================================================================

  describe('提交识别任务', () => {
    it('应该正确构造提交请求', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: vi.fn().mockResolvedValue({
          message: 'Success',
          id: 'task-123',
        }),
      })
      
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      const file = new File(['test'], 'test.mp3', { type: 'audio/mp3' })
      const apiConfig: ApiConfig = {
        appid: 'test-app',
        token: 'test-token',
      }
      const params = {
        language: 'zh-CN',
        words_per_line: 46,
        max_lines: 1,
        caption_type: 'auto',
        use_itn: false,
        use_punc: false,
        use_ddc: false,
        with_speaker_info: false,
      }
      
      await speechRecognition.startRecognition(file, apiConfig, params)
      
      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/speech/submit'),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            Authorization: 'Bearer; test-token',
          }),
          body: expect.any(FormData),
        })
      )
    })

    it('应该处理提交失败的情况', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        text: vi.fn().mockResolvedValue('Submit failed'),
      })
      
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      const file = new File(['test'], 'test.mp3', { type: 'audio/mp3' })
      const apiConfig: ApiConfig = {
        appid: 'test-app',
        token: 'test-token',
      }
      const params = {
        language: 'zh-CN',
        words_per_line: 46,
        max_lines: 1,
        caption_type: 'auto',
        use_itn: false,
        use_punc: false,
        use_ddc: false,
        with_speaker_info: false,
      }
      
      await speechRecognition.startRecognition(file, apiConfig, params)

      expect(speechRecognition.errorMessage.value).toContain('Submit failed')
      expect(speechRecognition.isProcessing.value).toBe(false)
    })

    it('应该处理无效响应消息', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: vi.fn().mockResolvedValue({
          message: 'Failed',
          id: '',
        }),
      })
      
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      const file = new File(['test'], 'test.mp3', { type: 'audio/mp3' })
      const apiConfig: ApiConfig = {
        appid: 'test-app',
        token: 'test-token',
      }
      const params = {
        language: 'zh-CN',
        words_per_line: 46,
        max_lines: 1,
        caption_type: 'auto',
        use_itn: false,
        use_punc: false,
        use_ddc: false,
        with_speaker_info: false,
      }
      
      await speechRecognition.startRecognition(file, apiConfig, params)
      
      expect(speechRecognition.errorMessage.value).toContain('Failed')
      expect(speechRecognition.isProcessing.value).toBe(false)
    })
  })

  // ============================================================================
  // 查询识别结果测试
  // ============================================================================

  describe('查询识别结果', () => {
    it('应该正确构造查询请求', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: vi.fn().mockResolvedValue({
            message: 'Success',
            id: 'task-123',
          }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: vi.fn().mockResolvedValue({
            code: 0,
            message: 'Success',
            duration: 5000,
            utterances: [
              {
                start_time: 0,
                end_time: 1000,
                text: 'Hello world',
              },
            ],
          }),
        })
      
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      const file = new File(['test'], 'test.mp3', { type: 'audio/mp3' })
      const apiConfig: ApiConfig = {
        appid: 'test-app',
        token: 'test-token',
      }
      const params = {
        language: 'zh-CN',
        words_per_line: 46,
        max_lines: 1,
        caption_type: 'auto',
        use_itn: false,
        use_punc: false,
        use_ddc: false,
        with_speaker_info: false,
      }
      
      await speechRecognition.startRecognition(file, apiConfig, params)

      // 触发轮询回调
      flushNextTimeout()

      // 检查第二个fetch调用（查询请求）
      expect(mockFetch).toHaveBeenNthCalledWith(2,
        expect.stringContaining('/api/speech/query'),
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            Authorization: 'Bearer; test-token',
          }),
        })
      )
    })

    it('应该处理查询失败的情况', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: vi.fn().mockResolvedValue({
            message: 'Success',
            id: 'task-123',
          }),
        })
        // 模拟多次查询失败，直到达到最大重试次数
        .mockRejectedValue(new Error('Query failed'))
      
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      const file = new File(['test'], 'test.mp3', { type: 'audio/mp3' })
      const apiConfig: ApiConfig = {
        appid: 'test-app',
        token: 'test-token',
      }
      const params = {
        language: 'zh-CN',
        words_per_line: 46,
        max_lines: 1,
        caption_type: 'auto',
        use_itn: false,
        use_punc: false,
        use_ddc: false,
        with_speaker_info: false,
      }
      
      await speechRecognition.startRecognition(file, apiConfig, params)

      // 触发初始轮询延迟
      flushNextTimeout()
      
      // 模拟达到最大重试次数 - 需要模拟大量的重试
      for (let i = 0; i < 60; i++) {
        flushNextTimeout()
      }

      expect(speechRecognition.errorMessage.value).toContain('Query failed')
      expect(speechRecognition.isProcessing.value).toBe(false)
    })

    it('应该处理识别成功的结果', async () => {
      const successResult = {
        code: 0,
        message: 'Success',
        duration: 5000,
        utterances: [
          {
            start_time: 0,
            end_time: 1000,
            text: 'Hello world',
          },
        ],
      }
      
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: vi.fn().mockResolvedValue({
            message: 'Success',
            id: 'task-123',
          }),
        })
        .mockResolvedValue({
          ok: true,
          json: vi.fn().mockResolvedValue(successResult),
        })
      
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      const file = new File(['test'], 'test.mp3', { type: 'audio/mp3' })
      const apiConfig: ApiConfig = {
        appid: 'test-app',
        token: 'test-token',
      }
      const params = {
        language: 'zh-CN',
        words_per_line: 46,
        max_lines: 1,
        caption_type: 'auto',
        use_itn: false,
        use_punc: false,
        use_ddc: false,
        with_speaker_info: false,
      }
      
      await speechRecognition.startRecognition(file, apiConfig, params)

      // 触发初始轮询延迟
      flushNextTimeout()
      // 触发第一次查询
      flushNextTimeout()

      expect(speechRecognition.recognitionResult.value).toEqual(successResult)
      expect(speechRecognition.isProcessing.value).toBe(false)
      expect(speechRecognition.errorMessage.value).toBe('')
    })
  })

  // ============================================================================
  // 轮询逻辑测试
  // ============================================================================

  describe('轮询逻辑', () => {
    it('应该处理处理中状态并继续轮询', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: vi.fn().mockResolvedValue({
            message: 'Success',
            id: 'task-123',
          }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: vi.fn().mockResolvedValue({
            code: 1001, // 处理中
            message: 'Processing',
          }),
        })
        .mockResolvedValue({
          ok: true,
          json: vi.fn().mockResolvedValue({
            code: 0, // 成功
            duration: 5000,
            utterances: [],
          }),
        })
      
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      const file = new File(['test'], 'test.mp3', { type: 'audio/mp3' })
      const apiConfig: ApiConfig = {
        appid: 'test-app',
        token: 'test-token',
      }
      const params = {
        language: 'zh-CN',
        words_per_line: 46,
        max_lines: 1,
        caption_type: 'auto',
        use_itn: false,
        use_punc: false,
        use_ddc: false,
        with_speaker_info: false,
      }
      
      await speechRecognition.startRecognition(file, apiConfig, params)

      // 触发初始轮询延迟
      flushNextTimeout()
      // 触发第一次查询（返回处理中状态）
      flushNextTimeout()
      // 触发第二次轮询延迟
      flushNextTimeout()
      // 触发第二次查询（返回成功状态）  
      flushNextTimeout()

      expect(mockFetch).toHaveBeenCalledTimes(3) // submit + 2 queries
    })

    it('应该处理识别失败的状态', async () => {
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: vi.fn().mockResolvedValue({
            message: 'Success',
            id: 'task-123',
          }),
        })
        .mockResolvedValue({
          ok: true,
          json: vi.fn().mockResolvedValue({
            code: 1002, // 失败
            message: 'Recognition failed',
          }),
        })
      
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      const file = new File(['test'], 'test.mp3', { type: 'audio/mp3' })
      const apiConfig: ApiConfig = {
        appid: 'test-app',
        token: 'test-token',
      }
      const params = {
        language: 'zh-CN',
        words_per_line: 46,
        max_lines: 1,
        caption_type: 'auto',
        use_itn: false,
        use_punc: false,
        use_ddc: false,
        with_speaker_info: false,
      }
      
      await speechRecognition.startRecognition(file, apiConfig, params)

      // 触发初始轮询延迟
      flushNextTimeout()
      // 触发查询
      flushNextTimeout()

      expect(speechRecognition.errorMessage.value).toContain('Recognition failed')
      expect(speechRecognition.isProcessing.value).toBe(false)
    })
  })

  // ============================================================================
  // 边界情况测试
  // ============================================================================

  describe('边界情况测试', () => {
    it('应该拒绝无效参数', async () => {
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      // 测试空文件
      await speechRecognition.startRecognition(
        null as any,
        { appid: 'test', token: 'test' },
        {} as any
      )
      
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该拒绝空的 API 配置', async () => {
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      const file = new File(['test'], 'test.mp3')
      
      // 测试空 appid
      await speechRecognition.startRecognition(
        file,
        { appid: '', token: 'test' },
        {} as any
      )
      
      expect(mockFetch).not.toHaveBeenCalled()
      
      // 测试空 token
      await speechRecognition.startRecognition(
        file,
        { appid: 'test', token: '' },
        {} as any
      )
      
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该防止重复处理', async () => {
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      // 设置为处理中状态
      speechRecognition.isProcessing.value = true
      
      const file = new File(['test'], 'test.mp3')
      
      await speechRecognition.startRecognition(
        file,
        { appid: 'test', token: 'test' },
        {} as any
      )
      
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it('应该处理网络错误', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'))
      
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      const file = new File(['test'], 'test.mp3', { type: 'audio/mp3' })
      const apiConfig: ApiConfig = {
        appid: 'test-app',
        token: 'test-token',
      }
      const params = {
        language: 'zh-CN',
        words_per_line: 46,
        max_lines: 1,
        caption_type: 'auto',
        use_itn: false,
        use_punc: false,
        use_ddc: false,
        with_speaker_info: false,
      }
      
      await speechRecognition.startRecognition(file, apiConfig, params)
      
      expect(speechRecognition.errorMessage.value).toContain('Network error')
      expect(speechRecognition.isProcessing.value).toBe(false)
    }, 10000)
  })

  // ============================================================================
  // 状态管理测试
  // ============================================================================

  describe('状态管理', () => {
    it('应该正确重置状态', async () => {
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      // 设置一些状态
      speechRecognition.isProcessing.value = true
      speechRecognition.jobId.value = 'test-job'
      speechRecognition.errorMessage.value = 'test error'
      speechRecognition.recognitionResult.value = {
        code: 1,
        duration: 1000,
        utterances: [],
      }
      
      speechRecognition.resetRecognition()
      
      expect(speechRecognition.isProcessing.value).toBe(false)
      expect(speechRecognition.jobId.value).toBe('')
      expect(speechRecognition.errorMessage.value).toBe('')
      expect(speechRecognition.recognitionResult.value).toEqual({
        code: 0,
        duration: 0,
        utterances: [],
      })
    })

    it('应该正确合并识别结果', async () => {
      mergeRecognitionText.mockReturnValue('Hello world')
      
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      speechRecognition.recognitionResult.value = {
        code: 0,
        duration: 5000,
        utterances: [
          {
            start_time: 0,
            end_time: 1000,
            text: 'Hello',
          },
          {
            start_time: 1000,
            end_time: 2000,
            text: 'world',
          },
        ],
      }
      
      const merged = speechRecognition.mergeRecognitionResult()
      
      expect(merged).toBe('Hello world')
      expect(mergeRecognitionText).toHaveBeenCalledWith(speechRecognition.recognitionResult.value)
    })

    it('应该处理空的识别结果', async () => {
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      speechRecognition.recognitionResult.value = undefined
      
      const merged = speechRecognition.mergeRecognitionResult()
      
      expect(merged).toBe('')
      expect(mergeRecognitionText).not.toHaveBeenCalled()
    })
  })

  // ============================================================================
  // 参数验证测试
  // ============================================================================

  describe('参数验证', () => {
    it('应该正确处理布尔参数', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          message: 'Success',
          id: 'task-123',
        }),
      })
      
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      const file = new File(['test'], 'test.mp3', { type: 'audio/mp3' })
      const apiConfig: ApiConfig = {
        appid: 'test-app',
        token: 'test-token',
      }
      const params = {
        language: 'en-US',
        words_per_line: 55,
        max_lines: 2,
        caption_type: 'speech',
        use_itn: true,
        use_punc: true,
        use_ddc: true,
        with_speaker_info: true,
      }
      
      await speechRecognition.startRecognition(file, apiConfig, params)
      
      const fetchCall = mockFetch.mock.calls[0]
      const url = fetchCall[0]
      
      expect(url).toContain('use_itn=true')
      expect(url).toContain('use_punc=true')
      expect(url).toContain('use_ddc=true')
      expect(url).toContain('with_speaker_info=true')
    })

    it('应该正确处理数字参数', async () => {
      // Mock first call (submit task)
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          message: 'Success',
          id: 'task-123',
        }),
      })

      // Mock second call (query result) - return success immediately
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({
          code: 0,
          message: 'Success',
          utterances: [],
        }),
      })

      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()

      const file = new File(['test'], 'test.mp3', { type: 'audio/mp3' })
      const apiConfig: ApiConfig = {
        appid: 'test-app',
        token: 'test-token',
      }
      const params = {
        language: 'zh-CN',
        words_per_line: 20,
        max_lines: 3,
        caption_type: 'auto',
        use_itn: false,
        use_punc: false,
        use_ddc: false,
        with_speaker_info: false,
      }

      await speechRecognition.startRecognition(file, apiConfig, params)

      // Check the first fetch call (submit task) contains the parameters
      const submitCall = mockFetch.mock.calls[0]
      const submitUrl = submitCall[0]

      expect(submitUrl).toContain('words_per_line=20')
      expect(submitUrl).toContain('max_lines=3')
    })
  })

  // ============================================================================
  // 函数存在性测试
  // ============================================================================

  describe('函数存在性测试', () => {
    it('应该导出所有必要的函数和类型', async () => {
      const module = await import('../useSpeechRecognition')
      
      expect(module.useSpeechRecognition).toBeDefined()
      expect(typeof module.useSpeechRecognition).toBe('function')
    })

    it('所有返回的方法都应该是函数类型', async () => {
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const result = useSpeechRecognition()
      
      expect(typeof result.startRecognition).toBe('function')
      expect(typeof result.resetRecognition).toBe('function')
      expect(typeof result.mergeRecognitionResult).toBe('function')
    })
  })

  // ============================================================================
  // 类型检查测试
  // ============================================================================

  describe('类型检查测试', () => {
    it('应该返回正确的接口', async () => {
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const result = useSpeechRecognition()
      
      // 验证响应式状态
      expect(result.isProcessing).toHaveProperty('value')
      expect(result.recognitionResult).toHaveProperty('value')
      expect(result.jobId).toHaveProperty('value')
      expect(result.errorMessage).toHaveProperty('value')
      expect(result.canStartRecognition).toHaveProperty('value')
      
      // 验证方法
      expect(typeof result.startRecognition).toBe('function')
      expect(typeof result.resetRecognition).toBe('function')
      expect(typeof result.mergeRecognitionResult).toBe('function')
    })

    it('应该接受正确的参数类型', async () => {
      const { useSpeechRecognition } = await import('../useSpeechRecognition')
      const speechRecognition = useSpeechRecognition()
      
      const file = new File(['test'], 'test.mp3', { type: 'audio/mp3' })
      const apiConfig: ApiConfig = {
        appid: 'test-app',
        token: 'test-token',
      }
      const params = {
        language: 'zh-CN',
        words_per_line: 46,
        max_lines: 1,
        caption_type: 'auto',
        use_itn: false,
        use_punc: false,
        use_ddc: false,
        with_speaker_info: false,
      }
      
      expect(() => {
        speechRecognition.startRecognition(file, apiConfig, params)
      }).not.toThrow()
      
      expect(() => {
        speechRecognition.resetRecognition()
      }).not.toThrow()
      
      expect(() => {
        speechRecognition.mergeRecognitionResult()
      }).not.toThrow()
    })
  })
})