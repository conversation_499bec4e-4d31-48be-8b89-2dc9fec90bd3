import { describe, it, expect, vi, beforeEach } from 'vitest'
import type { Subtitle } from '~/types/subtitle/subtitle'
import type { Sentence } from '@julebu/shared'

// Mock Vue dependencies
const mockComputed = vi.fn((fn) => ({ value: fn() }))

vi.mock('vue', () => ({
  computed: mockComputed,
}))

// Make Vue functions available globally
global.computed = mockComputed

// Mock dependencies
vi.mock('lodash-es', () => ({
  filter: vi.fn((arr, fn) => arr.filter(fn)),
  isEmpty: vi.fn((val) => !val || val.length === 0),
  some: vi.fn((arr, fn) => arr.some(fn)),
}))

vi.mock('~/utils/shared/constants', () => ({
  AUDIO_MIME_PREFIX: 'audio/',
  VIDEO_MIME_PREFIX: 'video/',
}))

vi.mock('./useTimeUtils', () => ({
  parseSrtTime: vi.fn((timeStr: string) => {
    if (timeStr === '00:00:01,000') return 1
    if (timeStr === '00:00:02,000') return 2
    if (timeStr === '00:00:03,000') return 3
    if (timeStr === '00:00:05,000') return 5
    return 0
  }),
}))

// Mock Nuxt functions
const mockUseNuxtApp = vi.fn(() => ({
  $trpc: {
    sentence: {
      batchProcessSentences: {
        mutate: vi.fn(() => Promise.resolve()),
      },
    },
  },
}))

const mockUseLoadingSpinner = vi.fn(() => ({
  startLoading: vi.fn(),
  finishLoading: vi.fn(),
}))

vi.mock('#app', () => ({
  useNuxtApp: mockUseNuxtApp,
}))

vi.mock('~/composables/loadingSpinner', () => ({
  useLoadingSpinner: mockUseLoadingSpinner,
}))

// Make Nuxt functions available globally
global.useNuxtApp = mockUseNuxtApp
global.useLoadingSpinner = mockUseLoadingSpinner

// Mock stores
const mockPlayerStore = {
  mediaFile: { value: null },
  mediaFileHash: { value: '' },
  originalMediaFileHash: { value: '' },
}

const mockLrcStore = {
  hasLrc: { value: false },
}

const mockSubtitleStore = {
  subtitles: { value: [] as Subtitle[] },
}

const mockCourseStore = {
  currentCourse: { value: null as any },
  sentences: { value: [] as Sentence[] },
}

// Override global mocks with test-specific ones
vi.mock('~/composables/subtitle/stores/lrcStore', () => ({
  useLrcStore: vi.fn(() => mockLrcStore),
}))

vi.mock('~/composables/subtitle/stores/subtitleStore', () => ({
  useSubtitleStore: vi.fn(() => mockSubtitleStore),
}))

vi.mock('~/stores/subtitle/playerStore', () => ({
  usePlayerStore: vi.fn(() => mockPlayerStore),
}))

vi.mock('~/stores/course', () => ({
  useCourseStore: vi.fn(() => mockCourseStore),
}))

// Import the functions after mocks
import {
  useChangeDetection,
  checkAndProcessUnprocessedSentences,
} from '../useChangeDetection'

describe('useChangeDetection', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset mock implementations
    mockComputed.mockImplementation((fn) => ({ value: fn() }))
    
    // Reset store states
    mockPlayerStore.mediaFile.value = null
    mockPlayerStore.mediaFileHash.value = ''
    mockPlayerStore.originalMediaFileHash.value = ''
    mockLrcStore.hasLrc.value = false
    mockSubtitleStore.subtitles.value = []
    mockCourseStore.currentCourse.value = null
    mockCourseStore.sentences.value = []
  })

  // ============================================================================
  // 基本变更检测测试
  // ============================================================================

  describe('hasUnsavedChanges 基本功能', () => {
    it('应该存在 useChangeDetection 函数', () => {
      expect(typeof useChangeDetection).toBe('function')
    })

    it('应该正确初始化变更检测', () => {
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges).toHaveProperty('value')
      expect(typeof hasUnsavedChanges.value).toBe('boolean')
    })

    it('无媒体文件时应该返回 false', () => {
      mockPlayerStore.mediaFile.value = null
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(false)
    })

    it('媒体文件格式无效时应该返回 false', () => {
      mockPlayerStore.mediaFile.value = {
        type: 'text/plain', // 无效的媒体格式
      } as File
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(false)
    })

    it('无 LRC 内容时应该返回 false', () => {
      mockPlayerStore.mediaFile.value = {
        type: 'audio/mp3',
      } as File
      mockLrcStore.hasLrc.value = false
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(false)
    })

    it('无有效字幕内容时应该返回 false', () => {
      mockPlayerStore.mediaFile.value = {
        type: 'audio/mp3',
      } as File
      mockLrcStore.hasLrc.value = true
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: '',
          translationText: '',
        },
      ]
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(false)
    })
  })

  // ============================================================================
  // 音频文件变更检测测试
  // ============================================================================

  describe('音频文件变更检测', () => {
    it('应该正确检测音频文件', () => {
      mockPlayerStore.mediaFile.value = {
        type: 'audio/mp3',
      } as File
      mockLrcStore.hasLrc.value = true
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
          sentenceId: 'sent1', // 需要设置 sentenceId
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [
          {
            id: 'sent1',
            content: 'Hello',
            chinese: '你好',
            startTime: 1,
            endTime: 2,
          },
        ],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(false) // 没有变更
    })

    it('应该正确检测视频文件', () => {
      mockPlayerStore.mediaFile.value = {
        type: 'video/mp4',
      } as File
      mockLrcStore.hasLrc.value = true
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
          sentenceId: 'sent1', // 需要设置 sentenceId
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [
          {
            id: 'sent1',
            content: 'Hello',
            chinese: '你好',
            startTime: 1,
            endTime: 2,
          },
        ],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(false) // 没有变更
    })
  })

  // ============================================================================
  // 内容变更检测测试
  // ============================================================================

  describe('内容变更检测', () => {
    beforeEach(() => {
      mockPlayerStore.mediaFile.value = {
        type: 'audio/mp3',
      } as File
      mockLrcStore.hasLrc.value = true
    })

    it('应该检测新增字幕（无 sentenceId）', () => {
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'New subtitle',
          translationText: '新字幕',
          sentenceId: undefined, // 新增字幕没有 sentenceId
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(true)
    })

    it('应该检测文本内容变更', () => {
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Modified text', // 修改后的文本
          translationText: '你好',
          sentenceId: 'sent1',
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [
          {
            id: 'sent1',
            content: 'Original text', // 原始文本
            chinese: '你好',
            startTime: 1,
            endTime: 2,
          },
        ],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(true)
    })

    it('应该检测翻译内容变更', () => {
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '修改后的翻译', // 修改后的翻译
          sentenceId: 'sent1',
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [
          {
            id: 'sent1',
            content: 'Hello',
            chinese: '你好', // 原始翻译
            startTime: 1,
            endTime: 2,
          },
        ],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(true)
    })

    it('应该检测时间变更', () => {
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000', // 新时间
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
          sentenceId: 'sent1',
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [
          {
            id: 'sent1',
            content: 'Hello',
            chinese: '你好',
            startTime: 3, // 原始时间（不同）
            endTime: 2,
          },
        ],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(true)
    })

    it('应该检测删除的句子', () => {
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
          sentenceId: 'sent2', // 只有 sent2，sent1 被删除了
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [
          {
            id: 'sent1', // 原始存在的句子，但在当前字幕中被删除了
            content: 'Hello',
            chinese: '你好',
            startTime: 1,
            endTime: 2,
          },
          {
            id: 'sent2', // 这个句子还存在
            content: 'World',
            chinese: '世界',
            startTime: 2,
            endTime: 3,
          },
        ],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(true)
    })

    it('应该检测媒体文件变更', () => {
      mockPlayerStore.mediaFileHash.value = 'new-hash'
      mockPlayerStore.originalMediaFileHash.value = 'original-hash'
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
          sentenceId: 'sent1',
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [
          {
            id: 'sent1',
            content: 'Hello',
            chinese: '你好',
            startTime: 1,
            endTime: 2,
          },
        ],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(true)
    })

    it('无变更时应该返回 false', () => {
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
          sentenceId: 'sent1',
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [
          {
            id: 'sent1',
            content: 'Hello',
            chinese: '你好',
            startTime: 1,
            endTime: 2,
          },
        ],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(false)
    })
  })

  // ============================================================================
  // 边界情况测试
  // ============================================================================

  describe('边界情况测试', () => {
    beforeEach(() => {
      mockPlayerStore.mediaFile.value = {
        type: 'audio/mp3',
      } as File
      mockLrcStore.hasLrc.value = true
    })

    it('应该处理原始句子不存在的情况', () => {
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
          sentenceId: 'non-existent-id', // 不存在的ID
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [], // 没有原始句子
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(true)
    })

    it('应该处理空的原始句子数组', () => {
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
          // 没有 sentenceId，是新增字幕
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(true) // 新增字幕有内容，算作变更
    })

    it('应该处理 null 课程情况', () => {
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
          // 没有 sentenceId，是新增字幕
        },
      ]
      mockCourseStore.currentCourse.value = null
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(true) // 新增字幕有内容，算作变更
    })

    it('应该处理空白文本变更', () => {
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: '  trimmed  ', // 包含空格
          translationText: '你好',
          sentenceId: 'sent1',
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [
          {
            id: 'sent1',
            content: 'trimmed', // 去除空格后相同
            chinese: '你好',
            startTime: 1,
            endTime: 2,
          },
        ],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(false)
    })

    it('应该处理缺少媒体文件哈希的情况', () => {
      mockPlayerStore.mediaFileHash.value = ''
      mockPlayerStore.originalMediaFileHash.value = ''
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
          sentenceId: 'sent1',
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [
          {
            id: 'sent1',
            content: 'Hello',
            chinese: '你好',
            startTime: 1,
            endTime: 2,
          },
        ],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      expect(hasUnsavedChanges.value).toBe(false)
    })
  })

  // ============================================================================
  // 未处理句子检查和加工测试
  // ============================================================================

  describe('checkAndProcessUnprocessedSentences', () => {
    it('应该存在 checkAndProcessUnprocessedSentences 函数', () => {
      expect(typeof checkAndProcessUnprocessedSentences).toBe('function')
    })

    it('当前课程不存在时应该返回 true', async () => {
      mockCourseStore.currentCourse.value = null
      
      const result = await checkAndProcessUnprocessedSentences()
      
      expect(result).toBe(true)
    })

    it('没有未加工句子时应该返回 true', async () => {
      mockCourseStore.currentCourse.value = {
        id: 'course-1',
      }
      mockCourseStore.sentences.value = [
        {
          elements: [{ id: 'elem1' }], // 有 elements
        },
        {
          elements: [{ id: 'elem2' }], // 有 elements
        },
      ]
      
      const result = await checkAndProcessUnprocessedSentences()
      
      expect(result).toBe(true)
    })

    it('有未加工句子时应该调用批量处理接口', async () => {
      const mockMutate = vi.fn(() => Promise.resolve())
      mockUseNuxtApp.mockReturnValue({
        $trpc: {
          sentence: {
            batchProcessSentences: {
              mutate: mockMutate,
            },
          },
        },
      })
      
      mockCourseStore.currentCourse.value = {
        id: 'course-1',
      }
      mockCourseStore.sentences.value = [
        {
          elements: [], // 没有 elements
        },
        {
          elements: [{ id: 'elem1' }], // 有 elements
        },
      ]
      
      const result = await checkAndProcessUnprocessedSentences()
      
      expect(mockMutate).toHaveBeenCalledWith({
        courseId: 'course-1',
      })
      expect(result).toBe(true)
    })

    it('处理失败时应该返回 false', async () => {
      const mockMutate = vi.fn(() => Promise.reject(new Error('Processing failed')))
      mockUseNuxtApp.mockReturnValue({
        $trpc: {
          sentence: {
            batchProcessSentences: {
              mutate: mockMutate,
            },
          },
        },
      })
      
      mockCourseStore.currentCourse.value = {
        id: 'course-1',
      }
      mockCourseStore.sentences.value = [
        {
          elements: [], // 没有 elements
        },
      ]
      
      const result = await checkAndProcessUnprocessedSentences()
      
      expect(result).toBe(false)
    })

    it('应该正确调用加载状态管理', async () => {
      const mockStartLoading = vi.fn()
      const mockFinishLoading = vi.fn()
      mockUseLoadingSpinner.mockReturnValue({
        startLoading: mockStartLoading,
        finishLoading: mockFinishLoading,
      })
      
      mockCourseStore.currentCourse.value = {
        id: 'course-1',
      }
      mockCourseStore.sentences.value = [
        {
          elements: [], // 没有 elements
        },
      ]
      
      await checkAndProcessUnprocessedSentences()
      
      expect(mockStartLoading).toHaveBeenCalled()
      expect(mockFinishLoading).toHaveBeenCalled()
    })

    it('处理失败时也应该调用 finishLoading', async () => {
      const mockStartLoading = vi.fn()
      const mockFinishLoading = vi.fn()
      mockUseLoadingSpinner.mockReturnValue({
        startLoading: mockStartLoading,
        finishLoading: mockFinishLoading,
      })
      
      const mockMutate = vi.fn(() => Promise.reject(new Error('Processing failed')))
      mockUseNuxtApp.mockReturnValue({
        $trpc: {
          sentence: {
            batchProcessSentences: {
              mutate: mockMutate,
            },
          },
        },
      })
      
      mockCourseStore.currentCourse.value = {
        id: 'course-1',
      }
      mockCourseStore.sentences.value = [
        {
          elements: [], // 没有 elements
        },
      ]
      
      await checkAndProcessUnprocessedSentences()
      
      expect(mockStartLoading).toHaveBeenCalled()
      expect(mockFinishLoading).toHaveBeenCalled()
    })
  })

  // ============================================================================
  // 函数存在性测试
  // ============================================================================

  describe('函数存在性测试', () => {
    it('应该导出所有必要的函数', () => {
      expect(useChangeDetection).toBeDefined()
      expect(checkAndProcessUnprocessedSentences).toBeDefined()
    })

    it('所有导出的函数都应该是函数类型', () => {
      expect(typeof useChangeDetection).toBe('function')
      expect(typeof checkAndProcessUnprocessedSentences).toBe('function')
    })
  })

  // ============================================================================
  // 类型检查测试
  // ============================================================================

  describe('类型检查测试', () => {
    it('应该接受正确的参数类型', () => {
      expect(() => {
        useChangeDetection()
      }).not.toThrow()
      
      expect(() => {
        checkAndProcessUnprocessedSentences()
      }).not.toThrow()
    })

    it('checkAndProcessUnprocessedSentences 应该返回 Promise<boolean>', async () => {
      const result = await checkAndProcessUnprocessedSentences()
      expect(typeof result).toBe('boolean')
    })
  })

  // ============================================================================
  // 集成测试
  // ============================================================================

  describe('集成测试', () => {
    it('完整的变更检测流程', () => {
      // 设置完整的测试环境
      mockPlayerStore.mediaFile.value = {
        type: 'audio/mp3',
      } as File
      mockLrcStore.hasLrc.value = true
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
          sentenceId: 'sent1',
        },
        {
          uuid: 'uuid2',
          id: 2,
          startTime: '00:00:02,000',
          endTime: '00:00:03,000',
          text: 'New subtitle', // 新增的字幕
          translationText: '新字幕',
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [
          {
            id: 'sent1',
            content: 'Hello',
            chinese: '你好',
            startTime: 1,
            endTime: 2,
          },
          {
            id: 'sent2', // 这个句子被删除了
            content: 'Deleted',
            chinese: '删除的',
            startTime: 3,
            endTime: 5,
          },
        ],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      // 应该检测到变更（新增字幕 + 删除句子）
      expect(hasUnsavedChanges.value).toBe(true)
    })

    it('复杂的多重变更检测', () => {
      mockPlayerStore.mediaFile.value = {
        type: 'video/mp4',
      } as File
      mockPlayerStore.mediaFileHash.value = 'new-hash'
      mockPlayerStore.originalMediaFileHash.value = 'original-hash'
      mockLrcStore.hasLrc.value = true
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:03,000', // 时间变更
          text: 'Modified Hello', // 文本变更
          translationText: '修改的你好', // 翻译变更
          sentenceId: 'sent1',
        },
      ]
      mockCourseStore.currentCourse.value = {
        sentences: [
          {
            id: 'sent1',
            content: 'Hello',
            chinese: '你好',
            startTime: 1,
            endTime: 2,
          },
        ],
      }
      
      const { hasUnsavedChanges } = useChangeDetection()
      
      // 应该检测到多重变更（文本+翻译+时间+媒体文件）
      expect(hasUnsavedChanges.value).toBe(true)
    })
  })
})