import { describe, it, expect } from 'vitest'
import {
  formatTimeToString,
  formatTimeForVtt,
  formatTimeForSRT,
  formatDurationToMinute,
  millisecondsToSeconds,
  normalizeVttTime,
  parseSrtTime,
  parseTimeStringToSeconds,
  parseNormalizedTimeToSeconds,
  timestampToSeconds,
  validateSrtTimeFormat,
  validateTimeLogic,
  hasTimestampError,
  isValidTimestamp,
} from '../useTimeUtils'

describe('useTimeUtils', () => {
  // ============================================================================
  // 时间格式化函数测试
  // ============================================================================

  describe('formatTimeToString', () => {
    it('应该正确格式化秒数为 HH:MM:SS,mmm 格式', () => {
      expect(formatTimeToString(0)).toBe('00:00:00,000')
      expect(formatTimeToString(3661.123)).toBe('01:01:01,123')
      expect(formatTimeToString(3661.999)).toBe('01:01:01,999')
      expect(formatTimeToString(123.456)).toBe('00:02:03,456')
    })

    it('应该支持不同的分隔符', () => {
      expect(formatTimeToString(123.456, '.')).toBe('00:02:03.456')
      expect(formatTimeToString(123.456, ',')).toBe('00:02:03,456')
    })

    it('应该支持 MM:SS 格式', () => {
      expect(formatTimeToString(123.456, ',', 'MM:SS')).toBe('02:03,456')
      expect(formatTimeToString(3661.123, ',', 'MM:SS')).toBe('61:01,123')
    })

    it('应该处理 null 值', () => {
      expect(formatTimeToString(null as any)).toBe('00:00:00,000')
      expect(formatTimeToString(undefined as any)).toBe('00:00:00,000')
    })

    it('应该处理小数精度', () => {
      expect(formatTimeToString(1.0001)).toBe('00:00:01,000')
      expect(formatTimeToString(1.5)).toBe('00:00:01,500')
      expect(formatTimeToString(1.9999)).toBe('00:00:02,000')
    })
  })

  describe('formatTimeForVtt', () => {
    it('应该将时间转换为 VTT 格式', () => {
      expect(formatTimeForVtt('01:23:45,678')).toBe('01:23:45.678')
      expect(formatTimeForVtt('1:23:45,678')).toBe('01:23:45.678')
      expect(formatTimeForVtt('23:45,678')).toBe('00:23:45.678')
    })

    it('应该保持已经是 VTT 格式的时间不变', () => {
      expect(formatTimeForVtt('01:23:45.678')).toBe('01:23:45.678')
    })

    it('应该处理各种输入格式', () => {
      expect(formatTimeForVtt('3:5')).toBe('00:03:05.000')
      expect(formatTimeForVtt('3:5.1')).toBe('00:03:05.100')
      expect(formatTimeForVtt('1:3:5.1')).toBe('01:03:05.100')
    })
  })

  describe('formatTimeForSRT', () => {
    it('应该正确格式化秒数为 SRT 格式', () => {
      expect(formatTimeForSRT(0)).toBe('00:00:00,000')
      expect(formatTimeForSRT(3661.123)).toBe('01:01:01,123')
      expect(formatTimeForSRT(123.456)).toBe('00:02:03,456')
    })

    it('应该处理边界值', () => {
      expect(formatTimeForSRT(0.001)).toBe('00:00:00,001')
      expect(formatTimeForSRT(359999.999)).toBe('99:59:59,999')
    })
  })

  describe('formatDurationToMinute', () => {
    it('应该正确格式化毫秒为分钟秒格式', () => {
      expect(formatDurationToMinute(0)).toBe('0:00')
      expect(formatDurationToMinute(30000)).toBe('0:30')
      expect(formatDurationToMinute(60000)).toBe('1:00')
      expect(formatDurationToMinute(90000)).toBe('1:30')
      expect(formatDurationToMinute(3600000)).toBe('60:00')
    })

    it('应该处理不完整的秒数', () => {
      expect(formatDurationToMinute(1500)).toBe('0:01')
      expect(formatDurationToMinute(59999)).toBe('0:59')
    })
  })

  describe('millisecondsToSeconds', () => {
    it('应该正确转换毫秒为秒', () => {
      expect(millisecondsToSeconds(0)).toBe(0)
      expect(millisecondsToSeconds(1000)).toBe(1)
      expect(millisecondsToSeconds(1500)).toBe(1.5)
      expect(millisecondsToSeconds(123456)).toBe(123.456)
    })
  })

  describe('normalizeVttTime', () => {
    it('应该标准化 mm:ss 格式', () => {
      expect(normalizeVttTime('3:5')).toBe('00:03:05,000')
      expect(normalizeVttTime('23:45')).toBe('00:23:45,000')
      expect(normalizeVttTime('3:5.1')).toBe('00:03:05,100')
      expect(normalizeVttTime('3:5.123')).toBe('00:03:05,123')
      expect(normalizeVttTime('3:5.1234')).toBe('00:03:05,123')
    })

    it('应该标准化 HH:MM:SS 格式', () => {
      expect(normalizeVttTime('1:23:45')).toBe('01:23:45,000')
      expect(normalizeVttTime('1:23:45.678')).toBe('01:23:45,678')
      expect(normalizeVttTime('01:23:45,678')).toBe('01:23:45,678')
    })

    it('应该支持不同的分隔符', () => {
      expect(normalizeVttTime('1:23:45.678', '.')).toBe('01:23:45.678')
      expect(normalizeVttTime('1:23:45.678', ',')).toBe('01:23:45,678')
    })

    it('应该处理边界情况', () => {
      expect(normalizeVttTime('0:0')).toBe('00:00:00,000')
      expect(normalizeVttTime('59:59')).toBe('00:59:59,000')
      expect(normalizeVttTime('23:59:59.999')).toBe('23:59:59,999')
    })

    it('应该对无效格式原样返回', () => {
      expect(normalizeVttTime('invalid')).toBe('invalid')
      expect(normalizeVttTime('1:2:3:4')).toBe('1:2:3:4')
      expect(normalizeVttTime('')).toBe('')
    })
  })

  // ============================================================================
  // 时间解析函数测试
  // ============================================================================

  describe('parseSrtTime', () => {
    it('应该正确解析 SRT 格式时间', () => {
      expect(parseSrtTime('00:00:00,000')).toBe(0)
      expect(parseSrtTime('00:00:01,000')).toBe(1)
      expect(parseSrtTime('00:01:00,000')).toBe(60)
      expect(parseSrtTime('01:00:00,000')).toBe(3600)
      expect(parseSrtTime('01:01:01,123')).toBe(3661.123)
    })

    it('应该对非标准格式返回 null', () => {
      expect(parseSrtTime('invalid')).toBe(null)
      expect(parseSrtTime('')).toBe(null)
      expect(parseSrtTime('1:2:3:4')).toBe(null)
    })

    it('应该处理边界值', () => {
      expect(parseSrtTime('23:59:59,999')).toBe(86399.999)
      expect(parseSrtTime('00:00:00,001')).toBe(0.001)
    })
  })

  describe('parseTimeStringToSeconds', () => {
    it('应该正确解析各种时间格式', () => {
      expect(parseTimeStringToSeconds('3:5')).toBe(185)
      expect(parseTimeStringToSeconds('3:5.1')).toBe(185.1)
      expect(parseTimeStringToSeconds('1:3:5')).toBe(3785)
      expect(parseTimeStringToSeconds('1:3:5.123')).toBe(3785.123)
    })

    it('应该对无效格式返回 0', () => {
      expect(parseTimeStringToSeconds('invalid')).toBe(0)
      expect(parseTimeStringToSeconds('')).toBe(0)
    })
  })

  describe('parseNormalizedTimeToSeconds', () => {
    it('应该正确解析标准化时间格式', () => {
      expect(parseNormalizedTimeToSeconds('00:00:00,000')).toBe(0)
      expect(parseNormalizedTimeToSeconds('00:00:01,000')).toBe(1)
      expect(parseNormalizedTimeToSeconds('01:01:01,123')).toBe(3661.123)
    })

    it('应该支持点分隔符', () => {
      expect(parseNormalizedTimeToSeconds('01:01:01.123')).toBe(3661.123)
    })

    it('应该验证时间范围', () => {
      expect(parseNormalizedTimeToSeconds('00:60:00,000')).toBe(null) // 分钟超过60
      expect(parseNormalizedTimeToSeconds('00:00:60,000')).toBe(null) // 秒超过60
      expect(parseNormalizedTimeToSeconds('100:00:00,000')).toBe(null) // 小时超过100
      expect(parseNormalizedTimeToSeconds('00:00:00,1000')).toBe(null) // 毫秒超过1000
    })

    it('应该对无效格式返回 null', () => {
      expect(parseNormalizedTimeToSeconds('invalid')).toBe(null)
      expect(parseNormalizedTimeToSeconds('1:2:3,4')).toBe(null) // 格式不正确
    })
  })

  describe('timestampToSeconds', () => {
    it('应该处理数字类型输入', () => {
      expect(timestampToSeconds(0)).toBe(0)
      expect(timestampToSeconds(123.456)).toBe(123.456)
      expect(timestampToSeconds(-1)).toBe(-1)
    })

    it('应该处理时间字符串', () => {
      expect(timestampToSeconds('3:5')).toBe(185)
      expect(timestampToSeconds('1:3:5.123')).toBe(3785.123)
    })

    it('应该处理秒数字符串', () => {
      expect(timestampToSeconds('123')).toBe(123)
      expect(timestampToSeconds('123.456')).toBe(123.456)
    })

    it('应该对无效输入返回 0', () => {
      expect(timestampToSeconds('invalid')).toBe(0)
      expect(timestampToSeconds('')).toBe(0)
      expect(timestampToSeconds('-123')).toBe(0) // 负数
    })
  })

  // ============================================================================
  // 时间验证函数测试
  // ============================================================================

  describe('validateSrtTimeFormat', () => {
    it('应该验证正确的 SRT 格式', () => {
      expect(validateSrtTimeFormat('00:00:00,000')).toBe(true)
      expect(validateSrtTimeFormat('01:23:45,678')).toBe(true)
      expect(validateSrtTimeFormat('23:59:59,999')).toBe(true)
    })

    it('应该拒绝非标准格式', () => {
      expect(validateSrtTimeFormat('1:2:3,4')).toBe(false)
      expect(validateSrtTimeFormat('01:23:45.678')).toBe(false) // 点分隔符
      expect(validateSrtTimeFormat('3:5')).toBe(false) // 缺少小时
      expect(validateSrtTimeFormat('invalid')).toBe(false)
    })

    it('应该验证时间范围', () => {
      expect(validateSrtTimeFormat('00:60:00,000')).toBe(false) // 分钟超过60
      expect(validateSrtTimeFormat('00:00:60,000')).toBe(false) // 秒超过60
      expect(validateSrtTimeFormat('00:00:00,1000')).toBe(false) // 毫秒超过1000
    })
  })

  describe('validateTimeLogic', () => {
    it('应该验证开始时间不晚于结束时间', () => {
      expect(validateTimeLogic('00:00:00,000', '00:00:01,000')).toBe(true)
      expect(validateTimeLogic('00:00:00,000', '00:00:00,000')).toBe(true) // 相等也有效
      expect(validateTimeLogic('00:00:01,000', '00:00:00,000')).toBe(false)
    })

    it('应该处理各种格式', () => {
      expect(validateTimeLogic('3:5', '3:6')).toBe(true)
      expect(validateTimeLogic('1:3:5', '1:3:6')).toBe(true)
      expect(validateTimeLogic('1:3:6', '1:3:5')).toBe(false)
    })

    it('应该对无效格式返回 false', () => {
      expect(validateTimeLogic('invalid', '00:00:01,000')).toBe(false)
      expect(validateTimeLogic('00:00:00,000', 'invalid')).toBe(false)
    })
  })

  describe('hasTimestampError', () => {
    const createSubtitle = (id: number, startTime: string, endTime: string) => ({
      uuid: `uuid-${id}`,
      id,
      startTime,
      endTime,
      text: `Text ${id}`,
      translationText: `Translation ${id}`,
    })

    it('应该检测格式错误', () => {
      const subtitle = createSubtitle(1, 'invalid', '00:00:01,000')
      expect(hasTimestampError(subtitle)).toBe(true)
    })

    it('应该检测时间逻辑错误', () => {
      const subtitle = createSubtitle(1, '00:00:02,000', '00:00:01,000')
      expect(hasTimestampError(subtitle)).toBe(true)
    })

    it('应该检测与前一字幕的顺序错误', () => {
      const prevSubtitle = createSubtitle(1, '00:00:01,000', '00:00:03,000')
      const subtitle = createSubtitle(2, '00:00:02,000', '00:00:04,000')
      expect(hasTimestampError(subtitle, prevSubtitle)).toBe(true)
    })

    it('应该检测与下一字幕的顺序错误', () => {
      const subtitle = createSubtitle(1, '00:00:01,000', '00:00:04,000')
      const nextSubtitle = createSubtitle(2, '00:00:03,000', '00:00:05,000')
      expect(hasTimestampError(subtitle, undefined, nextSubtitle)).toBe(true)
    })

    it('应该通过正确的字幕序列验证', () => {
      const prevSubtitle = createSubtitle(1, '00:00:01,000', '00:00:02,000')
      const subtitle = createSubtitle(2, '00:00:03,000', '00:00:04,000')
      const nextSubtitle = createSubtitle(3, '00:00:05,000', '00:00:06,000')
      expect(hasTimestampError(subtitle, prevSubtitle, nextSubtitle)).toBe(false)
    })

    it('应该允许时间相等的情况', () => {
      const prevSubtitle = createSubtitle(1, '00:00:01,000', '00:00:02,000')
      const subtitle = createSubtitle(2, '00:00:02,000', '00:00:03,000')
      const nextSubtitle = createSubtitle(3, '00:00:03,000', '00:00:04,000')
      expect(hasTimestampError(subtitle, prevSubtitle, nextSubtitle)).toBe(false)
    })
  })

  describe('isValidTimestamp', () => {
    it('应该验证数字类型时间戳', () => {
      expect(isValidTimestamp(0)).toBe(true)
      expect(isValidTimestamp(123.456)).toBe(true)
      expect(isValidTimestamp(-1)).toBe(false)
    })

    it('应该验证 SRT 格式时间戳', () => {
      expect(isValidTimestamp('00:00:00,000')).toBe(true)
      expect(isValidTimestamp('01:23:45,678')).toBe(true)
      expect(isValidTimestamp('01:23:45.678')).toBe(true)
    })

    it('应该验证秒数字符串', () => {
      expect(isValidTimestamp('123')).toBe(true)
      expect(isValidTimestamp('123.456')).toBe(true)
      expect(isValidTimestamp('0')).toBe(true)
    })

    it('应该拒绝无效格式', () => {
      expect(isValidTimestamp('invalid')).toBe(false)
      expect(isValidTimestamp('')).toBe(false)
      expect(isValidTimestamp('-123')).toBe(false)
    })
  })

  // ============================================================================
  // 边界情况和性能测试
  // ============================================================================

  describe('边界情况测试', () => {
    it('应该处理极大的时间值', () => {
      const largeSeconds = 359999.999 // 99:59:59.999
      expect(formatTimeForSRT(largeSeconds)).toBe('99:59:59,999')
    })

    it('应该处理极小的时间值', () => {
      expect(formatTimeForSRT(0.001)).toBe('00:00:00,001')
      expect(parseTimeStringToSeconds('0:0.001')).toBe(0.001)
    })

    it('应该处理精度问题', () => {
      const result = parseTimeStringToSeconds('0:0.123')
      expect(result).toBeCloseTo(0.123, 3)
    })
  })

  describe('性能测试', () => {
    it('应该快速处理大量时间格式化', () => {
      const start = Date.now()
      for (let i = 0; i < 1000; i++) {
        formatTimeForSRT(i * 1.234)
      }
      const end = Date.now()
      expect(end - start).toBeLessThan(1000) // 应该在1秒内完成
    })

    it('应该快速处理大量时间解析', () => {
      const start = Date.now()
      for (let i = 0; i < 1000; i++) {
        parseTimeStringToSeconds(`${i}:${i % 60}.${i % 1000}`)
      }
      const end = Date.now()
      expect(end - start).toBeLessThan(1000) // 应该在1秒内完成
    })
  })
})