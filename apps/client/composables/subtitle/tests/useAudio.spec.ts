import { describe, it, expect, vi, beforeEach } from 'vitest'

// Mock Vue dependencies
const mockRef = vi.fn((value) => ({ value }))
const mockReactive = vi.fn((value) => value)
const mockWatch = vi.fn()
const mockOnBeforeUnmount = vi.fn()

vi.mock('vue', () => ({
  ref: mockRef,
  reactive: mockReactive,
  watch: mockWatch,
  onBeforeUnmount: mockOnBeforeUnmount,
}))

// Make Vue functions available globally
global.ref = mockRef
global.reactive = mockReactive
global.watch = mockWatch
global.onBeforeUnmount = mockOnBeforeUnmount

// Mock WaveSurfer and plugins
vi.mock('wavesurfer.js', () => ({
  default: {
    create: vi.fn(() => ({
      destroy: vi.fn(),
      play: vi.fn(),
      pause: vi.fn(),
      playPause: vi.fn(),
      setPlaybackRate: vi.fn(),
      setVolume: vi.fn(),
      load: vi.fn(() => Promise.resolve()),
      setTime: vi.fn(),
      getCurrentTime: vi.fn(() => 0),
      getDuration: vi.fn(() => 100),
      isPlaying: vi.fn(() => false),
      on: vi.fn(),
    })),
  },
}))

vi.mock('wavesurfer.js/dist/plugins/regions.esm.js', () => ({
  default: {
    create: vi.fn(() => ({
      avoidOverlapping: vi.fn(),
      on: vi.fn(),
      un: vi.fn(),
      clearRegions: vi.fn(),
      addRegion: vi.fn(() => ({
        id: 'region-1',
        start: 0,
        end: 10,
        setContent: vi.fn(),
        setOptions: vi.fn(),
        element: {
          querySelector: vi.fn(() => ({
            style: { cssText: '' },
          })),
        },
      })),
      getRegions: vi.fn(() => []),
    })),
  },
}))

vi.mock('wavesurfer.js/dist/plugins/timeline.esm.js', () => ({
  default: {
    create: vi.fn(() => ({})),
  },
}))

vi.mock('wavesurfer.js/dist/plugins/hover.esm.js', () => ({
  default: {
    create: vi.fn(() => ({})),
  },
}))

// Mock lodash-es
vi.mock('lodash-es', () => ({
  find: vi.fn((arr, fn) => arr.find(fn)),
  findIndex: vi.fn((arr, fn) => arr.findIndex(fn)),
  forEach: vi.fn((arr, fn) => arr.forEach(fn)),
  sortBy: vi.fn((arr, fn) => arr.sort((a, b) => fn(a) - fn(b))),
}))

// Mock stores
const mockPlayerStore = {
  isPlaying: false,
  playbackRate: 1,
  seekRequest: null,
  loopingSubtitleUuid: null,
  isLooping: false,
  loopStartTime: null,
  loopEndTime: null,
  isPracticePlaying: false,
  practiceEndTime: null,
  updatePlayIngStatus: vi.fn(),
  updatePauseStatus: vi.fn(),
  setSeekRequest: vi.fn(),
  updateLoopTime: vi.fn(),
  stopPracticePlay: vi.fn(),
}

const mockSubtitleStore = {
  subtitles: [],
  updateSubtitleStartTime: vi.fn(),
  updateSubtitleEndTime: vi.fn(),
}

// Player store mock is handled in test-setup.ts

// Subtitle store mock is handled in test-setup.ts

// Mock time utils
vi.mock('./useTimeUtils', () => ({
  formatTimeToString: vi.fn((time) => `${Math.floor(time / 60)}:${(time % 60).toFixed(3).padStart(6, '0')}`),
  parseSrtTime: vi.fn((timeStr) => {
    if (timeStr === '00:00:01,000') return 1
    if (timeStr === '00:00:02,000') return 2
    if (timeStr === '00:00:03,000') return 3
    return 0
  }),
}))

// Mock speech recognition
vi.mock('~/composables/subtitle/useSpeechRecognition', () => ({
  ApiConfig: {},
}))

// Mock DOM
global.document = {
  createElement: vi.fn((tag) => ({
    style: { cssText: '' },
    textContent: '',
    innerHTML: '',
    appendChild: vi.fn(),
    querySelector: vi.fn(),
  })),
} as any

global.URL = {
  createObjectURL: vi.fn(() => 'blob:test-url'),
  revokeObjectURL: vi.fn(),
} as any

global.window = {
  setInterval: vi.fn(),
  clearInterval: vi.fn(),
} as any

// Import the functions after mocks
import {
  useSpeechRecognitionConfig,
  useWaveformPlayerSync,
  useWaveformRegions,
  useWaveSurfer,
} from '../useAudio'

describe('useAudio', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset mock implementations
    mockRef.mockImplementation((value) => ({ value }))
    mockReactive.mockImplementation((value) => value)
    mockWatch.mockImplementation(() => {})
    mockOnBeforeUnmount.mockImplementation(() => {})
  })

  // ============================================================================
  // 语音识别配置测试
  // ============================================================================

  describe('useSpeechRecognitionConfig', () => {
    it('应该存在 useSpeechRecognitionConfig 函数', () => {
      expect(typeof useSpeechRecognitionConfig).toBe('function')
    })

    it('应该正确初始化语音识别配置', () => {
      const config = useSpeechRecognitionConfig()
      
      expect(config).toHaveProperty('apiConfig')
      expect(config).toHaveProperty('selectedLanguage')
      expect(config).toHaveProperty('selectedContentType')
      expect(config).toHaveProperty('features')
      expect(config).toHaveProperty('maxCharsPerLine')
      expect(config).toHaveProperty('maxLines')
      expect(config).toHaveProperty('languageOptions')
      expect(config).toHaveProperty('contentTypeOptions')
      expect(config).toHaveProperty('getRecommendedCharsPerLine')
      expect(config).toHaveProperty('getLanguageLabel')
      expect(config).toHaveProperty('getContentTypeLabel')
    })

    it('应该正确设置语言选项', () => {
      const config = useSpeechRecognitionConfig()
      
      expect(config.languageOptions).toHaveLength(2)
      expect(config.languageOptions[0]).toEqual({
        value: 'zh-CN',
        label: '中文普通话（支持中英混合及方言）',
      })
      expect(config.languageOptions[1]).toEqual({
        value: 'en-US',
        label: '英语（美国）',
      })
    })

    it('应该正确设置内容类型选项', () => {
      const config = useSpeechRecognitionConfig()
      
      expect(config.contentTypeOptions).toHaveLength(3)
      expect(config.contentTypeOptions[0]).toEqual({
        value: 'auto',
        label: '自动判断',
      })
      expect(config.contentTypeOptions[1]).toEqual({
        value: 'speech',
        label: '语音',
      })
      expect(config.contentTypeOptions[2]).toEqual({
        value: 'singing',
        label: '歌词',
      })
    })

    it('应该正确获取推荐字符数', () => {
      const config = useSpeechRecognitionConfig()
      
      expect(typeof config.getRecommendedCharsPerLine).toBe('function')
      expect(config.getRecommendedCharsPerLine()).toBe(55) // 默认 en-US
    })

    it('应该正确获取语言标签', () => {
      const config = useSpeechRecognitionConfig()
      
      expect(config.getLanguageLabel('zh-CN')).toBe('中文普通话（支持中英混合及方言）')
      expect(config.getLanguageLabel('en-US')).toBe('英语（美国）')
      expect(config.getLanguageLabel('unknown')).toBe('unknown')
    })

    it('应该正确获取内容类型标签', () => {
      const config = useSpeechRecognitionConfig()
      
      expect(config.getContentTypeLabel('auto')).toBe('自动判断')
      expect(config.getContentTypeLabel('speech')).toBe('语音')
      expect(config.getContentTypeLabel('singing')).toBe('歌词')
      expect(config.getContentTypeLabel('unknown')).toBe('unknown')
    })

    it('应该正确处理特色功能配置', () => {
      const config = useSpeechRecognitionConfig()
      
      expect(config.features).toHaveProperty('usePunc')
      expect(config.features).toHaveProperty('useDdc')
      expect(config.features).toHaveProperty('withSpeakerInfo')
      expect(config.features).toHaveProperty('useItn')
      expect(config.features.usePunc).toBe(false)
      expect(config.features.useDdc).toBe(false)
      expect(config.features.withSpeakerInfo).toBe(false)
      expect(config.features.useItn).toBe(false)
    })
  })

  // ============================================================================
  // WaveSurfer 播放器同步测试
  // ============================================================================

  describe('useWaveformPlayerSync', () => {
    it('应该存在 useWaveformPlayerSync 函数', () => {
      expect(typeof useWaveformPlayerSync).toBe('function')
    })

    it('应该正确同步播放器状态', () => {
      const mockWavesurfer = { value: null }
      const mockStatus = { value: 'ready' }
      const mockPlay = vi.fn()
      const mockPause = vi.fn()
      const mockSetPlaybackRate = vi.fn()

      expect(() => {
        useWaveformPlayerSync(mockWavesurfer, mockStatus, mockPlay, mockPause, mockSetPlaybackRate)
      }).not.toThrow()

      expect(mockWatch).toHaveBeenCalledTimes(3)
    })

    it('应该处理状态不是 ready 的情况', () => {
      const mockWavesurfer = { value: null }
      const mockStatus = { value: 'loading' }
      const mockPlay = vi.fn()
      const mockPause = vi.fn()
      const mockSetPlaybackRate = vi.fn()

      expect(() => {
        useWaveformPlayerSync(mockWavesurfer, mockStatus, mockPlay, mockPause, mockSetPlaybackRate)
      }).not.toThrow()

      expect(mockWatch).toHaveBeenCalledTimes(3)
    })
  })

  // ============================================================================
  // WaveSurfer Regions 管理测试
  // ============================================================================

  describe('useWaveformRegions', () => {
    const mockRegionsPlugin = {
      on: vi.fn(),
      un: vi.fn(),
      clearRegions: vi.fn(),
      addRegion: vi.fn(() => ({
        id: 'region-1',
        start: 0,
        end: 10,
        setContent: vi.fn(),
        setOptions: vi.fn(),
        element: {
          querySelector: vi.fn(() => ({
            style: { cssText: '' },
          })),
        },
      })),
      getRegions: vi.fn(() => []),
    }

    beforeEach(() => {
      mockSubtitleStore.subtitles.value = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
        },
        {
          uuid: 'uuid2',
          id: 2,
          startTime: '00:00:02,000',
          endTime: '00:00:03,000',
          text: 'World',
          translationText: '世界',
        },
      ]
    })

    it('应该存在 useWaveformRegions 函数', () => {
      expect(typeof useWaveformRegions).toBe('function')
    })

    it('应该正确初始化 regions 管理器', () => {
      const regions = useWaveformRegions(mockRegionsPlugin)
      
      expect(regions).toHaveProperty('subtitleRegionMap')
      expect(regions).toHaveProperty('registerRegionEvents')
      expect(regions).toHaveProperty('unregisterRegionEvents')
      expect(regions).toHaveProperty('syncRegions')
    })

    it('应该正确注册 region 事件', () => {
      const regions = useWaveformRegions(mockRegionsPlugin)
      
      regions.registerRegionEvents()
      
      expect(mockRegionsPlugin.on).toHaveBeenCalledWith('region-updated', expect.any(Function))
      expect(mockRegionsPlugin.on).toHaveBeenCalledWith('region-clicked', expect.any(Function))
    })

    it('应该正确注销 region 事件', () => {
      const regions = useWaveformRegions(mockRegionsPlugin)
      
      regions.unregisterRegionEvents()
      
      expect(mockRegionsPlugin.un).toHaveBeenCalledWith('region-updated', expect.any(Function))
      expect(mockRegionsPlugin.un).toHaveBeenCalledWith('region-clicked', expect.any(Function))
    })

    it('应该正确同步 regions', () => {
      const regions = useWaveformRegions(mockRegionsPlugin)
      const mockWavesurfer = { test: 'wavesurfer' }
      
      regions.syncRegions(mockWavesurfer, 'ready')
      
      expect(mockRegionsPlugin.clearRegions).toHaveBeenCalled()
      expect(mockRegionsPlugin.addRegion).toHaveBeenCalledTimes(2)
    })

    it('应该在状态不是 ready 时跳过同步', () => {
      const regions = useWaveformRegions(mockRegionsPlugin)
      const mockWavesurfer = { test: 'wavesurfer' }
      
      regions.syncRegions(mockWavesurfer, 'loading')
      
      expect(mockRegionsPlugin.clearRegions).not.toHaveBeenCalled()
      expect(mockRegionsPlugin.addRegion).not.toHaveBeenCalled()
    })

    it('应该正确处理无效的字幕时间', () => {
      // 设置无效的字幕时间
      mockSubtitleStore.subtitles = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: 'invalid',
          endTime: 'invalid',
          text: 'Invalid',
          translationText: '',
        },
      ]

      const regions = useWaveformRegions(mockRegionsPlugin)
      const mockWavesurfer = { test: 'wavesurfer' }
      
      regions.syncRegions(mockWavesurfer, 'ready')
      
      expect(mockRegionsPlugin.clearRegions).toHaveBeenCalled()
      expect(mockRegionsPlugin.addRegion).not.toHaveBeenCalled()
    })
  })

  // ============================================================================
  // WaveSurfer 主要功能测试
  // ============================================================================

  describe('useWaveSurfer', () => {
    it('应该存在 useWaveSurfer 函数', () => {
      expect(typeof useWaveSurfer).toBe('function')
    })

    it('应该正确初始化 WaveSurfer 实例', () => {
      const waveSurfer = useWaveSurfer()
      
      expect(waveSurfer).toHaveProperty('wavesurfer')
      expect(waveSurfer).toHaveProperty('status')
      expect(waveSurfer).toHaveProperty('error')
      expect(waveSurfer).toHaveProperty('initialize')
      expect(waveSurfer).toHaveProperty('loadAudio')
      expect(waveSurfer).toHaveProperty('destroy')
      expect(waveSurfer).toHaveProperty('togglePlay')
      expect(waveSurfer).toHaveProperty('play')
      expect(waveSurfer).toHaveProperty('pause')
      expect(waveSurfer).toHaveProperty('setPlaybackRate')
      expect(waveSurfer).toHaveProperty('regionsPluginInstance')
    })

    it('应该正确处理初始化', async () => {
      const waveSurfer = useWaveSurfer()
      const mockContainer = document.createElement('div')
      
      await expect(waveSurfer.initialize({
        container: mockContainer,
        onReady: vi.fn(),
        onError: vi.fn(),
        onSeek: vi.fn(),
        onDurationAvailable: vi.fn(),
        playbackRate: 1.5,
      })).resolves.not.toThrow()
    })

    it('应该正确处理音频加载', async () => {
      const waveSurfer = useWaveSurfer()
      const mockContainer = document.createElement('div')
      
      await waveSurfer.initialize({
        container: mockContainer,
      })
      
      const mockFile = new File(['test'], 'test.mp3', { type: 'audio/mpeg' })
      
      await expect(waveSurfer.loadAudio(mockFile)).resolves.not.toThrow()
      expect(global.URL.createObjectURL).toHaveBeenCalledWith(mockFile)
    })

    it('应该正确处理播放控制', async () => {
      const waveSurfer = useWaveSurfer()
      const mockContainer = document.createElement('div')
      
      await waveSurfer.initialize({
        container: mockContainer,
      })
      
      // 模拟状态为 ready
      waveSurfer.status.value = 'ready'
      
      // 测试播放控制方法存在且不抛出错误
      expect(() => waveSurfer.play()).not.toThrow()
      expect(() => waveSurfer.pause()).not.toThrow()
      expect(() => waveSurfer.togglePlay()).not.toThrow()
      expect(() => waveSurfer.setPlaybackRate(1.5)).not.toThrow()
    })

    it('应该正确处理销毁', async () => {
      const waveSurfer = useWaveSurfer()
      const mockContainer = document.createElement('div')
      
      await waveSurfer.initialize({
        container: mockContainer,
      })
      
      expect(() => waveSurfer.destroy()).not.toThrow()
      expect(waveSurfer.status.value).toBe('idle')
      expect(waveSurfer.error.value).toBe(null)
    })

    it('应该在未初始化时抛出错误', async () => {
      const waveSurfer = useWaveSurfer()
      const mockFile = new File(['test'], 'test.mp3', { type: 'audio/mpeg' })
      
      await expect(waveSurfer.loadAudio(mockFile)).rejects.toThrow('WaveSurfer not initialized before loadAudio')
      expect(waveSurfer.status.value).toBe('error')
    })

    it('应该正确处理播放控制在错误状态下的调用', async () => {
      const waveSurfer = useWaveSurfer()
      const mockContainer = document.createElement('div')
      
      await waveSurfer.initialize({
        container: mockContainer,
      })
      
      // 设置错误状态
      waveSurfer.status.value = 'error'
      
      // 这些操作应该不会抛出错误
      expect(() => waveSurfer.play()).not.toThrow()
      expect(() => waveSurfer.pause()).not.toThrow()
      expect(() => waveSurfer.togglePlay()).not.toThrow()
      expect(() => waveSurfer.setPlaybackRate(1.5)).not.toThrow()
    })
  })

  // ============================================================================
  // 边界情况和错误处理测试
  // ============================================================================

  describe('边界情况测试', () => {
    it('应该处理空的字幕数组', () => {
      mockSubtitleStore.subtitles = []
      
      const mockRegionsPlugin = {
        on: vi.fn(),
        un: vi.fn(),
        clearRegions: vi.fn(),
        addRegion: vi.fn(),
        getRegions: vi.fn(() => []),
      }
      
      const regions = useWaveformRegions(mockRegionsPlugin)
      const mockWavesurfer = { test: 'wavesurfer' }
      
      regions.syncRegions(mockWavesurfer, 'ready')
      
      expect(mockRegionsPlugin.clearRegions).toHaveBeenCalled()
      expect(mockRegionsPlugin.addRegion).not.toHaveBeenCalled()
    })

    it('应该处理无效的 regions plugin', () => {
      const regions = useWaveformRegions(null)
      
      expect(() => {
        regions.registerRegionEvents()
      }).not.toThrow()
      
      expect(() => {
        regions.unregisterRegionEvents()
      }).not.toThrow()
    })

    it('应该处理无效的 WaveSurfer 实例', () => {
      const mockRegionsPlugin = {
        on: vi.fn(),
        un: vi.fn(),
        clearRegions: vi.fn(),
        addRegion: vi.fn(),
        getRegions: vi.fn(() => []),
      }
      
      const regions = useWaveformRegions(mockRegionsPlugin)
      
      regions.syncRegions(null, 'ready')
      
      expect(mockRegionsPlugin.clearRegions).not.toHaveBeenCalled()
      expect(mockRegionsPlugin.addRegion).not.toHaveBeenCalled()
    })
  })

  // ============================================================================
  // 函数存在性测试
  // ============================================================================

  describe('函数存在性测试', () => {
    it('应该导出所有必要的函数', () => {
      expect(useSpeechRecognitionConfig).toBeDefined()
      expect(useWaveformPlayerSync).toBeDefined()
      expect(useWaveformRegions).toBeDefined()
      expect(useWaveSurfer).toBeDefined()
    })

    it('所有导出的函数都应该是函数类型', () => {
      expect(typeof useSpeechRecognitionConfig).toBe('function')
      expect(typeof useWaveformPlayerSync).toBe('function')
      expect(typeof useWaveformRegions).toBe('function')
      expect(typeof useWaveSurfer).toBe('function')
    })
  })

  // ============================================================================
  // 类型检查测试
  // ============================================================================

  describe('类型检查测试', () => {
    it('应该接受正确的参数类型', () => {
      expect(() => {
        useSpeechRecognitionConfig()
      }).not.toThrow()
      
      expect(() => {
        useWaveformPlayerSync(
          { value: null },
          { value: 'ready' },
          vi.fn(),
          vi.fn(),
          vi.fn()
        )
      }).not.toThrow()
      
      expect(() => {
        useWaveformRegions({})
      }).not.toThrow()
      
      expect(() => {
        useWaveSurfer()
      }).not.toThrow()
    })
  })
})