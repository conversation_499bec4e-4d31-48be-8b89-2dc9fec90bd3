import { describe, it, expect, vi, beforeEach } from 'vitest'

// Create mock functions
const mockRef = vi.fn((value) => ({ value }))
const mockOnMounted = vi.fn()

// Mock Vue dependencies
vi.mock('vue', () => ({
  ref: mockRef,
  onMounted: mockOnMounted,
}))

// Make Vue functions available globally
globalThis.ref = mockRef
globalThis.onMounted = mockOnMounted

// Mock dependencies
vi.mock('./useFormatProcessing', () => ({
  convertStatementsToSubtitles: vi.fn(() => [
    {
      uuid: 'uuid1',
      id: 1,
      startTime: '00:00:01,000',
      endTime: '00:00:02,000',
      text: 'Hello',
      translationText: '你好',
    },
  ]),
  subtitleToLrc: vi.fn(() => '[00:01.00]Hello'),
}))

// Mock Nuxt functions
vi.mock('#imports', () => ({
  useRoute: vi.fn(() => ({
    query: {
      coursePackId: 'pack-1',
      courseId: 'course-1',
    },
  })),
}))

// Store mocks are handled in test-setup.ts

// Course and CoursePack store mocks are handled in test-setup.ts

describe('useCourseDetail', () => {
  let mockRef: any
  let mockOnMounted: any
  let mockUseRoute: any
  let mockPlayerStore: any
  let mockLrcStore: any
  let mockSubtitleStore: any
  let mockCoursePackStore: any
  let mockCourseStore: any
  let mockConvertStatementsToSubtitles: any
  let mockSubtitleToLrc: any

  beforeEach(async () => {
    vi.clearAllMocks()
    
    // Import Vue mocks
    const vueMocks = await import('vue')
    mockRef = vueMocks.ref as any
    mockOnMounted = vueMocks.onMounted as any
    
    // Import route mock
    const importsMocks = await import('#imports')
    mockUseRoute = importsMocks.useRoute as any
    
    // Import store mocks
    const playerStoreMocks = await import('~/composables/subtitle/stores/playerStore')
    mockPlayerStore = (playerStoreMocks.usePlayerStore as any)()
    
    const lrcStoreMocks = await import('~/composables/subtitle/stores/lrcStore')
    mockLrcStore = (lrcStoreMocks.useLrcStore as any)()
    
    const subtitleStoreMocks = await import('~/composables/subtitle/stores/subtitleStore')
    mockSubtitleStore = (subtitleStoreMocks.useSubtitleStore as any)()
    
    const coursePackStoreMocks = await import('~/stores/coursePack')
    mockCoursePackStore = (coursePackStoreMocks.useCoursePackStore as any)()
    
    const courseStoreMocks = await import('~/stores/course')
    mockCourseStore = (courseStoreMocks.useCourseStore as any)()
    
    // Import format processing mocks
    const formatMocks = await import('../useFormatProcessing')
    mockConvertStatementsToSubtitles = formatMocks.convertStatementsToSubtitles as any
    mockSubtitleToLrc = formatMocks.subtitleToLrc as any
    
    // Reset implementations
    mockRef.mockImplementation((value: any) => ({ value }))
    mockOnMounted.mockImplementation((fn: any) => {
      if (typeof fn === 'function') {
        setTimeout(fn, 0)
      }
    })
    
    mockUseRoute.mockReturnValue({
      query: {
        coursePackId: 'pack-1',
        courseId: 'course-1',
      },
    })
    
    // Reset store states
    mockCourseStore.currentCourse = null
    mockPlayerStore.setMediaUrlWithProxy.mockResolvedValue(undefined)
    mockCoursePackStore.init.mockResolvedValue(undefined)
    mockCourseStore.init.mockResolvedValue(undefined)
  })

  // ============================================================================
  // 基本功能测试
  // ============================================================================

  describe('基本功能', () => {
    it('应该存在 useCourseDetail 函数', async () => {
      const { useCourseDetail } = await import('../useCourseDetail')
      expect(typeof useCourseDetail).toBe('function')
    })

    it('应该正确初始化状态', async () => {
      const { useCourseDetail } = await import('../useCourseDetail')
      const result = useCourseDetail()
      
      expect(result).toHaveProperty('loading')
      expect(result).toHaveProperty('error')
      expect(result).toHaveProperty('courseDetail')
      expect(result.loading.value).toBe(false)
      expect(result.error.value).toBe('')
      expect(result.courseDetail.value).toBeUndefined()
    })

    it('应该正确获取路由参数', async () => {
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      expect(mockUseRoute).toHaveBeenCalled()
    })

    it('应该在组件挂载时调用初始化', async () => {
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      expect(mockOnMounted).toHaveBeenCalled()
      expect(typeof mockOnMounted.mock.calls[0][0]).toBe('function')
    })
  })

  // ============================================================================
  // 初始化流程测试
  // ============================================================================

  describe('初始化流程', () => {
    it('应该正确处理有效的路由参数', async () => {
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 'pack-1',
          courseId: 'course-1',
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockCoursePackStore.init).toHaveBeenCalledWith('pack-1')
      expect(mockCourseStore.init).toHaveBeenCalledWith('pack-1', 'course-1')
    })

    it('应该跳过初始化当路由参数缺失时', async () => {
      mockUseRoute.mockReturnValue({
        query: {
          // 缺少必要参数
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockCoursePackStore.init).not.toHaveBeenCalled()
      expect(mockCourseStore.init).not.toHaveBeenCalled()
    })

    it('应该跳过初始化当课程ID为数组时', async () => {
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: ['pack-1'], // 数组形式
          courseId: ['course-1'], // 数组形式
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockCoursePackStore.init).not.toHaveBeenCalled()
      expect(mockCourseStore.init).not.toHaveBeenCalled()
    })

    it('应该处理部分路由参数', async () => {
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 'pack-1',
          // 缺少 courseId
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockCoursePackStore.init).not.toHaveBeenCalled()
      expect(mockCourseStore.init).not.toHaveBeenCalled()
    })

    it('应该在有缓存时跳过重新加载', async () => {
      mockCourseStore.currentCourse = {
        id: 'course-1', // 与路由参数一致
        title: 'Test Course',
      }
      
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 'pack-1',
          courseId: 'course-1',
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockCoursePackStore.init).not.toHaveBeenCalled()
      expect(mockCourseStore.init).not.toHaveBeenCalled()
    })

    it('应该在课程ID不匹配时重新加载', async () => {
      mockCourseStore.currentCourse = {
        id: 'different-course', // 与路由参数不一致
        title: 'Different Course',
      }
      
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 'pack-1',
          courseId: 'course-1',
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockPlayerStore.clearMedia).toHaveBeenCalled()
      expect(mockCoursePackStore.init).toHaveBeenCalledWith('pack-1')
      expect(mockCourseStore.init).toHaveBeenCalledWith('pack-1', 'course-1')
    })
  })

  // ============================================================================
  // 字幕编辑器初始化测试
  // ============================================================================

  describe('字幕编辑器初始化', () => {
    it('应该正确初始化字幕编辑器', async () => {
      mockCourseStore.currentCourse = {
        id: 'course-1',
        title: 'Test Course',
        mediaUrl: 'https://example.com/media.mp3',
        sentences: [
          {
            id: 'sent1',
            content: 'Hello',
            chinese: '你好',
            startTime: 1,
            endTime: 2,
          },
        ],
      }
      
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 'pack-1',
          courseId: 'course-1',
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockPlayerStore.setMediaUrlWithProxy).toHaveBeenCalledWith('https://example.com/media.mp3')
      expect(mockSubtitleStore.setSubtitles).toHaveBeenCalledWith(
        expect.any(Array),
        false // preserveHistory = false
      )
      expect(mockLrcStore.updateLrcContent).toHaveBeenCalledWith('', '[00:01.00]Hello')
    })

    it('当前课程不存在时应该跳过字幕编辑器初始化', async () => {
      mockCourseStore.currentCourse = null
      
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 'pack-1',
          courseId: 'course-1',
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockPlayerStore.setMediaUrlWithProxy).not.toHaveBeenCalled()
      expect(mockSubtitleStore.setSubtitles).not.toHaveBeenCalled()
      expect(mockLrcStore.updateLrcContent).not.toHaveBeenCalled()
    })

    it('应该正确处理空的句子数组', async () => {
      mockCourseStore.currentCourse = {
        id: 'course-1',
        title: 'Test Course',
        mediaUrl: 'https://example.com/media.mp3',
        sentences: [], // 空数组
      }
      
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 'pack-1',
          courseId: 'course-1',
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockPlayerStore.setMediaUrlWithProxy).toHaveBeenCalledWith('https://example.com/media.mp3')
      expect(mockSubtitleStore.setSubtitles).toHaveBeenCalled()
      expect(mockLrcStore.updateLrcContent).toHaveBeenCalled()
    })
  })

  // ============================================================================
  // 错误处理测试
  // ============================================================================

  describe('错误处理', () => {
    it('应该处理课程包初始化失败', async () => {
      mockCoursePackStore.init.mockRejectedValue(new Error('Course pack init failed'))
      
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 'pack-1',
          courseId: 'course-1',
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockCoursePackStore.init).toHaveBeenCalledWith('pack-1')
      // 即使失败，loading 也应该被重置
    })

    it('应该处理课程初始化失败', async () => {
      mockCourseStore.init.mockRejectedValue(new Error('Course init failed'))
      
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 'pack-1',
          courseId: 'course-1',
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockCourseStore.init).toHaveBeenCalledWith('pack-1', 'course-1')
    })

    it('应该处理媒体加载失败', async () => {
      mockPlayerStore.setMediaUrlWithProxy.mockRejectedValue(new Error('Media load failed'))
      
      mockCourseStore.currentCourse = {
        id: 'course-1',
        title: 'Test Course',
        mediaUrl: 'https://example.com/media.mp3',
        sentences: [],
      }
      
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 'pack-1',
          courseId: 'course-1',
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockPlayerStore.setMediaUrlWithProxy).toHaveBeenCalledWith('https://example.com/media.mp3')
    })
  })

  // ============================================================================
  // 边界情况测试
  // ============================================================================

  describe('边界情况测试', () => {
    it('应该处理空字符串路由参数', async () => {
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: '',
          courseId: '',
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockCoursePackStore.init).not.toHaveBeenCalled()
      expect(mockCourseStore.init).not.toHaveBeenCalled()
    })

    it('应该处理 null 路由参数', async () => {
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: null,
          courseId: null,
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockCoursePackStore.init).not.toHaveBeenCalled()
      expect(mockCourseStore.init).not.toHaveBeenCalled()
    })

    it('应该处理 undefined 路由参数', async () => {
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: undefined,
          courseId: undefined,
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockCoursePackStore.init).not.toHaveBeenCalled()
      expect(mockCourseStore.init).not.toHaveBeenCalled()
    })

    it('应该处理数字类型的路由参数', async () => {
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 123,
          courseId: 456,
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockCoursePackStore.init).not.toHaveBeenCalled()
      expect(mockCourseStore.init).not.toHaveBeenCalled()
    })

    it('应该处理缺少mediaUrl的课程', async () => {
      mockCourseStore.currentCourse = {
        id: 'course-1',
        title: 'Test Course',
        // 缺少 mediaUrl
        sentences: [],
      }
      
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 'pack-1',
          courseId: 'course-1',
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockPlayerStore.setMediaUrlWithProxy).toHaveBeenCalledWith(undefined)
      expect(mockSubtitleStore.setSubtitles).toHaveBeenCalled()
      expect(mockLrcStore.updateLrcContent).toHaveBeenCalled()
    })
  })

  // ============================================================================
  // 函数存在性测试
  // ============================================================================

  describe('函数存在性测试', () => {
    it('应该导出 useCourseDetail 函数', async () => {
      const { useCourseDetail } = await import('../useCourseDetail')
      expect(useCourseDetail).toBeDefined()
    })

    it('useCourseDetail 应该是函数类型', async () => {
      const { useCourseDetail } = await import('../useCourseDetail')
      expect(typeof useCourseDetail).toBe('function')
    })
  })

  // ============================================================================
  // 类型检查测试
  // ============================================================================

  describe('类型检查测试', () => {
    it('应该返回正确的接口', async () => {
      const { useCourseDetail } = await import('../useCourseDetail')
      const result = useCourseDetail()
      
      expect(result).toHaveProperty('loading')
      expect(result).toHaveProperty('error')
      expect(result).toHaveProperty('courseDetail')
      
      expect(typeof result.loading).toBe('object')
      expect(typeof result.error).toBe('object')
      expect(typeof result.courseDetail).toBe('object')
    })

    it('应该正确设置初始值', async () => {
      const { useCourseDetail } = await import('../useCourseDetail')
      const result = useCourseDetail()
      
      expect(result.loading.value).toBe(false)
      expect(result.error.value).toBe('')
      expect(result.courseDetail.value).toBeUndefined()
    })
  })

  // ============================================================================
  // 集成测试
  // ============================================================================

  describe('集成测试', () => {
    it('完整的初始化流程', async () => {
      mockCourseStore.currentCourse = {
        id: 'course-1',
        title: 'Test Course',
        mediaUrl: 'https://example.com/media.mp3',
        sentences: [
          {
            id: 'sent1',
            content: 'Hello world',
            chinese: '你好世界',
            startTime: 1,
            endTime: 3,
          },
          {
            id: 'sent2',
            content: 'How are you',
            chinese: '你好吗',
            startTime: 3,
            endTime: 5,
          },
        ],
      }
      
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 'pack-1',
          courseId: 'course-1',
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      // 验证完整流程
      expect(mockPlayerStore.clearMedia).toHaveBeenCalled()
      expect(mockCoursePackStore.init).toHaveBeenCalledWith('pack-1')
      expect(mockCourseStore.init).toHaveBeenCalledWith('pack-1', 'course-1')
      expect(mockPlayerStore.setMediaUrlWithProxy).toHaveBeenCalledWith('https://example.com/media.mp3')
      expect(mockSubtitleStore.setSubtitles).toHaveBeenCalledWith(
        expect.any(Array),
        false
      )
      expect(mockLrcStore.updateLrcContent).toHaveBeenCalledWith('', '[00:01.00]Hello')
    })

    it('错误恢复和清理', async () => {
      mockCoursePackStore.init.mockRejectedValue(new Error('Init failed'))
      
      mockUseRoute.mockReturnValue({
        query: {
          coursePackId: 'pack-1',
          courseId: 'course-1',
        },
      })
      
      const { useCourseDetail } = await import('../useCourseDetail')
      useCourseDetail()
      
      // 等待异步初始化完成
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(mockPlayerStore.clearMedia).toHaveBeenCalled()
      expect(mockCoursePackStore.init).toHaveBeenCalledWith('pack-1')
      // 即使出错，也应该进行了清理
    })
  })
})