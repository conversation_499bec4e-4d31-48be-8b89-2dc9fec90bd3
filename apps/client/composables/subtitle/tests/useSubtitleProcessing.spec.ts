import { describe, it, expect, vi } from 'vitest'
import type { Sentence } from '@julebu/shared'
import type { Subtitle } from '~/types/subtitle/subtitle'
import {
  reindexSubtitles,
  processSubtitleChanges,
  getUnprocessedSentences,
  useSubtitleTimeValidation,
} from '../useSubtitleProcessing'

// Mock dependencies
vi.mock('./useTimeUtils', () => ({
  parseSrtTime: vi.fn((time: string) => {
    if (time === '00:00:01,000') return 1
    if (time === '00:00:02,000') return 2
    if (time === '00:00:03,000') return 3
    if (time === '00:00:04,000') return 4
    if (time === '00:00:05,000') return 5
    if (time === 'invalid') return null
    return 0
  }),
}))

describe('useSubtitleProcessing', () => {
  // ============================================================================
  // 字幕基础处理函数测试
  // ============================================================================

  describe('reindexSubtitles', () => {
    it('应该重新编号字幕 id 字段', () => {
      const subtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 5,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
        },
        {
          uuid: 'uuid2',
          id: 10,
          startTime: '00:00:03,000',
          endTime: '00:00:04,000',
          text: 'World',
          translationText: '世界',
        },
      ]

      const result = reindexSubtitles(subtitles)

      expect(result).toHaveLength(2)
      expect(result[0].id).toBe(1)
      expect(result[1].id).toBe(2)
      expect(result[0].uuid).toBe('uuid1')
      expect(result[1].uuid).toBe('uuid2')
    })

    it('应该处理空数组', () => {
      const result = reindexSubtitles([])
      expect(result).toEqual([])
    })

    it('应该从 1 开始编号', () => {
      const subtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 100,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Test',
          translationText: '测试',
        },
      ]

      const result = reindexSubtitles(subtitles)

      expect(result[0].id).toBe(1)
    })
  })

  // ============================================================================
  // 字幕变更处理测试
  // ============================================================================

  describe('processSubtitleChanges', () => {
    it('应该识别新增的字幕', () => {
      const currentSubtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'New subtitle',
          translationText: '新字幕',
        },
        {
          uuid: 'uuid2',
          id: 2,
          startTime: '00:00:03,000',
          endTime: '00:00:04,000',
          text: 'Another new',
          translationText: '另一个新的',
          sentenceId: '', // 空 sentenceId 也算新增
        },
      ]

      const originalSubtitles: Subtitle[] = []

      const result = processSubtitleChanges(currentSubtitles, originalSubtitles)

      expect(result.newSubtitles).toHaveLength(2)
      expect(result.updatedSubtitles).toHaveLength(0)
      expect(result.unchangedSubtitles).toHaveLength(0)
      expect(result.deletedSentences).toHaveLength(0)
    })

    it('应该识别更新的字幕', () => {
      const currentSubtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Updated text',
          translationText: '更新的文本',
          sentenceId: 'sent1',
        },
        {
          uuid: 'uuid2',
          id: 2,
          startTime: '00:00:03,000',
          endTime: '00:00:04,000',
          text: 'Original text',
          translationText: '更新的翻译',
          sentenceId: 'sent2',
        },
      ]

      const originalSubtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Original text',
          translationText: '原始文本',
          sentenceId: 'sent1',
        },
        {
          uuid: 'uuid2',
          id: 2,
          startTime: '00:00:03,000',
          endTime: '00:00:04,000',
          text: 'Original text',
          translationText: '原始翻译',
          sentenceId: 'sent2',
        },
      ]

      const result = processSubtitleChanges(currentSubtitles, originalSubtitles)

      expect(result.newSubtitles).toHaveLength(0)
      expect(result.updatedSubtitles).toHaveLength(2)
      expect(result.unchangedSubtitles).toHaveLength(0)
      expect(result.deletedSentences).toHaveLength(0)
    })

    it('应该识别未变更的字幕', () => {
      const currentSubtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Same text',
          translationText: '相同文本',
          sentenceId: 'sent1',
        },
      ]

      const originalSubtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Same text',
          translationText: '相同文本',
          sentenceId: 'sent1',
        },
      ]

      const result = processSubtitleChanges(currentSubtitles, originalSubtitles)

      expect(result.newSubtitles).toHaveLength(0)
      expect(result.updatedSubtitles).toHaveLength(0)
      expect(result.unchangedSubtitles).toHaveLength(1)
      expect(result.deletedSentences).toHaveLength(0)
    })

    it('应该识别被删除的字幕', () => {
      const currentSubtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Remaining subtitle',
          translationText: '保留的字幕',
          sentenceId: 'sent1',
        },
      ]

      const originalSubtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Remaining subtitle',
          translationText: '保留的字幕',
          sentenceId: 'sent1',
        },
        {
          uuid: 'uuid2',
          id: 2,
          startTime: '00:00:03,000',
          endTime: '00:00:04,000',
          text: 'Deleted subtitle',
          translationText: '被删除的字幕',
          sentenceId: 'sent2',
        },
      ]

      const result = processSubtitleChanges(currentSubtitles, originalSubtitles)

      expect(result.newSubtitles).toHaveLength(0)
      expect(result.updatedSubtitles).toHaveLength(0)
      expect(result.unchangedSubtitles).toHaveLength(1)
      expect(result.deletedSentences).toHaveLength(1)
      expect(result.deletedSentences[0].sentenceId).toBe('sent2')
    })

    it('应该处理复杂的变更情况', () => {
      const currentSubtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Same text',
          translationText: '相同文本',
          sentenceId: 'sent1',
        },
        {
          uuid: 'uuid2',
          id: 2,
          startTime: '00:00:03,000',
          endTime: '00:00:04,000',
          text: 'Updated text',
          translationText: '更新的文本',
          sentenceId: 'sent2',
        },
        {
          uuid: 'uuid3',
          id: 3,
          startTime: '00:00:05,000',
          endTime: '00:00:06,000',
          text: 'New text',
          translationText: '新文本',
        },
      ]

      const originalSubtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Same text',
          translationText: '相同文本',
          sentenceId: 'sent1',
        },
        {
          uuid: 'uuid2',
          id: 2,
          startTime: '00:00:03,000',
          endTime: '00:00:04,000',
          text: 'Original text',
          translationText: '原始文本',
          sentenceId: 'sent2',
        },
        {
          uuid: 'uuid4',
          id: 4,
          startTime: '00:00:07,000',
          endTime: '00:00:08,000',
          text: 'Deleted text',
          translationText: '被删除的文本',
          sentenceId: 'sent3',
        },
      ]

      const result = processSubtitleChanges(currentSubtitles, originalSubtitles)

      expect(result.newSubtitles).toHaveLength(1)
      expect(result.updatedSubtitles).toHaveLength(1)
      expect(result.unchangedSubtitles).toHaveLength(1)
      expect(result.deletedSentences).toHaveLength(1)
    })

    it('应该处理有 sentenceId 但找不到原始记录的情况', () => {
      const currentSubtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'New with sentenceId',
          translationText: '有sentenceId的新字幕',
          sentenceId: 'sent1',
        },
      ]

      const originalSubtitles: Subtitle[] = []

      const result = processSubtitleChanges(currentSubtitles, originalSubtitles)

      expect(result.newSubtitles).toHaveLength(1)
      expect(result.updatedSubtitles).toHaveLength(0)
      expect(result.unchangedSubtitles).toHaveLength(0)
      expect(result.deletedSentences).toHaveLength(0)
    })

    it('应该忽略空的或无效的 sentenceId', () => {
      const currentSubtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Text 1',
          translationText: '文本1',
          sentenceId: '',
        },
        {
          uuid: 'uuid2',
          id: 2,
          startTime: '00:00:03,000',
          endTime: '00:00:04,000',
          text: 'Text 2',
          translationText: '文本2',
          sentenceId: '   ',
        },
      ]

      const originalSubtitles: Subtitle[] = [
        {
          uuid: 'uuid3',
          id: 3,
          startTime: '00:00:05,000',
          endTime: '00:00:06,000',
          text: 'Text 3',
          translationText: '文本3',
          sentenceId: '',
        },
      ]

      const result = processSubtitleChanges(currentSubtitles, originalSubtitles)

      expect(result.newSubtitles).toHaveLength(2)
      expect(result.deletedSentences).toHaveLength(0)
    })
  })

  // ============================================================================
  // 句子处理状态检查测试
  // ============================================================================

  describe('getUnprocessedSentences', () => {
    it('应该返回未加工的句子列表', () => {
      const subtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
          sentenceId: 'sent1',
        },
        {
          uuid: 'uuid2',
          id: 2,
          startTime: '00:00:03,000',
          endTime: '00:00:04,000',
          text: 'World',
          translationText: '世界',
          sentenceId: 'sent3',
        },
      ]

      const sentences: Sentence[] = [
        {
          id: 'sent1',
          content: 'Hello',
          chinese: '你好',
          elements: [
            {
              chinese: '你好',
              phonetic: '[həˈloʊ]',
              word: 'hello',
            },
          ],
        },
        {
          id: 'sent2',
          content: 'Test',
          chinese: '测试',
          elements: [], // 空元素数组，需要加工
        },
      ]

      const result = getUnprocessedSentences(subtitles, sentences)

      expect(result).toContain('sent3') // 字幕中有但句子中没有
      expect(result).toContain('sent2') // 句子中有但需要加工
    })

    it('应该识别需要加工的句子', () => {
      const subtitles: Subtitle[] = []

      const sentences: Sentence[] = [
        {
          id: 'sent1',
          content: 'Hello',
          chinese: '你好',
          elements: [
            {
              chinese: '你好',
              phonetic: '[həˈloʊ]',
              word: 'hello',
            },
          ],
        },
        {
          id: 'sent2',
          content: 'Test',
          chinese: '测试',
          elements: [], // 空元素数组，需要加工
        },
        {
          id: 'sent3',
          content: 'World',
          chinese: '世界',
          elements: [
            {
              chinese: '', // 缺少中文，需要加工
              phonetic: '[wɜːrld]',
              word: 'world',
            },
          ],
        },
        {
          id: 'sent4',
          content: 'Good',
          chinese: '好',
          elements: [
            {
              chinese: '好',
              phonetic: '', // 缺少音标，需要加工
              word: 'good',
            },
          ],
        },
      ]

      const result = getUnprocessedSentences(subtitles, sentences)

      expect(result).toContain('sent2')
      expect(result).toContain('sent3')
      expect(result).toContain('sent4')
      expect(result).not.toContain('sent1')
    })

    it('应该处理空句子对象', () => {
      const subtitles: Subtitle[] = []

      const sentences: Sentence[] = [
        {} as Sentence, // 空对象，需要加工
      ]

      const result = getUnprocessedSentences(subtitles, sentences)

      expect(result).toHaveLength(1)
    })

    it('应该处理空数组', () => {
      const result = getUnprocessedSentences([], [])
      expect(result).toEqual([])
    })

    it('应该去重结果', () => {
      const subtitles: Subtitle[] = [
        {
          uuid: 'uuid1',
          id: 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: 'Hello',
          translationText: '你好',
          sentenceId: 'sent1',
        },
      ]

      const sentences: Sentence[] = [
        {
          id: 'sent1',
          content: 'Hello',
          chinese: '你好',
          elements: [], // 需要加工
        },
      ]

      const result = getUnprocessedSentences(subtitles, sentences)

      expect(result).toEqual(['sent1'])
    })
  })

  // ============================================================================
  // 字幕时间验证测试
  // ============================================================================

  describe('useSubtitleTimeValidation', () => {
    it('应该存在 useSubtitleTimeValidation 函数', () => {
      // 由于 useSubtitleTimeValidation 使用 Vue 的 ref 和 computed，
      // 在单元测试环境中需要更复杂的设置，这里只验证函数存在
      expect(typeof useSubtitleTimeValidation).toBe('function')
    })
  })

  // ============================================================================
  // 边界情况和错误处理测试
  // ============================================================================

  describe('边界情况测试', () => {
    it('应该处理大量字幕的重新编号', () => {
      const subtitles: Subtitle[] = []
      for (let i = 0; i < 1000; i++) {
        subtitles.push({
          uuid: `uuid${i}`,
          id: Math.random() * 10000,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: `Text ${i}`,
          translationText: `翻译 ${i}`,
        })
      }

      const result = reindexSubtitles(subtitles)

      expect(result).toHaveLength(1000)
      expect(result[0].id).toBe(1)
      expect(result[999].id).toBe(1000)
    })

    it('应该处理复杂的字幕变更场景', () => {
      const currentSubtitles: Subtitle[] = []
      const originalSubtitles: Subtitle[] = []

      for (let i = 0; i < 100; i++) {
        originalSubtitles.push({
          uuid: `uuid${i}`,
          id: i + 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: `Original text ${i}`,
          translationText: `原始翻译 ${i}`,
          sentenceId: `sent${i}`,
        })
      }

      // 保留前 50 个，修改中间 30 个，删除后 20 个，新增 10 个
      for (let i = 0; i < 50; i++) {
        currentSubtitles.push({
          uuid: `uuid${i}`,
          id: i + 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: `Original text ${i}`,
          translationText: `原始翻译 ${i}`,
          sentenceId: `sent${i}`,
        })
      }

      for (let i = 50; i < 80; i++) {
        currentSubtitles.push({
          uuid: `uuid${i}`,
          id: i + 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: `Updated text ${i}`,
          translationText: `更新翻译 ${i}`,
          sentenceId: `sent${i}`,
        })
      }

      for (let i = 100; i < 110; i++) {
        currentSubtitles.push({
          uuid: `uuid${i}`,
          id: i + 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: `New text ${i}`,
          translationText: `新翻译 ${i}`,
        })
      }

      const result = processSubtitleChanges(currentSubtitles, originalSubtitles)

      expect(result.unchangedSubtitles).toHaveLength(50)
      expect(result.updatedSubtitles).toHaveLength(30)
      expect(result.deletedSentences).toHaveLength(20)
      expect(result.newSubtitles).toHaveLength(10)
    })

    it('应该处理深层嵌套的句子元素', () => {
      const subtitles: Subtitle[] = []

      const sentences: Sentence[] = [
        {
          id: 'sent1',
          content: 'Complex sentence',
          chinese: '复杂句子',
          elements: [
            {
              chinese: '复杂',
              phonetic: '[ˈkɒmpleks]',
              word: 'complex',
            },
            {
              chinese: '', // 缺少中文
              phonetic: '[ˈsentəns]',
              word: 'sentence',
            },
          ],
        },
      ]

      const result = getUnprocessedSentences(subtitles, sentences)

      expect(result).toContain('sent1')
    })
  })

  // ============================================================================
  // 性能测试
  // ============================================================================

  describe('性能测试', () => {
    it('应该快速处理大量字幕变更', () => {
      const currentSubtitles: Subtitle[] = []
      const originalSubtitles: Subtitle[] = []

      for (let i = 0; i < 1000; i++) {
        currentSubtitles.push({
          uuid: `uuid${i}`,
          id: i + 1,
          startTime: '00:00:01,000',
          endTime: '00:00:02,000',
          text: `Text ${i}`,
          translationText: `翻译 ${i}`,
          sentenceId: `sent${i}`,
        })
      }

      const start = Date.now()
      const result = processSubtitleChanges(currentSubtitles, originalSubtitles)
      const end = Date.now()

      expect(result.newSubtitles).toHaveLength(1000)
      expect(end - start).toBeLessThan(100) // 应该在 100ms 内完成
    })

    it('应该快速处理大量句子状态检查', () => {
      const subtitles: Subtitle[] = []
      const sentences: Sentence[] = []

      for (let i = 0; i < 1000; i++) {
        sentences.push({
          id: `sent${i}`,
          content: `Content ${i}`,
          chinese: `中文 ${i}`,
          elements: [],
        })
      }

      const start = Date.now()
      const result = getUnprocessedSentences(subtitles, sentences)
      const end = Date.now()

      expect(result).toHaveLength(1000)
      expect(end - start).toBeLessThan(50) // 应该在 50ms 内完成
    })
  })
})