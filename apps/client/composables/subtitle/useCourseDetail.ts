import type { CourseDetailDTO } from '@julebu/shared'
import { useRoute } from '#imports'
import { onMounted, ref } from 'vue'
import { useLrcStore } from '~/composables/subtitle/stores/lrcStore'
import { usePlayerStore } from '~/composables/subtitle/stores/playerStore'
import { useSubtitleStore } from '~/composables/subtitle/stores/subtitleStore'
import { useCourseStore } from '~/stores/course'
import { useCoursePackStore } from '~/stores/coursePack'
import { convertStatementsToSubtitles, subtitleToLrc } from './useFormatProcessing'

export function useCourseDetail() {
  const loading = ref(false)
  const error = ref('')
  const courseDetail = ref<CourseDetailDTO | undefined>(undefined)

  const route = useRoute()

  const coursePackId = typeof route.query.coursePackId === 'string' ? route.query.coursePackId : undefined
  const courseId = typeof route.query.courseId === 'string' ? route.query.courseId : undefined
  const playerStore = usePlayerStore()
  const lrcStore = useLrcStore()
  const subtitleStore = useSubtitleStore()
  const coursePackStore = useCoursePackStore()
  const courseStore = useCourseStore()

  // 字幕编辑器初始化
  async function initSubtitleEditor() {
    const currentCourse = courseStore.currentCourse
    if (!currentCourse) {
      return
    }
    // 将 mediaUrl 加载到播放器（使用代理方法避免 CORS 和强制下载问题）
    await playerStore.setMediaUrlWithProxy(currentCourse.mediaUrl)

    // 将课程语句转换并更新到 subtitleStore 和 lrcStore
    const subtitles = convertStatementsToSubtitles(currentCourse.sentences)
    // 不保留历史记录，因为这是初始化
    const preserveHistory = false
    subtitleStore.setSubtitles(subtitles, preserveHistory)
    const lrcContent = subtitleToLrc(subtitles)
    lrcStore.updateLrcContent('', lrcContent)
  }

  const initPage = async () => {
    loading.value = true

    if (coursePackId !== undefined && courseId !== undefined) {
      // 如果有缓存，直接返回
      if (courseStore.currentCourse?.id === courseId) {
        return
      }
      // 先清除缓存，再重新加载
      playerStore.clearMedia()
      try {
        await coursePackStore.init(coursePackId)
        await courseStore.init(coursePackId, courseId)
        await initSubtitleEditor()
      } finally {
        loading.value = false
      }
    }
  }

  onMounted(() => {
    void initPage()
  })

  return {
    loading,
    error,
    courseDetail,
  }
}
