import type { Region } from 'wavesurfer.js/dist/plugins/regions.esm.js'
import type { ApiConfig } from '~/composables/subtitle/useSpeechRecognition'
import type { Subtitle } from '~/types/subtitle/subtitle'
import { find, findIndex, forEach, sortBy } from 'lodash-es'
import WaveSurfer from 'wavesurfer.js'
import HoverPlugin from 'wavesurfer.js/dist/plugins/hover.esm.js'
import RegionsPlugin from 'wavesurfer.js/dist/plugins/regions.esm.js'
import TimelinePlugin from 'wavesurfer.js/dist/plugins/timeline.esm.js'
import { usePlayerStore } from '~/composables/subtitle/stores/playerStore'
import { useSubtitleStore } from '~/composables/subtitle/stores/subtitleStore'
import { formatTimeToString, parseSrtTime } from './useTimeUtils'

// ============================================================================
// 语音识别配置
// ============================================================================

export function useSpeechRecognitionConfig() {
  // API 配置
  const apiConfig = reactive<ApiConfig>({
    appid: '9528098044',
    token: '-igcmNSCpyAXDFtrAF_YDcdQp6E0hYac',
  })

  // 响应式数据
  const selectedLanguage = ref('en-US')
  const selectedContentType = ref('auto')

  // 特色功能配置
  const features = reactive({
    usePunc: false, // 增加标点
    useDdc: false, // 水词标注
    withSpeakerInfo: false, // 说话人识别
    useItn: false, // 数字转换
  })

  const maxCharsPerLine = ref(46) // 每行最多显示字数
  const maxLines = ref(1) // 每屏最多显示行数

  // 语言选项（根据火山引擎API文档）
  const languageOptions = [
    { value: 'zh-CN', label: '中文普通话（支持中英混合及方言）' },
    { value: 'en-US', label: '英语（美国）' },
  ]

  // 字幕内容类型选项
  const contentTypeOptions = [
    { value: 'auto', label: '自动判断' },
    { value: 'speech', label: '语音' },
    { value: 'singing', label: '歌词' },
  ]

  // 推荐字符数映射
  const recommendedCharsMap: Record<string, number> = {
    'zh-CN': 15,
    'ug': 55,
    'en-US': 55,
  }

  // 方法
  const getRecommendedCharsPerLine = () => {
    return recommendedCharsMap[selectedLanguage.value] || 46
  }

  const getLanguageLabel = (code: string) => {
    return languageOptions.find(opt => opt.value === code)?.label || code
  }

  const getContentTypeLabel = (type: string) => {
    return contentTypeOptions.find(opt => opt.value === type)?.label || type
  }

  // 监听语言变化，更新推荐字符数
  watch(selectedLanguage, () => {
    maxCharsPerLine.value = getRecommendedCharsPerLine()
  })

  return {
    // 响应式状态
    apiConfig,
    selectedLanguage,
    selectedContentType,
    features,
    maxCharsPerLine,
    maxLines,

    // 选项
    languageOptions,
    contentTypeOptions,

    // 方法
    getRecommendedCharsPerLine,
    getLanguageLabel,
    getContentTypeLabel,
  }
}

// ============================================================================
// WaveSurfer 播放器同步
// ============================================================================

/**
 * 用于同步 WaveSurfer 实例与 playerStore 状态的 composable。
 * @param {unknown} wavesurfer - WaveSurfer 实例。
 * @param {import('vue').Ref<string>} status - WaveSurfer 状态。
 * @param {Function} play - WaveSurfer 播放方法。
 * @param {Function} pause - WaveSurfer 暂停方法。
 * @param {Function} setPlaybackRate - WaveSurfer 设置播放速率方法。
 */
export function useWaveformPlayerSync(
  wavesurfer: Ref<WaveSurfer | null>,
  status: { value: string },
  play: () => void,
  pause: () => void,
  setPlaybackRate: (rate: number) => void,
) {
  const playerStore = usePlayerStore()

  /**
   * 监听 playerStore.isPlaying，同步控制 WaveSurfer 播放/暂停。
   */
  watch(
    () => playerStore.isPlaying.value,
    (newIsPlaying) => {
      if (status.value !== 'ready')
        return
      if (newIsPlaying) {
        play()
      } else {
        pause()
      }
    },
  )

  /**
   * 监听 playerStore.playbackRate，同步设置 WaveSurfer 播放速率。
   */
  watch(
    () => playerStore.playbackRate.value,
    (newRate) => {
      if (status.value === 'ready' && wavesurfer.value) {
        setPlaybackRate(newRate)
      }
    },
  )

  /**
   * 监听 playerStore.seekRequest，主动同步到 WaveSurfer。
   * 目的是为了当点击进度条的时候同步 wavesurfer 的进度，但是这个实现不好，因为当点击进度条的时候，会触发两次，导致进度条闪烁
   * TODO: 在点击进度条的地方，更新进度条，然后更新 wavesurfer 的进度
   */
  watch(
    () => playerStore.seekRequest.value,
    (newReq, oldReq) => {
      if (
        status.value === 'ready'
        && wavesurfer.value
        && typeof wavesurfer.value.setTime === 'function'
        && newReq
        && (!oldReq || newReq.key !== oldReq.key)
      ) {
        wavesurfer.value.setTime(newReq.time)
      }
    },
  )
}

// ============================================================================
// WaveSurfer Regions 管理
// ============================================================================

const REGION_COLOR = 'rgba(99, 102, 241, 0.22)' // 蓝紫色，提升透明度
const HANDLE_COLOR = '#a78bfa' // 浅紫色，和波形进度色一致

/**
 * 用于管理 WaveSurfer Regions（字幕区域）的 composable。
 * @param {unknown} regionsPluginInstance - WaveSurfer 的 regions 插件实例。
 * @returns {object} - 包含注册/注销事件和 region 同步的相关方法。
 */
export function useWaveformRegions(regionsPluginInstance: unknown) {
  const playerStore = usePlayerStore()
  const subtitleStore = useSubtitleStore()
  const subtitleRegionMap = ref(new Map())

  /**
   * 生成 Region 显示内容的 DOM 元素，包含起止时间和字幕文本。
   * @param {Subtitle} sub - 字幕对象。
   * @param {number} [index] - 字幕序号，用于显示在区域中。
   * @returns {HTMLElement} - 用于显示的 DOM 元素。
   */
  function getRegionContentElement(sub: Subtitle, index?: number): HTMLElement {
    const { startTime, endTime, text, translationText } = sub

    // 创建内容元素
    const content = document.createElement('div')
    content.style.cssText
      = 'position:relative;display:flex;flex-direction:column;height:80%;justify-content:center;padding:0.22rem 0.48rem;box-sizing:border-radius:0.36rem;border-bottom:2px solid #a78bfa;background:rgba(99,102,241,0.16);box-shadow:0 2px 8px 0 rgba(99,102,241,0.10);min-width:0;pointer-events:auto;transition:border 0.2s;'

    // 添加序号徽章（如果提供了index），定位到左上角
    if (typeof index === 'number') {
      const sequenceNumber = document.createElement('span')
      sequenceNumber.style.cssText = `
        position: absolute;
        top: 4px;
        left: 4px;
        z-index: 10;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 20px;
        border-radius: 4px;
        color: rgba(255, 255, 255, 0.85);
        font-weight: bold;
        font-size: 0.7rem;
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.3);
      `
      sequenceNumber.textContent = index.toString()

      content.appendChild(sequenceNumber)
    }

    // 时间戳
    const header = document.createElement('div')
    header.style.cssText
      = 'display:flex;justify-content:space-between;width:100%;font-family:inherit;font-size:0.72rem;color:rgba(255,255,255,0.82);letter-spacing:0.01em;opacity:0.96;margin-top:0.08rem;margin-bottom:0.14rem;filter:drop-shadow(0 0 1px #000);border-radius:0.18rem;align-items:center;padding-left: 34px; box-sizing: border-box;'
    header.innerHTML = `<em style="font-style:normal;">${startTime}</em><span style="flex:1"></span><em style="font-style:normal;margin-left: 12px;">${endTime}</em>`

    // 文本
    const body = document.createElement('div')
    body.style.cssText
      = 'font-family:inherit;font-size:1.04rem;font-weight:700;color:#f3f4f6;overflow:hidden;white-space:normal;line-height:1.48;letter-spacing:0.01em;background:transparent;border-radius:0;padding:0;filter:drop-shadow(0 0 2px #000);margin-top:1rem;max-width:100%;flex:1 1 auto;word-break:break-all;display:block;'
    body.textContent = `${text}`

    content.appendChild(header)
    content.appendChild(body)

    // 翻译文本（如果有）
    if (translationText) {
      const translation = document.createElement('div')
      translation.style.cssText
        = 'font-family:inherit;font-size:0.68rem;font-weight:400;color:#e0e7ef;opacity:0.85;overflow:hidden;white-space:normal;line-height:1.28;letter-spacing:0.01em;background:transparent;border-radius:0;padding:0;margin-top:0.18rem;max-width:100%;flex:0 0 auto;word-break:break-all;display:block;'
      translation.textContent = `${translationText}`
      body.appendChild(translation)
    }
    return content
  }

  /**
   * 自定义 Region 拖拽手柄样式（虚线、宽度、透明背景）。
   * @param {unknown} region - 需要设置手柄样式的 Region 实例。
   */
  function styleRegionHandles(region: unknown) {
    const r = region as { element: HTMLElement }
    const leftHandle = r.element.querySelector('div[part="region-handle region-handle-left"]')
    if (leftHandle) {
      ;(leftHandle as HTMLElement).style.cssText += `border-left:2px dashed ${HANDLE_COLOR};width:4px;background:transparent;`
    }
    const rightHandle = r.element.querySelector('div[part="region-handle region-handle-right"]')
    if (rightHandle) {
      ;(rightHandle as HTMLElement).style.cssText += `border-right:2px dashed ${HANDLE_COLOR};width:4px;background:transparent;`
    }
  }

  /**
   * region-updated 事件监听器：自动吸附与自动合并（防止错位）逻辑。
   * @param {unknown} region - 被更新的 Region 实例。
   */
  const handleRegionUpdate = (region: unknown) => {
    // 类型断言
    const r = region as {
      id: string
      start: number
      end: number
      setContent: (c: HTMLElement) => void
      setOptions: (opts: { start?: number, end?: number, content?: HTMLElement }) => void
    }
    const subtitleUuid = r.id
    let newStartTime = r.start
    let newEndTime = r.end
    let adjusted = false

    // 找到对应字幕对象
    const currentSub = find(subtitleStore.subtitles.value, s => s.uuid === subtitleUuid)
    if (!currentSub)
      return

    // 获取所有 region，按 start 时间排序
    const allRegions = Array.from(subtitleRegionMap.value.values())
    const sortedRegions = sortBy(allRegions, a => a.start)
    const currentIndex = findIndex(sortedRegions, reg => reg.id === subtitleUuid)
    const prevRegion = currentIndex > 0 ? sortedRegions[currentIndex - 1] : null
    const nextRegion = currentIndex < sortedRegions.length - 1 ? sortedRegions[currentIndex + 1] : null

    // 检查是否完全跨越前/后 region（自动合并/防止错位）
    if ((prevRegion && newEndTime <= prevRegion.start) || (nextRegion && newStartTime >= nextRegion.end)) {
      // 恢复到原始位置
      const originalStart = parseSrtTime(currentSub.startTime)
      const originalEnd = parseSrtTime(currentSub.endTime)
      if (originalStart == null || originalEnd == null)
        return
      r.setOptions({ start: originalStart, end: originalEnd })
      r.setOptions({
        content: getRegionContentElement(currentSub),
      })
      styleRegionHandles(r)
      return
    }

    // 自动吸附：防止与前/后 region 部分重叠
    if (prevRegion && newStartTime < prevRegion.end) {
      adjusted = true
      newStartTime = prevRegion.end
      if (newStartTime >= newEndTime) {
        newEndTime = newStartTime + 0.1
      }
    }
    if (nextRegion && newEndTime > nextRegion.start) {
      adjusted = true
      newEndTime = nextRegion.start
      if (newEndTime <= newStartTime) {
        newStartTime = newEndTime - 0.1
      }
    }

    if (adjusted) {
      r.setOptions({ start: newStartTime, end: newEndTime })
    }

    // 格式化时间
    const newStartTimeFormatted = formatTimeToString(newStartTime)
    const newEndTimeFormatted = formatTimeToString(newEndTime)

    // 查找当前字幕在数组中的索引
    const subtitleIndex = findIndex(subtitleStore.subtitles.value, s => s.uuid === subtitleUuid)
    const sequenceNumber = subtitleIndex >= 0 ? subtitleIndex + 1 : undefined

    // 刷新 region 内容
    r.setOptions({
      content: getRegionContentElement({
        ...currentSub,
        startTime: newStartTimeFormatted,
        endTime: newEndTimeFormatted,
      }, sequenceNumber),
    })
    styleRegionHandles(r)

    // 更新字幕 store
    if (currentSub.startTime !== newStartTimeFormatted || currentSub.endTime !== newEndTimeFormatted) {
      subtitleStore.updateSubtitleStartTime(r.id, newStartTimeFormatted)
      subtitleStore.updateSubtitleEndTime(r.id, newEndTimeFormatted)
    }

    // 如果当前字幕正在循环播放，更新循环时间
    if (playerStore.loopingSubtitleUuid.value === currentSub.uuid) {
      playerStore.updateLoopTime(newStartTime, newEndTime)
    }
  }

  /**
   * region-clicked 事件监听器：点击 region 时跳转到对应媒体时间。
   * @param {unknown} region - 被点击的 Region 实例。
   * @param {MouseEvent} e - 鼠标事件对象。
   */
  const regionClickedListener = (region: unknown, e: MouseEvent) => {
    const r = region as { start: number }
    e.stopPropagation()
    playerStore.setSeekRequest(r.start)
  }

  /**
   * 注册 region 相关事件（region-updated、region-clicked）。
   */
  function registerRegionEvents() {
    const plugin = regionsPluginInstance as { on: (...args: unknown[]) => unknown } | undefined
    if (plugin) {
      plugin.on('region-updated', handleRegionUpdate)
      plugin.on('region-clicked', regionClickedListener)
    }
  }

  /**
   * 注销 region 相关事件（region-updated、region-clicked）。
   */
  function unregisterRegionEvents() {
    const plugin = regionsPluginInstance as { un: (...args: unknown[]) => unknown } | undefined
    if (plugin) {
      plugin.un('region-updated', handleRegionUpdate)
      plugin.un('region-clicked', regionClickedListener)
    }
  }

  /**
   * 清空所有现有区域
   * @param {unknown} plugin - regions 插件实例
   */
  function clearAllRegions(plugin: { clearRegions: (...args: unknown[]) => unknown, getRegions?: () => Array<{ remove: () => void }> }) {
    // 先逐个移除所有region
    if (typeof plugin.getRegions === 'function') {
      try {
        const regions = plugin.getRegions() as Array<{ remove: () => void }>
        forEach(regions, (region) => {
          if (region && typeof region.remove === 'function') {
            region.remove()
          }
        })
      } catch (e) {
        console.log('%c AT 🥝 e 🥝-243', 'font-size:13px; background:#b58127; color:#f9c56b;', e)
      }
    }
    plugin.clearRegions()
    subtitleRegionMap.value.clear()
  }

  /**
   * 同步字幕数据到 regions，自动清空并重建所有 region。
   * @param {unknown} wavesurfer - WaveSurfer 实例。
   * @param {string} status - WaveSurfer 状态。
   */
  function syncRegions(wavesurfer: unknown, status: string) {
    const ws = wavesurfer as { [key: string]: unknown } | undefined
    if (!ws || status !== 'ready')
      return
    const plugin = regionsPluginInstance as
      | {
        clearRegions: (...args: unknown[]) => unknown
        addRegion: (...args: unknown[]) => unknown
        getRegions?: () => Array<{ remove: () => void }>
      }
      | undefined
    if (!plugin)
      return

    // 清空所有区域
    clearAllRegions(plugin)

    forEach(subtitleStore.subtitles.value, (sub, index) => {
      const start = parseSrtTime(sub.startTime)
      const end = parseSrtTime(sub.endTime)
      if (start === null || end === null || start >= end)
        return
      try {
        const region = plugin.addRegion({
          id: sub.uuid,
          start,
          end,
          content: getRegionContentElement(sub, index + 1),
          color: REGION_COLOR,
          drag: true,
          resize: true,
          minLength: 0.1,
        })
        subtitleRegionMap.value.set(sub.uuid, region)
        styleRegionHandles(region)
      } catch (err) {
        console.log('%c AT 🥝 err 🥝-267', 'font-size:13px; background:#b58127; color:#f9c56b;', err)
        // 可根据需要添加错误处理
      }
    })
  }

  return {
    subtitleRegionMap,
    registerRegionEvents,
    unregisterRegionEvents,
    syncRegions,
  }
}

// ============================================================================
// WaveSurfer 主要功能
// ============================================================================

interface WaveSurferOptions {
  container: HTMLElement
  timelineContainer?: HTMLElement
  onSeek?: (time: number) => void
  onError?: (error: Error) => void
  onReady?: () => void
  onDurationAvailable?: (duration: number) => void
  playbackRate?: number
}

type WaveSurferStatus = 'idle' | 'initializing' | 'loading' | 'ready' | 'error'

export function useWaveSurfer() {
  const wavesurfer = ref<WaveSurfer | null>(null)
  const status = ref<WaveSurferStatus>('idle')
  const error = ref<Error | null>(null)
  const currentBlobUrl = ref<string | null>(null)
  const playerStore = usePlayerStore()
  const loopCheckInterval = ref<number | null>(null)

  // 修补了 RegionPlug 以避免重叠区域的错误
  const regionsPluginInstance = RegionsPlugin.create()

  // @ts-expect-error 覆盖 avoidOverlapping 方法，此方法为 RegionPlugin 中的私有方法
  regionsPluginInstance.avoidOverlapping = (_region: Region) => {
    // 什么都不做
  }

  // 处理循环播放逻辑
  const checkLoopPlayback = (currentTime?: number) => {
    if (!wavesurfer.value || !playerStore.isLooping.value || playerStore.loopEndTime.value === null) {
      return
    }
    // 优先使用 audioprocess 事件传入的 currentTime，兼容原有定时器调用
    const time = typeof currentTime === 'number' ? currentTime : wavesurfer.value.getCurrentTime()
    if (time >= playerStore.loopEndTime.value) {
      // 如果到达循环结束时间，跳回循环开始时间
      if (playerStore.loopStartTime.value !== null) {
        wavesurfer.value.setTime(playerStore.loopStartTime.value)
        playerStore.setSeekRequest(playerStore.loopStartTime.value)
      }
    }
  }

  // 处理练习点播放逻辑
  const checkPracticePlayback = (currentTime?: number) => {
    if (!wavesurfer.value || !playerStore.isPracticePlaying.value || playerStore.practiceEndTime.value === null) {
      return
    }
    // 优先使用 audioprocess 事件传入的 currentTime，兼容原有定时器调用
    const time = typeof currentTime === 'number' ? currentTime : wavesurfer.value.getCurrentTime()
    if (time >= playerStore.practiceEndTime.value) {
      // 如果到达练习结束时间，停止播放
      playerStore.stopPracticePlay()
    }
  }

  // 停止循环检查
  const stopLoopCheck = () => {
    if (loopCheckInterval.value !== null) {
      clearInterval(loopCheckInterval.value)
      loopCheckInterval.value = null
    }
  }

  // 开始循环检查
  const startLoopCheck = () => {
    stopLoopCheck() // 先停止之前的检查
    // 缩短检测间隔为 30ms，提高精度
    loopCheckInterval.value = window.setInterval(() => checkLoopPlayback(), 30)
  }

  const play = () => {
    if (wavesurfer.value && status.value === 'ready') {
      void wavesurfer.value.play()
      playerStore.updatePlayIngStatus()
    } else {
      console.warn(`无法播放: WaveSurfer 状态为 ${status.value}`)
    }
  }

  const pause = () => {
    if (wavesurfer.value && status.value === 'ready') {
      wavesurfer.value.pause()
      playerStore.updatePauseStatus()
    } else {
      console.warn(`无法暂停: WaveSurfer 状态为 ${status.value}`)
    }
  }

  const setPlaybackRate = (rate: number) => {
    if (wavesurfer.value && status.value === 'ready') {
      wavesurfer.value.setPlaybackRate(rate)
    }
  }

  const initialize = async (options: WaveSurferOptions) => {
    status.value = 'initializing'
    error.value = null

    try {
      // 销毁旧实例
      if (wavesurfer.value) {
        wavesurfer.value.destroy()
        wavesurfer.value = null
      }

      // 释放旧的 Blob URL
      if (currentBlobUrl.value !== null && currentBlobUrl.value !== '') {
        URL.revokeObjectURL(currentBlobUrl.value)
        currentBlobUrl.value = null
      }

      // 创建新实例
      wavesurfer.value = WaveSurfer.create({
        container: options.container, // 波形图的 HTML 容器元素
        width: '100%', // 波形图的宽度，'100%' 表示占满容器
        waveColor: '#6366f1', // 未播放部分的波形颜色（蓝紫色）
        progressColor: '#a78bfa', // 已播放部分的波形颜色（浅紫色）
        cursorColor: '#f472b6', // 播放指示光标的颜色（粉紫色）
        normalize: true, // 是否对音频数据进行标准化处理 (使波形高度更一致)
        minPxPerSec: 100, // 每秒音频至少显示的像素宽度 (控制缩放级别)
        fillParent: true, // 波形图是否填充其父容器的高度
        backend: 'MediaElement', // 使用的音频处理后端 ('MediaElement' 使用 HTML5 Audio/Video 元素)
        hideScrollbar: false, // 当波形宽度超过容器时是否隐藏滚动条

        height: 'auto', // 波形图的高度 (像素)
        autoCenter: true, // 播放时是否自动将当前播放位置居中显示
        interact: true, // 是否允许用户通过点击或拖动与波形图交互
        plugins: [
          options.timelineContainer
          && TimelinePlugin.create({
            timeInterval: 0.1, // 时间轴刻度标记的最小间隔 (秒)
            primaryLabelInterval: 1, // 主要时间标签的间隔 (秒)
            style: {
              fontSize: '12px', // 时间轴标签的字体大小
            },
          }),
          HoverPlugin.create({
            lineColor: '#a78bfa', // 鼠标悬停时指示线的颜色（与主色调协调的蓝紫色）
            lineWidth: 2, // 鼠标悬停时指示线的宽度
            labelBackground: '#555', // 鼠标悬停时时间标签的背景色
            labelColor: '#fff', // 鼠标悬停时时间标签的文字颜色
            labelSize: '12px', // 鼠标悬停时时间标签的文字大小
            formatTimeCallback: formatTimeToString,
          }),
          regionsPluginInstance,
        ].filter(Boolean) as unknown as never[],
      })

      wavesurfer.value.setVolume(0)

      // 设置初始播放速度
      if (options.playbackRate !== undefined) {
        wavesurfer.value.setPlaybackRate(options.playbackRate)
      }

      // 在成功创建实例后调用 useWaveformPlayerSync
      useWaveformPlayerSync(wavesurfer as Ref<WaveSurfer | null>, status, play, pause, setPlaybackRate)

      // 事件监听
      wavesurfer.value.on('ready', () => {
        status.value = 'ready'

        // 在 ready 事件中释放 Blob URL
        if (currentBlobUrl.value !== null && currentBlobUrl.value !== '') {
          URL.revokeObjectURL(currentBlobUrl.value)
          currentBlobUrl.value = null
        }

        const duration = wavesurfer.value?.getDuration() ?? 0
        options.onDurationAvailable?.(duration)
        options.onReady?.()
      })

      wavesurfer.value.on('error', (err: Error) => {
        console.error('WaveSurfer 发出 "error" 事件:', err)
        error.value = err
        status.value = 'error'
        options.onError?.(err)
      })

      // 播放结束事件监听，自动复位到开始
      wavesurfer.value.on('finish', () => {
        playerStore.updatePauseStatus()
      })

      // 监听播放状态变化
      wavesurfer.value.on('play', () => {
        if (playerStore.isLooping.value) {
          startLoopCheck()
        }
      })

      wavesurfer.value.on('pause', () => {
        stopLoopCheck()
      })

      // 只用于更新时间和循环检测
      wavesurfer.value.on('audioprocess', (currentTime) => {
        options.onSeek?.(currentTime)
        if (playerStore.isLooping.value && playerStore.loopEndTime.value !== null) {
          checkLoopPlayback(currentTime)
        }
        if (playerStore.isPracticePlaying.value && playerStore.practiceEndTime.value !== null) {
          checkPracticePlayback(currentTime)
        }
      })

      // 监听用户交互事件
      wavesurfer.value.on('interaction', (time) => {
        playerStore.setSeekRequest(time)
      })

      // 监听循环状态变化
      watch(
        () => playerStore.isLooping.value,
        (newIsLooping) => {
          if (newIsLooping && wavesurfer.value?.isPlaying()) {
            startLoopCheck()
          } else {
            stopLoopCheck()
          }
        },
      )
    } catch (err) {
      console.error('WaveSurfer 初始化期间出错:', err)
      error.value = err as Error
      status.value = 'error'
      options.onError?.(err as Error)
    }
  }

  const loadAudio = async (file: File) => {
    if (!file) {
      return
    }

    if (!wavesurfer.value) {
      const err = new Error('WaveSurfer not initialized before loadAudio')
      error.value = err
      status.value = 'error'
      throw err
    }

    try {
      status.value = 'loading'
      error.value = null

      // 释放旧的 Blob URL
      if (currentBlobUrl.value !== null && currentBlobUrl.value !== '') {
        URL.revokeObjectURL(currentBlobUrl.value)
      }

      const url = URL.createObjectURL(file)
      currentBlobUrl.value = url

      await wavesurfer.value.load(url)
    } catch (err) {
      // 如果加载失败，立即释放 Blob URL
      if (currentBlobUrl.value !== null && currentBlobUrl.value !== '') {
        URL.revokeObjectURL(currentBlobUrl.value)
        currentBlobUrl.value = null
      }
      error.value = err as Error
      status.value = 'error'
      throw err
    }
  }

  const destroy = () => {
    if (wavesurfer.value) {
      wavesurfer.value.destroy()
      wavesurfer.value = null
    }

    // 释放当前的 Blob URL
    if (currentBlobUrl.value !== null && currentBlobUrl.value !== '') {
      URL.revokeObjectURL(currentBlobUrl.value)
      currentBlobUrl.value = null
    }

    status.value = 'idle'
    error.value = null
  }

  const togglePlay = () => {
    if (wavesurfer.value && status.value === 'ready') {
      void wavesurfer.value.playPause()
    } else {
      console.warn(`无法切换播放/暂停: WaveSurfer 状态为 ${status.value}`)
    }
  }

  onBeforeUnmount(() => {
    stopLoopCheck()
    destroy()
  })

  return {
    wavesurfer,
    status,
    error,
    initialize,
    loadAudio,
    destroy,
    togglePlay,
    play,
    pause,
    setPlaybackRate,
    regionsPluginInstance,
  }
}
