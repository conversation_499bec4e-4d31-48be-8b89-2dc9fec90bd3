import type { Subtitle } from '~/types/subtitle/subtitle'
import { parseSrtTime, validateSrtTimeFormat } from './useTimeUtils'

// ============================================================================
// 格式验证函数
// ============================================================================

/**
 * 验证SRT时间格式
 */
export function isSrtTimeFormat(time: string): boolean {
  const srtTimeRegex = /^\d{2}:\d{2}:\d{2},\d{3}$/
  if (!srtTimeRegex.test(time)) {
    return false
  }

  // 验证时间逻辑有效性
  const parts = time.split(/[:,]/)
  const hours = Number.parseInt(parts[0], 10)
  const minutes = Number.parseInt(parts[1], 10)
  const seconds = Number.parseInt(parts[2], 10)
  const milliseconds = Number.parseInt(parts[3], 10)

  return hours <= 23 && minutes <= 59 && seconds <= 59 && milliseconds <= 999
}

/**
 * 验证VTT时间格式
 */
export function isVttTimeFormat(time: string): boolean {
  const vttTimeRegex = /^\d{1,2}:\d{2}:\d{2}\.\d{3}$/
  if (!vttTimeRegex.test(time)) {
    return false
  }

  // 验证时间逻辑有效性（VTT允许小时超过23）
  const parts = time.split(/[:.]/)
  const minutes = Number.parseInt(parts[1], 10)
  const seconds = Number.parseInt(parts[2], 10)
  const milliseconds = Number.parseInt(parts[3], 10)

  return minutes <= 59 && seconds <= 59 && milliseconds <= 999
}

/**
 * 验证LRC时间格式
 */
export function isLrcTimeFormat(time: string): boolean {
  const lrcTimeRegex = /^\[\d{2}:\d{2}\.\d{2}\]$/
  if (!lrcTimeRegex.test(time)) {
    return false
  }

  // 验证时间逻辑有效性
  const content = time.slice(1, -1) // 移除方括号
  const parts = content.split(/[:.]/)
  const minutes = Number.parseInt(parts[0], 10)
  const seconds = Number.parseInt(parts[1], 10)
  const centiseconds = Number.parseInt(parts[2], 10)

  return minutes <= 59 && seconds <= 59 && centiseconds <= 99
}

/**
 * 验证文件MIME类型
 */
export function isValidMimeType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type)
}

/**
 * 验证音频文件格式
 */
export function isAudioFile(filename: string): boolean {
  const audioExtensions = ['.mp3', '.wav', '.m4a', '.ogg', '.flac', '.aac']
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))
  return audioExtensions.includes(extension)
}

/**
 * 验证视频文件格式
 */
export function isVideoFile(filename: string): boolean {
  const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.wmv']
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))
  return videoExtensions.includes(extension)
}

/**
 * 验证字幕文件格式
 */
export function isSubtitleFile(filename: string): boolean {
  const subtitleExtensions = ['.srt', '.vtt', '.lrc', '.ass', '.ssa']
  const extension = filename.toLowerCase().substring(filename.lastIndexOf('.'))
  return subtitleExtensions.includes(extension)
}

/**
 * 验证媒体文件格式
 */
export function isMediaFile(filename: string): boolean {
  return isAudioFile(filename) || isVideoFile(filename)
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(filename: string): string {
  return filename.toLowerCase().substring(filename.lastIndexOf('.'))
}

/**
 * 验证十六进制颜色代码
 */
export function isValidHexColor(color: string): boolean {
  const hexColorRegex = /^#(?:[A-F0-9]{6}|[A-F0-9]{3})$/i
  return hexColorRegex.test(color)
}

/**
 * 验证数字字符串
 */
export function isNumericString(value: string): boolean {
  if (value.trim() !== value)
    return false // 拒绝前后有空格
  if (value === '' || value === 'Infinity' || value === '-Infinity' || value === 'NaN')
    return false
  return !Number.isNaN(Number(value)) && !Number.isNaN(Number.parseFloat(value))
}

/**
 * 验证正整数字符串
 */
export function isPositiveIntegerString(value: string): boolean {
  if (value.trim() !== value)
    return false // 拒绝前后有空格
  if (value.startsWith('+'))
    return false // 拒绝正号
  if (value.includes('.'))
    return false // 拒绝小数点
  if (value.includes('e') || value.includes('E'))
    return false // 拒绝科学记数法
  const num = Number(value)
  return Number.isInteger(num) && num > 0
}

/**
 * 验证文件大小
 */
export function isValidFileSize(file: File, maxSizeMB: number): boolean {
  const maxSizeBytes = maxSizeMB * 1024 * 1024
  return file.size <= maxSizeBytes
}

/**
 * 验证URL格式
 */
export function isValidUrl(url: string): boolean {
  try {
    void new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 验证电子邮箱格式
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@][^\s.@]*\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证字符串长度范围
 */
export function isValidLength(str: string, min: number, max: number): boolean {
  return str.length >= min && str.length <= max
}

/**
 * 验证字符串是否只包含允许的字符
 */
export function hasOnlyAllowedChars(str: string, allowedCharsRegex: RegExp): boolean {
  return allowedCharsRegex.test(str)
}

/**
 * 验证时间戳范围（毫秒）
 */
export function isValidTimestamp(timestamp: number, maxDuration?: number): boolean {
  if (timestamp < 0)
    return false
  if (maxDuration !== undefined && timestamp > maxDuration)
    return false
  return true
}

/**
 * 验证时间戳字符串（支持多种格式）
 */
export function isValidTimeString(timeStr: string): boolean {
  // SRT格式：HH:MM:SS,mmm
  if (isSrtTimeFormat(timeStr))
    return true

  // VTT格式：HH:MM:SS.mmm
  if (isVttTimeFormat(timeStr))
    return true

  // LRC格式：[MM:SS.cc]
  if (isLrcTimeFormat(timeStr))
    return true

  // 简单格式：MM:SS 或 HH:MM:SS
  if (/^\d{1,2}:\d{2}(?::\d{2})?$/.test(timeStr))
    return true

  // 秒数格式：123.456
  if (/^\d+(?:\.\d+)?$/.test(timeStr))
    return true

  return false
}

// ============================================================================
// 字幕验证函数
// ============================================================================

/**
 * 时间错误信息接口
 */
export interface TimeError {
  index: number
  subtitle: Subtitle
  errorType: string
}

/**
 * 内容错误信息接口
 */
export interface ContentError {
  index: number
  subtitle: Subtitle
}

/**
 * 字幕验证结果接口
 */
export interface SubtitleValidationResult {
  isValid: boolean
  timeErrors: TimeError[]
  errorMessages: string[]
}

/**
 * 内容验证结果接口
 */
export interface ContentValidationResult {
  isValid: boolean
  contentErrors: ContentError[]
  errorMessages: string[]
}

/**
 * 完整验证结果接口
 */
export interface CompleteValidationResult {
  isValid: boolean
  hasEmptyContent: boolean
  hasTimeErrors: boolean
  hasNoMediaFile: boolean
  timeErrors: TimeError[]
  contentErrors: ContentError[]
  errorMessage?: string
}

/**
 * 检测字幕列表中是否存在时间问题
 * @param subtitles 字幕列表
 * @param maxDuration 音频总时长（毫秒），可选
 * @returns 包含错误信息的验证结果
 */
export function validateSubtitleTiming(subtitles: Subtitle[], maxDuration?: number): SubtitleValidationResult {
  const timeErrors: TimeError[] = []

  for (let i = 0; i < subtitles.length; i++) {
    const current = subtitles[i]
    // 具体分析错误类型，按优先级判断
    let errorType = ''

    // 1. 首先进行严格的时间格式验证
    if (!validateSrtTimeFormat(current.startTime)) {
      errorType = '开始时间格式错误'
    } else if (!validateSrtTimeFormat(current.endTime)) {
      errorType = '结束时间格式错误'
    } else {
      // 2. 格式正确后，进行时间解析和逻辑验证
      try {
        const startTime = parseSrtTime(current.startTime)
        const endTime = parseSrtTime(current.endTime)

        if (startTime != null && endTime != null && startTime >= endTime) {
          errorType = '开始时间不能大于或等于结束时间'
        } else {
          // 检查是否超过音频总时长
          if (!errorType && maxDuration !== undefined && maxDuration > 0 && endTime != null && endTime > maxDuration) {
            errorType = '不能超过音频总时长'
          }
        }
      } catch (error) {
        console.warn(error)
        errorType = '时间格式错误'
      }
    }

    // 如果有错误，添加到错误列表
    if (errorType) {
      timeErrors.push({
        index: i,
        subtitle: current,
        errorType,
      })
    }
  }

  // 生成错误消息
  const errorMessages = timeErrors.map(error =>
    `第${error.subtitle.id}行：${error.errorType}`,
  )

  return {
    isValid: timeErrors.length === 0,
    timeErrors,
    errorMessages,
  }
}

/**
 * 检测字幕内容是否有效
 * @param subtitles 字幕列表
 * @returns 是否所有字幕都有内容
 */
export function validateSubtitleContent(subtitles: Subtitle[]): boolean {
  return !subtitles.some(subtitle => subtitle.text.trim() === '' || subtitle.translationText.trim() === '')
}

/**
 * 详细检测字幕内容问题
 * @param subtitles 字幕列表
 * @returns 包含错误信息的验证结果
 */
export function validateSubtitleContentDetailed(subtitles: Subtitle[]): ContentValidationResult {
  const contentErrors: ContentError[] = []

  for (let i = 0; i < subtitles.length; i++) {
    const current = subtitles[i]
    if (current.text.trim() === '' || current.translationText.trim() === '') {
      contentErrors.push({
        index: i,
        subtitle: current,
      })
    }
  }

  // 生成错误消息
  const errorMessages = contentErrors.map(error =>
    `第${error.subtitle.id}行`,
  )

  return {
    isValid: contentErrors.length === 0,
    contentErrors,
    errorMessages,
  }
}

/**
 * 完整验证字幕和媒体文件
 * @param subtitles 字幕列表
 * @param mediaFile 媒体文件
 * @param maxDuration 音频总时长（毫秒），可选
 * @returns 完整的验证结果
 */
export function validateComplete(subtitles: Subtitle[], mediaFile: File | null, maxDuration?: number): CompleteValidationResult {
  // 1. 检查内容
  const contentValidation = validateSubtitleContentDetailed(subtitles)
  if (!contentValidation.isValid) {
    const errorMessages = contentValidation.errorMessages.slice(0, 3) // 只显示前3个错误
    const message = contentValidation.contentErrors.length > 3
      ? `${contentValidation.contentErrors.length}个句子没有填写内容：${errorMessages.join('、')}...，请先添加内容再保存`
      : `句子缺少填写内容：${errorMessages.join('、')}，请先添加内容再保存`

    return {
      isValid: false,
      hasEmptyContent: true,
      hasTimeErrors: false,
      hasNoMediaFile: false,
      timeErrors: [],
      contentErrors: contentValidation.contentErrors,
      errorMessage: message,
    }
  }

  // 2. 检查时间问题
  const timingValidation = validateSubtitleTiming(subtitles, maxDuration)
  if (!timingValidation.isValid) {
    const errorMessages = timingValidation.errorMessages.slice(0, 3) // 只显示前3个错误
    const message = timingValidation.timeErrors.length > 3
      ? `发现${timingValidation.timeErrors.length}个时间问题，请先解决：${errorMessages.join('；')}...`
      : `发现时间问题，请先解决：${errorMessages.join('；')}`

    return {
      isValid: false,
      hasEmptyContent: false,
      hasTimeErrors: true,
      hasNoMediaFile: false,
      timeErrors: timingValidation.timeErrors,
      contentErrors: [],
      errorMessage: message,
    }
  }

  // 3. 检查媒体文件
  if (!mediaFile) {
    return {
      isValid: false,
      hasEmptyContent: false,
      hasTimeErrors: false,
      hasNoMediaFile: true,
      timeErrors: [],
      contentErrors: [],
      errorMessage: '需要上传媒体文件，才能完成处理',
    }
  }

  // 所有验证都通过
  return {
    isValid: true,
    hasEmptyContent: false,
    hasTimeErrors: false,
    hasNoMediaFile: false,
    timeErrors: [],
    contentErrors: [],
  }
}

/**
 * 验证字幕时间范围是否合理
 */
export function validateSubtitleTimeRange(subtitle: Subtitle): {
  valid: boolean
  errors: string[]
} {
  const errors: string[] = []

  try {
    const startMs = parseSrtTime(subtitle.startTime)
    const endMs = parseSrtTime(subtitle.endTime)

    if (startMs != null && startMs < 0) {
      errors.push('开始时间不能为负数')
    }

    if (endMs != null && endMs < 0) {
      errors.push('结束时间不能为负数')
    }

    if (startMs != null && endMs != null && startMs >= endMs) {
      errors.push('开始时间必须小于结束时间')
    }

    const duration = endMs != null && startMs != null ? endMs - startMs : 0
    if (duration != null && duration < 100) {
      errors.push('字幕持续时间不能少于100ms')
    }

    if (duration != null && duration > 30000) {
      errors.push('字幕持续时间不能超过30秒')
    }
  } catch (error) {
    console.warn(error)
    errors.push('时间格式错误')
  }

  return {
    valid: errors.length === 0,
    errors,
  }
}

/**
 * 检查字幕列表是否按时间顺序排列
 */
export function validateSubtitleOrder(subtitles: Subtitle[]): {
  valid: boolean
  errors: Array<{ index: number, message: string }>
} {
  const errors: Array<{ index: number, message: string }> = []

  for (let i = 1; i < subtitles.length; i++) {
    try {
      const prevEnd = parseSrtTime(subtitles[i - 1].endTime)
      const currentStart = parseSrtTime(subtitles[i].startTime)

      if (currentStart != null && prevEnd != null && currentStart < prevEnd) {
        errors.push({
          index: i,
          message: `第${i + 1}个字幕的开始时间早于第${i}个字幕的结束时间`,
        })
      }
    } catch (error) {
      console.warn(error)
      errors.push({
        index: i,
        message: `第${i + 1}个字幕时间格式错误`,
      })
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  }
}

// ============================================================================
// 质量检查函数
// ============================================================================

// 翻译错误接口
export interface TranslationError {
  type: 'missing' | 'extra' | 'inconsistent' | 'format'
  message: string
  severity: 'low' | 'medium' | 'high'
}

export interface TranslationQuality {
  score: number
  confidence: number
  suggestions: string[]
}

/**
 * 检测翻译错误
 */
export function detectTranslationErrors(originalText: string, translatedText: string): TranslationError[] {
  const errors: TranslationError[] = []

  // 检查是否为空
  if (!translatedText || translatedText.trim() === '') {
    errors.push({
      type: 'missing',
      message: '翻译文本为空',
      severity: 'high',
    })
    return errors
  }

  // 检查长度异常
  const lengthRatio = translatedText.length / originalText.length
  if (lengthRatio > 3 || lengthRatio < 0.3) {
    errors.push({
      type: 'inconsistent',
      message: '翻译长度与原文差异过大',
      severity: 'medium',
    })
  }

  // 检查格式问题
  if (hasFormatIssues(translatedText)) {
    errors.push({
      type: 'format',
      message: '翻译文本格式异常',
      severity: 'low',
    })
  }

  return errors
}

/**
 * 检查格式问题
 */
export function hasFormatIssues(text: string): boolean {
  // 检查是否有连续的特殊字符
  return /[^\w\s\u4E00-\u9FFF]{3,}/.test(text)
}

export function checkTranslationQuality(source: string, translation: string): TranslationQuality {
  // 基础质量检查逻辑
  const score = calculateQualityScore(source, translation)
  const confidence = calculateConfidence(source, translation)
  const suggestions = generateSuggestions(source, translation)

  return {
    score,
    confidence,
    suggestions,
  }
}

export function calculateQualityScore(source: string, translation: string): number {
  // 简单的质量评分逻辑
  if (!source || !translation)
    return 0

  // 基于长度比例的简单评分
  const lengthRatio = translation.length / source.length
  if (lengthRatio < 0.3 || lengthRatio > 3)
    return 0.3

  return 0.8 // 默认较高分数
}

export function calculateConfidence(source: string, translation: string): number {
  // 置信度计算
  if (!source || !translation)
    return 0
  return 0.75
}

export function generateSuggestions(source: string, translation: string): string[] {
  const suggestions: string[] = []

  if (!translation) {
    suggestions.push('缺少翻译内容')
  }

  if (translation.length < source.length * 0.3) {
    suggestions.push('翻译可能过于简短')
  }

  return suggestions
}

/**
 * 字幕内容质量检查
 */
export interface SubtitleQualityIssue {
  type: 'empty' | 'too_short' | 'too_long' | 'invalid_chars' | 'missing_translation'
  message: string
  severity: 'low' | 'medium' | 'high'
  suggestion?: string
}

export interface SubtitleQualityResult {
  score: number
  issues: SubtitleQualityIssue[]
  suggestions: string[]
}

/**
 * 检查字幕内容质量
 */
export function checkSubtitleQuality(text: string, translationText?: string): SubtitleQualityResult {
  const issues: SubtitleQualityIssue[] = []

  // 检查内容是否为空
  if (!text || text.trim() === '') {
    issues.push({
      type: 'empty',
      message: '字幕内容为空',
      severity: 'high',
      suggestion: '请添加字幕内容',
    })
  } else {
    // 检查内容长度
    if (text.length < 2) {
      issues.push({
        type: 'too_short',
        message: '字幕内容过短',
        severity: 'medium',
        suggestion: '字幕内容至少应包含2个字符',
      })
    }

    if (text.length > 200) {
      issues.push({
        type: 'too_long',
        message: '字幕内容过长',
        severity: 'medium',
        suggestion: '建议将长字幕分割为多个较短的字幕',
      })
    }

    // 检查无效字符
    if (hasInvalidChars(text)) {
      issues.push({
        type: 'invalid_chars',
        message: '包含无效字符',
        severity: 'low',
        suggestion: '移除特殊符号或格式化字符',
      })
    }
  }

  // 检查翻译
  if (text && (!translationText || translationText.trim() === '')) {
    issues.push({
      type: 'missing_translation',
      message: '缺少翻译内容',
      severity: 'medium',
      suggestion: '为字幕添加翻译',
    })
  }

  // 计算质量分数
  const score = calculateSubtitleScore(text, translationText, issues)

  // 生成建议
  const suggestions = issues.map(issue => issue.suggestion).filter(Boolean) as string[]

  return {
    score,
    issues,
    suggestions,
  }
}

/**
 * 检查是否包含无效字符
 */
function hasInvalidChars(text: string): boolean {
  // 检查控制字符和异常符号（避免直接用控制字符正则）
  for (let i = 0; i < text.length; i++) {
    const code = text.charCodeAt(i)
    if (
      (code >= 0x00 && code <= 0x1F)
      || (code >= 0x7F && code <= 0x9F)
      || code === 0xFEFF
    ) {
      return true
    }
  }
  return false
}

/**
 * 计算字幕质量分数
 */
function calculateSubtitleScore(text: string, translationText?: string, issues: SubtitleQualityIssue[] = []): number {
  if (!text)
    return 0

  let score = 100

  // 根据问题严重程度扣分
  issues.forEach((issue) => {
    switch (issue.severity) {
      case 'high':
        score -= 30
        break
      case 'medium':
        score -= 15
        break
      case 'low':
        score -= 5
        break
    }
  })

  // 有翻译加分
  if (translationText && translationText.trim()) {
    score += 10
  }

  return Math.max(0, Math.min(100, score))
}

/**
 * 批量检查字幕质量
 */
export function batchCheckSubtitleQuality(subtitles: Array<{ text: string, translationText?: string }>): {
  overallScore: number
  totalIssues: number
  highSeverityIssues: number
  results: SubtitleQualityResult[]
} {
  const results = subtitles.map(subtitle =>
    checkSubtitleQuality(subtitle.text, subtitle.translationText),
  )

  const scores = results.map(r => r.score)
  const overallScore = scores.length > 0 ? scores.reduce((sum, score) => sum + score, 0) / scores.length : 0

  const allIssues = results.flatMap(r => r.issues)
  const totalIssues = allIssues.length
  const highSeverityIssues = allIssues.filter(issue => issue.severity === 'high').length

  return {
    overallScore,
    totalIssues,
    highSeverityIssues,
    results,
  }
}
