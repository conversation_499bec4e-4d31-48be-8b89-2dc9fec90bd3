import type { RecognitionResult } from '~/composables/subtitle/useSpeechRecognition'
import { forEach } from 'lodash-es'
import { SUMMARY_LENGTH } from '~/utils/shared/constants'

// ============================================================================
// 文本语言检测
// ============================================================================

/**
 * 检测文本语言类型
 * 基于英文字符比例进行判断：英文字符比例≥50%为英文，否则为中文
 */
export function detectTextLanguage(text: string): 'chinese' | 'english' {
  const englishChars = (text.match(/[a-z]/gi) || []).length
  const chineseChars = (text.match(/[\u4E00-\u9FFF\u3400-\u4DBF\u3000-\u303F]/g) || []).length

  const totalLetterChars = englishChars + chineseChars

  // 如果没有字母字符，默认为中文
  if (totalLetterChars === 0) {
    return 'chinese'
  }

  // 计算英文字符比例
  const englishRatio = englishChars / totalLetterChars

  // 英文字符比例≥50%判断为英文，否则为中文
  return englishRatio >= 0.5 ? 'english' : 'chinese'
}

/**
 * 分类字幕行文本
 */
export function classifySubtitleLines(lines: string[]): { text: string, translationText: string } {
  const textLines: string[] = []
  const translationLines: string[] = []

  forEach(lines, (line) => {
    const language = detectTextLanguage(line)
    if (language === 'chinese') {
      translationLines.push(line)
    } else {
      // english 放入 text
      textLines.push(line)
    }
  })

  return {
    text: textLines.join('\n'),
    translationText: translationLines.join('\n'),
  }
}

// ============================================================================
// 语音识别结果处理
// ============================================================================

/**
 * 合并识别结果中的文本片段
 * @param recognitionResult 火山引擎识别结果
 * @returns 合并后的文本，最大长度为SUMMARY_LENGTH
 */
export function mergeRecognitionText(recognitionResult: RecognitionResult): string {
  if (!recognitionResult?.utterances || recognitionResult.utterances.length === 0) {
    return ''
  }

  // 将所有句子用空格拼接成一个长字符串
  const mergedText = recognitionResult.utterances.map(utterance => utterance.text).join(' ')

  // 返回前SUMMARY_LENGTH个字符
  return mergedText.substring(0, SUMMARY_LENGTH)
}

/**
 * 将识别结果转换为句子数组
 * @param recognitionResult 火山引擎识别结果
 * @returns 句子数组
 */
export function mergeRecognitionToSentences(recognitionResult: RecognitionResult): string[] {
  if (!recognitionResult?.utterances || recognitionResult.utterances.length === 0) {
    return []
  }

  // 将所有识别片段的文本提取出来
  const sentences: string[] = []
  forEach(recognitionResult.utterances, (utterance) => {
    if (utterance.text.trim()) {
      sentences.push(utterance.text.trim())
    }
  })

  return sentences
}

// ============================================================================
// 通用文本处理工具
// ============================================================================

/**
 * 清理文本中的多余空格和换行符
 * @param text 输入文本
 * @returns 清理后的文本
 */
export function cleanText(text: string): string {
  return text
    .replace(/\s+/g, ' ') // 将多个空格替换为单个空格
    .replace(/\n+/g, '\n') // 将多个换行符替换为单个换行符
    .trim() // 去除首尾空格
}

/**
 * 截取文本到指定长度
 * @param text 输入文本
 * @param maxLength 最大长度
 * @param ellipsis 是否在末尾添加省略号
 * @returns 截取后的文本
 */
export function truncateText(text: string, maxLength: number, ellipsis: boolean = true): string {
  if (text.length <= maxLength) {
    return text
  }

  const truncated = text.substring(0, maxLength)
  return ellipsis ? `${truncated}...` : truncated
}

/**
 * 检查文本是否为空（包括只有空格的情况）
 * @param text 输入文本
 * @returns 是否为空
 */
export function isTextEmpty(text: string | null | undefined): boolean {
  return !text || text.trim().length === 0
}

/**
 * 统计文本中的字符数（不包括空格）
 * @param text 输入文本
 * @returns 字符数
 */
export function countTextCharacters(text: string): number {
  return text.replace(/\s/g, '').length
}

/**
 * 统计文本中的单词数
 * @param text 输入文本
 * @returns 单词数
 */
export function countWords(text: string): number {
  return text.trim().split(/\s+/).filter(word => word.length > 0).length
}

/**
 * 检查文本是否包含中文字符
 * @param text 输入文本
 * @returns 是否包含中文
 */
export function containsChinese(text: string): boolean {
  return /[\u4E00-\u9FFF\u3400-\u4DBF\u3000-\u303F]/.test(text)
}

/**
 * 检查文本是否包含英文字符
 * @param text 输入文本
 * @returns 是否包含英文
 */
export function containsEnglish(text: string): boolean {
  return /[a-z]/i.test(text)
}

/**
 * 将文本按行分割并过滤空行
 * @param text 输入文本
 * @returns 非空行数组
 */
export function splitIntoLines(text: string): string[] {
  return text.split('\n').filter(line => line.trim().length > 0)
}

/**
 * 将文本按句子分割
 * @param text 输入文本
 * @returns 句子数组
 */
export function splitIntoSentences(text: string): string[] {
  // 基于中英文标点符号分割句子
  return text.split(/[。！？.!?]+/)
    .map(sentence => sentence.trim())
    .filter(sentence => sentence.length > 0)
}

/**
 * 格式化文本为显示用的格式
 * @param text 输入文本
 * @param maxLineLength 每行最大字符数
 * @returns 格式化后的文本
 */
export function formatTextForDisplay(text: string, maxLineLength: number = 50): string {
  if (!text)
    return ''

  const words = text.split(/\s+/)
  const lines: string[] = []
  let currentLine = ''

  for (const word of words) {
    if (currentLine.length + word.length + 1 <= maxLineLength) {
      currentLine += currentLine ? ` ${word}` : word
    } else {
      if (currentLine) {
        lines.push(currentLine)
      }
      currentLine = word
    }
  }

  if (currentLine) {
    lines.push(currentLine)
  }

  return lines.join('\n')
}

/**
 * 提取文本中的时间标记
 * @param text 输入文本
 * @returns 时间标记数组
 */
export function extractTimeMarkers(text: string): string[] {
  const timePattern = /\d{1,2}:\d{2}(?::\d{2})?(?:[.,]\d{1,3})?/g
  return text.match(timePattern) || []
}

/**
 * 移除文本中的时间标记
 * @param text 输入文本
 * @returns 移除时间标记后的文本
 */
export function removeTimeMarkers(text: string): string {
  const timePattern = /\d{1,2}:\d{2}(?::\d{2})?(?:[.,]\d{1,3})?/g
  return text.replace(timePattern, '').trim()
}
