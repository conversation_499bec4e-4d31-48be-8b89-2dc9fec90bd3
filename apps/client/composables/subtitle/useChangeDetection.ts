import { filter, isEmpty, map, some } from 'lodash-es'
import { useLrcStore } from '~/composables/subtitle/stores/lrcStore'
import { usePlayerStore } from '~/composables/subtitle/stores/playerStore'
import { useSubtitleStore } from '~/composables/subtitle/stores/subtitleStore'
import { useCourseStore } from '~/stores/course'
import { AUDIO_MIME_PREFIX, VIDEO_MIME_PREFIX } from '~/utils/shared/constants'
import { parseSrtTime } from './useTimeUtils'

/**
 * 检测字幕编辑器中的变更状态
 * 统一的变更检测逻辑，避免重复代码
 */
export function useChangeDetection() {
  const playerStore = usePlayerStore()
  const lrcStore = useLrcStore()
  const subtitleStore = useSubtitleStore()
  const courseStore = useCourseStore()

  // 判断是否有有效的内容变更
  const hasUnsavedChanges = computed(() => {
    // 检查是否有媒体文件
    if (!playerStore.mediaFile.value) {
      return false
    }

    const { type } = playerStore.mediaFile.value
    // 检查媒体文件格式
    if (!type.startsWith(AUDIO_MIME_PREFIX) && !type.startsWith(VIDEO_MIME_PREFIX)) {
      return false
    }

    // 检查是否有LRC内容
    if (!lrcStore.hasLrc.value) {
      return false
    }

    // 检查字幕内容是否有效
    const hasValidSubtitles = some(subtitleStore.subtitles.value, subtitle => subtitle.text.trim() !== '' || subtitle.translationText.trim() !== '')

    if (!hasValidSubtitles) {
      return false
    }

    // 检查内容是否有变更（时间/原文/翻译）
    const originalSentences = courseStore.currentCourse?.sentences ?? []
    const hasContentChanges = some(subtitleStore.subtitles.value, (subtitle) => {
      // 对于新增的字幕（没有sentenceId），如果有内容就算有变更
      if (!subtitle.sentenceId) {
        return subtitle.text.trim() !== '' || subtitle.translationText.trim() !== ''
      }

      // 对于已存在的句子，检查是否有变更
      const originalSentence = originalSentences.find(s => s.id === subtitle.sentenceId)
      if (!originalSentence) {
        return true // 原始句子不存在，算作有变更
      }

      // 检查内容变更
      const textChanged = subtitle.text.trim() !== (originalSentence.content || '').trim()
      const translationChanged = subtitle.translationText.trim() !== (originalSentence.chinese || '').trim()

      // 检查时间变更
      const currentStartTime = parseSrtTime(subtitle.startTime)
      const currentEndTime = parseSrtTime(subtitle.endTime)
      const timeChanged = currentStartTime !== originalSentence.startTime || currentEndTime !== originalSentence.endTime

      return textChanged || translationChanged || timeChanged
    })

    // 检查是否有原始句子被删除
    const currentSentenceIds = new Set(map(subtitleStore.subtitles.value, s => s.sentenceId).filter(Boolean))
    const hasDeletedSentences = originalSentences.some(originalSentence =>
      originalSentence.id && !currentSentenceIds.has(originalSentence.id),
    )

    // 检查媒体文件是否有变更
    const hasMediaFileChanges = playerStore.mediaFileHash.value && playerStore.originalMediaFileHash.value
      && playerStore.mediaFileHash.value !== playerStore.originalMediaFileHash.value

    if (!hasContentChanges && !hasDeletedSentences && !hasMediaFileChanges) {
      return false
    }

    return true
  })

  return {
    hasUnsavedChanges,
  }
}

/**
 * 检查并加工未处理的句子（elements为空的句子）
 * @returns Promise<boolean> 成功返回true，失败返回false
 */
export async function checkAndProcessUnprocessedSentences(): Promise<boolean> {
  const { $trpc } = useNuxtApp()
  const courseStore = useCourseStore()
  const loadingSpinner = useLoadingSpinner()

  try {
    // 获取当前课程
    const currentCourse = courseStore.currentCourse
    if (!currentCourse) {
      console.warn('当前课程不存在，跳过句子加工检查')
      return true
    }

    // 检查是否有未加工的句子（elements为空）
    const unprocessedSentences = filter(courseStore.sentences, sentence => sentence.elements.length === 0)

    if (isEmpty(unprocessedSentences)) {
      // 没有需要加工的句子，直接返回成功
      return true
    }

    // 开始加工
    loadingSpinner.startLoading()

    await $trpc.sentence.batchProcessSentences.mutate({
      courseId: currentCourse.id,
    })

    return true
  } catch (error) {
    console.error('句子加工失败:', error)
    return false
  } finally {
    loadingSpinner.finishLoading()
  }
}
