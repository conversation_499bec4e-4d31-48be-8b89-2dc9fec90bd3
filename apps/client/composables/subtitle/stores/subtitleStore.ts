import type { Subtitle, SubtitleFilterType } from '~/types/subtitle/subtitle'
import { cloneDeep, filter, find, findIndex, forEach, isEmpty } from 'lodash-es'
import { v4 as uuidv4 } from 'uuid'
import { usePlayerStore } from '~/composables/subtitle/stores/playerStore'
import { reindexSubtitles } from '~/composables/subtitle/useSubtitleProcessing'
import { formatTimeToString, hasTimestampError, parseSrtTime } from '~/composables/subtitle/useTimeUtils'
import {
  DEFAULT_SUBTITLE_DURATION,
  DEFAULT_SUBTITLE_END_TIME,
  DEFAULT_SUBTITLE_START_TIME,
  SUBTITLE_MERGE_SEPARATOR,
} from '~/utils/shared/constants'

// State - 模块级别的响应式状态，在所有调用间共享
const subtitles = ref<Subtitle[]>([])
const historySubtitles = ref<Subtitle[][]>([])
const future = ref<Subtitle[][]>([])
const currentFilter = ref<SubtitleFilterType>('all')

/**
 * 字幕管理组合式函数
 * 负责管理字幕数据及其编辑历史，提供字幕的增删改查、合并、撤销/重做等功能
 * 使用模块级别的状态，在所有调用间共享同一份数据
 */
export function useSubtitleStore() {
  // Getters
  const canUndo = computed(() => !isEmpty(historySubtitles.value))
  const canRedo = computed(() => !isEmpty(future.value))

  // 筛选后的字幕列表
  const filteredSubtitles = computed(() => {
    if (currentFilter.value === 'all') {
      return subtitles.value
    }

    return filter(subtitles.value, (subtitle, index) => {
      const hasText = subtitle.text.trim() !== ''
      const hasTranslation = subtitle.translationText.trim() !== ''

      switch (currentFilter.value) {
        case 'textEmpty':
          return !hasText
        case 'textOnly':
          return hasText && !hasTranslation
        case 'translationOnly':
          return !hasText && hasTranslation
        case 'bothEmpty':
          return !hasText && !hasTranslation
        case 'hasContent':
          return hasText || hasTranslation
        case 'timestampError': {
          const prevSubtitle = index > 0 ? subtitles.value[index - 1] : undefined
          const nextSubtitle = index < subtitles.value.length - 1 ? subtitles.value[index + 1] : undefined
          return hasTimestampError(subtitle, prevSubtitle, nextSubtitle)
        }
        case 'all':
          return true
        default:
          return true
      }
    })
  })

  /**
   * 设置字幕
   * @param newSubtitles 新的字幕列表
   * @param preserveHistory 是否保留历史记录，默认保留
   */
  function setSubtitles(newSubtitles: Subtitle[], preserveHistory = true) {
    subtitles.value = newSubtitles
    if (!preserveHistory) {
      historySubtitles.value = []
      future.value = []
    }
  }

  function addSubtitle(afterId: number | null) {
    historySubtitles.value.push(cloneDeep(subtitles.value))
    future.value = []
    let idx = afterId === null ? subtitles.value.length - 1 : findIndex(subtitles.value, s => s.id === afterId)
    if (idx < 0)
      idx = subtitles.value.length - 1
    let start = DEFAULT_SUBTITLE_START_TIME
    let end = DEFAULT_SUBTITLE_END_TIME
    if (idx >= 0 && subtitles.value[idx] != null) {
      start = subtitles.value[idx].endTime
      const startSeconds = parseSrtTime(start)
      if (startSeconds !== null) {
        const endSeconds = startSeconds + DEFAULT_SUBTITLE_DURATION
        end = formatTimeToString(endSeconds)
      } else {
        end = DEFAULT_SUBTITLE_END_TIME
      }
    }
    const insertIndex = idx + 1
    if (insertIndex < subtitles.value.length) {
      const nextSubtitle = subtitles.value[insertIndex]
      const nextStartTime = nextSubtitle.startTime
      const newEndSeconds = parseSrtTime(end)
      const nextStartSeconds = parseSrtTime(nextStartTime)
      if (newEndSeconds !== null && nextStartSeconds !== null && newEndSeconds > nextStartSeconds) {
        end = nextStartTime
      }
    }
    const newSub: Subtitle = {
      uuid: uuidv4(),
      id: 0,
      startTime: start,
      endTime: end,
      text: '',
      translationText: '',
    }
    subtitles.value.splice(idx + 1, 0, newSub)
    subtitles.value = reindexSubtitles(subtitles.value)
  }

  function deleteSubtitle(uuid: string) {
    historySubtitles.value.push(cloneDeep(subtitles.value))
    future.value = []
    const playerStore = usePlayerStore()
    if (playerStore.loopingSubtitleUuid.value === uuid) {
      playerStore.stopLoop()
    }
    subtitles.value = reindexSubtitles(filter(subtitles.value, s => s.uuid !== uuid))
  }

  function mergeWithNextSubtitle(uuid: string) {
    historySubtitles.value.push(cloneDeep(subtitles.value))
    future.value = []
    const idx = subtitles.value.findIndex(s => s.uuid === uuid)
    if (idx === -1 || idx === subtitles.value.length - 1)
      return
    const currentSubtitle = subtitles.value[idx]
    const nextSubtitle = subtitles.value[idx + 1]
    const playerStore = usePlayerStore()
    if (playerStore.loopingSubtitleUuid.value === nextSubtitle.uuid) {
      playerStore.stopLoop()
    } else if (playerStore.loopingSubtitleUuid.value === currentSubtitle.uuid) {
      const endSeconds = parseSrtTime(nextSubtitle.endTime)
      if (endSeconds !== null) {
        playerStore.updateLoopTime(null, endSeconds)
      }
    }
    const mergedText = currentSubtitle.text + SUBTITLE_MERGE_SEPARATOR + nextSubtitle.text
    const currentTrans = currentSubtitle.translationText
    const nextTrans = nextSubtitle.translationText
    const hasCurrentTrans = !!(currentTrans && currentTrans.trim() !== '')
    const hasNextTrans = !!(nextTrans && nextTrans.trim() !== '')
    let mergedTranslation = ''
    if (hasCurrentTrans && hasNextTrans) {
      mergedTranslation = currentTrans + SUBTITLE_MERGE_SEPARATOR + nextTrans
    } else if (hasCurrentTrans) {
      mergedTranslation = currentTrans
    } else if (hasNextTrans) {
      mergedTranslation = nextTrans
    }
    const merged: Subtitle = {
      ...currentSubtitle,
      endTime: nextSubtitle.endTime,
      text: mergedText,
      translationText: mergedTranslation,
    }
    subtitles.value.splice(idx, 2, merged)
    subtitles.value = reindexSubtitles(subtitles.value)
  }

  function updateSubtitleText(uuid: string, newText: string) {
    historySubtitles.value.push(cloneDeep(subtitles.value))
    future.value = []
    const idx = subtitles.value.findIndex(s => s.uuid === uuid)
    if (idx === -1)
      return
    subtitles.value[idx].text = newText
  }

  function updateSubtitleStartTime(uuid: string, newStartTime: string) {
    historySubtitles.value.push(cloneDeep(subtitles.value))
    future.value = []
    const idx = subtitles.value.findIndex(s => s.uuid === uuid)
    if (idx === -1)
      return
    subtitles.value[idx].startTime = newStartTime
  }

  function updateSubtitleEndTime(uuid: string, newEndTime: string) {
    historySubtitles.value.push(cloneDeep(subtitles.value))
    future.value = []
    const idx = subtitles.value.findIndex(s => s.uuid === uuid)
    if (idx === -1)
      return
    subtitles.value[idx].endTime = newEndTime
  }

  function replaceTextAndTranslationBatch(
    replacements: {
      uuid: string
      newOriginalText?: string
      newTranslatedText?: string
    }[],
  ) {
    historySubtitles.value.push(cloneDeep(subtitles.value))
    future.value = []
    forEach(replacements, ({ uuid, newOriginalText, newTranslatedText }) => {
      const idx = findIndex(subtitles.value, s => s.uuid === uuid)
      if (idx !== -1) {
        if (typeof newOriginalText === 'string') {
          subtitles.value[idx].text = newOriginalText
        }
        if (typeof newTranslatedText === 'string') {
          subtitles.value[idx].translationText = newTranslatedText
        }
      }
    })
  }

  function undo() {
    if (historySubtitles.value.length === 0)
      return
    future.value.push(cloneDeep(subtitles.value))
    const previousState = historySubtitles.value.pop()!
    subtitles.value = cloneDeep(previousState)
    const playerStore = usePlayerStore()
    if (playerStore.isLooping.value && playerStore.loopingSubtitleUuid.value !== null) {
      const subInNewCurrentState = find(subtitles.value, s => s.uuid === playerStore.loopingSubtitleUuid.value)
      const subInOldCurrentState = future.value[future.value.length - 1] != null
        ? find(future.value[future.value.length - 1], s => s.uuid === playerStore.loopingSubtitleUuid.value)
        : undefined
      if (!subInNewCurrentState) {
        playerStore.stopLoop()
      } else if (
        subInOldCurrentState
        && (subInNewCurrentState.startTime !== subInOldCurrentState.startTime
          || subInNewCurrentState.endTime !== subInOldCurrentState.endTime)
      ) {
        const startSeconds = parseSrtTime(subInNewCurrentState.startTime)
        const endSeconds = parseSrtTime(subInNewCurrentState.endTime)
        if (startSeconds !== null && endSeconds !== null) {
          playerStore.updateLoopTime(startSeconds, endSeconds)
        }
      }
    }
  }

  function redo() {
    if (future.value.length === 0)
      return
    historySubtitles.value.push(cloneDeep(subtitles.value))
    const nextState = future.value.pop()!
    subtitles.value = cloneDeep(nextState)
    const playerStore = usePlayerStore()
    if (playerStore.isLooping.value && playerStore.loopingSubtitleUuid.value !== null) {
      const currentRedoneSub = find(subtitles.value, s => s.uuid === playerStore.loopingSubtitleUuid.value)
      const previousSubBeforeRedo = historySubtitles.value[historySubtitles.value.length - 1] != null
        ? find(historySubtitles.value[historySubtitles.value.length - 1], s => s.uuid === playerStore.loopingSubtitleUuid.value)
        : undefined
      if (!currentRedoneSub) {
        playerStore.stopLoop()
      } else if (
        previousSubBeforeRedo
        && (currentRedoneSub.startTime !== previousSubBeforeRedo.startTime || currentRedoneSub.endTime !== previousSubBeforeRedo.endTime)
      ) {
        const startSeconds = parseSrtTime(currentRedoneSub.startTime)
        const endSeconds = parseSrtTime(currentRedoneSub.endTime)
        if (startSeconds !== null && endSeconds !== null) {
          playerStore.updateLoopTime(startSeconds, endSeconds)
        }
      }
    }
  }

  function updateSubtitleTranslationText(uuid: string, newTranslation: string) {
    historySubtitles.value.push(cloneDeep(subtitles.value))
    future.value = []
    const idx = subtitles.value.findIndex(s => s.uuid === uuid)
    if (idx === -1)
      return
    subtitles.value[idx].translationText = newTranslation
  }

  function setFilter(filterType: SubtitleFilterType) {
    currentFilter.value = filterType
  }

  return {
    // State
    subtitles,
    historySubtitles,
    future,
    currentFilter,
    // Getters
    canUndo,
    canRedo,
    filteredSubtitles,

    // Actions
    setSubtitles,
    addSubtitle,
    deleteSubtitle,
    mergeWithNextSubtitle,
    updateSubtitleText,
    updateSubtitleStartTime,
    updateSubtitleEndTime,
    replaceTextAndTranslationBatch,
    undo,
    redo,
    updateSubtitleTranslationText,
    setFilter,
  }
}
