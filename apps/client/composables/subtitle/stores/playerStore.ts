import { includes } from 'lodash-es'
import { calculateFileHash } from '~/utils/file'
import { AUDIO_MIME_PREFIX, VIDEO_MIME_PREFIX } from '~/utils/shared/constants'

export enum PlayerStatusEnum {
  IDLE = 'idle',
  LOADING = 'loading',
  PLAYING = 'playing',
  PAUSED = 'paused',
  ERROR = 'error',
}

// 定义可选的播放速度值
export const PLAYBACK_RATES = [0.5, 0.75, 1, 1.25, 1.5, 2, 3, 4]
const INVALID_MEDIA_FILE_ERROR_MSG = '请选择有效的媒体文件' // 用户可见的错误提示

// State - 模块级别的响应式状态，在所有调用间共享
const status = ref<PlayerStatusEnum>(PlayerStatusEnum.IDLE)
const currentTime = ref(0)
const duration = ref(0)
const mediaUrl = ref<string | null>(null)
const mediaFile = ref<File | null>(null)
const mediaFileHash = ref<string | null>(null)
const originalMediaFileHash = ref<string | null>(null)
const error = ref<Error | null>(null)
const playbackRate = ref(1)
// 进度跳转请求
const seekRequest = ref<{ time: number, key: number } | null>(null)

// 循环播放相关状态
const isLooping = ref(false)
const loopingSubtitleUuid = ref<string | null>(null)
const loopStartTime = ref<number | null>(null)
const loopEndTime = ref<number | null>(null)

// 练习点播放相关状态
const practicePlayingUuid = ref<string | null>(null)
const practiceEndTime = ref<number | null>(null)

/**
 * 音频播放器管理组合式函数
 * 负责管理音频播放状态、播放控制和媒体文件处理
 * 使用模块级别的状态，在所有调用间共享同一份数据
 */
export function usePlayerStore() {
  // Computed
  const isPlaying = computed(() => status.value === PlayerStatusEnum.PLAYING)
  const isPracticePlaying = computed(() => practicePlayingUuid.value !== null)

  // Actions
  async function setMediaFile(file: File) {
    if (!file.type.startsWith(AUDIO_MIME_PREFIX) && !file.type.startsWith(VIDEO_MIME_PREFIX)) {
      const err = new Error(INVALID_MEDIA_FILE_ERROR_MSG)
      setError(err)
      return
    }

    clearMedia()
    mediaFile.value = file
    mediaUrl.value = URL.createObjectURL(file)
    currentTime.value = 0
    duration.value = 0
    error.value = null
    status.value = PlayerStatusEnum.LOADING

    // 计算文件 hash
    try {
      const fileHash = await calculateFileHash(file)
      mediaFileHash.value = fileHash
    } catch (err) {
      console.error('计算媒体文件 hash 失败:', err)
      mediaFileHash.value = null
    }
  }

  const setOriginalMediaFileHash = (hash: string | null) => {
    originalMediaFileHash.value = hash
  }

  function setMediaUrl(url: string) {
    if (!url || typeof url !== 'string') {
      // const err = new Error('请提供有效的媒体URL')
      // setError(err)
      return
    }

    clearMedia()
    mediaUrl.value = url
    mediaFile.value = null // 远程URL时没有本地文件
    currentTime.value = 0
    duration.value = 0
    error.value = null
    status.value = PlayerStatusEnum.LOADING
  }

  async function setMediaUrlWithProxy(url: string) {
    if (!url || typeof url !== 'string') {
      return
    }

    try {
      status.value = PlayerStatusEnum.LOADING

      // 通过 fetch 下载文件到内存，设置 referrerPolicy 避免 Referer 检查
      const response = await fetch(url, {
        referrerPolicy: 'no-referrer',
        mode: 'cors',
      })
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const blob = await response.blob()
      const blobUrl = URL.createObjectURL(blob)

      // 从 URL 中提取文件名
      const urlParts = url.split('/')
      const fileName = decodeURIComponent(urlParts[urlParts.length - 1])

      // 创建一个模拟的 File 对象，包含必要的属性
      const mockFile = new File([blob], fileName, {
        type: blob.type || 'video/mp4',
      })

      mediaUrl.value = blobUrl
      mediaFile.value = mockFile
      currentTime.value = 0
      duration.value = 0
      error.value = null

      // 计算下载文件的 hash
      try {
        const fileHash = await calculateFileHash(mockFile)
        setOriginalMediaFileHash(fileHash)
      } catch (hashErr) {
        console.error('计算代理下载文件 hash 失败:', hashErr)
        mediaFileHash.value = null
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('下载媒体文件失败')
      setError(error)
      console.error('下载媒体文件失败:', err)
    }
  }

  function setDuration(newDuration: number) {
    if (newDuration > 0) {
      duration.value = newDuration
    }
  }

  function setStatus(newStatus: PlayerStatusEnum) {
    status.value = newStatus
  }

  function setError(err: Error | null) {
    error.value = err
    if (err) {
      status.value = PlayerStatusEnum.ERROR
    }
  }

  function seek(time: number) {
    if (time >= 0 && time <= duration.value) {
      currentTime.value = time
    }
  }

  function updatePlayIngStatus() {
    if (status.value !== PlayerStatusEnum.ERROR && mediaUrl.value !== null) {
      status.value = PlayerStatusEnum.PLAYING
    }
  }

  function updatePauseStatus() {
    status.value = PlayerStatusEnum.PAUSED
  }

  function togglePlay() {
    if (status.value === PlayerStatusEnum.PLAYING) {
      updatePauseStatus()
    } else if (status.value === PlayerStatusEnum.PAUSED || status.value === PlayerStatusEnum.LOADING) {
      updatePlayIngStatus()
    }
  }

  function clearMedia() {
    status.value = PlayerStatusEnum.IDLE
    currentTime.value = 0
    duration.value = 0
    mediaUrl.value = null
    mediaFile.value = null
    mediaFileHash.value = null
    error.value = null
    // 清除循环播放状态
    isLooping.value = false
    loopingSubtitleUuid.value = null
    loopStartTime.value = null
    loopEndTime.value = null
    // 清除练习点播放状态
    practicePlayingUuid.value = null
    practiceEndTime.value = null
  }

  function setPlaybackRate(rate: number) {
    if (includes(PLAYBACK_RATES, rate)) {
      playbackRate.value = rate
    }
  }

  // 循环播放相关方法
  function startLoop(subtitleUuid: string, startTime: number, endTime: number) {
    // 停止练习点播放（互斥）
    if (isPracticePlaying.value) {
      stopPracticePlay()
    }

    isLooping.value = true
    loopingSubtitleUuid.value = subtitleUuid
    loopStartTime.value = startTime
    loopEndTime.value = endTime
    // 确保从循环起点开始播放
    setSeekRequest(startTime)
    updatePlayIngStatus()
  }

  function stopLoop() {
    isLooping.value = false
    loopingSubtitleUuid.value = null
    loopStartTime.value = null
    loopEndTime.value = null
  }

  function updateLoopTime(startTime: number | null, endTime: number | null) {
    if (!isLooping.value)
      return
    // 更新循环时间
    if (startTime !== null) {
      loopStartTime.value = startTime
      // 如果当前播放时间在新的循环区间之前，则跳转到新的开始时间
      if (currentTime.value < startTime) {
        setSeekRequest(startTime)
      }
    }
    if (endTime !== null) {
      loopEndTime.value = endTime
      // 如果当前播放时间超过新的结束时间，则跳转到开始时间
      if (currentTime.value > endTime && loopStartTime.value !== null) {
        setSeekRequest(loopStartTime.value)
      }
    }

    // 如果正在播放，确保继续播放
    if (status.value === PlayerStatusEnum.PLAYING) {
      updatePlayIngStatus()
    }
  }

  function setSeekRequest(time: number) {
    seekRequest.value = { time, key: Date.now() }
    seek(time)
  }

  // 练习点播放相关方法
  function startPracticePlay(subtitleUuid: string, startTime: number, endTime: number) {
    // 停止循环播放（互斥）
    if (isLooping.value) {
      stopLoop()
    }
    // 停止其他练习点播放
    if (practicePlayingUuid.value && practicePlayingUuid.value !== subtitleUuid) {
      stopPracticePlay()
    }

    practicePlayingUuid.value = subtitleUuid
    practiceEndTime.value = endTime
    // 跳转到开始时间并开始播放
    setSeekRequest(startTime)
    updatePlayIngStatus()
  }

  function stopPracticePlay() {
    practicePlayingUuid.value = null
    practiceEndTime.value = null
    updatePauseStatus()
  }

  return {
    // State
    status,
    currentTime,
    duration,
    mediaUrl,
    mediaFile,
    mediaFileHash,
    originalMediaFileHash,
    error,
    playbackRate,
    seekRequest,
    // 循环播放状态
    isLooping,
    loopingSubtitleUuid,
    loopStartTime,
    loopEndTime,
    // 练习点播放状态
    practicePlayingUuid,
    practiceEndTime,
    // Computed
    isPlaying,
    isPracticePlaying,
    // Actions
    setMediaFile,
    setMediaUrl,
    setMediaUrlWithProxy,
    setOriginalMediaFileHash,
    setDuration,
    setStatus,
    setError,
    seek,
    updatePlayIngStatus,
    updatePauseStatus,
    togglePlay,
    clearMedia,
    setPlaybackRate,
    setSeekRequest,
    // 循环播放方法
    startLoop,
    stopLoop,
    updateLoopTime,
    // 练习点播放方法
    startPracticePlay,
    stopPracticePlay,
  }
}
