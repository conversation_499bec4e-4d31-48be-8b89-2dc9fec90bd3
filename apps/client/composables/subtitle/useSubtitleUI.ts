import type { Course, Sentence } from '@julebu/shared'
import type { LrcData } from './useFormatProcessing'
import type { Subtitle } from '~/types/subtitle/subtitle'
import { useRafFn } from '@vueuse/core'
import { cloneDeep, find, findIndex, isEmpty, map } from 'lodash-es'
import { toast } from 'vue-sonner'
import { useLoadingSpinner } from '~/composables/loadingSpinner'
import { useLrcStore } from '~/composables/subtitle/stores/lrcStore'
import { usePlayerStore } from '~/composables/subtitle/stores/playerStore'
import { useSubtitleStore } from '~/composables/subtitle/stores/subtitleStore'
import { useCourseStore } from '~/stores/course'
import { useGameUpdateStore } from '~/stores/gameUpdate'
import { calculateFileHash, uploadMediaFileToS3 } from '~/utils/file'
import { useChangeDetection } from './useChangeDetection'
import { convertStatementsToSubtitles, lrcToSrt, parseSrt, parseVtt, subtitleToLrc } from './useFormatProcessing'
import { processSubtitleChanges } from './useSubtitleProcessing'
import { parseSrtTime, parseTimeStringToSeconds } from './useTimeUtils'
import { validateComplete } from './useValidation'

// ============================================================================
// 字幕操作相关函数
// ============================================================================

export function useSubtitleActions(
  subtitle: Ref<Subtitle>,
  isAddDisabled: Ref<boolean>,
  isLast: Ref<boolean>,
) {
  const subtitleStore = useSubtitleStore()
  const addTooltipContent = computed(() => {
    return isAddDisabled.value ? '没有空间添加' : '添加一行'
  })

  function handleAddSubtitle() {
    if (!isAddDisabled.value) {
      subtitleStore.addSubtitle(subtitle.value.id)
    }
  }

  function handleDeleteSubtitle() {
    subtitleStore.deleteSubtitle(subtitle.value.uuid)
  }

  function handleMergeSubtitle() {
    if (!isLast.value) {
      subtitleStore.mergeWithNextSubtitle(subtitle.value.uuid)
    }
  }

  return {
    addTooltipContent,
    handleAddSubtitle,
    handleDeleteSubtitle,
    handleMergeSubtitle,
  }
}

export function useSubtitleEditing(subtitle: Ref<Subtitle>) {
  const subtitleStore = useSubtitleStore()
  const editing = ref(false)
  const editingText = ref(subtitle.value.text)
  const editingTranslationText = ref(subtitle.value.translationText || '')
  const editingType = ref<null | 'text' | 'translation'>(null)
  const startTime = ref(subtitle.value.startTime)
  const endTime = ref(subtitle.value.endTime)

  const textareaRef = ref<HTMLTextAreaElement | null>(null)
  const translationTextareaRef = ref<HTMLTextAreaElement | null>(null)
  const startTimeInputRef = ref<ComponentPublicInstance | null>(null)
  const endTimeInputRef = ref<ComponentPublicInstance | null>(null)

  watch(
    () => subtitle.value.text,
    (val) => {
      if (!editing.value)
        editingText.value = val
    },
  )

  watch(
    () => subtitle.value.startTime,
    (val) => {
      startTime.value = val
    },
    { deep: true },
  )

  watch(
    () => subtitle.value.endTime,
    (val) => {
      endTime.value = val
    },
  )

  function startEdit(type: 'text' | 'translation') {
    editing.value = true
    editingType.value = type
    if (type === 'text') {
      editingText.value = subtitle.value.text
      void nextTick(() => {
        textareaRef.value?.focus()
      })
    } else {
      editingTranslationText.value = subtitle.value.translationText || ''
      void nextTick(() => {
        translationTextareaRef.value?.focus()
      })
    }
  }

  function confirmEdit() {
    if (editingType.value === 'text') {
      subtitleStore.updateSubtitleText(subtitle.value.uuid, editingText.value)
    } else if (editingType.value === 'translation') {
      if (!editingTranslationText.value.trim()) {
        subtitleStore.updateSubtitleTranslationText(subtitle.value.uuid, '')
      } else {
        subtitleStore.updateSubtitleTranslationText(subtitle.value.uuid, editingTranslationText.value)
      }
    }
    editing.value = false
    editingType.value = null
  }

  function cancelEdit() {
    editingText.value = subtitle.value.text
    editingTranslationText.value = subtitle.value.translationText || ''
    editing.value = false
    editingType.value = null
  }

  function onStartTime(val: string) {
    startTime.value = val
    subtitleStore.updateSubtitleStartTime(subtitle.value.uuid, val)
  }

  function onEndTime(val: string) {
    endTime.value = val
    subtitleStore.updateSubtitleEndTime(subtitle.value.uuid, val)
  }

  return {
    editing,
    editingText,
    editingTranslationText,
    editingType,
    startTime,
    endTime,
    textareaRef,
    translationTextareaRef,
    startTimeInputRef,
    endTimeInputRef,
    startEdit,
    confirmEdit,
    cancelEdit,
    onStartTime,
    onEndTime,
  }
}

// ============================================================================
// 合并高亮效果
// ============================================================================

/**
 * 用于管理字幕合并高亮框的组合式函数
 * @param store - subtitleStore 实例，需包含 subtitles 响应式数组
 * @param scrollContainerRef - 可选的滚动容器 ref，用于修正高亮框在滚动时的错位问题
 * @returns 高亮框样式、显示状态、鼠标悬停/离开处理函数
 */
export function useMergeHighlight(scrollContainerRef?: Ref<HTMLElement | null>) {
  const subtitleStore = useSubtitleStore()
  // 当前悬停的合并候选字幕项的 uuid
  const hoveredMergeCandidateUuid = ref<string | null>(null)
  // 高亮框的样式对象（top/left/width/height）
  const highlightBoxStyle = ref<Record<string, string>>({})
  // 是否显示高亮框
  const showHighlightBox = ref(false)

  // 包裹 updateHighlightBox，节流到每帧只执行一次
  const { resume: runUpdateHighlightBox } = useRafFn(updateHighlightBox, { immediate: false })

  /**
   * 鼠标悬停在合并按钮时调用，设置当前候选 uuid 并更新高亮框
   * @param uuid - 当前悬停字幕项的 uuid
   */
  function handleMergeButtonHover(uuid: string) {
    hoveredMergeCandidateUuid.value = uuid
    void nextTick(() => {
      runUpdateHighlightBox()
    })
  }

  /**
   * 鼠标离开合并按钮时调用，隐藏高亮框
   */
  function handleMergeButtonLeave() {
    hoveredMergeCandidateUuid.value = null
    showHighlightBox.value = false
  }

  /**
   * 根据当前悬停的字幕项，计算并设置高亮框的样式
   * 包括当前项和下一项的整体区域
   */
  function updateHighlightBox() {
    if (!hoveredMergeCandidateUuid.value) {
      showHighlightBox.value = false
      return
    }
    // 查找当前悬停字幕项的索引
    const currentIndex = findIndex(subtitleStore.subtitles.value, sub => sub.uuid === hoveredMergeCandidateUuid.value)
    if (currentIndex === -1) {
      showHighlightBox.value = false
      return
    }
    // 获取当前字幕项的 DOM 元素
    const currentId = `subtitle-item-${subtitleStore.subtitles.value[currentIndex].uuid}`
    const currentEl = document.getElementById(currentId)
    if (!currentEl) {
      showHighlightBox.value = false
      return
    }
    // 获取滚动容器的 scrollTop/scrollLeft
    let scrollTop = 0
    let scrollLeft = 0
    if (scrollContainerRef && scrollContainerRef.value) {
      scrollTop = scrollContainerRef.value.scrollTop
      scrollLeft = scrollContainerRef.value.scrollLeft
    }
    const top = currentEl.offsetTop - scrollTop
    const left = currentEl.offsetLeft - scrollLeft
    const width = currentEl.offsetWidth
    let height = currentEl.offsetHeight
    // 检查是否有下一项，若有则合并高亮区域
    if (currentIndex + 1 < subtitleStore.subtitles.value.length) {
      const nextId = `subtitle-item-${subtitleStore.subtitles.value[currentIndex + 1].uuid}`
      const nextEl = document.getElementById(nextId)
      if (nextEl) {
        // 计算当前项与下一项之间的间距
        const gap = nextEl.offsetTop - (currentEl.offsetTop + currentEl.offsetHeight)
        height += gap > 0 ? gap : 0
        height += nextEl.offsetHeight
      }
    }
    // 设置高亮框样式
    highlightBoxStyle.value = {
      top: `${top}px`,
      left: `${left}px`,
      width: `${width}px`,
      height: `${height}px`,
    }
    showHighlightBox.value = true
  }

  // 当字幕数据变化时，自动隐藏高亮框并重置悬停状态
  watch(
    () => subtitleStore.subtitles.value.length,
    () => {
      showHighlightBox.value = false
      hoveredMergeCandidateUuid.value = null
    },
  )

  // 返回高亮框样式、显示状态及事件处理函数
  return {
    highlightBoxStyle,
    showHighlightBox,
    handleMergeButtonHover,
    handleMergeButtonLeave,
  }
}

// ============================================================================
// TopBar 逻辑处理
// ============================================================================

/**
 * TopBar 逻辑抽离
 */
export function useTopBarLogic() {
  const lrcStore = useLrcStore()
  const playerStore = usePlayerStore()
  const subtitleStore = useSubtitleStore()
  const courseStore = useCourseStore()
  const { $trpc } = useNuxtApp()
  const config = useRuntimeConfig()
  const { withLoading } = useLoadingSpinner()
  const gameUpdateStore = useGameUpdateStore()
  const { hasUnsavedChanges } = useChangeDetection()

  // 音频文件上传 input
  const audioFileInputRef = ref<HTMLInputElement | null>(null)
  // 在线歌词弹窗
  const isOnlineLrcModalOpen = ref(false)
  // vtt 文件上传 input
  const subtitleFileInputRef = ref<HTMLInputElement | null>(null)

  const totalElementsCount = computed(() => {
    return courseStore.currentCourse?.sentences.length ?? 0
  })

  // 使用共享的变更检测逻辑
  const canSaveChanges = hasUnsavedChanges

  // 触发音频文件上传
  const triggerAudioFileInput = () => {
    audioFileInputRef.value?.click()
  }
  // 触发合并字幕文件上传
  const triggerSubtitleFileInput = () => {
    subtitleFileInputRef.value?.click()
  }

  // 合并字幕文件上传处理
  const handleSubtitleFileUpload = async (event: Event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) {
      return
    }

    // 保存当前字幕状态到历史记录
    if (!isEmpty(subtitleStore.subtitles)) {
      subtitleStore.historySubtitles.value.push(cloneDeep(subtitleStore.subtitles.value))
      subtitleStore.future.value = []
    }

    const ext = file.name.split('.').pop()?.toLowerCase()
    if (ext === 'lrc') {
      await lrcStore.uploadLrc(file)
      const srtSubtitles = lrcToSrt(lrcStore.lrcLines.value)
      subtitleStore.setSubtitles(srtSubtitles)
    } else if (ext === 'srt') {
      const content = await file.text()
      const srtSubtitles = parseSrt(content)
      subtitleStore.setSubtitles(srtSubtitles)
      const lrcContent = subtitleToLrc(srtSubtitles)
      lrcStore.updateLrcContent('', lrcContent)
    } else if (ext === 'vtt') {
      const content = await file.text()
      const vttSubtitles = parseVtt(content)
      subtitleStore.setSubtitles(vttSubtitles)
      const lrcContent = subtitleToLrc(vttSubtitles)
      lrcStore.updateLrcContent('', lrcContent)
    }
    // 清空 input 以便连续上传同一文件
    ;(event.target as HTMLInputElement).value = ''
  }

  // 处理获取在线歌词
  const handleGetLrc = () => {
    isOnlineLrcModalOpen.value = true
  }

  // 处理应用在线歌词
  const handleApplyOnlineLrc = (lrc: LrcData) => {
    // 保存当前字幕状态到历史记录
    if (!isEmpty(subtitleStore.subtitles.value)) {
      subtitleStore.historySubtitles.value.push(cloneDeep(subtitleStore.subtitles.value))
      subtitleStore.future.value = []
    }

    lrcStore.parseLrcContent(lrc.syncedLyrics)
    const srtSubtitles = lrcToSrt(lrcStore.lrcLines.value)
    subtitleStore.setSubtitles(srtSubtitles)
  }

  // 处理音频文件上传
  const handleAudioFileUpload = async (event: Event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (file) {
      await withLoading(async () => {
        await playerStore.setMediaFile(file)
      }, '正在加载媒体文件...', [
        '正在解析媒体格式...',
        '正在计算文件信息...',
        '准备播放器组件...',
      ])
      // 清空 input 以便连续上传同一文件
      ;(event.target as HTMLInputElement).value = ''
    }
  }

  // 检测字幕顺序变更，只输出有顺序变更的 affectedSentences
  function detectSubtitleOrderChangeAndGenerateAffectedSentences(subtitles: Subtitle[], originalSentences: Sentence[]) {
    // 1. 构建原始 position 映射
    const originalPositionMap = new Map<string, number>()
    originalSentences.forEach((s) => {
      if (s.id && typeof s.position === 'number') {
        originalPositionMap.set(s.id, s.position)
      }
    })

    // 2. 只处理有 sentenceId 的字幕
    const affectedSentences: { sentenceId: string, position: number }[] = []
    let position = 1
    for (const sub of subtitles) {
      if (sub.sentenceId != null) {
        const oldPos = originalPositionMap.get(sub.sentenceId)
        if (oldPos !== position) {
          affectedSentences.push({ sentenceId: sub.sentenceId, position })
        }
        position++
      }
    }
    return affectedSentences
  }

  // 统一错误处理机制
  const handleError = (error: unknown, userMessage: string, shouldThrow = false) => {
    console.error(error)
    toast.error(userMessage)
    if (shouldThrow) {
      throw error
    }
  }

  const handleValidationError = (validation: ReturnType<typeof validateComplete>) => {
    toast.error(validation.errorMessage!)
    if (validation.hasEmptyContent) {
      console.error('有句子没有填写内容')
    } else if (validation.hasTimeErrors) {
      console.error('时间问题:', validation.timeErrors)
    } else if (validation.hasNoMediaFile) {
      console.error('需要上传媒体文件，才能完成处理')
    }
  }

  // 验证数据
  const validateData = () => {
    const validation = validateComplete(subtitleStore.subtitles.value, playerStore.mediaFile.value, playerStore.duration.value)
    if (!validation.isValid) {
      handleValidationError(validation)
      return false
    }
    return true
  }

  // 句子新增和更新处理
  const processSentenceUpserts = async (courseId: string, newSubtitles: Subtitle[], updatedSubtitles: Subtitle[]) => {
    if (isEmpty(newSubtitles) && isEmpty(updatedSubtitles)) {
      return
    }

    try {
      const sentences = [
        ...map(newSubtitles, subtitle => ({
          uuid: subtitle.uuid,
          content: subtitle.text,
          chinese: subtitle.translationText,
        })),
        ...map(updatedSubtitles, subtitle => ({
          uuid: subtitle.uuid,
          sentenceId: subtitle.sentenceId,
          content: subtitle.text,
          chinese: subtitle.translationText,
        })),
      ]

      const response = await $trpc.sentence.batchUpsertSentences.mutate({
        courseId,
        sentences,
      })

      const newSubtitlesWithSentenceId = map(subtitleStore.subtitles.value, (subtitle: Subtitle) => ({
        ...subtitle,
        sentenceId: subtitle.sentenceId == null ? find(response.sentences, sentence => sentence.uuid === subtitle.uuid)?.sentenceId : subtitle.sentenceId,
      }))

      subtitleStore.setSubtitles(newSubtitlesWithSentenceId)
    } catch (error) {
      handleError(error, '批量操作句子失败', true)
    }
  }

  // 句子删除处理
  const processSentenceDeletions = async (courseId: string, deletedSentences: Subtitle[]) => {
    if (deletedSentences.length === 0) {
      return
    }

    try {
      const sentenceList = deletedSentences.map(s => ({
        sentenceId: s.sentenceId!,
        courseId,
      }))

      const deleteResult = await $trpc.sentence.deleteBatchSentences.mutate({ sentenceList })

      if (!isEmpty(deleteResult.failedIds)) {
        console.error('部分句子删除失败:', deleteResult.failedIds)
        toast.warning('部分句子删除失败')
      }
    } catch (error) {
      handleError(error, '批量删除句子失败', true)
    }
  }

  // 排序处理
  const processSubtitleOrder = async (courseId: string, latestCourse: Course) => {
    const affectedSentences = detectSubtitleOrderChangeAndGenerateAffectedSentences(
      subtitleStore.subtitles.value,
      latestCourse.sentences,
    )

    if (!isEmpty(affectedSentences)) {
      try {
        await $trpc.sentence.movePosition.mutate({ courseId, affectedSentences })
      } catch (err) {
        handleError(err, '顺序调整失败')
      }
    }
  }

  // 获取最新的课程数据
  const getLatestCourseData = async (courseId: string): Promise<Course> => {
    try {
      if (courseStore.currentCourse == null) {
        throw new Error('当前课程不存在')
      }

      const course = await $trpc.course.findOne.query({
        courseId,
        coursePackId: courseStore.currentCourse.coursePackId,
      })
      return course
    } catch (error) {
      handleError(error, '获取最新课程信息失败', true)
    }
    return null as unknown as Course
  }

  // 媒体文件变更检查
  const checkMediaFileChanges = async (): Promise<boolean> => {
    if (!playerStore.mediaFile.value) {
      return false
    }

    try {
      const currentHash = await calculateFileHash(playerStore.mediaFile.value)
      return currentHash !== playerStore.originalMediaFileHash.value
    } catch (err) {
      handleError(err, '计算媒体文件hash失败')
      return true // 出错时认为有变更
    }
  }

  // 媒体文件上传
  const uploadMediaFile = async () => {
    if (!playerStore.mediaFile.value) {
      throw new Error('媒体文件不存在')
    }

    try {
      const fileKey = await uploadMediaFileToS3(playerStore.mediaFile.value)
      const mediaUrl = `${config.public.s3.bucketGameCDN}${fileKey}`

      await $trpc.course.edit.mutate({
        courseId: courseStore.currentCourse?.id as string,
        coursePackId: courseStore.currentCourse?.coursePackId as string,
        title: courseStore.currentCourse?.title as string,
        description: courseStore.currentCourse?.description as string,
        mediaUrl,
      })

      const fileHash = await calculateFileHash(playerStore.mediaFile.value)
      playerStore.setOriginalMediaFileHash(fileHash)
      return mediaUrl
    } catch (error) {
      handleError(error, '上传媒体文件失败', true)
    }
  }

  /**
   * 批量根据字幕更新时间到 element
   * @param subtitles 当前字幕数组
   * @param latestSentences 最新句子数组（含 elements）
   */
  async function updateElementsTimeFromSubtitles(subtitles: Subtitle[], latestSentences: Sentence[]) {
    // 构建批量更新时间 payload，确保每个sentenceId只推送一次（取第一个）
    const updates: Array<{ sentenceId: string, startTime: number, endTime: number }> = []
    const pushedSentenceIds = new Set<string>()
    for (const subtitle of subtitles) {
      if (subtitle.sentenceId == null) {
        continue
      }
      if (pushedSentenceIds.has(subtitle.sentenceId)) {
        continue
      }
      const sentence = latestSentences.find(s => s.id === subtitle.sentenceId)
      if (!sentence) {
        continue
      }
      const startSeconds = parseSrtTime(subtitle.startTime)
      const endSeconds = parseSrtTime(subtitle.endTime)
      if (startSeconds == null || endSeconds == null) {
        continue
      }
      // 只有时间有变化时才推送
      if (sentence.startTime === startSeconds && sentence.endTime === endSeconds) {
        continue
      }
      updates.push({
        sentenceId: subtitle.sentenceId,
        startTime: startSeconds,
        endTime: endSeconds,
      })
      pushedSentenceIds.add(subtitle.sentenceId)
    }
    if (!isEmpty(updates)) {
      try {
        await $trpc.sentence.batchUpdateElementsTime.mutate({ updates })
        console.log(`已批量更新 ${updates.length} 个句子的时间`)
      } catch (err) {
        toast.error('批量更新时间到句子失败')
        console.error('批量更新时间到句子失败:', err)
      }
    }
  }

  // 点击完成按钮
  const handleFinish = async () => {
    // 1. 验证和准备数据
    const isValid = validateData()
    if (!isValid) {
      return
    }

    const courseId = courseStore.currentCourse?.id
    if (courseId == null) {
      handleError(new Error('课程ID不存在'), '当前课程ID不存在')
      return null
    }

    await withLoading(async () => {
      const { newSubtitles, updatedSubtitles, deletedSentences } = processSubtitleChanges(
        subtitleStore.subtitles.value,
        convertStatementsToSubtitles(courseStore.currentCourse?.sentences ?? []),
      )

      // 2. 第一批并行操作：句子数据库操作 + 媒体文件处理
      await Promise.all([
      // 句子数据库操作
        Promise.all([
          // 句子新增和更新处理
          processSentenceUpserts(courseId, newSubtitles, updatedSubtitles),
          // 句子删除处理
          processSentenceDeletions(courseId, deletedSentences),
        ]),
        // 媒体文件检查和上传可以完全并行
        checkMediaFileChanges().then(async (changed) => {
          if (changed) {
            await uploadMediaFile()
          }
          return changed
        }),
      ])

      // 3. 获取最新课程数据（用于处理未加工句子）
      const latestCourse = await getLatestCourseData(courseId)

      // 4. 并行处理
      await Promise.all([
        // 排序处理
        processSubtitleOrder(courseId, latestCourse),
        // 更新元素时间
        !isEmpty(latestCourse?.sentences) && updateElementsTimeFromSubtitles(subtitleStore.subtitles.value, latestCourse.sentences),
      ])

      gameUpdateStore.markCourseForUpdate()

      // 5. 获取最终的最新课程数据（包含所有处理后的修改时间）
      const finalLatestCourse = await getLatestCourseData(courseId)

      // 6. 更新 store
      courseStore.updateCourseData(finalLatestCourse)
      subtitleStore.setSubtitles(subtitleStore.subtitles.value, true)

      toast.success('保存成功')
    }, '正在处理课程数据...', [
      '正在验证字幕数据...',
      '正在保存句子信息...',
      '正在上传媒体文件...',
      '正在发布到游戏端...',
      '即将完成，请稍候...',
    ])
  }

  // 获取subtitle的所有句子，进行时间的格式转换
  const getSubtitleSentences = () => {
    return map(subtitleStore.subtitles.value, subtitle => ({
      ...subtitle,
      startTime: parseTimeStringToSeconds(subtitle.startTime) * 1000,
      endTime: parseTimeStringToSeconds(subtitle.endTime) * 1000,
    }))
  }

  // 调用ai接口，处理字幕数据
  const handleAiSubtitle = async () => {
    const sentences = getSubtitleSentences()
    console.log('%c AT 🥝 sentences 🥝-751', 'font-size:13px; background:#359675; color:#79dab9;', sentences)
    console.log('%c AT 🥝 sentences 🥝-751', 'font-size:13px; background:#359675; color:#79dab9;', JSON.stringify(sentences))
  }

  // 音视频字幕生成
  const handleSpeechRecognition = async () => {
    void handleAiSubtitle()

    // await withLoading(async () => {
    //   // 1. 检查是否有媒体文件
    //   if (!playerStore.mediaFile.value) {
    //     toast.error('请先上传音频或视频文件')
    //     return
    //   }

    //   try {
    //     // 3. 构建查询参数（参考 srt-editor 的参数格式）
    //     const queryParams = {
    //       language: 'en-US',
    //       caption_type: 'singing' as const, // auto speech singing
    //     }

    //     // 4. 提交语音识别任务 - 使用FormData格式
    //     const fileBuffer = await playerStore.mediaFile.value.arrayBuffer()
    //     const buffer = new Uint8Array(fileBuffer)

    //     const submitResult = await $trpc.speech.submit.mutate({
    //       fileData: buffer,
    //       fileName: playerStore.mediaFile.value.name,
    //       contentType: playerStore.mediaFile.value.type,
    //       queryParams,
    //     })

    //     if (!submitResult.id || submitResult.message !== 'Success') {
    //       throw new Error(submitResult.message || '语音识别任务提交失败：未获得任务ID')
    //     }

    //     // 5. 轮询查询识别结果（参考 srt-editor 的轮询逻辑）
    //     const taskId = submitResult.id
    //     let attempts = 0
    //     const maxAttempts = 60 // 最大尝试次数（5分钟）
    //     const pollInterval = 5000 // 轮询间隔（5秒）

    //     const pollResult = async (): Promise<QueryResponse> => {
    //       attempts++

    //       try {
    //         const queryResult = await $trpc.speech.query.query({
    //           queryParams: {
    //             id: taskId,
    //           },
    //         })

    //         // 检查识别状态
    //         if (queryResult.code === 0) {
    //           // 识别成功
    //           return queryResult
    //         } else if (queryResult.code === 1001) {
    //           // 识别中，继续轮询
    //           if (attempts >= maxAttempts) {
    //             throw new Error('语音识别超时，请重试')
    //           }
    //           await new Promise(resolve => setTimeout(resolve, pollInterval))
    //           return await pollResult()
    //         } else {
    //           // 识别失败
    //           throw new Error(`语音识别失败: ${queryResult.message || '未知错误'}`)
    //         }
    //       } catch (error) {
    //         // 查询失败时的重试逻辑
    //         if (attempts < maxAttempts) {
    //           await new Promise(resolve => setTimeout(resolve, pollInterval))
    //           return pollResult()
    //         } else {
    //           throw error
    //         }
    //       }
    //     }

    //     // 延迟开始轮询（参考 srt-editor 的 3 秒延迟）
    //     await new Promise(resolve => setTimeout(resolve, 3000))
    //     const finalResult = await pollResult()

    //     console.log('%c AT 🥝 finalResult.utterances 🥝-817', 'font-size:13px; background:#cc7951; color:#ffbd95;', finalResult.utterances)
    //     console.log('%c AT 🥝 finalResult.utterances 🥝-817', 'font-size:13px; background:#cc7951; color:#ffbd95;', JSON.stringify(finalResult.utterances))
    //     // 6. 处理识别结果
    //     if (!isEmpty(finalResult.utterances)) {
    //       // 过滤掉 words，只要句子
    //       const sentences = map(finalResult.utterances, utterance => ({
    //         text: utterance.text,
    //         startTime: utterance.start_time,
    //         endTime: utterance.end_time,
    //       }))
    //       console.log('%c AT 🥝 sentences 🥝-825', 'font-size:13px; background:#7c4019; color:#c0845d;', JSON.stringify(sentences))
    //       console.log('%c AT 🥝 sentences 🥝-830', 'font-size:13px; background:#7c4019; color:#c0845d;', sentences)
    //     } else {
    //       toast.warning('未识别到语音内容')
    //     }
    //   } catch (error) {
    //     console.error('💥 语音识别失败:', error)
    //     const errorMessage = error instanceof Error ? error.message : '语音识别失败'
    //     toast.error(errorMessage)
    //   }
    // }, '正在进行语音识别...', [
    //   '正在分析音频内容...',
    //   '正在生成字幕文本...',
    //   '正在处理识别结果...',
    // ])
  }

  // 字幕打轴
  const handleSubtitleAlign = async () => {
    // 1. 验证和准备数据
    const isValid = validateData()
    if (!isValid) {
      return
    }

    await handleSpeechRecognition()
  }

  // 字幕格式化
  const handleSubtitleFormat = async () => {
    console.log('字幕格式化')
  }

  return {
    isOnlineLrcModalOpen,
    audioFileInputRef,
    canSaveChanges,
    subtitleFileInputRef,
    totalElementsCount,

    triggerAudioFileInput,
    handleAudioFileUpload,
    handleFinish,
    handleGetLrc,
    handleApplyOnlineLrc,
    triggerSubtitleFileInput,
    handleSubtitleFileUpload,
    handleSubtitleAlign,
    handleSubtitleFormat,
  }
}
