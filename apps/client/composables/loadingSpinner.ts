const isLoading = ref(false)
const loadingTip = ref('')

let tipInterval: NodeJS.Timeout | null

function cycleTips(tips: string[]) {
  let index = 0
  loadingTip.value = tips[index]
  tipInterval = setInterval(() => {
    index = (index + 1) % tips.length
    loadingTip.value = tips[index]
  }, 2000)
}

function clearTipInterval() {
  if (tipInterval !== null) {
    clearInterval(tipInterval)
    tipInterval = null
  }
}

export function useLoadingSpinner() {
  const minLoadingTime = 500 // 最小显示时间（毫秒）
  let loadingStartTime: number

  function startLoading(tips: string | string[] = '') {
    loadingStartTime = Date.now()
    isLoading.value = true
    changeLoadingTip(tips)
  }

  function finishLoading() {
    const currentTime = Date.now()
    const elapsedTime = currentTime - loadingStartTime
    const remainingTime = Math.max(0, minLoadingTime - elapsedTime)

    setTimeout(() => {
      isLoading.value = false
      loadingTip.value = ''
      clearTipInterval()
    }, remainingTime)
  }

  function changeLoadingTip(tips: string | string[]) {
    clearTipInterval()
    if (Array.isArray(tips)) {
      cycleTips(tips)
    } else {
      loadingTip.value = tips
    }
  }

  /**
   * 包装异步函数，自动处理加载状态和提示信息
   * @param asyncFn 要执行的异步函数
   * @param message 单一加载提示信息，默认为 '处理中...'
   * @param tips 多个提示信息数组，如果提供则会循环显示
   * @returns 异步函数的执行结果
   */
  const withLoading = async <T>(
    asyncFn: () => Promise<T>,
    message: string = '处理中...',
    tips: string[] = [],
  ): Promise<T> => {
    startLoading(tips.length > 0 ? tips : message)
    try {
      return await asyncFn()
    } finally {
      finishLoading()
    }
  }

  return {
    isLoading,
    loadingTip,
    startLoading,
    finishLoading,
    changeLoadingTip,
    withLoading,
  }
}
