import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'

const navItemOptions = ref([
  {
    icon: 'i-ph-house-light',
    label: '首页',
    to: '/dashboard',
  },
  {
    icon: 'i-ph-books-light',
    label: '课程包',
    to: '/dashboard/course-packs',
    description: '打造你的专属课程',
  },
  {
    icon: 'i-heroicons-inbox',
    label: '收集箱',
    to: '/dashboard/inbox',
    description: '收集你感兴趣的内容',
  },
  {
    icon: 'i-ph-diamond-light',
    label: '钻石记录',
    to: '/dashboard/currency',
    description: '查看钻石消费记录',
  },
  {
    icon: 'i-ph-gear-light',
    label: '设置',
    to: '/dashboard/settings',
  },
])

export function useSidebar() {
  const route = useRoute()

  const navItems = computed(() => {
    return navItemOptions.value.map((item) => {
      // 如果是课程包菜单项且当前路径以/dashboard/course-packs开头
      if (
        item.to === '/dashboard/course-packs'
        && route.path.startsWith('/dashboard/course-packs')
      ) {
        return {
          ...item,
          active: true,
        }
      }
      return {
        ...item,
        active: item.to === route.path,
      }
    })
  })

  const currentItem = computed(() =>
    navItems.value.find((item) => {
      return item.to === route.path
    }),
  )

  return {
    navItems,
    currentItem,
  }
}
