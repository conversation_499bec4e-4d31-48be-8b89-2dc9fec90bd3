import type { PermissionCheck, UserPermissionContext } from '~/types/permissions'
import { computed } from 'vue'
import { PermissionCheck as PermissionCheckEnum } from '~/types/permissions'

/**
 * 解析用户ID列表字符串
 * @param userIdsString 逗号分隔的用户ID字符串
 * @returns 处理后的用户ID数组
 */
export function parseUserIdList(userIdsString: string | undefined): string[] {
  if (!userIdsString || typeof userIdsString !== 'string') {
    return []
  }

  return userIdsString
    .split(',')
    .map(id => id.trim())
    .filter(Boolean)
}

/**
 * 检查用户权限
 * @param userContext 用户权限上下文
 * @param authorizedIds 授权用户ID列表
 * @returns 权限检查结果
 */
export function checkUserPermission(
  userContext: UserPermissionContext | undefined,
  authorizedIds: string[],
): PermissionCheck {
  if (!userContext?.sub) {
    return PermissionCheckEnum.USER_NOT_AUTHENTICATED
  }

  if (authorizedIds.length === 0) {
    return PermissionCheckEnum.CONFIG_MISSING
  }

  return authorizedIds.includes(userContext.sub)
    ? PermissionCheckEnum.GRANTED
    : PermissionCheckEnum.DENIED
}

/**
 * 权限管理 composable
 * @returns 权限相关的响应式状态和方法
 */
export function usePermissions() {
  const config = useRuntimeConfig()
  const { userInfo } = useUserStore()

  // 缓存处理后的用户ID列表
  const authorizedUserIds = computed(() => {
    const userIds = config.public.auth.authUserIds as string | undefined
    const result = parseUserIdList(userIds)
    return result
  })

  // 用户权限上下文
  const userPermissionContext = computed((): UserPermissionContext | undefined => {
    if (!userInfo)
      return undefined

    return {
      sub: userInfo.sub,
      username: userInfo.username ?? undefined,
    }
  })

  // 响应式权限检查结果
  const hasFeaturePermission = computed(() => {
    const permissionResult = checkUserPermission(
      userPermissionContext.value,
      authorizedUserIds.value,
    )

    return permissionResult === PermissionCheckEnum.GRANTED
  })

  return {
    hasFeaturePermission,
  }
}
