import { describe, test, expect } from "vitest";
import { parseTextToSentences } from "../sentence";

describe("parseTextToSentences", () => {
  test("应正确解析只包含英文句子的文本", () => {
    const input = `Hello world.
This is a test.`;
    const expected = [
      { content: "Hello world.", chinese: "" },
      { content: "This is a test.", chinese: "" },
    ];
    expect(parseTextToSentences(input)).toEqual(expected);
  });

  test("应正确解析包含英文和中文句子的文本", () => {
    const input = `Hello world.
你好世界。
This is a test.
这是一个测试。`;
    const expected = [
      { content: "Hello world.", chinese: "你好世界。" },
      { content: "This is a test.", chinese: "这是一个测试。" },
    ];
    expect(parseTextToSentences(input)).toEqual(expected);
  });

  test("应正确处理混合的英文和中文句子", () => {
    const input = `Hello world.
This is a test.
这是一个中文句子。
Another English sentence.`;
    const expected = [
      { content: "Hello world.", chinese: "" },
      { content: "This is a test.", chinese: "这是一个中文句子。" },
      { content: "Another English sentence.", chinese: "" },
    ];
    expect(parseTextToSentences(input)).toEqual(expected);
  });

  test("应忽略空行", () => {
    const input = `Hello world.

你好世界。

This is a test.`;
    const expected = [
      { content: "Hello world.", chinese: "你好世界。" },
      { content: "This is a test.", chinese: "" },
    ];
    expect(parseTextToSentences(input)).toEqual(expected);
  });

  test("把中文的符号替换成英文的", () => {
    const input = "“you think about most of the time”";
    const expected = [
      {
        chinese: "",
        content: '"you think about most of the time"',
      },
    ];
    expect(parseTextToSentences(input)).toEqual(expected);
  });
});
