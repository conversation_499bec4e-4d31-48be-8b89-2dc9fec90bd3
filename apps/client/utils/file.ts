import type { AudioMimeType, MimeType, VideoMimeType } from '@julebu/shared'
import { ofetch } from 'ofetch'
import { s3Api } from '~/api/s3'

export function exportObjectToJsonFile(exportObj: unknown, exportName: string) {
  const dataStr
    = `data:text/json;charset=utf-8,${
      encodeURIComponent(JSON.stringify(exportObj))}`
  const downloadAnchorNode = document.createElement('a')
  downloadAnchorNode.setAttribute('href', dataStr)
  downloadAnchorNode.setAttribute('download', `${exportName}.json`)
  document.body.appendChild(downloadAnchorNode) // required for firefox
  downloadAnchorNode.click()
  downloadAnchorNode.remove()
}

export function getFileExtension(filename: string) {
  return filename.split('.').pop()?.toLowerCase()
}

export async function calculateFileHash(file: File): Promise<string> {
  const arrayBuffer = await file.arrayBuffer()
  const hashBuffer = await crypto.subtle.digest('SHA-256', arrayBuffer)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  const hashHex = hashArray
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
  return hashHex
}

// 工具函数：将base64转换为File对象
export function base64ToFile(
  base64Data: string,
  filename: string = 'ai-generated-image.webp',
): File {
  const base64 = base64Data.replace(/^data:image\/[a-z]+;base64,/, '')
  const byteCharacters = atob(base64)
  const byteNumbers = Array.from({ length: byteCharacters.length })

  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }

  const byteArray = new Uint8Array(byteNumbers as number[])
  const blob = new Blob([byteArray], { type: 'image/webp' })

  return new File([blob], filename, { type: 'image/webp' })
}

// Image upload function (for backward compatibility)
export async function uploadImageFileToS3(file: File): Promise<string> {
  // 计算文件哈希和获取尺寸
  const fileHash = await calculateFileHash(file)
  const extension = getFileExtension(file.name)

  // 构建文件名
  const fileName = `${fileHash}.${extension}`

  // 获取预签名URL并上传
  const presignedUrl = await s3Api.getImagePresignedUrl({
    key: fileName,
    contentLength: file.size,
    mimeType: file.type as MimeType,
  })

  await ofetch(presignedUrl, {
    method: 'PUT',
    body: file,
  })

  return `/${fileName}`
}

// Audio upload function
export async function uploadAudioFileToS3(file: File): Promise<string> {
  const fileHash = await calculateFileHash(file)
  const extension = getFileExtension(file.name)
  const fileName = `${fileHash}.${extension}`

  const presignedUrl = await s3Api.getMediaPresignedUrl({
    key: fileName,
    contentLength: file.size,
    mimeType: file.type as AudioMimeType,
  })

  await ofetch(presignedUrl, {
    method: 'PUT',
    body: file,
  })

  return `/audios/${fileName}`
}

// Video upload function
export async function uploadVideoFileToS3(file: File): Promise<string> {
  const fileHash = await calculateFileHash(file)
  const extension = getFileExtension(file.name)
  const fileName = `${fileHash}.${extension}`

  const presignedUrl = await s3Api.getMediaPresignedUrl({
    key: fileName,
    contentLength: file.size,
    mimeType: file.type as VideoMimeType,
  })

  await ofetch(presignedUrl, {
    method: 'PUT',
    body: file,
  })

  return `/videos/${fileName}`
}

// Generic media upload function
export async function uploadMediaFileToS3(file: File): Promise<string> {
  const fileType = file.type

  if (fileType.startsWith('image/')) {
    return uploadImageFileToS3(file)
  } else if (fileType.startsWith('audio/')) {
    return uploadAudioFileToS3(file)
  } else if (fileType.startsWith('video/')) {
    return uploadVideoFileToS3(file)
  } else {
    throw new Error(`Unsupported file type: ${fileType}`)
  }
}

// Get image stats (existing function)
export async function getImageStats(
  file: File,
): Promise<{ width: number, height: number, fileSize: number, mimeType: string }> {
  const fileSize = file.size
  const mimeType = file.type
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      resolve({ width: img.width, height: img.height, fileSize, mimeType })
    }
    img.onerror = reject
    if (file.size > 0) {
      img.src = URL.createObjectURL(file)
    }
  })
}
