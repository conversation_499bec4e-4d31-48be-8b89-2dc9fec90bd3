// 将中文符号转换为英文符号
function normalizeQuotes(text: string): string {
  return text
    .replace(/\u201C/g, '"') // 中文左双引号转英文双引号
    .replace(/\u201D/g, '"') // 中文右双引号转英文双引号
    .replace(/\u2018/g, '\'') // 中文左单引号转英文单引号
    .replace(/\u2019/g, '\'') // 中文右单引号转英文单引号
}

export function parseTextToSentences(text: string) {
  const lines = text.split('\n').filter(line => line.trim() !== '')
  const sentences: { content: string, chinese: string }[] = []

  for (let i = 0; i < lines.length; i++) {
    const currentLine = normalizeQuotes(lines[i].trim())
    const nextLine = lines[i + 1]?.trim()

    if (nextLine && /[\u4E00-\u9FA5]/.test(nextLine)) {
      // 如果下一行是中文
      sentences.push({
        content: currentLine,
        chinese: nextLine,
      })
      i++ // 跳过中文行
    } else {
      // 如果是全英文或者最后一行
      sentences.push({
        content: currentLine,
        chinese: '',
      })
    }
  }

  return sentences
}
