import type { ElementImage } from '@julebu/shared'

const url = useRuntimeConfig().public.s3.bucketImagesCDN

// options 还有其他参数参考 https://docs.bitiful.com/coreix/features/resize
export function getImageUrl(
  image: ElementImage,
  options?: {
    w: number
    h: number
  },
) {
  // 确保 url + image.fileKey, 拼接过程不会出现 //
  const bucketImagesUrl = url.endsWith('/')
    ? url.slice(0, -1)
    : url
  const fileKey = image.fileKey.startsWith('/')
    ? image.fileKey.slice(1)
    : image.fileKey

  return `${bucketImagesUrl}/${fileKey}?w=${options?.w || image.width}&h=${options?.h || image.height}`
}
