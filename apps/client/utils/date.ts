import { format, parseISO } from 'date-fns'

/**
 * 格式化日期为标准日期格式 (yyyy-MM-dd)
 * @param dateString 日期字符串
 */
export function formatDate(dateString: string | null) {
  if (!dateString)
    return ''
  // 对于带Z的格式，需要先解析
  const date = dateString.endsWith('Z') ? parseISO(dateString) : new Date(dateString)
  return format(date, 'yyyy-MM-dd')
}

/**
 * 格式化日期为标准日期时间格式 (yyyy-MM-dd HH:mm)
 * @param dateString 日期字符串
 */
export function formatDateTime(dateString: string) {
  if (!dateString)
    return ''
  // 对于带Z的格式，需要先解析
  const date = dateString.endsWith('Z') ? parseISO(dateString) : new Date(dateString)
  return format(date, 'yyyy-MM-dd HH:mm')
}

/**
 * 格式化日期为标准日期时间格式，带秒 (yyyy-MM-dd HH:mm:ss)
 * @param dateString 日期字符串
 */
export function formatDateTimeWithSeconds(dateString: string) {
  if (!dateString)
    return ''
  // 对于带Z的格式，需要先解析
  const date = dateString.endsWith('Z') ? parseISO(dateString) : new Date(dateString)
  return format(date, 'yyyy-MM-dd HH:mm:ss')
}

/**
 * 格式化为相对时间 (例如：刚刚、10分钟前、1小时前、昨天、前天等)
 * @param dateString 日期字符串
 */
export function formatRelativeTime(dateString: string) {
  if (!dateString)
    return ''
  // 对于带Z的格式，需要先解析
  const date = dateString.endsWith('Z') ? parseISO(dateString) : new Date(dateString)
  const now = new Date()

  const diffMs = now.getTime() - date.getTime()
  const diffSec = Math.floor(diffMs / 1000)
  const diffMin = Math.floor(diffSec / 60)
  const diffHour = Math.floor(diffMin / 60)
  const diffDay = Math.floor(diffHour / 24)

  if (diffSec < 60)
    return '刚刚'
  if (diffMin < 60)
    return `${diffMin}分钟前`
  if (diffHour < 24)
    return `${diffHour}小时前`
  if (diffDay === 1)
    return '昨天'
  if (diffDay === 2)
    return '前天'
  if (diffDay < 30)
    return `${diffDay}天前`

  return formatDate(dateString)
}

/**
 * 获取当前时间格式化字符串
 * @param formatStr 格式化字符串
 */
export function getCurrentDateTime(formatStr = 'yyyy-MM-dd HH:mm:ss') {
  return format(new Date(), formatStr)
}

/**
 * 格式化日期为年/月/日格式
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
export function formatYearMonthDay(dateString: string | null): string {
  if (!dateString)
    return ''
  // 对于带Z的格式，需要先解析
  const date = dateString.endsWith('Z') ? parseISO(dateString) : new Date(dateString)
  return format(date, 'yyyy/MM/dd')
}
