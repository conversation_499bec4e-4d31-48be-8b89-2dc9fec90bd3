// https://nuxt.com/docs/api/configuration/nuxt-config
import { codeInspectorPlugin } from 'code-inspector-plugin'

export default defineNuxtConfig({
  ssr: false,
  app: {
    head: {
      title: '句乐部 | 编辑端',
      link: [
        {
          rel: 'icon',
          href: '/favicon.ico',
        },
      ],
    },
  },
  experimental: {
    inlineRouteRules: true,
  },
  devtools: {
    enabled: false,
  },

  css: ['~/assets/global.css'],

  colorMode: {
    preference: 'dark',
  },

  typescript: {
    shim: false,
  },

  build: {
    transpile: ['trpc-nuxt', '@julebu/shared'],
  },

  vite: {
    plugins: [codeInspectorPlugin({ bundler: 'vite', editor: 'cursor' })],
  },
  extends: [],

  modules: [
    '@nuxt/ui',
    '@pinia/nuxt',
    'pinia-plugin-persistedstate/nuxt',
    '@vueuse/nuxt',
    '@nuxt/image',
    '@formkit/auto-animate',
    '@nuxt/eslint',
  ],
  runtimeConfig: {
    apiHubBaseUrl: '',
    databaseURL: '',
    gameDatabaseURL: '',
    cosSecretId: '',
    cosSecretKey: '',
    public: {
      gameURL: process.env.NUXT_GAME_URL,
      apiBase: process.env.API_BASE || '',
      s3: {
        imagesPrefix: process.env.S3_IMAGES_PREFIX,
        bucketGameCDN: process.env.S3_BUCKET_GAME_CDN,
        bucketImagesCDN: process.env.S3_BUCKET_IMAGES_CDN,
      },
      logto: {
        endpoint: process.env.NUXT_LOGTO_ENDPOINT,
        appId: process.env.NUXT_LOGTO_APP_ID,
        resources: [process.env.API_BASE || ''],
        signInRedirectURI: process.env.NUXT_LOGTO_SIGN_IN_REDIRECT_URI || '',
        signOutRedirectURI: process.env.NUXT_LOGTO_SIGN_OUT_REDIRECT_URI || '',
      },
      auth: {
        // 新功能 指定用户可见 userId 列表
        authUserIds: process.env.AUTH_USER_IDS ?? '',
      },
    },
  },

  // Re-add icon configuration for local bundling
  icon: {
    clientBundle: {
      // 自动扫描组件中使用的图标
      scan: true,
    },
  },
})
