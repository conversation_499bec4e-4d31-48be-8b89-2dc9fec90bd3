// 专门用来检测课程包/课程是否需要更新到游戏端
// 用户点击更新课程包成功时 会把当前课程包记录成不需更新的状态
// 在用户执行更新课程包的操作时 会标记为需要更新
// 这样用户再次点击更新课程包时 我们就可以知道是否需要真的更新了

import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useCourseStore } from './course'
import { useCoursePackStore } from './coursePack'

export const useGameUpdateStore = defineStore(
  'gameUpdate',
  () => {
    const coursePackStore = useCoursePackStore()
    const courseStore = useCourseStore()
    const coursePackUpdateMap = ref<Record<string, boolean>>({})
    const courseUpdateMap = ref<Record<string, boolean>>({})

    const initCoursePackUpdateState = (packId: string) => {
      if (coursePackUpdateMap.value[packId] !== undefined) {
        return
      }
      coursePackUpdateMap.value[packId] = true
    }

    const needsCoursePackUpdate = (packId: string): boolean => {
      return coursePackUpdateMap.value[packId] || false
    }

    const needsCourseUpdate = (courseId: string): boolean => {
      return courseUpdateMap.value[courseId] || false
    }

    const markCoursePackForUpdate = () => {
      const coursePackId = coursePackStore.currentCoursePack?.id || ''
      coursePackUpdateMap.value[coursePackId] = true
    }

    const markCourseForUpdate = () => {
      const courseId = courseStore.currentCourse?.id || ''
      courseUpdateMap.value[courseId] = true
      // 课程更新了 那么对应的课程包也需要更新
      markCoursePackForUpdate()
    }

    const markCoursePackUpdated = () => {
      const coursePackId = coursePackStore.currentCoursePack?.id || ''
      coursePackUpdateMap.value[coursePackId] = false
    }

    const markCourseUpdated = () => {
      const courseId = courseStore.currentCourse?.id || ''
      courseUpdateMap.value[courseId] = false
    }

    function markCurrentCoursesUpdated() {
      coursePackStore.courses.forEach((course) => {
        courseUpdateMap.value[course.id] = false
      })
    }

    function markCurrentCoursesForUpdate() {
      coursePackStore.courses.forEach((course) => {
        courseUpdateMap.value[course.id] = true
      })
    }

    return {
      coursePackUpdateMap,
      courseUpdateMap,
      initCoursePackUpdateState,
      needsCoursePackUpdate,
      needsCourseUpdate,
      markCoursePackForUpdate,
      markCourseForUpdate,
      markCoursePackUpdated,
      markCourseUpdated,
      markCurrentCoursesUpdated,
      markCurrentCoursesForUpdate,
    }
  },
  {
    persist: {
      key: 'course-pack-update-state',
      storage: localStorage,
      pick: ['coursePackUpdateMap', 'courseUpdateMap'],
    },
  },
)
