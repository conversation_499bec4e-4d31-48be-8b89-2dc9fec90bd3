import type { UserInfoResponse } from '@logto/vue'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { userApi } from '~/api/user'

export const useUserStore = defineStore('user', () => {
  const userInfo = ref<UserInfoResponse>()
  const isInitialized = ref(false)

  async function initUser(userInfoResponse: UserInfoResponse) {
    userInfo.value = userInfoResponse

    // 调用后端初始化接口
    if (userInfo.value) {
      await userApi.initializeAfterLogin()
    }
  }

  return {
    initUser,
    userInfo,
    isInitialized,
  }
})
