import type { CoursePack } from '@julebu/shared'
import Fuse from 'fuse.js'
import { defineStore } from 'pinia'

// Fuse.js 配置
const fuseOptions = {
  keys: ['title', 'description'],
  threshold: 0.3,
  location: 0,
  distance: 100,
  minMatchCharLength: 1,
  shouldSort: true,
  includeScore: true,
  useExtendedSearch: true,
}

export const useCoursePacksStore = defineStore(
  'course-packs',
  () => {
    const { $trpc } = useNuxtApp()
    const coursePacks = ref<CoursePack[]>([])
    const isInitialized = ref(false)

    // 课程包搜索
    const searchQuery = ref('')

    // 添加筛选状态
    const filterStatus = ref<'all' | 'published' | 'unpublished'>('all')

    // 添加排序状态
    const sortDirection = ref<'asc' | 'desc'>('asc')

    // 创建 Fuse 实例
    const fuse = computed(() => new Fuse(coursePacks.value, fuseOptions))

    // 基础的置顶和未置顶课程包
    const basePinnedCoursePacks = computed(() =>
      coursePacks.value
        .filter(pack => pack.isPinned)
        .sort((a, b) => a.position - b.position),
    )

    const baseUnpinnedCoursePacks = computed(() =>
      coursePacks.value
        .filter(pack => !pack.isPinned)
        .sort((a, b) => a.position - b.position),
    )

    // 修改排序计算属性
    const pinnedCoursePacks = computed(() => {
      const query = searchQuery.value.trim()
      const result = query
        ? fuse.value
            .search(query)
            .map(result => result.item)
            .filter(pack => pack.isPinned)
        : basePinnedCoursePacks.value

      return sortItems(result)
    })

    const unpinnedCoursePacks = computed(() => {
      const query = searchQuery.value.trim()
      const result = query
        ? fuse.value
            .search(query)
            .map(result => result.item)
            .filter(pack => !pack.isPinned)
        : baseUnpinnedCoursePacks.value

      return sortItems(result)
    })

    const publishedCoursePacks = computed(
      () => coursePacks.value.filter(pack => pack.gameId).length,
    )

    // 筛选后的课程包
    const filteredPinnedCoursePacks = computed(() => {
      let packs = pinnedCoursePacks.value

      if (filterStatus.value === 'published') {
        packs = packs.filter(pack => pack.gameId)
      } else if (filterStatus.value === 'unpublished') {
        packs = packs.filter(pack => !pack.gameId)
      }

      return packs
    })

    const filteredUnpinnedCoursePacks = computed(() => {
      let packs = unpinnedCoursePacks.value

      if (filterStatus.value === 'published') {
        packs = packs.filter(pack => pack.gameId)
      } else if (filterStatus.value === 'unpublished') {
        packs = packs.filter(pack => !pack.gameId)
      }

      return packs
    })

    // 方法
    async function init() {
      const data = await $trpc.coursePack.list.query()
      coursePacks.value = data.map((pack: CoursePack) => ({
        ...pack,
        isPinned: pack.isPinned || false,
      }))
      isInitialized.value = true
    }

    async function updateCoursePacks() {
      const data = await $trpc.coursePack.list.query()
      coursePacks.value = data.map((newPack: CoursePack) => {
        const existingPack = coursePacks.value.find(p => p.id === newPack.id)
        return {
          ...newPack,
          isPinned: existingPack?.isPinned || false,
        }
      })
    }

    async function createCoursePack(title: string, description: string, cover: string, categoryId?: string) {
      await $trpc.coursePack.create.mutate({
        title,
        description,
        cover,
        categoryId,
      })

      await updateCoursePacks()
    }

    async function deleteCoursePack(coursePackId: string) {
      await $trpc.coursePack.delete.mutate({
        coursePackId,
      })

      coursePacks.value = coursePacks.value.filter(
        pack => pack.id !== coursePackId,
      )
    }

    async function pinCoursePack(id: string) {
      try {
        await $trpc.coursePack.pinOne.mutate({
          coursePackId: id,
        })
        const packIndex = coursePacks.value.findIndex(p => p.id === id)
        if (packIndex !== -1) {
          coursePacks.value[packIndex] = {
            ...coursePacks.value[packIndex],
            isPinned: true,
          }
        }
      } catch (error) {
        console.error('置顶课程包失败:', error)
        throw error
      }
    }

    async function unpinCoursePack(id: string) {
      try {
        await $trpc.coursePack.unpinOne.mutate({
          coursePackId: id,
        })
        const packIndex = coursePacks.value.findIndex(p => p.id === id)
        if (packIndex !== -1) {
          coursePacks.value[packIndex] = {
            ...coursePacks.value[packIndex],
            isPinned: false,
          }
        }
      } catch (error) {
        console.error('取消置顶课程包失败:', error)
        throw error
      }
    }

    function updateSearchQuery(query: string) {
      searchQuery.value = query
    }

    function sortItems(items: CoursePack[]) {
      return [...items].sort((a, b) => {
        const compareResult = a.position - b.position
        return sortDirection.value === 'asc' ? compareResult : -compareResult
      })
    }

    function toggleSortDirection() {
      sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
    }

    function updateFilterStatus(status: 'all' | 'published' | 'unpublished') {
      filterStatus.value = status
    }

    return {
      isInitialized,
      coursePacks,
      searchQuery,
      filterStatus,
      sortDirection,
      pinnedCoursePacks,
      unpinnedCoursePacks,
      publishedCoursePacks,
      filteredPinnedCoursePacks,
      filteredUnpinnedCoursePacks,
      init,
      updateCoursePacks,
      createCoursePack,
      deleteCoursePack,
      pinCoursePack,
      unpinCoursePack,
      updateSearchQuery,
      toggleSortDirection,
      updateFilterStatus,
    }
  },
  {
    persist: {
      pick: ['sortDirection', 'filterStatus'],
    },
  },
)
