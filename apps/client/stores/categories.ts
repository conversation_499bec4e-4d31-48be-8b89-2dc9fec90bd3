import type { Category } from '~/api/categories'
import { defineStore } from 'pinia'
import { computed, readonly, ref } from 'vue'
import { categoriesApi } from '~/api/categories'

export const useCategoriesStore = defineStore('categories', () => {
  const categories = ref<Category[]>([])
  const isLoading = ref(false)
  const isLoaded = ref(false)

  // 获取所有分类
  async function fetchCategories() {
    if (isLoaded.value)
      return categories.value

    try {
      isLoading.value = true
      const data = await categoriesApi.fetchCategories()
      categories.value = data
      isLoaded.value = true
      return data
    } catch (error) {
      console.error('获取分类失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 根据ID获取分类
  function getCategoryById(categoryId: string) {
    return categories.value.find(category => category.id === categoryId)
  }

  // 根据value获取分类
  function getCategoryByValue(categoryValue: string) {
    return categories.value.find(category => category.value === categoryValue)
  }

  // 兼容性：根据key获取分类
  function getCategoryByKey(categoryKey: string) {
    return getCategoryByValue(categoryKey)
  }

  // 获取根分类（第一层）
  const rootCategories = computed(() => {
    return categories.value
      .filter(cat => !cat.parentId)
      .sort((a, b) => a.sortOrder - b.sortOrder)
  })

  // 根据父ID获取子分类
  function getChildCategories(parentId: string) {
    return categories.value
      .filter(cat => cat.parentId === parentId)
      .sort((a, b) => a.sortOrder - b.sortOrder)
  }

  // 获取分类的完整路径
  function getCategoryPath(categoryId: string): Category[] {
    const path: Category[] = []
    let current = categories.value.find(cat => cat.id === categoryId) || null

    while (current) {
      path.unshift(current)
      current = current.parentId ? categories.value.find(cat => cat.id === current!.parentId) || null : null
    }

    return path
  }

  // 重置状态
  function reset() {
    categories.value = []
    isLoading.value = false
    isLoaded.value = false
  }

  return {
    categories: readonly(categories),
    rootCategories: readonly(rootCategories),
    isLoading: readonly(isLoading),
    isLoaded: readonly(isLoaded),
    fetchCategories,
    getCategoryById,
    getCategoryByValue,
    getCategoryByKey,
    getChildCategories,
    getCategoryPath,
    reset,
  }
})

// 重新导出Category类型以保持向后兼容
export type { Category } from '~/api/categories'
