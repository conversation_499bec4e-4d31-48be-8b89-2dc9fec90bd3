import type {
  Course,
  Element as ElementApiResponse,
  ElementImage,
  ElementProps,
  ImageSource,
  ImageStyle,
  LearningContent,
  Sentence as SentenceApiResponse,
  WordDetail,
} from '@julebu/shared'
import Fuse from 'fuse.js'
import { defineStore } from 'pinia'
import { createImageWithTextLink } from '~/api/image'
import { generatePhonetic } from '~/services/phonetic'
import { useGameUpdateStore } from '~/stores/gameUpdate'
import { getImageStats, uploadImageFileToS3 } from '~/utils/file'
import { useCoursePackStore } from './coursePack'

class Element {
  id: string
  content: string
  english: string
  chinese: string
  phonetic: string
  image: ElementImage | null

  constructor(data: ElementApiResponse) {
    this.id = data.id
    this.content = data.content
    this.english = data.english
    this.chinese = data.chinese
    this.phonetic = data.phonetic
    this.image = data.image
  }

  setProps(data: ElementApiResponse) {
    this.content = data.content
    this.english = data.english
    this.chinese = data.chinese
    this.phonetic = data.phonetic
  }

  async changeElementProp(key: ElementProps, val: string) {
    const { $trpc } = useNuxtApp()
    if (Reflect.get(this, key) !== val) {
      Reflect.set(this, key, val)
      await $trpc.element.updateProp.mutate({
        elementId: this.id,
        key,
        val,
      })
    }
  }

  async process() {
    const { $trpc } = useNuxtApp()
    const result = await $trpc.element.processOne.mutate({
      elementId: this.id,
    })

    if (result) {
      this.chinese = result.chinese
      this.english = result.english
      this.phonetic = result.phonetic
    }
  }
}

class Sentence {
  id: string
  content: string
  chinese: string
  learningContent: LearningContent | null
  courseId: string
  elements: Element[]
  position: number
  selectedElement: Element | null
  gameUpdateStore = useGameUpdateStore()
  wordDetails: WordDetail[]

  constructor(data: SentenceApiResponse) {
    this.id = data.id
    this.content = data.content
    this.chinese = data.chinese
    this.learningContent = data.learningContent
    this.courseId = data.courseId
    this.position = data.position
    this.wordDetails = data.wordDetails || []
    this.selectedElement = null
    this.elements = []
    this.setElementsFromApiResponse(data.elements)
  }

  async clearElements() {
    const { $trpc } = useNuxtApp()
    const result = await $trpc.sentence.clearOne.mutate({
      courseId: this.courseId,
      sentenceId: this.id,
    })

    if (result) {
      this.elements = []
      this.gameUpdateStore.markCourseForUpdate()
    }
  }

  isEmpty() {
    return this.elements.length === 0
  }

  async generatePhonetic() {
    await generatePhonetic(this.content)
    this.gameUpdateStore.markCourseForUpdate()
  }

  async split() {
    const { $trpc } = useNuxtApp()
    const { elements, chinese } = await $trpc.sentence.splitOne.mutate({
      courseId: this.courseId,
      sentenceId: this.id,
    })

    this.setElementsFromApiResponse(elements)
    this.chinese = chinese
    this.gameUpdateStore.markCourseForUpdate()
  }

  async changeContent(newContent: string) {
    const { $trpc } = useNuxtApp()
    const result = await $trpc.sentence.changeContent.mutate({
      sentenceId: this.id,
      content: newContent,
    })

    if (result) {
      this.content = newContent
      this.elements = []
      this.gameUpdateStore.markCourseForUpdate()
    }
  }

  async changeChinese(newChinese: string) {
    const { $trpc } = useNuxtApp()
    const result = await $trpc.sentence.changeChinese.mutate({
      sentenceId: this.id,
      chinese: newChinese,
    })

    if (result) {
      this.chinese = newChinese
      this.elements = []
      this.gameUpdateStore.markCourseForUpdate()
    }
  }

  async changeImage({
    file,
    elementId,
    description,
    source,
    style,
  }: {
    file: File
    elementId: string
    description: string
    source: ImageSource
    style?: ImageStyle
  }) {
    const element = this.findElementById(elementId)

    if (!element) {
      throw new Error('Element not found')
    }

    const fileKey = await uploadImageFileToS3(file)
    const { width, height, fileSize, mimeType } = await getImageStats(file)

    const imageId = await createImageWithTextLink({
      // for image
      fileKey,
      width,
      height,
      fileSize,
      mimeType,
      description,
      source,
      styleTags: style ? [style] : undefined,
      categoryTags: [],
      // for text image link
      textContent: element.content,
      linkType: 'image',
    })

    await this.changeElementProp(element.id, 'imageId', imageId)

    element.image = {
      width,
      height,
      fileKey,
      description,
    }
  }

  async removeImage(elementId: string) {
    await this.changeElementProp(elementId, 'imageId', '')
    const element = this.findElementById(elementId)!
    element.image = null
  }

  async generateLearningContent() {
    const { $trpc } = useNuxtApp()
    const learningContent
      = await $trpc.sentence.generateLearningContentOne.mutate({
        sentenceId: this.id,
      })

    if (learningContent) {
      this.learningContent = learningContent
      this.gameUpdateStore.markCourseForUpdate()
    }
  }

  async deleteLearningContent() {
    const { $trpc } = useNuxtApp()
    await $trpc.sentence.deleteLearningContent.mutate({
      sentenceId: this.id,
    })

    this.learningContent = null
    this.gameUpdateStore.markCourseForUpdate()
  }

  setElementsFromApiResponse(elements: ElementApiResponse[]) {
    this.elements = elements.map(info => reactive(new Element(info)))
  }

  async process() {
    const { $trpc } = useNuxtApp()
    const result = await $trpc.sentence.processOne.mutate({
      courseId: this.courseId,
      sentenceId: this.id,
    })

    this.chinese = result?.chinese || ''
    this.gameUpdateStore.markCourseForUpdate()

    if (result?.elements && result.elements.length > 0) {
      if (this.elements.length > 0) {
        result.elements.forEach((element) => {
          const updatedElement = this.findElementById(element.id)
          if (updatedElement) {
            updatedElement.setProps(element)
          }
        })
      } else {
        this.setElementsFromApiResponse(result.elements)
      }
    }
  }

  findElementById(elementId: string) {
    return this.elements.find(element => element.id === elementId)
  }

  async moveElementPosition(targetPosition: number, sourcePosition: number) {
    const { $trpc } = useNuxtApp()
    const start = Math.min(targetPosition, sourcePosition)
    const end = Math.max(targetPosition, sourcePosition)
    const affectedElements = this.elements.slice(start, end + 1)
    const affectedElementsWithIndex = affectedElements.map(
      (element, index) => ({
        id: element.id,
        position: start + index + 1, // 从 1 开始
      }),
    )

    await $trpc.element.movePosition.mutate({
      sentenceId: this.id,
      affectedElements: affectedElementsWithIndex,
    })
    this.gameUpdateStore.markCourseForUpdate()
  }

  async deleteElement(elementId: string) {
    const { $trpc } = useNuxtApp()
    this.elements = this.elements.filter(element => element.id !== elementId)

    await $trpc.element.deleteOne.mutate({
      courseId: this.courseId,
      sentenceId: this.id,
      elementId,
    })
    this.gameUpdateStore.markCourseForUpdate()
  }

  insertElement(elementId: string, direction: 'above' | 'below') {
    const elementIndex = this.elements.findIndex((element) => {
      return element.id === elementId
    })

    const fakeElement = this.createFakeElement()

    if (elementIndex !== undefined) {
      const insertIndex
        = direction === 'below' ? elementIndex + 1 : elementIndex
      this.elements.splice(insertIndex, 0, fakeElement)

      return fakeElement
    }
  }

  createFakeElement(element?: Element) {
    if (element) {
      return reactive(
        new Element({
          id: '', // id 不可以 copy
          content: element.content,
          english: element.english,
          chinese: element.chinese,
          phonetic: element.phonetic,
          image: element.image,
        }),
      )
    } else {
      return reactive(
        new Element({
          id: '', // id 先是一个空的 等请求完后端后 在更新
          content: '我是新添加的',
          english: '',
          chinese: '',
          phonetic: '',
          image: null,
        }),
      )
    }
  }

  async insertElementAbove(elementId: string) {
    const { $trpc } = useNuxtApp()
    const fakeElement = this.insertElement(elementId, 'above')
    // 通知后端
    const newElement = await $trpc.element.insertAbove.mutate({
      courseId: this.courseId,
      sentenceId: this.id,
      elementId,
    })

    // 更新 elements 主要是前端生成的 element 没有 id
    if (fakeElement) {
      fakeElement.id = newElement.id
      this.gameUpdateStore.markCourseForUpdate()
      return newElement.id
    }
  }

  async insertElementBelow(elementId: string) {
    const { $trpc } = useNuxtApp()
    const fakeElement = this.insertElement(elementId, 'below')
    // 通知后端
    const newElement = await $trpc.element.insertBelow.mutate({
      courseId: this.courseId,
      sentenceId: this.id,
      elementId,
    })

    // 更新 elements 主要是前端生成的 element 没有 id
    if (fakeElement) {
      fakeElement.id = newElement.id
      this.gameUpdateStore.markCourseForUpdate()

      return newElement.id
    }
  }

  async copyElement(elementId: string) {
    const { $trpc } = useNuxtApp()
    const element = this.findElementById(elementId)
    const fakeElement = this.createFakeElement(element)
    // 通知后端
    const newElement = await $trpc.element.copyOne.mutate({
      courseId: this.courseId,
      sentenceId: this.id,
      elementId,
    })

    // 更新 elements 主要是前端生成的 element 没有 id
    if (fakeElement) {
      fakeElement.id = newElement.id
      this.gameUpdateStore.markCourseForUpdate()
    }

    const elementIndex = this.elements.findIndex((element) => {
      return element.id === elementId
    })

    this.elements.splice(elementIndex + 1, 0, fakeElement)

    return newElement.id
  }

  async processElement(elementId: string) {
    const element = this.findElementById(elementId)
    if (element) {
      await element.process()
      this.gameUpdateStore.markCourseForUpdate()
    }
  }

  async changeElementProp(elementId: string, key: ElementProps, val: string) {
    const element = this.findElementById(elementId)
    if (element) {
      await element.changeElementProp(key, val)
      this.gameUpdateStore.markCourseForUpdate()
    }
  }

  // 是否还需要加工
  needsProcessing() {
    // 没有元素加工会直接把句子的content 作为练习元素
    // 或者 有元素没有中文 或者 有元素没有音标
    return (
      this.elements.length === 0
      || this.elements.some(element => !element.chinese || !element.phonetic)
    )
  }

  async updateWordDetail(
    word: string,
    update: { pos?: string, definition?: string },
  ) {
    const { $trpc } = useNuxtApp()
    const result = await $trpc.sentence.updateWordDetail.mutate({
      sentenceId: this.id,
      word,
      ...update,
    })

    if (result) {
      const wordDetailIndex = this.wordDetails.findIndex(
        detail => detail.word === word,
      )
      if (wordDetailIndex !== -1) {
        this.wordDetails[wordDetailIndex] = {
          ...this.wordDetails[wordDetailIndex],
          ...update,
        }
      }
      this.gameUpdateStore.markCourseForUpdate()
    }
  }

  async analyzeSentence() {
    const { $trpc } = useNuxtApp()
    const result = await $trpc.sentence.analyze.mutate({
      sentenceId: this.id,
    })

    if (result) {
      this.wordDetails = result.wordDetails
      this.gameUpdateStore.markCourseForUpdate()
    }
  }
}

const sentenceFuseOptions = {
  keys: ['content', 'chinese'],
  threshold: 0.4,
  distance: 100,
}

// 管理 course 本身 以及管理 course 下的 sentences
export const useCourseStore = defineStore('course', () => {
  const { $trpc } = useNuxtApp()
  const coursePackStore = useCoursePackStore()
  const gameUpdateStore = useGameUpdateStore()
  const currentCourse = ref<Course>()
  const searchSentences = ref<Sentence[]>([])
  const searchSentencesQuery = ref('')
  const sentencesSortDirection = ref<'asc' | 'desc'>('asc')

  const sentences = ref<Sentence[]>([])
  const selectedSentence = ref<Sentence | null>(null)

  // 使用 computed 创建 fuse 实例
  const fuse = computed(() => new Fuse(sentences.value, sentenceFuseOptions))

  // 修改 watchEffect，使用 computed 的 fuse
  watchEffect(() => {
    let result = [...sentences.value]

    if (searchSentencesQuery.value && result.length > 0) {
      const searchResults = fuse.value.search(searchSentencesQuery.value)
      result = searchResults.map(result => result.item)
    }

    // 处理排序
    result.sort((a, b) => {
      const comparison = a.position - b.position
      return sentencesSortDirection.value === 'asc' ? comparison : -comparison
    })

    searchSentences.value = result
  })

  function updateSentencesSortDirection(direction: 'asc' | 'desc') {
    sentencesSortDirection.value = direction
    // 直接对 sentences 进行排序
    sentences.value.sort((a, b) => {
      const comparison = a.position - b.position
      return direction === 'asc' ? comparison : -comparison
    })
  }

  // 共用的数据设置逻辑
  function setCourseData(courseData: Course) {
    currentCourse.value = {
      ...courseData,
      sentences: courseData.sentences.map(sentence => ({
        ...sentence,
        isPass: false,
        isCollapsed: false,
      })),
    }

    sentences.value = courseData.sentences.map((sentence) => {
      return reactive(new Sentence(sentence))
    })

    // 应用当前排序
    updateSentencesSortDirection(sentencesSortDirection.value)
  }

  async function init(coursePackId: string, courseId: string, forceRefresh = false) {
    if (currentCourse.value?.id === courseId && !forceRefresh)
      return false

    const data = await $trpc.course.findOne.query({ courseId, coursePackId })
    setCourseData(data)

    return true
  }

  // 直接更新课程数据（不重新请求接口）
  function updateCourseData(courseData: Course) {
    setCourseData(courseData)
  }

  const totalElementsCount = computed(() => {
    let count = 0
    sentences.value.forEach((sentence) => {
      count += sentence.elements.length
    })
    return count
  })

  async function editCourse(title: string, description: string) {
    const result = await $trpc.course.edit.mutate({
      coursePackId: coursePackStore.currentCoursePack?.id || '',
      courseId: currentCourse.value?.id || '',
      title,
      description,
    })

    if (result) {
      if (currentCourse.value?.title) {
        currentCourse.value.title = title
      }

      if (currentCourse.value?.description) {
        currentCourse.value.description = description
      }

      // 还需要更新下 course pack
      // 只更新前端的数据就可以
      coursePackStore.updateCourseByCourseId(currentCourse.value?.id || '', {
        title,
        description,
      })

      gameUpdateStore.markCourseForUpdate()
    }
  }

  async function deleteCourse(courseId: string) {
    await $trpc.course.deleteOne.mutate({
      coursePackId: coursePackStore.currentCoursePack?.id || '',
      courseId,
    })

    await coursePackStore.updateCourses()

    gameUpdateStore.markCourseForUpdate()
  }

  async function publishToGame() {
    const result = await $trpc.course.publishToGame.mutate({
      coursePackId: coursePackStore.currentCoursePack?.id || '',
      courseId: currentCourse.value?.id || '',
    })

    if (currentCourse.value) {
      currentCourse.value.gameId = result.courseId
      await coursePackStore.updateCourses()
      gameUpdateStore.markCourseUpdated()
    }

    return result
  }

  async function unpublishFromGame() {
    const result = await $trpc.course.unpublishFromGame.mutate({
      coursePackId: coursePackStore.currentCoursePack?.id || '',
      courseId: currentCourse.value?.id || '',
    })

    if (result) {
      currentCourse.value!.gameId = ''
      await coursePackStore.updateCourses()
    }
  }

  async function updateToGame() {
    await $trpc.course.updateToGame.mutate({
      coursePackId: coursePackStore.currentCoursePack?.id || '',
      courseId: currentCourse.value?.id || '',
    })
  }

  function selectSentence(sentenceOrId: Sentence | string | null) {
    if (sentenceOrId === null && selectedSentence.value === null) {
      return
    }

    if (typeof sentenceOrId === 'string') {
      // 如果传入的是 id，则根据 id 查找对应的 sentence
      const sentence = sentences.value.find(s => s.id === sentenceOrId)
      selectedSentence.value = sentence || null
    } else {
      // 如果传入的是 sentence 对象或 null，直接赋值
      selectedSentence.value = sentenceOrId
    }
  }

  async function addSentences(content: string) {
    const parsedSentences = parseTextToSentences(content)

    const newSentenceInfos = await $trpc.sentence.createSentences.mutate({
      courseId: currentCourse.value!.id,
      sentences: parsedSentences,
    })

    sentences.value.push(
      ...newSentenceInfos.map((info, index) => {
        return reactive(
          new Sentence({
            id: info.id,
            content: parsedSentences[index].content,
            chinese: parsedSentences[index].chinese,
            elements: [],
            learningContent: null,
            courseId: currentCourse.value!.id,
            position: sentences.value.length,
            wordDetails: info.wordDetails,
            startTime: null,
            endTime: null,
          }),
        )
      }),
    )

    gameUpdateStore.markCourseForUpdate()
  }

  async function clearAllSentencesElements() {
    const result = await $trpc.sentence.clearAll.mutate({
      courseId: currentCourse.value!.id,
    })

    if (result) {
      sentences.value.forEach((sentence) => {
        sentence.elements = []
      })

      gameUpdateStore.markCourseForUpdate()
    }
  }

  async function deleteSentence(sentenceId: string) {
    sentences.value = sentences.value.filter((sentence) => {
      return sentence.id !== sentenceId
    })

    if (selectedSentence.value) {
      if (sentenceId === selectedSentence.value?.id) {
        selectSentence(null)
      }
    }

    await $trpc.sentence.deleteOne.mutate({
      courseId: currentCourse.value!.id,
      sentenceId,
    })

    gameUpdateStore.markCourseForUpdate()
  }

  function isEmpty() {
    return sentences.value.length === 0
  }

  function hasAllSentencesContent() {
    return sentences.value.every((sentence) => {
      return !sentence.isEmpty()
    })
  }

  function hasAllSentencesLearningContent() {
    return sentences.value.every((sentence) => {
      return sentence.learningContent !== null
    })
  }

  async function splitAllSentences() {
    const result = await $trpc.sentence.splitAll.mutate({
      courseId: currentCourse.value!.id,
    })

    result.forEach((newInfo) => {
      if (newInfo) {
        const { elements, sentenceId, chinese } = newInfo
        const sentence = findSentenceById(sentenceId)
        if (sentence) {
          sentence.setElementsFromApiResponse(elements)
          sentence.chinese = chinese
        }
      }
    })

    gameUpdateStore.markCourseForUpdate()
  }

  function findSentenceById(sentenceId: string) {
    return sentences.value.find((sentence) => {
      return sentence.id === sentenceId
    })
  }

  async function deleteAllSentences() {
    const oldSentencesValue = [...sentences.value]
    try {
      sentences.value = []
      selectSentence(null)
      await $trpc.sentence.deleteAll.mutate({
        courseId: currentCourse.value!.id,
      })
      gameUpdateStore.markCourseForUpdate()
    } catch {
      sentences.value = [...oldSentencesValue]
    }
  }

  async function moveSentencePosition(
    targetPosition: number,
    sourcePosition: number,
  ) {
    const start = Math.min(targetPosition, sourcePosition)
    const end = Math.max(targetPosition, sourcePosition)
    const affectedSentences = sentences.value.slice(start, end + 1)

    let affectedSentencesWithIndex
    if (sentencesSortDirection.value === 'asc') {
      affectedSentencesWithIndex = affectedSentences.map((sentence, index) => ({
        sentenceId: sentence.id,
        position: start + index + 1,
      }))
    } else {
      const totalLength = sentences.value.length - 1
      affectedSentencesWithIndex = affectedSentences.map((sentence, index) => ({
        sentenceId: sentence.id,
        position: totalLength - start - index + 1,
      }))
    }

    await $trpc.sentence.movePosition.mutate({
      courseId: currentCourse.value!.id,
      affectedSentences: affectedSentencesWithIndex,
    })

    gameUpdateStore.markCourseForUpdate()
  }

  async function processAllSentences() {
    const result = await $trpc.sentence.processAll.mutate({
      courseId: currentCourse.value!.id,
    })

    result.forEach((item) => {
      if (item) {
        const { sentenceId, elements, chinese } = item
        const sentence = findSentenceById(sentenceId)
        if (sentence) {
          sentence.setElementsFromApiResponse(elements)
          sentence.chinese = chinese
        }
      }
    })

    gameUpdateStore.markCourseForUpdate()
  }

  async function generateLearningAllSentences() {
    const result = await $trpc.sentence.generateLearningContentAll.mutate({
      courseId: currentCourse.value!.id,
    })

    result.forEach((item) => {
      const sentence = findSentenceById(item.id)
      if (sentence && item.learningContent) {
        sentence.learningContent = item.learningContent
      }
    })

    gameUpdateStore.markCourseForUpdate()

    // 这里返回一个失败的 sentence ids 让 ui 那边给出提示
    return {
      failedSentenceIds: result
        .filter(item => !item.learningContent)
        .map(item => item.id),
    }
  }

  function updateSentencesSearchQuery(query: string) {
    searchSentencesQuery.value = query
  }

  function cleanCurrentCourse() {
    currentCourse.value = undefined
    // 当 currentCourse 为 undefined 时 下次在进入到 course 页面会重新请求数据
  }

  // 升级课程
  async function upgradeCourse(courseId: string) {
    const { $trpc } = useNuxtApp()
    const coursePackStore = useCoursePackStore()
    const coursePackId = coursePackStore.currentCoursePack?.id

    if (!coursePackId) {
      throw new Error('课程包ID不存在')
    }

    const result = await $trpc.course.upgradeCourse.mutate({
      courseId,
      coursePackId,
    })

    // 如果升级成功，标记课程需要更新
    if (result.updatedSentences > 0) {
      const gameUpdateStore = useGameUpdateStore()
      gameUpdateStore.markCourseForUpdate()
    }

    return result
  }

  async function generateAudio() {
    const { $trpc } = useNuxtApp()
    if (!currentCourse.value?.id) {
      throw new Error('课程ID不存在')
    }

    return $trpc.tts.generateTTSForCourse.mutate({
      courseId: currentCourse.value.id,
    })
  }

  async function insertSentenceAbove(
    sentenceId: string,
    content: string = '请输入新句子',
    chinese: string = '',
  ) {
    const { $trpc } = useNuxtApp()
    if (!currentCourse.value?.id) {
      throw new Error('课程ID不存在')
    }

    const targetSentence = sentences.value.find(s => s.id === sentenceId)
    if (!targetSentence) {
      throw new Error('目标句子不存在')
    }

    const result = await $trpc.sentence.insertAbove.mutate({
      courseId: currentCourse.value.id,
      sentenceId,
      content,
      chinese,
    })

    if (result) {
      // 创建新的 Sentence 实例
      const newSentence = reactive(
        new Sentence({
          id: result.id,
          content,
          chinese,
          elements: [],
          learningContent: null,
          courseId: currentCourse.value.id,
          position: targetSentence.position,
          wordDetails: result.wordDetails || [],
          startTime: null,
          endTime: null,
        }),
      )

      // 1. 更新所有大于等于目标位置的句子的 position
      sentences.value.forEach((sentence) => {
        if (sentence.position >= targetSentence.position) {
          sentence.position += 1
        }
      })

      // 2. 在目标位置插入新句子
      const insertIndex = sentences.value.findIndex(s => s.id === sentenceId)
      sentences.value.splice(insertIndex, 0, newSentence)

      // 标记课程需要更新
      gameUpdateStore.markCourseForUpdate()

      return newSentence
    }
  }

  async function insertSentenceBelow(
    sentenceId: string,
    content: string = '请输入新句子',
    chinese: string = '',
  ) {
    const { $trpc } = useNuxtApp()
    if (!currentCourse.value?.id) {
      throw new Error('课程ID不存在')
    }

    const targetSentence = sentences.value.find(s => s.id === sentenceId)
    if (!targetSentence) {
      throw new Error('目标句子不存在')
    }

    const result = await $trpc.sentence.insertBelow.mutate({
      courseId: currentCourse.value.id,
      sentenceId,
      content,
      chinese,
    })

    if (result) {
      // 创建新的 Sentence 实例
      const newSentence = reactive(
        new Sentence({
          id: result.id,
          content,
          chinese,
          elements: [],
          learningContent: null,
          courseId: currentCourse.value.id,
          position: targetSentence.position + 1,
          wordDetails: result.wordDetails || [],
          startTime: null,
          endTime: null,
        }),
      )

      // 1. 更新所有大于目标位置的句子的 position
      sentences.value.forEach((sentence) => {
        if (sentence.position > targetSentence.position) {
          sentence.position += 1
        }
      })

      // 2. 在目标位置后插入新句子
      const insertIndex = sentences.value.findIndex(s => s.id === sentenceId)
      sentences.value.splice(insertIndex + 1, 0, newSentence)

      // 标记课程需要更新
      gameUpdateStore.markCourseForUpdate()

      return newSentence
    }
  }

  async function moveSentenceToTop(sentenceId: string) {
    const { $trpc } = useNuxtApp()
    if (!currentCourse.value?.id) {
      throw new Error('课程ID不存在')
    }

    const targetSentence = sentences.value.find(s => s.id === sentenceId)
    if (!targetSentence) {
      throw new Error('目标句子不存在')
    }

    // 如果已经在顶部，直接返回
    if (targetSentence.position === 1) {
      return
    }

    const result = await $trpc.sentence.moveToTop.mutate({
      courseId: currentCourse.value.id,
      sentenceId,
    })

    if (result) {
      // 更新所有受影响的句子的 position
      sentences.value.forEach((sentence) => {
        if (sentence.position < targetSentence.position) {
          sentence.position += 1
        }
      })
      targetSentence.position = 1

      // 重新排序数组
      sentences.value.sort((a, b) => a.position - b.position)

      // 标记课程需要更新
      gameUpdateStore.markCourseForUpdate()
    }
  }

  async function moveSentenceToBottom(sentenceId: string) {
    const { $trpc } = useNuxtApp()
    if (!currentCourse.value?.id) {
      throw new Error('课程ID不存在')
    }

    const targetSentence = sentences.value.find(s => s.id === sentenceId)
    if (!targetSentence) {
      throw new Error('目标句子不存在')
    }

    // 如果已经在底部，直接返回
    if (targetSentence.position === sentences.value.length) {
      return
    }

    const result = await $trpc.sentence.moveToBottom.mutate({
      courseId: currentCourse.value.id,
      sentenceId,
    })

    if (result) {
      // 更新所有受影响的句子的 position
      sentences.value.forEach((sentence) => {
        if (sentence.position > targetSentence.position) {
          sentence.position -= 1
        }
      })
      targetSentence.position = sentences.value.length

      // 重新排序数组
      sentences.value.sort((a, b) => a.position - b.position)

      // 标记课程需要更新
      gameUpdateStore.markCourseForUpdate()
    }
  }

  return {
    currentCourse,
    totalElementsCount,
    editCourse,
    init,
    deleteCourse,
    sentences,
    searchSentences,
    selectedSentence,
    searchSentencesQuery,
    findSentenceById,
    selectSentence,
    addSentences,
    clearAllSentencesElements,
    deleteSentence,
    isEmpty,
    hasAllSentencesContent,
    hasAllSentencesLearningContent,
    splitAllSentences,
    deleteAllSentences,
    moveSentencePosition,
    processAllSentences,
    generateLearningAllSentences,
    updateSentencesSearchQuery,
    updateSentencesSortDirection,
    publishToGame,
    unpublishFromGame,
    cleanCurrentCourse,
    updateToGame,
    upgradeCourse,
    generateAudio,
    insertSentenceAbove,
    insertSentenceBelow,
    moveSentenceToTop,
    moveSentenceToBottom,
    updateCourseData,
  }
})
