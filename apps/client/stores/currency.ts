import type { CurrencyTransaction } from '~/api/currency'
import { defineStore } from 'pinia'
import { currencyApi } from '~/api/currency'

/**
 * 货币相关状态管理
 */
export const useCurrencyStore = defineStore('currency', () => {
  // 状态定义
  const balance = ref(0)
  const transactions = ref<CurrencyTransaction[]>([])
  const cycleStartDate = ref<string | null>(null)
  const cycleEndDate = ref<string | null>(null)

  // 计算属性
  const totalSpent = computed(() => {
    return transactions.value
      .filter(t => t.amount < 0)
      .reduce((sum, t) => sum + t.amount, 0)
  })

  const formattedBalance = computed(() => {
    return balance.value.toString().replace(/\B(?=(?:\d{3})+(?!\d))/g, ',')
  })

  /**
   * 获取当前钻石余额
   */
  async function fetchBalance() {
    try {
      const result = await currencyApi.getBalance()
      balance.value = result.balance
      return result
    } catch (error) {
      console.error('获取钻石余额失败:', error)
      throw error
    }
  }

  /**
   * 获取当前账单周期的交易记录
   */
  async function fetchCurrentCycleTransactions() {
    try {
      const result = await currencyApi.getCurrentCycleTransactions()
      transactions.value = result.transactions
      cycleStartDate.value = result.cycleStartDate
      cycleEndDate.value = result.cycleEndDate
      return result
    } catch (error) {
      console.error('获取交易记录失败:', error)
      transactions.value = []
      cycleStartDate.value = null
      cycleEndDate.value = null
      throw error
    }
  }

  /**
   * 初始化货币数据
   */
  async function initialize() {
    return Promise.all([
      fetchBalance(),
      fetchCurrentCycleTransactions(),
    ])
  }

  return {
    // 状态
    balance,
    transactions,
    cycleStartDate,
    cycleEndDate,

    // 计算属性
    totalSpent,
    formattedBalance,

    // 方法
    fetchBalance,
    fetchCurrentCycleTransactions,
    initialize,
  }
})
