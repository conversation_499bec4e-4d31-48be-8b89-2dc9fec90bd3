import Fuse from 'fuse.js'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export interface InboxItem {
  id: string
  content: string
  remark?: string
  tags: string[]
  sourceUrl?: string
  createdAt: string
}

// Fuse.js 配置
const fuseOptions = {
  keys: ['content', 'remark', 'tags'],
  threshold: 0.4,
  includeMatches: true,
  useExtendedSearch: true,
}

export const useInboxStore = defineStore('inbox', () => {
  const { $trpc } = useNuxtApp()
  const items = ref<InboxItem[]>([])
  const searchQuery = ref('')
  const sortDirection = ref<'asc' | 'desc'>('asc')
  const fuse = ref<Fuse<InboxItem> | null>(null)

  async function init() {
    const data = await $trpc.inbox.findAll.query()
    items.value = data
    if (items.value.length > 0) {
      fuse.value = new Fuse(items.value, fuseOptions)
    }
  }

  // 切换排序方向
  function toggleSort() {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
  }

  // 过滤和排序后的列表
  const filteredItems = computed(() => {
    let result = [...items.value]

    // 搜索过滤
    if (searchQuery.value && fuse.value) {
      const searchStr = searchQuery.value.toLowerCase()

      // 检查是否是标签搜索 (以 # 开头)
      if (searchStr.startsWith('#')) {
        const tagQuery = searchStr.slice(1)
        result = result.filter(item =>
          item.tags.some(tag => tag.toLowerCase().includes(tagQuery)),
        )
      } else {
        // 普通搜索
        const searchResults = fuse.value.search(searchStr)
        result = searchResults.map(res => res.item)
      }
    }

    // 排序
    if (sortDirection.value === 'desc') {
      result.reverse()
    }

    return result
  })

  async function deleteItem(id: string) {
    await $trpc.inbox.deleteOne.mutate({ id })
    items.value = items.value.filter(item => item.id !== id)
  }

  async function batchDelete(ids: string[]) {
    await $trpc.inbox.batchDelete.mutate({ ids })
    items.value = items.value.filter(item => !ids.includes(item.id))
  }

  async function deleteAllItems() {
    await $trpc.inbox.deleteAll.mutate()
    items.value = []
  }

  async function addItems(
    courseId: string,
    inboxItems: InboxItem[],
  ) {
    await $trpc.sentence.createSentences.mutate({
      courseId,
      sentences: inboxItems.map(item => ({
        content: item.content.trim(),
        chinese: '',
      })),
    })

    void batchDelete(inboxItems.map(item => item.id))
  }

  async function addItem(courseId: string, item: InboxItem) {
    await $trpc.sentence.createSentences.mutate({
      courseId,
      sentences: [
        {
          content: item.content.trim(),
          chinese: '',
        },
      ],
    })

    void deleteItem(item.id)
  }

  return {
    items,
    searchQuery,
    sortDirection,
    filteredItems,
    init,
    toggleSort,
    deleteItem,
    batchDelete,
    deleteAllItems,
    addItems,
    addItem,
  }
})
