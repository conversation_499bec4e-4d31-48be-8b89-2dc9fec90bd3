import type {
  CoursePackShareLevel,
  CoursePackWithCourses,
  CourseType,
} from '@julebu/shared'
import Fuse from 'fuse.js'
import { find, map } from 'lodash-es'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { useGameUpdateStore } from './gameUpdate'

// 保留课程搜索的 Fuse 配置
const coursesFuseOptions = {
  keys: ['title', 'description'],
  threshold: 0.3,
  location: 0,
  distance: 100,
  minMatchCharLength: 1,
  shouldSort: true,
  includeScore: true,
  useExtendedSearch: true,
}

export const useCoursePackStore = defineStore('coursePack', () => {
  const { $trpc } = useNuxtApp()
  const gameUpdateStore = useGameUpdateStore()
  const currentCoursePack = ref<CoursePackWithCourses>()

  // 新增：原始课程数据
  const originalCourses = ref<CoursePackWithCourses['courses']>([])
  // 新增：处理后的课程数据（用于展示和修改）
  const courses = ref<CoursePackWithCourses['courses']>([])
  const searchCourses = ref<CoursePackWithCourses['courses']>([])
  const searchCoursesQuery = ref('')
  const sortDirection = ref<'asc' | 'desc'>('asc')

  // 使用 computed 创建 fuse 实例
  const fuse = computed(() => new Fuse(courses.value, coursesFuseOptions))

  // 添加 filterStatus 状态
  const filterStatus = ref<'all' | 'published' | 'unpublished'>('all')

  // 处理非搜索的数据源
  watchEffect(() => {
    // 1. 从原始数据开始
    let result = [...originalCourses.value]

    // 2. 应用排序 - 基于 position
    result.sort((a, b) => {
      const comparison = a.position - b.position
      return sortDirection.value === 'asc' ? comparison : -comparison
    })

    // 3. 应用过滤
    if (filterStatus.value === 'published') {
      result = result.filter(course => !!course.gameId)
    } else if (filterStatus.value === 'unpublished') {
      result = result.filter(course => !course.gameId)
    }

    // 4. 更新 courses
    courses.value = result
  })

  // 处理搜索结果
  watchEffect(() => {
    // 1. 从原始数据开始
    let result = [...originalCourses.value]

    // 2. 应用搜索
    if (searchCoursesQuery.value && result.length > 0) {
      const searchResults = fuse.value.search(searchCoursesQuery.value)
      result = searchResults.map(result => result.item)
    }

    // 3. 应用排序 - 基于 position
    result.sort((a, b) => {
      const comparison = a.position - b.position
      return sortDirection.value === 'asc' ? comparison : -comparison
    })

    // 4. 应用过滤
    if (filterStatus.value === 'published') {
      result = result.filter(course => !!course.gameId)
    } else if (filterStatus.value === 'unpublished') {
      result = result.filter(course => !course.gameId)
    }

    // 5. 更新搜索结果
    searchCourses.value = result
  })

  async function init(coursePackId: string) {
    if (currentCoursePack.value?.id === coursePackId)
      return false
    const data = await $trpc.coursePack.findOne.query({ coursePackId })
    currentCoursePack.value = data
    originalCourses.value = data.courses
    courses.value = [...data.courses]

    gameUpdateStore.initCoursePackUpdateState(coursePackId)
    return true
  }

  async function updateCourses() {
    const coursePackId = currentCoursePack.value?.id || ''
    const data = await $trpc.coursePack.findOne.query({ coursePackId })
    currentCoursePack.value = data
    originalCourses.value = data.courses
    courses.value = [...data.courses]
  }

  function toggleSortDirection() {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
  }

  // 添加更新搜索查询的方法
  function updateSearchQuery(query: string) {
    searchCoursesQuery.value = query
  }

  async function moveCoursePosition(
    targetPosition: number,
    sourcePosition: number,
  ) {
    const start = Math.min(targetPosition, sourcePosition)
    const end = Math.max(targetPosition, sourcePosition)
    const affectedCourses = courses.value.slice(start, end + 1)

    let affectedCoursesWithIndex
    if (sortDirection.value === 'asc') {
      affectedCoursesWithIndex = map(affectedCourses, (course, index: number) => ({
        courseId: course.id,
        position: start + index + 1,
      }))
    } else {
      const totalLength = courses.value.length - 1
      affectedCoursesWithIndex = map(affectedCourses, (course, index: number) => ({
        courseId: course.id,
        position: totalLength - start - index + 1,
      }))
    }

    await $trpc.course.movePosition.mutate({
      coursePackId: currentCoursePack.value?.id || '',
      affectedCourses: affectedCoursesWithIndex,
    })

    gameUpdateStore.markCoursePackForUpdate()
  }

  function updateCourseByCourseId(
    courseId: string,
    { title, description }: { title: string, description: string },
  ) {
    const course = find(courses.value, c => c.id === courseId)
    if (course) {
      course.title = title
      course.description = description
    }
  }

  async function createCourse(title: string, description: string, type: CourseType) {
    await $trpc.course.create.mutate({
      coursePackId: currentCoursePack.value?.id || '',
      title,
      description,
      type,
    })

    await updateCourses()
    gameUpdateStore.markCoursePackForUpdate()
  }

  async function deleteCourse(courseId: string) {
    await $trpc.course.deleteOne.mutate({
      coursePackId: currentCoursePack.value?.id || '',
      courseId,
    })

    await updateCourses()

    gameUpdateStore.markCoursePackForUpdate()
  }

  async function batchDeleteCourses(courseIds: string[]) {
    await $trpc.course.batchDelete.mutate({
      coursePackId: currentCoursePack.value?.id || '',
      courseIds,
    })
    await updateCourses()
  }

  async function deleteAllCourses() {
    await $trpc.course.deleteAll.mutate({
      coursePackId: currentCoursePack.value?.id || '',
    })
    await updateCourses()
  }

  async function deleteCoursePack() {
    await $trpc.coursePack.delete.mutate({
      coursePackId: currentCoursePack.value?.id || '',
    })
  }

  async function editCoursePack(
    title: string,
    description: string,
    cover: string,
    shareLevel: CoursePackShareLevel,
    categoryId?: string,
  ) {
    const coursePackId = currentCoursePack.value?.id || ''
    await $trpc.coursePack.edit.mutate({
      coursePackId,
      title,
      description,
      cover,
      shareLevel,
      categoryId,
    })

    if (currentCoursePack.value!.title !== title) {
      currentCoursePack.value!.title = title
    }
    if (currentCoursePack.value!.description !== description) {
      currentCoursePack.value!.description = description
    }
    if (currentCoursePack.value!.cover !== cover) {
      currentCoursePack.value!.cover = cover
    }
    if (currentCoursePack.value!.shareLevel !== shareLevel) {
      currentCoursePack.value!.shareLevel = shareLevel
    }
    if (currentCoursePack.value!.categoryId !== categoryId) {
      currentCoursePack.value!.categoryId = categoryId
    }

    gameUpdateStore.markCoursePackForUpdate()
  }

  async function publishToGame() {
    // 发布课程包
    const coursePackResult = await $trpc.coursePack.publishToGame.mutate({
      coursePackId: currentCoursePack.value?.id || '',
    })

    if (coursePackResult) {
      currentCoursePack.value!.gameId = coursePackResult.coursePackId
    }

    gameUpdateStore.markCoursePackUpdated()
    gameUpdateStore.markCurrentCoursesUpdated()
    const courseStore = useCourseStore()
    courseStore.cleanCurrentCourse()
  }

  async function unpublishFromGame() {
    // 1. 先遍历 courses  看看有没有 gameId
    // 有的话 说明需要删除
    for (const course of courses.value) {
      if (course.gameId) {
        const result = await $trpc.course.unpublishFromGame.mutate({
          coursePackId: currentCoursePack.value?.id || '',
          courseId: course.id,
        })
        if (result) {
          course.gameId = ''
        }
      }
    }
    // 2. 等到所有的 course 全部下架完成后 在调用 删除 coursePack 的接口
    const result = await $trpc.coursePack.unpublishFromGame.mutate({
      coursePackId: currentCoursePack.value?.id || '',
    })

    if (result) {
      currentCoursePack.value!.gameId = ''

      gameUpdateStore.markCoursePackForUpdate()
      gameUpdateStore.markCurrentCoursesForUpdate()
      const courseStore = useCourseStore()
      courseStore.cleanCurrentCourse()
    }
  }

  async function updateToGame() {
    // 更新课程包
    await $trpc.coursePack.updateToGame.mutate({
      coursePackId: currentCoursePack.value?.id || '',
    })

    const courseStore = useCourseStore()
    courseStore.cleanCurrentCourse()

    // 更新所有的课程
    // 基于 gameId 来判断是创建还是更新
    // 这里还应该看一看当前的 course 需不需要更新
    for (const course of courses.value) {
      if (course.gameId) {
        if (gameUpdateStore.needsCourseUpdate(course.id)) {
          // 需要更新
          await $trpc.course.updateToGame.mutate({
            coursePackId: currentCoursePack.value?.id || '',
            courseId: course.id,
          })
        }
      } else {
        // 需要创建
        const result = await $trpc.course.publishToGame.mutate({
          coursePackId: currentCoursePack.value?.id || '',
          courseId: course.id,
        })

        course.gameId = result.courseId
      }
    }
  }

  function cleanSearchCoursesQuery() {
    searchCoursesQuery.value = ''
  }

  // 添加更新过滤状态的方法
  function updateFilterStatus(status: 'all' | 'published' | 'unpublished') {
    filterStatus.value = status
  }

  // 添加检查课程包下所有课程是否需要升级的方法
  async function checkCoursesNeedUpgrade(coursePackId: string) {
    try {
      const result = await $trpc.coursePack.checkCoursesNeedUpgrade.query({
        coursePackId,
      })
      return result
    } catch (error) {
      console.error('检查课程包升级状态失败:', error)
      throw error
    }
  }

  return {
    currentCoursePack,
    courses, // 替换原来的 filteredCourses
    searchCoursesQuery,
    searchCourses,
    deleteAllCourses,
    updateSearchQuery,
    moveCoursePosition,
    updateCourseByCourseId,
    cleanSearchCoursesQuery,
    init,
    createCourse,
    deleteCourse,
    currentSortDirection: sortDirection,
    toggleSortDirection,
    batchDeleteCourses,
    deleteCoursePack,
    editCoursePack,
    updateCourses,
    publishToGame,
    unpublishFromGame,
    updateToGame,
    filterStatus,
    updateFilterStatus,
    checkCoursesNeedUpgrade,
  }
})
