// 管理员课程包列表项类型（参考管理员接口文档）
export interface AdminCoursePack {
  id: string
  title: string
  description: string
  isFree: boolean
  cover: string
  createdAt: string
  updatedAt: string
  gameId: string
  shareLevel: string
  categoryId?: string
  isPinned: boolean
  position: number
  userId: string
}

// 通用API响应类型
export interface BaseResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}

// 批量创建数据类型
export interface BatchCreateData {
  coursePackId: string
  title: string
  cover: string
  usedDefaultCover: boolean
  courses: Array<{
    courseId: string
    title: string
    sentenceCount: number
    elementCount: number
  }>
}

// 批量处理课程数据类型
export interface ProcessAllCoursesData {
  coursePackId: string
  totalCourses: number
  totalProcessedCount: number
  results: Array<{
    courseId: string
    courseTitle: string
    processedCount: number
    success: boolean
    error?: string
  }>
}

// 批量拆分课程数据类型
export interface SplitAllCoursesData {
  coursePackId: string
  totalCourses: number
  totalSentenceCount: number
  totalElementCount: number
  results: Array<{
    courseId: string
    courseTitle: string
    sentenceCount: number
    elementCount: number
    success: boolean
    error?: string
  }>
}

export interface CourseInPackResponse {
  id: string
  title: string
  description: string
  type: string
  mediaUrl: string
  gameId: string
  position: number
  createdAt: string
  updatedAt: string
}

export interface CoursePackWithCoursesResponse {
  id: string
  title: string
  description: string
  cover: string
  gameId: string
  shareLevel: string
  isPinned: boolean
  position: number
  isFree: boolean
  createdAt: string
  updatedAt: string
  courses: CourseInPackResponse[]
}

// 单个句子更新项
export interface SentenceUpsertItem {
  sentenceId?: string // 可选，如果提供则更新，否则创建
  content?: string // 英文内容（会触发重新生成wordDetails等）
  english?: string // 标准化英文内容（不会触发重新生成）
  chinese?: string // 中文翻译
}

// 批量创建或更新句子请求
export interface BatchUpsertSentencesRequest {
  courseId: string // 课程ID
  sentences: SentenceUpsertItem[] // 句子列表
}

// 句子操作结果
export interface SentenceOperationResult {
  sentenceId: string
  uuid: string
  chinese: string
  content: string
}

// 批量创建或更新句子响应数据
export interface BatchUpsertSentencesData {
  created: number // 创建的句子数量
  updated: number // 更新的句子数量
  sentences: SentenceOperationResult[]
}

// 批量创建或更新句子完整响应
export interface BatchUpsertSentencesResponse {
  success: boolean
  message: string // "成功处理 X 个新句子，更新 Y 个句子"
  data: BatchUpsertSentencesData
}

// 处理后的元素结构
export interface ProcessedElement {
  id: string
  content: string
  chinese: string
  phonetic: string
  english: string
  sentenceId: string
  partOfSpeech: string
  position: number
  image: null | string
}

// 处理后的句子结构
export interface ProcessedSentence {
  sentenceId: string
  elements: ProcessedElement[]
  chinese: string
}

// 批量处理句子响应数据
export interface BatchProcessSentencesData {
  courseId: string
  processedSentences: ProcessedSentence[]
}

// 批量处理句子完整响应
export interface BatchProcessSentencesResponse {
  success: boolean
  message: string
  data: BatchProcessSentencesData
}

// 发布课程到游戏端数据
export interface PublishCourseToGameData {
  courseId: string
  statements: string[]
  sentences: string[]
}
