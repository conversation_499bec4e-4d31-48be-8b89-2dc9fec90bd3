/**
 * 单个分块项接口
 */
export interface ChunkItem {
  /** 分块序号 */
  index: number
  /** 分块文本内容 */
  content: string
  /** 字符数 */
  charCount: number
  /** 句子数 */
  sentenceCount: number
  /** 分块中的行数 */
  lineCount: number
}

/**
 * 上下文信息接口
 */
export interface ChunkContext {
  /** 前文信息（前一块的最后几行） */
  previousContent: string[]
  /** 后文信息（后一块的前几行） */
  afterContent: string[]
}

/**
 * 分块配置接口
 */
export interface ChunkingConfig {
  /** 每个块的最大字符数 */
  chunkSize: number
  /** 每个块的最大句子数 */
  maxSentences: number
  /** 前文上下文行数 */
  previousLines: number
  /** 后文上下文行数 */
  afterLines: number
}

/**
 * 分块结果接口
 */
export interface ChunkingResult {
  /** 分块数组 */
  chunks: ChunkItem[]
  /** 上下文信息映射 */
  contexts: Map<number, ChunkContext>
  /** 统计信息 */
  statistics: {
    /** 总分块数量 */
    totalChunks: number
    /** 平均字符数 */
    averageCharCount: number
    /** 平均句子数 */
    averageSentenceCount: number
    /** 最大分块字符数 */
    maxCharCount: number
    /** 最小分块字符数 */
    minCharCount: number
  }
}

/**
 * 翻译任务结构
 */
export interface TranslationTask {
  /** 分块索引 */
  index: number
  /** 分块项 */
  chunk: ChunkItem
  /** 上下文信息 */
  context?: ChunkContext
  /** 目标语言 */
  targetLanguage: string
  /** 任务创建时间 */
  createdAt: number
}

/**
 * 翻译结果结构
 */
export interface TranslationResult {
  /** 分块索引 */
  index: number
  /** 原始文本 */
  sourceText: string
  /** 翻译文本 */
  translatedText: string
  /** 相似度分数 */
  similarity: number
  /** 翻译耗时(毫秒) */
  duration: number
  /** 重试次数 */
  retryCount: number
  /** 翻译状态 */
  status: 'success' | 'failed' | 'warning'
  /** 错误信息 */
  error?: string
}

/**
 * 翻译质量指标
 */
export interface TranslationQualityMetrics {
  /** 平均相似度 */
  averageSimilarity: number
  /** 最低相似度 */
  minSimilarity: number
  /** 最高相似度 */
  maxSimilarity: number
  /** 低质量翻译数量 */
  lowQualityCount: number
  /** 失败翻译数量 */
  failedCount: number
  /** 警告翻译数量 */
  warningCount: number
  /** 总翻译时长 */
  totalDuration: number
  /** 平均长度比例 */
  averageLengthRatio: number
}

/**
 * 重试配置参数
 */
export interface RetryConfig {
  /** 最大重试次数 */
  maxAttempts: number
  /** 重试延迟基数(毫秒) */
  delayBase: number
  /** 是否使用指数退避 */
  useExponentialBackoff: boolean
  /** 重试条件判断函数 */
  shouldRetry?: (error: Error) => boolean
}
