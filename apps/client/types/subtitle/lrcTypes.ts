export interface LrcMetadata {
  ti?: string // 标题
  ar?: string // 艺术家
  al?: string // 专辑
  by?: string // 编者
  offset?: number // 全局偏移量，秒
}

export interface LrcLine {
  time: number // 时间（秒）
  text: string // 歌词文本
  translationText?: string // 翻译文本（如果有）
  translationTime?: number // 翻译文本的时间（秒，可选）
}

export interface LrcParseResult {
  lrcLines: LrcLine[]
  plainTextLines: string[]
  metadata: LrcMetadata
}
