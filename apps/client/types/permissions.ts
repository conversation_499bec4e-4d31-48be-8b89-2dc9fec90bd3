/**
 * 权限配置接口
 */
export interface PermissionConfig {
  /** 授权用户ID列表字符串 */
  authUserIds: string
}

/**
 * 用户权限上下文接口
 */
export interface UserPermissionContext {
  /** 用户唯一标识符 */
  sub?: string
  /** 用户名 */
  username?: string
}

/**
 * 权限检查结果枚举
 */
export enum PermissionCheck {
  /** 有权限 */
  GRANTED = 'granted',
  /** 无权限 */
  DENIED = 'denied',
  /** 配置缺失 */
  CONFIG_MISSING = 'config_missing',
  /** 用户未登录 */
  USER_NOT_AUTHENTICATED = 'user_not_authenticated',
}
