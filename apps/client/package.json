{"name": "client", "version": "1.0.34", "private": true, "scripts": {"dev": "nuxt dev --port 3200", "start:prod": "env-cmd -f .env.prod node .output/server/index.mjs", "generate": "nuxt generate --dotenv .env.prod", "postinstall": "nuxt prepare ", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "nuxt preview", "test": "vitest", "release": "tsx --env-file=.env.prod scripts/release.ts"}, "dependencies": {"@formkit/auto-animate": "^0.8.2", "@julebu/shared": "workspace:*", "@logto/vue": "^2.2.13", "@pinia/nuxt": "^0.5.1", "@trpc/client": "^10.45.2", "@trpc/server": "^10.45.2", "@vicons/tabler": "^0.13.0", "@vueuse/core": "^13.0.0", "@vueuse/nuxt": "^10.10.0", "animejs": "^3.2.2", "cos-js-sdk-v5": "^1.8.6", "date-fns": "^4.1.0", "decimal.js-light": "^2.5.1", "fuse.js": "^7.0.0", "isomorphic-dompurify": "^2.24.0", "lodash-es": "^4.17.21", "naive-ui": "^2.38.2", "ofetch": "^1.4.1", "path-to-regexp": "^8.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^4.1.3", "splitpanes": "^4.0.3", "superjson": "^2.2.1", "trpc-nuxt": "^0.10.21", "uuid": "^11.0.2", "video.js": "^8.22.0", "vue-dompurify-html": "^5.3.0", "vue-draggable-plus": "^0.5.0", "vue-loading-overlay": "^6.0", "vue-sonner": "^1.2.5", "wavesurfer.js": "^7.9.5"}, "devDependencies": {"@iconify-json/heroicons": "^1.2.2", "@iconify-json/ph": "^1.2.2", "@nuxt/devtools": "^1.3.3", "@nuxt/eslint": "^0.3.13", "@nuxt/image": "^1.7.0", "@nuxt/ui": "^2.22.0", "@types/lodash-es": "^4.17.12", "@types/node": "^20.14.2", "@types/prettier": "^3.0.0", "@types/splitpanes": "^2.2.6", "@types/video.js": "^7.3.58", "autoprefixer": "^10.4.19", "code-inspector-plugin": "^0.20.12", "daisyui": "^4.12.2", "env-cmd": "^10.1.0", "nuxt": "^3.17.1", "postcss": "^8.4.38", "prettier": "^3.3.1", "tailwindcss": "^3.4.14", "tsx": "^4.17.0", "typescript": "^4.9.5", "vitest": "^1.6.0", "zod": "^3.23.8"}}