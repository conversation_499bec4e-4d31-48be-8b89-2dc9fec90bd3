<script setup lang="ts">
import { useHandleSignInCallback, useLogto } from '@logto/vue'

const userStore = useUserStore()
const logto = useLogto()

const { isLoading } = useHandleSignInCallback(async () => {
  const res = await logto.fetchUserInfo()
  await userStore.initUser(res!)

  // 登录成功后 直接进入编辑
  navigateTo('/dashboard')
})
</script>

<template>
  <div class="flex min-h-screen items-center justify-center bg-zinc-950">
    <div v-if="isLoading" class="text-center">
      <!-- Logo with animation -->
      <img
        src="/logo.svg"
        alt="Logo"
        class="mx-auto mb-8 h-12 animate-pulse"
      >
      <!-- 提示文字 -->
      <h2 class="text-xl font-medium text-white">
        正在登录中
      </h2>
      <p class="mt-2 text-sm text-zinc-400">
        请稍候，马上就好...
      </p>
    </div>
  </div>
</template>

<style scoped></style>
