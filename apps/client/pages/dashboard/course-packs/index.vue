<script setup lang="ts">
import { ref } from 'vue'
import LoadingScreen from '~/components/common/LoadingScreen.vue'
import { useCoursePacksStore } from '~/stores/coursePacks'

definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
})

const coursePacksStore = useCoursePacksStore()
const isLoading = ref(!coursePacksStore.isInitialized)

onMounted(async () => {
  try {
    await coursePacksStore.init()
  }
  finally {
    isLoading.value = false
  }
})

const isCreatorOpen = ref(false)
</script>

<template>
  <div class="flex h-full flex-col">
    <LoadingScreen :show="isLoading" />

    <div v-show="!isLoading" class="h-full">
      <div class="flex h-full flex-col">
        <div class="sticky top-0 z-10">
          <div class="mx-6 mb-2 mt-4 rounded-md bg-slate-800 p-4">
            <div class="flex items-center justify-between">
              <div class="flex w-full items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="flex items-center space-x-6">
                    <div
                      class="flex items-center space-x-4 rounded-lg bg-slate-700/30 px-4 py-2"
                    >
                      <div class="flex items-center">
                        <span class="text-sm text-slate-300">总课程包</span>
                        <span
                          class="mx-2 text-lg font-semibold text-purple-400"
                        >{{ coursePacksStore.coursePacks.length }}</span>
                      </div>
                      <div class="h-4 w-px bg-slate-600" />
                      <div class="flex items-center">
                        <span class="text-sm text-slate-300">已发布</span>
                        <span
                          class="mx-2 text-lg font-semibold text-purple-400"
                        >{{ coursePacksStore.publishedCoursePacks }}</span>
                      </div>
                    </div>
                  </div>

                  <div
                    class="flex items-center space-x-2 rounded-lg bg-slate-700/30 p-1"
                  >
                    <button
                      v-for="status in [
                        { value: 'all', label: '全部' },
                        { value: 'published', label: '已发布' },
                        { value: 'unpublished', label: '未发布' },
                      ]"
                      :key="status.value"
                      class="rounded-md px-3 py-1.5 text-sm transition-all duration-200"
                      :class="[
                        coursePacksStore.filterStatus === status.value
                          ? 'bg-purple-500 text-white'
                          : 'text-slate-300 hover:bg-slate-700/50',
                      ]"
                      @click="coursePacksStore.updateFilterStatus(status.value as 'all' | 'published' | 'unpublished')"
                    >
                      {{ status.label }}
                    </button>
                  </div>

                  <button
                    class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
                    @click="coursePacksStore.toggleSortDirection"
                  >
                    <UIcon
                      name="ph:sort-ascending"
                      class="size-5 text-purple-400 transition-transform duration-200"
                      :class="{
                        'rotate-180': coursePacksStore.sortDirection === 'desc',
                      }"
                    />
                    {{
                      coursePacksStore.sortDirection === "asc" ? "正序" : "倒序"
                    }}
                  </button>
                </div>

                <div class="flex items-center space-x-4">
                  <CoursePacksSearch />
                  <button
                    class="flex items-center rounded-md bg-purple-500 px-4 py-2 text-white transition-colors duration-200 hover:bg-purple-600"
                    @click="isCreatorOpen = true"
                  >
                    <UIcon name="ph:plus" class="mr-2 size-5" />
                    创建课程包
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="min-h-0 flex-1">
          <div class="h-full overflow-auto px-6 py-2">
            <div
              v-if="!coursePacksStore.coursePacks.length"
              class="flex h-full min-h-[500px] flex-col items-center justify-center"
            >
              <UIcon
                name="i-heroicons-folder-open"
                class="size-16 text-slate-600"
              />
              <p class="mt-4 text-slate-400">
                还没有创建任何课程包
              </p>
              <button
                class="mt-6 flex items-center gap-2 rounded-lg bg-purple-500 px-4 py-2 text-white transition-all duration-200 hover:bg-purple-600 active:scale-95"
                @click="isCreatorOpen = true"
              >
                <UIcon name="ph:plus" class="size-5" />
                创建课程包
              </button>
            </div>

            <template v-else>
              <div
                v-if="coursePacksStore.filteredPinnedCoursePacks.length > 0"
                class="mb-6"
              >
                <h3 class="mb-3 text-sm font-medium text-slate-400">
                  📌 置顶课程包
                </h3>
                <div class="grid gap-4">
                  <CoursePacksItem
                    v-for="pack in coursePacksStore.filteredPinnedCoursePacks"
                    :key="pack.id"
                    :pack="pack"
                  />
                </div>
              </div>

              <div class="grid gap-4">
                <CoursePacksItem
                  v-for="pack in coursePacksStore.filteredUnpinnedCoursePacks"
                  :key="pack.id"
                  :pack="pack"
                />
              </div>
            </template>
          </div>
        </div>

        <CoursePacksCreator v-model="isCreatorOpen" />
      </div>
    </div>
  </div>
</template>

<style scoped></style>
