<script setup lang="ts">
import type { SortableEvent } from 'vue-draggable-plus'
import { CoursePackShareLevel } from '@julebu/shared'
import { useLoadingBar } from 'naive-ui'
import { ref } from 'vue'
import { VueDraggable } from 'vue-draggable-plus'
import { toast } from 'vue-sonner'
import Dialog from '~/components/common/Dialog.vue'
import LoadingScreen from '~/components/common/LoadingScreen.vue'
import CoursePackPublishModal from '~/components/course-pack/PublishModal.vue'
import { useCoursePackStore } from '~/stores/coursePack'
import { formatDate } from '~/utils/date'

definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
})

const loadingBar = useLoadingBar()
const route = useRoute()
const coursePackStore = useCoursePackStore()
const modal = useModal()
const router = useRouter()
const isLoading = ref(
  route.params.id !== coursePackStore.currentCoursePack?.id,
)
const coursePackId = route.params.id as string

onMounted(async () => {
  // 如果课程ID相同，直接返回
  if (coursePackStore.currentCoursePack?.id === coursePackId) {
    return
  }

  try {
    await coursePackStore.init(coursePackId)
  }
  finally {
    isLoading.value = false
  }
})

onUnmounted(() => {
  coursePackStore.cleanSearchCoursesQuery()
})

const shareLevel = computed(() => {
  const shareLevel = coursePackStore.currentCoursePack?.shareLevel
  if (shareLevel === CoursePackShareLevel.FounderOnly)
    return '会员共享'
  if (shareLevel === CoursePackShareLevel.Private)
    return '私有'
  return '公开'
})

// 使用 model 来控制 creator 的显示状态
const isCreatorOpen = ref(false)
const isEditorOpen = ref(false)

// 添加拖拽结束的处理函数
function handleDragEnd({ newIndex, oldIndex }: SortableEvent) {
  if (newIndex === undefined || oldIndex === undefined)
    return
  coursePackStore.moveCoursePosition(newIndex, oldIndex)
}

// 添加选择模式相关的状态
const isSelectionMode = ref(false)
const selectedCourses = ref<string[]>([])

// 切换选择模式
function toggleSelectionMode() {
  isSelectionMode.value = !isSelectionMode.value
  selectedCourses.value = [] // 清空选择
}

// 切换课程选择状态
function toggleCourseSelection(courseId: string) {
  const index = selectedCourses.value.indexOf(courseId)
  if (index === -1) {
    selectedCourses.value.push(courseId)
  }
  else {
    selectedCourses.value.splice(index, 1)
  }
}

// 批量删除处理函数
async function handleBatchDelete() {
  if (!selectedCourses.value.length)
    return

  modal.open(Dialog, {
    title: '批量删除课程',
    content: `确定要删除选中的 ${selectedCourses.value.length} 个课程吗？删除后将无法恢复。`,
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      try {
        const deleteCount = selectedCourses.value.length
        loadingBar.start()
        await coursePackStore.batchDeleteCourses(selectedCourses.value)
        loadingBar.finish()
        selectedCourses.value = [] // 清空选择
        isSelectionMode.value = false // 退出选择模式
        toast.success(`成功删除 ${deleteCount} 个课程`)
      }
      catch (error) {
        console.error('批量删除失败:', error)
        loadingBar.error()
        toast.error('批量删除课程失败，请重试')
      }
    },
  })
}

// 添加删除全部课程的处理函数
async function handleDeleteAll() {
  if (!coursePackStore.currentCoursePack?.courses?.length)
    return

  const courseCount = coursePackStore.currentCoursePack.courses.length
  modal.open(Dialog, {
    title: '删除全部课程',
    content: `确定要删除当前课程包中的所有课程吗？删除后将无法恢复。`,
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      try {
        loadingBar.start()
        await coursePackStore.deleteAllCourses()
        loadingBar.finish()
        toast.success(`成功删除 ${courseCount} 个课程`)
      }
      catch (error) {
        console.error('删除全部课程失败:', error)
        loadingBar.error()
        toast.error('删除全部课程失败，请重试')
      }
    },
  })
}

function handleDeleteCoursePack() {
  modal.open(Dialog, {
    title: '删除课程包',
    content: `确定要删除当前课程包吗？删除后将无法恢复。`,
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      if (coursePackStore.currentCoursePack?.gameId) {
        toast.warning('需要先下架 在删除哦')
        return
      }

      loadingBar.start()
      await coursePackStore.deleteCoursePack()
      loadingBar.finish()
      toast.success('课程包删除成功 即将跳转到课程包列表')
      await new Promise(resolve => setTimeout(resolve, 1000))
      router.push('/dashboard/course-packs')
    },
  })
}

// 添加状态控制变量
const isPublishModalOpen = ref(false)
function handlePublishCoursePack() {
  isPublishModalOpen.value = true
}
</script>

<template>
  <!-- 添加固定定位的容器 -->
  <div>
    <LoadingScreen :show="isLoading" />
    <div v-show="!isLoading" class="flex h-full flex-col">
      <!-- 固定的顶部操作栏 -->
      <div class="z-10">
        <div class="mx-6 mb-2 mt-4 rounded-md bg-slate-800 p-4">
          <div class="flex items-center justify-between">
            <div class="flex w-full items-center justify-between">
              <!-- 左侧按钮组 -->
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-6" />

                <div
                  class="flex items-center space-x-2 rounded-lg bg-slate-700/30 p-1"
                >
                  <button
                    v-for="status in [
                      { value: 'all', label: '全部' },
                      { value: 'published', label: '已发布' },
                      { value: 'unpublished', label: '未发布' },
                    ]"
                    :key="status.value"
                    class="rounded-md px-3 py-1.5 text-sm transition-all duration-200"
                    :class="[
                      coursePackStore.filterStatus === status.value
                        ? 'bg-purple-500 text-white'
                        : 'text-slate-300 hover:bg-slate-700/50',
                    ]"
                    @click="coursePackStore.updateFilterStatus(status.value as 'all' | 'published' | 'unpublished')"
                  >
                    {{ status.label }}
                  </button>
                </div>

                <button
                  class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
                  @click="coursePackStore.toggleSortDirection()"
                >
                  <UIcon
                    name="ph:sort-ascending"
                    class="size-5 text-purple-400 transition-transform duration-200"
                    :class="{
                      'rotate-180':
                        coursePackStore.currentSortDirection === 'desc',
                    }"
                  />
                  {{
                    coursePackStore.currentSortDirection === "asc"
                      ? "正序"
                      : "倒序"
                  }}
                </button>

                <!-- 添加删除全部按钮 -->
                <button
                  v-if="coursePackStore.currentCoursePack?.courses?.length"
                  class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-300 transition-all duration-200 hover:bg-slate-700/50"
                  @click="handleDeleteAll"
                >
                  <UIcon name="ph:trash" class="size-5" />
                  全部删除
                </button>

                <!-- 添加选择模式切换按钮 -->
                <button
                  class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50"
                  @click="toggleSelectionMode"
                >
                  <UIcon
                    :name="isSelectionMode ? 'ph:x' : 'ph:list-checks'"
                    class="size-5"
                  />
                  {{ isSelectionMode ? "退出选择" : "批量选择" }}
                </button>

                <!-- 显示删除按钮（仅在选择模式且有选中项时显示） -->
                <button
                  v-if="isSelectionMode && selectedCourses.length > 0"
                  class="inline-flex items-center gap-2 rounded-lg bg-red-500/20 px-4 py-2 text-red-400 transition-all duration-200 hover:bg-red-500/30"
                  @click="handleBatchDelete"
                >
                  <UIcon name="ph:trash" class="size-5" />
                  删除所选 ({{ selectedCourses.length }})
                </button>
              </div>

              <!-- 右侧搜索和创建 -->
              <div class="flex items-center space-x-4">
                <CoursePackCourseSearch />
                <button
                  class="flex items-center rounded-md bg-purple-500 px-4 py-2 text-white transition-colors duration-200 hover:bg-purple-600"
                  @click="isCreatorOpen = true"
                >
                  <UIcon name="ph:plus" class="mr-2 size-5" />
                  创建课程
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex-1">
        <div class="px-6 py-2">
          <div class="flex gap-3">
            <!-- 左侧课程包详情 - 移除 sticky 相关属性 -->
            <div class="w-96 shrink-0 transition-all duration-300">
              <div
                class="group overflow-hidden rounded-xl border border-slate-700/50 bg-slate-800/30 p-6 shadow-lg backdrop-blur-sm transition-all duration-300 hover:translate-y-[-2px] hover:border-slate-600/50 hover:bg-slate-700/40 hover:shadow-purple-500/20"
              >
                <div class="relative">
                  <NuxtImg
                    :src="coursePackStore.currentCoursePack?.cover"
                    :alt="coursePackStore.currentCoursePack?.title"
                    width="384"
                    height="216"
                    placeholder
                    class="aspect-video w-full rounded-lg object-cover shadow-md transition-all duration-300 group-hover:scale-[1.02] group-hover:shadow-lg"
                  />
                </div>

                <div class="mt-6 space-y-4">
                  <div>
                    <h2
                      class="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-2xl font-bold text-transparent transition-colors duration-300 group-hover:from-purple-300 group-hover:via-pink-300 group-hover:to-purple-300"
                    >
                      {{ coursePackStore.currentCoursePack?.title }}
                    </h2>
                    <p
                      class="mt-3 leading-relaxed text-slate-400 transition-colors duration-300 group-hover:text-slate-300"
                    >
                      {{ coursePackStore.currentCoursePack?.description }}
                    </p>
                  </div>

                  <div
                    class="space-y-3.5 rounded-lg bg-gradient-to-br from-slate-800/70 to-slate-800/40 p-5 ring-1 ring-slate-700/50 backdrop-blur-lg transition-all duration-300 group-hover:from-slate-800/80 group-hover:to-slate-800/50 group-hover:ring-slate-600/50"
                  >
                    <div class="flex items-center justify-between text-sm">
                      <div class="flex items-center gap-2">
                        <UIcon
                          name="i-heroicons-calendar"
                          class="size-4 text-slate-400"
                        />
                        <span class="font-medium text-slate-400">创建时间</span>
                      </div>
                      <span class="font-medium text-slate-300">{{
                        formatDate(
                          coursePackStore.currentCoursePack?.createdAt ?? "",
                        )
                      }}</span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                      <div class="flex items-center gap-2">
                        <UIcon
                          name="i-heroicons-clock"
                          class="size-4 text-slate-400"
                        />
                        <span class="font-medium text-slate-400">更新时间</span>
                      </div>
                      <span class="font-medium text-slate-300">{{
                        formatDate(
                          coursePackStore.currentCoursePack?.updatedAt ?? "",
                        )
                      }}</span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                      <div class="flex items-center gap-2">
                        <UIcon
                          name="i-heroicons-book-open"
                          class="size-4 text-slate-400"
                        />
                        <span class="font-medium text-slate-400">课程数量</span>
                      </div>
                      <span
                        class="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text font-semibold text-transparent transition-colors duration-300 group-hover:from-purple-300 group-hover:to-pink-300"
                      >
                        {{ coursePackStore.currentCoursePack?.courses.length }}
                      </span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                      <div class="flex items-center gap-2">
                        <UIcon
                          name="i-heroicons-signal"
                          class="size-4 text-slate-400"
                        />
                        <span class="font-medium text-slate-400">发布状态</span>
                      </div>
                      <span
                        class="rounded-full px-3.5 py-1.5 text-xs font-medium shadow-sm transition-all duration-300" :class="[
                          coursePackStore.currentCoursePack?.gameId
                            ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-400 ring-1 ring-green-500/30 group-hover:from-green-500/30 group-hover:to-emerald-500/30'
                            : 'bg-gradient-to-r from-amber-500/20 to-orange-500/20 text-amber-400 ring-1 ring-amber-500/30 group-hover:from-amber-500/30 group-hover:to-orange-500/30',
                        ]"
                      >
                        <div class="flex items-center gap-1.5">
                          <div
                            class="size-2 rounded-full" :class="[
                              coursePackStore.currentCoursePack?.gameId
                                ? 'animate-pulse bg-green-400 shadow-green-400/50'
                                : 'bg-amber-400 shadow-amber-400/50',
                            ]"
                          />
                          {{
                            coursePackStore.currentCoursePack?.gameId
                              ? "已发布"
                              : "未发布"
                          }}
                        </div>
                      </span>
                    </div>
                    <div class="flex items-center justify-between text-sm">
                      <div class="flex items-center gap-2">
                        <UIcon
                          name="i-heroicons-share"
                          class="size-4 text-slate-400"
                        />
                        <span class="font-medium text-slate-400">共享级别</span>
                      </div>
                      <span
                        class="rounded-full bg-gradient-to-r from-blue-500/20 to-indigo-500/20 px-3.5 py-1.5 text-xs font-medium text-blue-400 shadow-sm ring-1 ring-blue-500/30 transition-all duration-300 group-hover:from-blue-500/30 group-hover:to-indigo-500/30"
                      >
                        {{ shareLevel }}
                      </span>
                    </div>
                  </div>
                  <div class="flex gap-2">
                    <button
                      class="inline-flex flex-1 items-center justify-center rounded-lg bg-gradient-to-br from-slate-700/90 to-slate-800/90 px-4 py-2.5 text-sm font-medium text-slate-200 shadow-lg transition-all duration-300 hover:-translate-y-px hover:from-slate-600/90 hover:to-slate-700/90 hover:text-white hover:shadow-slate-500/30 active:scale-[0.98]"
                      @click="handleDeleteCoursePack"
                    >
                      <UIcon name="i-heroicons-trash" class="mr-1.5 size-4" />
                      删除
                    </button>
                    <button
                      class="inline-flex flex-1 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500/90 to-purple-600/90 px-4 py-2.5 text-sm font-medium text-white shadow-lg shadow-purple-500/20 transition-all duration-300 hover:-translate-y-px hover:from-purple-600 hover:to-purple-700 hover:shadow-purple-500/30 active:scale-[0.98]"
                      @click="isEditorOpen = true"
                    >
                      <UIcon name="i-heroicons-pencil" class="mr-1.5 size-4" />
                      编辑
                    </button>
                  </div>

                  <button
                    class="inline-flex w-full items-center justify-center rounded-lg bg-gradient-to-br from-purple-500/90 to-pink-500/90 px-5 py-2.5 text-sm font-medium text-white shadow-lg shadow-purple-500/20 transition-all duration-300 hover:-translate-y-px hover:from-purple-600 hover:to-pink-600 hover:shadow-purple-500/30 active:scale-[0.98]"
                    @click="handlePublishCoursePack"
                  >
                    <UIcon
                      name="i-heroicons-paper-airplane"
                      class="mr-1.5 size-4"
                    />
                    同步游戏端
                  </button>
                </div>
              </div>
            </div>

            <!-- 右侧课程列表 - 添加 overflow-auto -->
            <div
              class="h-[calc(100vh-12rem)] flex-1 overflow-auto transition-opacity duration-300"
            >
              <!-- 添加空状态显示 -->
              <div
                v-if="!coursePackStore.currentCoursePack?.courses?.length"
                class="flex h-full min-h-[500px] flex-col items-center justify-center"
              >
                <UIcon
                  name="i-heroicons-folder-open"
                  class="size-16 text-slate-600"
                />
                <p class="mt-4 text-slate-400">
                  当前课程包还没有任何课程
                </p>
                <button
                  class="mt-6 flex items-center gap-2 rounded-lg bg-purple-500 px-4 py-2 text-white transition-all duration-200 hover:bg-purple-600 active:scale-95"
                  @click="isCreatorOpen = true"
                >
                  <UIcon name="ph:plus" class="size-5" />
                  创建课程
                </button>
              </div>

              <!-- 常规课程包列表 -->
              <template v-if="coursePackStore.searchCoursesQuery.length > 0">
                <div class="space-y-4">
                  <div
                    v-for="course in coursePackStore.searchCourses"
                    :key="course.id"
                    class="relative"
                  >
                    <!-- 搜索模式下也显示选择框 -->
                    <div
                      v-if="isSelectionMode"
                      class="absolute left-2 top-1/2 z-10 -translate-y-1/2"
                    >
                      <input
                        type="checkbox"
                        :checked="selectedCourses.includes(course.id)"
                        class="size-5 rounded border-gray-600 bg-gray-700 text-purple-500 focus:ring-purple-500"
                        @change="toggleCourseSelection(course.id)"
                      >
                    </div>
                    <div :class="{ 'ml-10': isSelectionMode }">
                      <CoursePackCourseItem
                        :course="course"
                        :draggable="false"
                      />
                    </div>
                  </div>
                </div>
              </template>

              <template v-else>
                <VueDraggable
                  v-model="coursePackStore.courses"
                  class="space-y-4"
                  :animation="300"
                  handle=".drag-handle"
                  @end="handleDragEnd"
                >
                  <div
                    v-for="course in coursePackStore.courses"
                    :key="course.id"
                    class="relative"
                  >
                    <!-- 拖拽模式下的选择框 -->
                    <div
                      v-if="isSelectionMode"
                      class="absolute left-2 top-1/2 z-10 -translate-y-1/2"
                    >
                      <input
                        type="checkbox"
                        :checked="selectedCourses.includes(course.id)"
                        class="size-5 rounded border-gray-600 bg-gray-700 text-purple-500 focus:ring-purple-500"
                        @change="toggleCourseSelection(course.id)"
                      >
                    </div>
                    <div :class="{ 'ml-10': isSelectionMode }">
                      <CoursePackCourseItem
                        :course="course"
                        :draggable="true"
                      />
                    </div>
                  </div>
                </VueDraggable>
              </template>
            </div>
          </div>
        </div>
      </div>

      <!-- 添加 Creator 组件 -->
      <CoursePackCourseCreator v-model="isCreatorOpen" />
      <CoursePackEditor v-model="isEditorOpen" />
      <CoursePackUpgradeModal :course-pack-id="coursePackId" />
      <!-- 添加发布/更新 Modal -->
      <CoursePackPublishModal
        v-model="isPublishModalOpen"
        :course-pack-id="coursePackId"
      />
    </div>
  </div>
</template>

<style scoped>
/* 可以在这里添加任何必要的样式 */
</style>
