<script setup lang="ts">
import { map } from 'lodash-es'
import { onMounted, ref } from 'vue'
import { toast } from 'vue-sonner'
import DashboardTopBar from '~/components/common/DashboardTopBar.vue'
import Dialog from '~/components/common/Dialog.vue'
import LoadingScreen from '~/components/common/LoadingScreen.vue'
import { useCourseStore } from '~/stores/course'
import { useCoursePackStore } from '~/stores/coursePack'

definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
})

const route = useRoute()
const courseStore = useCourseStore()
const coursePackStore = useCoursePackStore()

const isLoading = ref(route.params.courseId !== courseStore.currentCourse?.id)
const loadingSpinner = useLoadingSpinner()
const modal = useModal()
const showContentAdd = ref(false)
const showEditor = ref(false)
const showDetail = ref(false)

onMounted(async () => {
  const coursePackId = route.params.id as string
  const courseId = route.params.courseId as string

  // 如果课程ID相同，直接返回
  if (courseStore.currentCourse?.id === courseId) {
    return
  }

  try {
    await coursePackStore.init(coursePackId)
    await courseStore.init(coursePackId, courseId)
    courseStore.selectSentence(null)
  }
  finally {
    isLoading.value = false
  }
})

// 修改添加句子的处理方法
function handleAddSentence() {
  showContentAdd.value = true
}

async function handleSplitAllSentences() {
  if (courseStore.isEmpty()) {
    toast.warning('先去添加一个句子吧')
    return
  }

  if (courseStore.hasAllSentencesContent()) {
    toast.warning('所有句子都已经有内容了，如果想重新拆分的话请先去清空句子')
    return
  }

  modal.open(Dialog, {
    title: '确认拆分全部句子',
    content:
      '此操作将拆分所有未处理的句子，需要消耗钻石（消费规则可以去钻石消费页面查看）\r\n 确认后不可撤销，请确认是否继续？',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      try {
        loadingSpinner.startLoading([
          '拆分的时长会比较久 请耐心等待',
          '拆分功能依赖大模型 所以时间会久一点',
          '如果拆分报错 可以针对每个句子单独点击拆分',
          '一节课的练习数量不要超过 200 否则练习时长会非常久！',
        ])
        await courseStore.splitAllSentences()
      }
      finally {
        loadingSpinner.finishLoading()
      }
    },
  })
}

async function handleProcessAllSentences() {
  if (courseStore.isEmpty()) {
    toast.warning('先去添加一个句子吧')
    return
  }

  if (courseStore.hasAllSentencesContent()) {
    toast.warning('所有句子都已加工完毕，无需重复操作。')
    return
  }

  modal.open(Dialog, {
    title: '确认加工全部句子',
    content: '此操作将加工所有未处理的句子，需要消耗钻石（消费规则可以去钻石消费页面查看）\r\n 确认后不可撤销，请确认是否继续？',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      loadingSpinner.startLoading()
      await courseStore.processAllSentences()
      loadingSpinner.finishLoading()
    },
  })
}

async function handleDeleteAllSentences() {
  if (courseStore.isEmpty()) {
    toast.warning('没句子 删除了个寂寞')
    return
  }

  modal.open(Dialog, {
    title: '警告',
    content: '你确定？删除了可就没法恢复了',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      loadingSpinner.startLoading()
      await courseStore.deleteAllSentences()
      loadingSpinner.finishLoading()
    },
  })
}

async function handleClearAllSentences() {
  if (courseStore.isEmpty()) {
    toast.warning('没句子 清空了个寂寞')
    return
  }

  modal.open(Dialog, {
    title: '警告',
    content: '你确定？清空了可就没法恢复了',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      loadingSpinner.startLoading()
      await courseStore.clearAllSentencesElements()
      loadingSpinner.finishLoading()
    },
  })
}

async function handleGenerateLearningContentAllSentences() {
  if (courseStore.isEmpty()) {
    toast.warning('先去添加一个句子吧')
    return
  }

  if (courseStore.hasAllSentencesLearningContent()) {
    toast.warning('所有句子都已经有讲解了')
    return
  }

  modal.open(Dialog, {
    title: '确认生成全部讲解',
    content:
      '此操作将为所有未生成讲解的句子生成详细内容，需要消耗钻石（消费规则可以去钻石消费页面查看）\r\n如果您只需要练习而不需要句子讲解，建议不要生成\r\n 确认后不可撤销，请确认是否继续？',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      loadingSpinner.startLoading()
      const { failedSentenceIds }
        = await courseStore.generateLearningAllSentences()
      loadingSpinner.finishLoading()

      if (failedSentenceIds.length > 0) {
        const failedSentences = map(failedSentenceIds, (id) => {
          const sentence = courseStore.findSentenceById(id)
          return sentence?.content || id
        })
        toast.warning(
          `以下句子生成讲解失败:\r\n${failedSentences.join('\n')}`,
        )
      }
    },
  })
}
</script>

<template>
  <div class="h-full">
    <div v-show="!isLoading" class="flex h-full flex-col">
      <!-- 顶部操作栏 -->
      <DashboardTopBar>
        <template #left-content>
          <!-- 句子操作区 -->
          <div class="flex items-center space-x-2">
            <!-- Elements 统计 -->
            <div
              class="flex items-center space-x-4 rounded-lg bg-slate-700/30 px-4 py-2"
            >
              <div class="flex items-center">
                <span class="text-sm text-slate-300">练习数</span>
                <span class="mx-2 text-lg font-semibold text-purple-400">{{
                  courseStore.totalElementsCount
                }}</span>
              </div>
            </div>

            <!-- 分隔线 -->
            <div class="h-8 w-px bg-slate-700/50" />

            <!-- 添加句子按钮 -->
            <button
              class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
              @click="handleAddSentence"
            >
              <UIcon name="ph:plus" class="size-5" />
              添加
            </button>

            <!-- 拆分按钮 -->
            <button
              class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
              @click="handleSplitAllSentences"
            >
              <UIcon name="ph:scissors" class="size-5" />
              拆分
            </button>

            <!-- 加工按钮 -->
            <button
              class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
              @click="handleProcessAllSentences"
            >
              <UIcon name="ph:wrench" class="size-5" />
              加工
            </button>

            <!-- 删除所有句子按钮 -->
            <button
              class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
              @click="handleDeleteAllSentences"
            >
              <UIcon name="ph:trash" class="size-5" />
              删除
            </button>

            <!-- 清空所有句子按钮 -->
            <button
              class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
              @click="handleClearAllSentences"
            >
              <UIcon name="ph:eraser" class="size-5" />
              清空
            </button>

            <!-- 生成讲解按钮 -->
            <button
              class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
              @click="handleGenerateLearningContentAllSentences"
            >
              <UIcon name="ph:sparkle" class="size-5" />
              生成讲解
            </button>
          </div>

          <!-- 分隔线 -->
          <div class="mx-4 h-8 w-px bg-slate-700/50" />
        </template>

        <template #right-content>
          <!-- 课程操作区 -->
          <div class="flex items-center space-x-2">
            <button
              class="inline-flex items-center gap-2 rounded-lg bg-purple-500 px-4 py-2 text-white transition-all duration-200 hover:bg-purple-600 hover:shadow-lg active:scale-95"
              @click="showDetail = true"
            >
              <UIcon name="i-heroicons-information-circle" class="size-5" />
              课程详情
            </button>
          </div>
        </template>
      </DashboardTopBar>

      <!-- 主要内容区域 -->
      <div class="mx-6 flex flex-1 gap-8 py-2">
        <Transition
          mode="out-in"
          enter-active-class="transition-all duration-300 ease-out"
          enter-from-class="opacity-0 translate-y-4"
          enter-to-class="opacity-100 translate-y-0"
          leave-active-class="transition-all duration-200 ease-in"
          leave-from-class="opacity-100 translate-y-0"
          leave-to-class="opacity-0 -translate-y-4"
        >
          <!-- 空状态提示 -->
          <div
            v-if="courseStore.isEmpty()"
            class="flex size-full items-center justify-center"
          >
            <div class="text-center">
              <UIcon
                name="ph:book-bookmark-bold"
                class="mx-auto mb-4 size-16 text-slate-500"
              />
              <h3 class="mb-3 text-xl font-medium text-slate-300">
                还没有添加任何句子
              </h3>
              <p class="mb-6 text-slate-400">
                点击下方按钮开始添加句子
              </p>
              <button
                class="inline-flex items-center gap-2 rounded-lg bg-purple-500 px-6 py-3 text-white transition-all duration-200 hover:bg-purple-600 hover:shadow-lg active:scale-95"
                @click="handleAddSentence"
              >
                <UIcon name="ph:plus" class="size-5" />
                添加句子
              </button>
            </div>
          </div>

          <!-- 列表和详情 -->
          <div v-else class="flex h-[calc(100vh-12rem)] w-full gap-3">
            <CourseSentenceList />
            <CourseSentenceDetail />
          </div>
        </Transition>
      </div>
    </div>
    <CourseEditor v-model="showEditor" />
    <LoadingScreen :show="isLoading" />
    <CourseContent v-model="showContentAdd" />
    <CourseDetail v-model="showDetail" />
  </div>
</template>

<style scoped></style>
