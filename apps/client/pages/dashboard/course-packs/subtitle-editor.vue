<script setup lang="ts">
import { Pane, Splitpanes } from 'splitpanes'
import LoadingScreen from '~/components/common/LoadingScreen.vue'
import CourseDetail from '~/components/course/Detail.vue'
import AudioPlayer from '~/components/subtitle-editor/AudioPlayer.vue'
import RightPane from '~/components/subtitle-editor/RightPane.vue'
import SubtitleEditor from '~/components/subtitle-editor/SubtitleEditor.vue'
import TopBar from '~/components/subtitle-editor/TopBar.vue'
import { useCourseDetail } from '~/composables/subtitle/useCourseDetail'
import { checkUnsavedChangesAndProcessSentences } from '~/composables/subtitle/useUnsavedChangesCheck'
import 'splitpanes/dist/splitpanes.css'

definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
})

const { loading } = useCourseDetail()
const showDetail = ref(false)

// 发布前检查
const beforePublish = () => checkUnsavedChangesAndProcessSentences('发布')

// 更新前检查
const beforeUpdate = () => checkUnsavedChangesAndProcessSentences('更新')
</script>

<template>
  <div class="h-full">
    <LoadingScreen :show="loading" text="正在加载页面..." />
    <div v-show="!loading" class="flex h-full flex-col">
      <!-- 顶部操作栏 -->
      <TopBar @show-detail="showDetail = true" />

      <!-- 主要内容区域 -->
      <div class="mx-6 flex flex-1 flex-col gap-4 overflow-hidden py-2">
        <div class="flex h-[calc(100vh-12rem)] w-full flex-col gap-3">
          <!-- 上半部分：分屏编辑器 -->
          <div class="flex-1 overflow-hidden rounded-lg border border-slate-700/50 bg-slate-800/30 backdrop-blur-sm">
            <Splitpanes class="splitpanes-main-horizontal h-full overflow-hidden">
              <Pane size="50" class="h-full overflow-auto">
                <SubtitleEditor />
              </Pane>
              <!-- 右侧区域：LRC 显示组件 -->
              <Pane size="50" class="relative h-full overflow-auto">
                <RightPane />
              </Pane>
            </Splitpanes>
          </div>

          <!-- 下半部分：音频波形 -->
          <div class="h-52 shrink-0 overflow-hidden rounded-lg border border-slate-700/50 bg-slate-800/30 backdrop-blur-sm">
            <AudioPlayer />
          </div>
        </div>
      </div>
    </div>

    <!-- 课程详情弹窗 -->
    <CourseDetail v-model="showDetail" :before-publish="beforePublish" :before-update="beforeUpdate" />
  </div>
</template>

<style scoped>
.splitpanes-main-horizontal :deep(.splitpanes__splitter) {
  background: rgba(100, 116, 139, 0.3);
  width: 2px;
  min-width: 2px;
  max-width: 2px;
  height: 100%;
  border-radius: 1px;
  position: relative;
  cursor: col-resize;
  transition:
    background 0.3s,
    box-shadow 0.3s;
  box-shadow: none;
  border: none;
}

.splitpanes-main-horizontal :deep(.splitpanes__splitter:hover) {
  background: rgba(147, 51, 234, 0.5);
  box-shadow: 0 0 6px 1px rgba(147, 51, 234, 0.3);
}

.splitpanes-main-horizontal :deep(.splitpanes__splitter::before) {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: -6px;
  right: -6px;
  background: transparent;
  z-index: 1;
  border-radius: 4px;
  transition: background 0.3s;
}

.splitpanes-main-horizontal :deep(.splitpanes__splitter:hover::before) {
  background: rgba(147, 51, 234, 0.1);
}

.splitpanes-main-horizontal :deep(.splitpanes--vertical > .splitpanes__splitter) {
  width: 2px;
  min-width: 2px;
  max-width: 2px;
  height: 100%;
}
</style>
