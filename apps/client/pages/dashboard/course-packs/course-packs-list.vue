<script setup lang="ts">
import { isArray, isEmpty } from 'lodash-es'
import { onMounted, ref } from 'vue'

definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
})

const { $trpc } = useNuxtApp()
const coursePacksStore = useCoursePacksStore()

const loading = ref(false)
const errorMessage = ref('')

onMounted(async () => {
  loading.value = true
  errorMessage.value = ''
  try {
    // 使用 editor 项目的 tRPC 端点
    const packs = await $trpc.coursePack.list.query()
    if (!isEmpty(packs) && isArray(packs)) {
      coursePacksStore.coursePacks = packs
    }
  }
  catch (err: unknown) {
    const errorMsg = err instanceof Error ? err.message : '获取课程包列表失败'
    errorMessage.value = errorMsg
  }
  finally {
    loading.value = false
  }
})

function handleSelect(packId: string) {
  navigateTo({ path: '/dashboard/course-list', query: { coursePackId: packId } })
}
</script>

<template>
  <div class="mx-auto max-w-4xl p-6">
    <h1 class="mb-8 text-4xl font-bold text-white">
      我的课程包
    </h1>
    <div v-if="loading" class="text-lg text-slate-300">
      加载中...
    </div>
    <div v-else-if="errorMessage" class="text-lg font-medium text-red-400">
      {{ errorMessage }}
    </div>
    <div v-else>
      <div v-if="coursePacksStore.coursePacks.length === 0" class="text-lg text-slate-400">
        暂无课程包
      </div>
      <div v-else class="space-y-4">
        <div
          v-for="pack in coursePacksStore.coursePacks"
          :key="pack.id"
          class="cursor-pointer rounded-lg border border-slate-600 bg-slate-800/50 p-6 transition-all duration-200 hover:border-blue-400 hover:bg-slate-700/50 hover:shadow-lg"
          @click="handleSelect(pack.id)"
        >
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-2xl font-bold text-white">
                {{ pack.title }}
              </h2>
              <p class="mt-2 text-base text-slate-300">
                {{ pack.description || '无描述' }}
              </p>
              <div class="mt-3 flex items-center space-x-6 text-sm text-slate-400">
                <span>创建时间: {{ new Date(pack.createdAt).toLocaleDateString() }}</span>
                <span>分享级别: {{ pack.shareLevel }}</span>
                <span>是否免费: {{ pack.isFree ? '免费' : '付费' }}</span>
              </div>
            </div>
            <div class="text-base font-medium text-blue-400 hover:text-blue-300">
              进入详情 →
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
</style>
