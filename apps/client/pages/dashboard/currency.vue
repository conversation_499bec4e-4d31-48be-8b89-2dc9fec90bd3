<script setup lang="ts">
import { toast } from 'vue-sonner'
import DiamondRulesModal from '~/components/diamond/DiamondRulesModal.vue'
import { useCurrencyStore } from '~/stores/currency'
import { formatDateTime, formatYearMonthDay } from '~/utils/date'

definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
})

// 使用Currency Store
const currencyStore = useCurrencyStore()

// 在UI层处理加载状态和消息
const isLoading = ref(true)

// 控制模态框显示
const showRulesModal = ref(false)

// 检查是否显示空状态
const showEmptyState = computed(() => {
  return !isLoading.value && currencyStore.transactions.length === 0
})

// 页面加载时初始化数据
onMounted(async () => {
  isLoading.value = true
  try {
    await currencyStore.initialize()
  }
  catch (error) {
    console.error('初始化数据失败:', error)
    toast.error('加载数据失败，请稍后重试')
  }
  finally {
    isLoading.value = false
  }
})
</script>

<template>
  <div class="flex h-full flex-col">
    <!-- 工具栏 -->
    <div class="sticky top-0 z-10">
      <div class="mx-6 mb-2 mt-4 rounded-md bg-slate-800 p-4">
        <div class="flex items-center justify-between">
          <!-- 左侧：汇总信息 -->
          <div class="flex items-center space-x-6">
            <!-- 钻石余额 -->
            <div
              v-if="!isLoading"
              class="flex items-center space-x-2 rounded-lg bg-slate-700/30 px-4 py-2"
            >
              <span class="text-sm text-slate-300">钻石余额</span>
              <!-- 应用参考样式 -->
              <span class="text-2xl font-bold text-blue-400">{{
                currencyStore.formattedBalance
              }}</span>
            </div>
            <!-- 支出 -->
            <div
              v-if="!isLoading && currencyStore.transactions.length > 0"
              class="flex items-center space-x-2 rounded-lg bg-slate-700/30 px-4 py-2"
            >
              <span class="text-sm text-slate-300">支出</span>
              <!-- 使用 abs 确保是正数，并应用参考样式 -->
              <span class="text-2xl font-bold text-red-400">{{
                Math.abs(currencyStore.totalSpent)
              }}</span>
            </div>
          </div>

          <!-- 右侧：日期显示和钻石规则按钮 -->
          <div class="flex items-center space-x-4">
            <!-- 显示账单日期范围 -->
            <div
              v-if="currencyStore.cycleStartDate && currencyStore.cycleEndDate"
              class="flex items-center rounded-lg bg-slate-600/40 px-4 py-2 text-sm"
            >
              <UIcon
                name="i-heroicons-calendar-days"
                class="mr-2 size-4 text-slate-300"
              />
              <span class="text-slate-300">
                账单周期: {{ formatYearMonthDay(currencyStore.cycleStartDate) }} -
                {{ formatYearMonthDay(currencyStore.cycleEndDate) }}
              </span>
            </div>
            <!-- 显示下次重置日 -->
            <div
              class="flex items-center rounded-lg bg-slate-600/40 px-4 py-2 text-sm"
            >
              <UIcon
                name="i-heroicons-arrow-path"
                class="mr-2 size-4 text-slate-300"
              />
              <span class="text-slate-300">
                下次重置日: {{ formatYearMonthDay(currencyStore.cycleEndDate) }}
              </span>
            </div>
            <button
              class="flex items-center space-x-2 rounded-lg bg-blue-500/20 px-4 py-2 text-sm font-medium text-blue-400 transition-all hover:bg-blue-500/30"
              @click="showRulesModal = true"
            >
              <UIcon name="i-heroicons-information-circle" class="size-5" />
              <span>查看钻石规则</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 内容列表 -->
    <div class="min-h-0 flex-1 overflow-auto px-6 py-2">
      <!-- 加载中状态 -->
      <div
        v-if="isLoading"
        class="flex h-full min-h-[500px] flex-col items-center justify-center"
      >
        <UIcon name="ph-spinner" class="size-12 animate-spin text-blue-400" />
        <p class="mt-4 text-slate-400">
          正在加载消费记录...
        </p>
      </div>

      <!-- 空状态 -->
      <div
        v-if="showEmptyState"
        class="flex h-full min-h-[500px] flex-col items-center justify-center"
      >
        <UIcon name="ph-receipt" class="size-16 text-slate-600" />
        <p class="mt-4 text-slate-400">
          暂无消费记录
        </p>
      </div>

      <!-- 列表内容 -->
      <div v-else class="grid gap-4">
        <div
          v-for="transaction in currencyStore.transactions"
          :key="transaction.id"
          class="rounded-xl bg-slate-800/30 p-4 transition-all duration-300 hover:bg-slate-800/50"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div
                class="mr-4 flex size-10 items-center justify-center rounded-full" :class="[
                  transaction.amount < 0
                    ? 'bg-red-500/20 text-red-400'
                    : 'bg-green-500/20 text-green-400',
                ]"
              >
                <UIcon
                  :name="
                    transaction.amount < 0 ? 'ph-arrow-down' : 'ph-arrow-up'
                  "
                  class="size-5"
                />
              </div>
              <div>
                <p class="font-medium">
                  {{ transaction.description || "钻石交易" }}
                </p>
                <p class="text-sm text-slate-400">
                  {{
                    formatDateTime(
                      typeof transaction.createdAt === "string"
                        ? transaction.createdAt
                        : transaction.createdAt.toISOString(),
                    )
                  }}
                </p>
              </div>
            </div>
            <div
              class="text-lg font-semibold" :class="[
                transaction.amount < 0 ? 'text-red-400' : 'text-green-400',
              ]"
            >
              {{ transaction.amount < 0 ? "-" : "+" }}
              {{ Math.abs(transaction.amount) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 钻石规则说明模态框 -->
    <DiamondRulesModal v-model="showRulesModal" />
  </div>
</template>

<style scoped></style>
