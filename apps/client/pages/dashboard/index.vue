<script setup lang="ts">
import { useCurrencyStore } from '~/stores/currency'

definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
})

// 使用货币store
const currencyStore = useCurrencyStore()

// 在UI组件层处理加载状态
const isLoading = ref(true)

// 视频教程链接
const tutorialUrl = 'https://www.bilibili.com/video/BV1a2GezAEur/'

// 打开教程
function openTutorial() {
  window.open(tutorialUrl, '_blank')
}

// 键盘事件处理
function handleKeyDown(event: KeyboardEvent) {
  if (event.key === 'Enter' || event.key === ' ') {
    openTutorial()
  }
}

// 页面加载时初始化数据
onMounted(async () => {
  isLoading.value = true
  try {
    await currencyStore.fetchBalance()
  }
  catch (error) {
    console.error('获取余额失败:', error)
  }
  finally {
    isLoading.value = false
  }
})
</script>

<template>
  <div class="p-8">
    <!-- 欢迎区域 -->
    <div class="mb-12">
      <h1
        class="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-5xl font-bold text-transparent"
      >
        欢迎回来
      </h1>
      <p class="mt-4 text-lg text-slate-400">
        今天又是美好的一天！
      </p>
    </div>

    <div class="grid grid-cols-1 gap-8 lg:grid-cols-3">
      <!-- 钻石余额展示 -->
      <div
        class="rounded-2xl border border-blue-500/20 bg-gradient-to-r from-blue-500/10 to-blue-600/10 p-6 transition-all duration-300 hover:border-blue-500/40"
      >
        <div class="mb-4 flex items-center justify-between">
          <div class="flex items-center">
            <UIcon
              name="i-ph-diamond"
              class="size-6 animate-pulse text-blue-400"
            />
            <h3 class="ml-2 text-base font-medium">
              钻石余额
            </h3>
          </div>
          <UButton
            icon="ph-receipt"
            color="blue"
            variant="ghost"
            size="xs"
            @click="navigateTo('/dashboard/currency')"
          >
            查看记录
          </UButton>
        </div>

        <!-- 加载中状态 -->
        <div v-if="isLoading" class="py-4">
          <div
            class="mb-2 h-8 w-1/3 animate-pulse rounded-md bg-slate-700/50"
          />
          <div class="h-4 w-2/3 animate-pulse rounded-md bg-slate-700/50" />
        </div>

        <!-- 加载完成状态 -->
        <template v-else>
          <div class="flex items-end justify-between">
            <div>
              <div class="text-3xl font-bold text-blue-400">
                {{ currencyStore.formattedBalance }}
              </div>
              <div class="mt-1 text-xs text-slate-400">
                可用于句子拆分、生成知识点讲解等 AI 功能
              </div>
            </div>
          </div>
        </template>
      </div>

      <!-- 教程入口 -->
      <div
        class="group relative cursor-pointer overflow-hidden rounded-2xl border-2 border-pink-500/30 bg-gradient-to-br from-pink-500/20 to-purple-500/20 p-6 transition-all duration-300 hover:border-pink-500/50 hover:shadow-lg hover:shadow-pink-500/10"
        tabindex="0"
        role="button"
        aria-label="观看视频教程"
        @click="openTutorial"
        @keydown="handleKeyDown"
      >
        <div
          class="absolute -right-4 -top-4 size-20 rotate-12 bg-pink-500/20 blur-xl"
        />

        <div class="mb-3 flex items-center">
          <div
            class="flex size-8 items-center justify-center rounded-full bg-pink-500/30"
          >
            <UIcon
              name="i-simple-icons-bilibili"
              class="size-5 text-pink-400 transition-transform duration-300 group-hover:scale-110"
            />
          </div>
          <h3 class="ml-3 text-base font-medium text-pink-100">
            新手必看教程
          </h3>
          <UBadge
            color="pink"
            variant="soft"
            size="xs"
            class="ml-2 animate-pulse"
          >
            推荐
          </UBadge>
        </div>

        <p class="mb-2 text-sm text-pink-100/70">
          全面了解如何使用编辑端的功能
        </p>

        <div class="mt-4 flex items-center text-xs text-pink-200/70">
          <UIcon name="i-ph-play-circle" class="mr-1 size-4" />
          <span>视频教程</span>
          <UIcon
            name="i-ph-arrow-right"
            class="ml-auto size-4 transition-transform duration-300 group-hover:translate-x-1"
          />
        </div>
      </div>

      <!-- 浏览课库 -->
      <div
        class="group cursor-pointer rounded-2xl bg-slate-800/50 p-6 transition-all duration-300 hover:bg-slate-800/60"
        @click="navigateTo('/dashboard/course-packs')"
      >
        <div class="mb-3 flex items-center">
          <UIcon
            name="ph-books"
            class="size-6 text-blue-400 transition-transform duration-300 group-hover:scale-110"
          />
          <h3 class="ml-3 text-base font-medium">
            浏览课程包
          </h3>
        </div>
        <p class="text-sm text-slate-400">
          查看和管理你的课程包
        </p>
      </div>
    </div>

    <!-- 数据统计区 -->
    <div class="mt-10 grid grid-cols-1 gap-8 md:grid-cols-2">
      <div
        class="rounded-2xl bg-slate-800/30 p-8 transition-all duration-300 hover:bg-slate-800/40 hover:shadow-lg"
      >
        <h3 class="mb-8 text-2xl font-semibold">
          最近动态
        </h3>
        <div class="py-16 text-center text-slate-400">
          <UIcon name="ph-clock" class="mx-auto mb-6 size-20 opacity-50" />
          <p class="text-lg">
            暂无最近动态
          </p>
        </div>
      </div>

      <div
        class="rounded-2xl bg-slate-800/30 p-8 transition-all duration-300 hover:bg-slate-800/40 hover:shadow-lg"
      >
        <h3 class="mb-8 text-2xl font-semibold">
          使用统计
        </h3>
        <div class="py-16 text-center text-slate-400">
          <UIcon
            name="ph-chart-line"
            class="mx-auto mb-6 size-20 opacity-50"
          />
          <p class="text-lg">
            暂无统计数据
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
