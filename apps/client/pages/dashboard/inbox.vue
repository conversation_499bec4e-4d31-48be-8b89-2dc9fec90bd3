<script setup lang="ts">
import type { InboxItem } from '~/stores/inbox'
import { useLoadingBar } from 'naive-ui'
import { onMounted, onUnmounted, ref } from 'vue'
import { toast } from 'vue-sonner'
import Dialog from '~/components/common/Dialog.vue'
import LoadingScreen from '~/components/common/LoadingScreen.vue'
import CourseSelector from '~/components/inbox/CourseSelector.vue'
import { useInboxStore } from '~/stores/inbox'
import { isMac } from '~/utils/os'

// 类型定义
definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
})

const modal = useModal()
const loadingBar = useLoadingBar()
const isLoading = ref(true)
const isRefreshing = ref(false)
const showCourseSelector = ref(false)
const currentItems = ref<InboxItem | InboxItem[] | null>(null)
const courseStore = useCourseStore()
const { searchInput } = useSearchShortcut()
const router = useRouter()

// 使用 store 中的状态
const inboxStore = useInboxStore()

// 初始化
onMounted(async () => {
  try {
    loadingBar.start()
    await inboxStore.init()
  }
  catch (error) {
    toast.error('获取内容失败')
    throw error // 向上抛出错误以便调用者处理
  }
  finally {
    loadingBar.finish()
    isLoading.value = false
  }
})

// 添加删除所有内容的方法
async function deleteAllItems() {
  try {
    loadingBar.start()
    await inboxStore.deleteAllItems()
    toast.success('已清空收集箱')
  }
  catch {
    toast.error('删除失败')
  }
  finally {
    loadingBar.finish()
  }
}

function showDeleteConfirm() {
  modal.open(Dialog, {
    title: '确认删除',
    content: '确定要删除所有收集的内容吗？此操作不可恢复。',
    showCancel: true,
    showConfirm: true,
    onConfirm() {
      deleteAllItems()
    },
  })
}

// 添加选择模式相关的状态
const isSelectionMode = ref(false)
const selectedItems = ref<string[]>([])

// 切换选择模式
function toggleSelectionMode() {
  isSelectionMode.value = !isSelectionMode.value
  selectedItems.value = [] // 清空选择
}

// 切换项目选择状态
function toggleItemSelection(itemId: string) {
  const index = selectedItems.value.indexOf(itemId)
  if (index === -1) {
    selectedItems.value.push(itemId)
  }
  else {
    selectedItems.value.splice(index, 1)
  }
}

// 批量删除处理函数
async function handleBatchDelete() {
  if (!selectedItems.value.length)
    return

  modal.open(Dialog, {
    title: '批量删除',
    content: `确定要删除选中的 ${selectedItems.value.length} 个内容吗？删除后将无法恢复。`,
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      try {
        loadingBar.start()
        await inboxStore.batchDelete(selectedItems.value)
        selectedItems.value = [] // 清空选择
        isSelectionMode.value = false // 退出选择模式
        toast.success('批量删除成功')
      }
      catch {
        toast.error('批量删除失败')
      }
      finally {
        loadingBar.finish()
      }
    },
  })
}

function useSearchShortcut() {
  const searchInput = ref<HTMLInputElement | null>(null)

  function handleShortcut(event: KeyboardEvent) {
    if ((event.metaKey || event.ctrlKey) && event.key.toLowerCase() === 'k') {
      event.preventDefault()
      searchInput.value?.focus()
    }
  }

  onMounted(() => {
    window.addEventListener('keydown', handleShortcut)
  })

  onUnmounted(() => {
    window.removeEventListener('keydown', handleShortcut)
  })

  return {
    searchInput,
  }
}

// 添加批量添加按钮的处理方法
function handleBatchAdd() {
  if (!selectedItems.value.length)
    return

  const selectedInboxItems = inboxStore.items.filter(item =>
    selectedItems.value.includes(item.id),
  )

  addToCourse(selectedInboxItems)
}

// 刷新收集箱
async function refreshInbox() {
  if (isRefreshing.value)
    return

  try {
    isRefreshing.value = true
    await inboxStore.init()
    toast.success('刷新成功')
  }
  catch (error) {
    console.error('Failed to refresh inbox:', error)
    toast.error('刷新失败')
  }
  finally {
    isRefreshing.value = false
  }
}

// 删除内容
async function deleteItem(id: string) {
  try {
    loadingBar.start()
    await inboxStore.deleteItem(id)
    toast.success('删除成功')
  }
  catch {
    toast.error('删除失败')
  }
  finally {
    loadingBar.finish()
  }
}

// 添加到课程
function addToCourse(items: InboxItem | InboxItem[]) {
  currentItems.value = items
  showCourseSelector.value = true
}

// 处理课程选择
async function handleCourseSelect(courseId: string, coursePackId: string) {
  if (!currentItems.value)
    return

  try {
    loadingBar.start()
    if (Array.isArray(currentItems.value)) {
      // 批量添加
      await inboxStore.addItems(courseId, currentItems.value)
      selectedItems.value = [] // 清空选择
      isSelectionMode.value = false // 退出选择模式
    }
    else {
      // 单个添加
      await inboxStore.addItem(courseId, currentItems.value)
    }
    courseStore.cleanCurrentCourse()
    toast.success(
      Array.isArray(currentItems.value)
        ? '批量添加到课程成功'
        : '添加到课程成功',
      {
        action: {
          label: '查看课程',
          onClick: () => {
            router.push(`/dashboard/course-packs/${coursePackId}/${courseId}`)
          },
        },
      },
    )
  }
  catch (error) {
    console.error('Failed to add to course:', error)
    toast.error(
      Array.isArray(currentItems.value)
        ? '批量添加到课程失败'
        : '添加到课程失败',
    )
  }
  finally {
    loadingBar.finish()
    currentItems.value = null
  }
}
</script>

<template>
  <div class="flex h-full flex-col">
    <LoadingScreen :show="isLoading" />

    <div v-show="!isLoading" class="h-full">
      <div class="flex h-full flex-col">
        <!-- 工具栏 -->
        <div class="sticky top-0 z-10">
          <div class="mx-6 mb-2 mt-4 rounded-md bg-slate-800 p-4">
            <div class="flex items-center justify-between">
              <div class="flex w-full items-center justify-between">
                <div class="flex items-center space-x-4">
                  <!-- 统计信息 -->
                  <div class="flex items-center space-x-6">
                    <div
                      class="flex items-center space-x-4 rounded-lg bg-slate-700/30 px-4 py-2"
                    >
                      <div class="flex items-center">
                        <span class="text-sm text-slate-300">收集内容</span>
                        <span
                          class="mx-2 text-lg font-semibold text-purple-400"
                        >{{ inboxStore.items.length }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 排序按钮 -->
                  <button
                    class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
                    @click="inboxStore.toggleSort"
                  >
                    <UIcon
                      name="ph:sort-ascending"
                      class="size-5 text-purple-400 transition-transform duration-200"
                      :class="{
                        'rotate-180': inboxStore.sortDirection === 'desc',
                      }"
                    />
                    {{ inboxStore.sortDirection === "asc" ? "正序" : "倒序" }}
                  </button>

                  <!-- 刷新按钮 -->
                  <button
                    class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95 disabled:cursor-not-allowed disabled:opacity-50"
                    :disabled="isRefreshing"
                    @click="refreshInbox"
                  >
                    <UIcon
                      name="i-heroicons-arrow-path"
                      class="size-5 transition-transform duration-500"
                      :class="{ 'animate-spin': isRefreshing }"
                    />
                    刷新
                  </button>

                  <!-- 全部删除按钮 -->
                  <button
                    v-if="inboxStore.items.length > 0"
                    class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
                    @click="showDeleteConfirm"
                  >
                    <UIcon name="ph:trash" class="size-5" />
                    全部删除
                  </button>

                  <!-- 添加选择模式切换按钮 -->
                  <button
                    class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
                    @click="toggleSelectionMode"
                  >
                    <UIcon
                      :name="isSelectionMode ? 'ph:x' : 'ph:list-checks'"
                      class="size-5"
                    />
                    {{ isSelectionMode ? "退出选择" : "批量选择" }}
                  </button>

                  <!-- 显示批量删除按钮（仅在选择模式且有选中项时显示） -->
                  <button
                    v-if="isSelectionMode && selectedItems.length > 0"
                    class="inline-flex items-center gap-2 rounded-lg bg-red-500/10 px-4 py-2 text-red-400 transition-all duration-200 hover:bg-red-500/20 hover:shadow-lg active:scale-95"
                    @click="handleBatchDelete"
                  >
                    <UIcon name="ph:trash" class="size-5" />
                    删除所选 ({{ selectedItems.length }})
                  </button>

                  <!-- 批量添加按钮 -->
                  <button
                    v-if="isSelectionMode && selectedItems.length > 0"
                    class="inline-flex items-center gap-2 rounded-lg bg-purple-500/10 px-4 py-2 text-purple-400 transition-all duration-200 hover:bg-purple-500/20 hover:shadow-lg active:scale-95"
                    @click="handleBatchAdd"
                  >
                    <UIcon name="ph:plus" class="size-5" />
                    批量添加 ({{ selectedItems.length }})
                  </button>
                </div>

                <!-- 搜索框 -->
                <div class="relative w-64">
                  <input
                    ref="searchInput"
                    v-model="inboxStore.searchQuery"
                    type="text"
                    :placeholder="isMac() ? '搜索 ⌘ + K' : '搜索 Ctrl + K'"
                    class="w-full rounded-lg bg-slate-700/30 px-4 py-2 text-sm text-white outline-none placeholder:text-slate-400 focus:ring-2 focus:ring-purple-500/50"
                  >
                  <UIcon
                    name="i-heroicons-magnifying-glass-20-solid"
                    class="absolute right-3 top-1/2 size-5 -translate-y-1/2 text-slate-400"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 内容列表 -->
        <div class="min-h-0 flex-1">
          <div class="h-full overflow-auto px-6 py-2">
            <!-- 空状态 -->
            <div
              v-if="inboxStore.filteredItems.length === 0"
              class="flex h-full min-h-[500px] flex-col items-center justify-center"
            >
              <UIcon
                name="i-heroicons-inbox"
                class="size-16 text-slate-600"
              />
              <p class="mt-4 text-slate-400">
                还没有收集任何内容
              </p>
            </div>

            <!-- 列表内容 -->
            <div v-else class="grid gap-4">
              <TransitionGroup name="list">
                <InboxItem
                  v-for="item in inboxStore.filteredItems"
                  :key="item.id"
                  :item="item"
                  :is-selection-mode="isSelectionMode"
                  :is-selected="selectedItems.includes(item.id)"
                  @delete="deleteItem"
                  @add-to-course="addToCourse"
                  @toggle-selection="toggleItemSelection"
                />
              </TransitionGroup>
            </div>
          </div>
        </div>
      </div>
    </div>

    <CourseSelector v-model="showCourseSelector" @select="handleCourseSelect" />
  </div>
</template>

<style scoped>
.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}
.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
</style>
