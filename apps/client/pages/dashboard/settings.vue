<script setup lang="ts">
definePageMeta({
  layout: 'dashboard',
  middleware: 'auth',
})
</script>

<template>
  <div class="flex min-h-screen flex-col items-center justify-center p-6">
    <div class="space-y-4 text-center">
      <UIcon
        name="i-heroicons-cog-6-tooth"
        class="animate-spin-slow size-16 text-slate-400"
      />
      <h2 class="text-2xl font-medium text-white">
        设置功能开发中
      </h2>
      <p class="text-slate-400">
        更多个性化设置功能即将到来，敬请期待！
      </p>
    </div>
  </div>
</template>

<style scoped>
.animate-spin-slow {
  animation: spin 6s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
