<script setup lang="ts">
import { isAuthenticated, signIn, signOut } from '~/services/auth'

useHead({
  title: '【句乐部】编辑端 - AI智能创建英语课程，个性化定制你的专属学习！',
  meta: [
    {
      name: 'description',
      content: `【句乐部】编辑端，您的专属智能英语课程设计平台。AI自动拆分句子，精准解析知识点，助您轻松创建、编辑和发布个性化英语练习。无论是自学定制、教学备课还是内容创作，都能在这里实现课程内容自由生成，打造您独一无二的"上瘾"英语学习体验！`,
    },
    {
      name: 'keywords',
      content: '句乐部编辑端, 英语课程设计, AI英语课程生成器, 英语练习生成器, 智能习题生成, 个性化英语内容生成, 英语学习资料生成, PDF英语导入工具, 英语句子拆分工具, 英语知识点解析工具, 英语备课软件, 英语教学工具, 英语内容创作平台, 英语试题生成器, 英语课件制作, 句乐部工具, 自动生成英语练习, 定制英语学习内容',
    },
    // Open Graph meta tags for social sharing
    {
      property: 'og:title',
      content: '【句乐部】编辑端 - AI智能创建英语课程，个性化定制你的专属学习！',
    },
    {
      property: 'og:description',
      content: '【句乐部】编辑端，您的专属智能英语课程设计平台。AI自动拆分句子，精准解析知识点，助您轻松创建、编辑和发布个性化英语练习。',
    },
    {
      property: 'og:type',
      content: 'website',
    },
    {
      property: 'og:url',
      content: 'https://editor.julebu.co',
    },
    // Twitter Card meta tags
    {
      name: 'twitter:card',
      content: 'summary_large_image',
    },
    {
      name: 'twitter:title',
      content: '【句乐部】编辑端 - AI智能创建英语课程，个性化定制你的专属学习！',
    },
    {
      name: 'twitter:description',
      content: '【句乐部】编辑端，您的专属智能英语课程设计平台。AI自动拆分句子，精准解析知识点。',
    },
    // Additional SEO meta tags
    {
      name: 'author',
      content: '句乐部',
    },
    {
      name: 'robots',
      content: 'index, follow',
    },
    {
      'http-equiv': 'Content-Language',
      'content': 'zh-CN',
    },
  ],
  link: [
    {
      rel: 'icon',
      href: '/favicon.ico',
    },
    {
      rel: 'canonical',
      href: 'https://editor.julebu.co',
    },
  ],
})

async function handleGetStarted() {
  if (isAuthenticated()) {
    navigateTo('/dashboard')
  }
  else {
    signIn()
  }
}
</script>

<template>
  <div class="min-h-screen overflow-x-hidden bg-[#0A0B1A] text-white">
    <div class="bg-gradient-radial absolute inset-0" />
    <header
      class="container relative z-10 mx-auto flex items-center justify-between px-4 py-6"
    >
      <a href="/" class="group flex items-center space-x-2">
        <div
          class="size-10 transition-all duration-300 ease-in-out hover:scale-110 group-hover:rotate-12"
        >
          <img
            src="/logo.svg"
            alt="句乐部 Logo"
            class="size-full animate-pulse"
          >
        </div>
        <span
          class="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-xl font-bold text-transparent"
        >句乐部</span>
      </a>
      <button
        v-if="!isAuthenticated()"
        class="rounded-md bg-gradient-to-r from-purple-600 to-blue-500 px-4 py-2 text-white transition-colors hover:scale-105 hover:from-purple-700 hover:to-blue-600 hover:shadow-[0_0_15px_rgba(124,58,237,0.3)]"
        @click="signIn"
      >
        登录
      </button>
      <button
        v-else
        class="rounded-md bg-gradient-to-r from-purple-600 to-blue-500 px-4 py-2 text-white transition-colors hover:scale-105 hover:from-purple-700 hover:to-blue-600 hover:shadow-[0_0_15px_rgba(124,58,237,0.3)]"
        @click="signOut"
      >
        登出
      </button>
    </header>

    <main class="container relative z-10 mx-auto px-4 pb-32 pt-20">
      <div class="animate-fadeInUp mx-auto max-w-3xl space-y-6 text-center">
        <h1
          class="text-5xl font-bold transition-all duration-500 hover:scale-105 md:text-7xl"
        >
          <span class="text-white">句乐部</span>
          <span
            class="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent hover:from-purple-500 hover:to-blue-500"
          >
            编辑器
          </span>
        </h1>
        <p class="mx-auto max-w-2xl text-2xl text-gray-400">
          录入你想要练习的任何内容
        </p>
        <div class="flex justify-center pt-8">
          <button
            class="group relative overflow-hidden rounded-full bg-gradient-to-r from-purple-600 to-blue-500 px-8 py-4 text-lg text-white transition-all duration-300 hover:scale-105 hover:from-purple-700 hover:to-blue-600 hover:shadow-lg"
            @click="handleGetStarted"
          >
            <span class="relative z-10">开启内容自由</span>
            <span
              class="absolute inset-0 bg-gradient-to-r from-purple-400 to-blue-400 opacity-0 transition-opacity duration-300 group-hover:opacity-50"
            />
            <span
              class="absolute inset-0 origin-left scale-x-0 bg-white opacity-25 transition-transform duration-300 group-hover:scale-x-100"
            />
          </button>
        </div>
      </div>

      <div
        class="mt-32 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:mx-auto xl:max-w-7xl"
      >
        <div
          class="animate-float rounded-xl bg-gradient-to-br from-purple-900/50 to-blue-900/50 p-6 transition-all duration-300 hover:scale-105 hover:from-purple-800/50 hover:to-blue-800/50 hover:shadow-xl"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="mb-4 size-12 text-purple-400 transition-transform hover:rotate-12"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M12 20h9" />
            <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z" />
          </svg>
          <h3 class="mb-2 text-2xl font-semibold text-white">
            AI智能拆分句子
          </h3>
          <p class="text-gray-300">
            基于AI智能分析的句子拆分技术，将长句分解成易于理解的小chunk，让学习更轻松高效。
          </p>
        </div>
        <div
          class="animate-float rounded-xl bg-gradient-to-br from-purple-900/50 to-blue-900/50 p-6 transition-all duration-300 hover:scale-105 hover:from-purple-800/50 hover:to-blue-800/50 hover:shadow-xl"
          style="animation-delay: 0.1s"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="mb-4 size-12 text-purple-400 transition-transform hover:rotate-12"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path
              d="M21.59 11.59a2 2 0 0 1 0 2.83l-4.24 4.24a2 2 0 0 1-2.83 0L2 6.14V2h4.14l12.52 12.52a2 2 0 0 1 0 2.83"
            />
          </svg>
          <h3 class="mb-2 text-2xl font-semibold text-white">
            知识点智能解析
          </h3>
          <p class="text-gray-300">
            自动分析生成句子中的语法要点、词组搭配等关键知识点，打造完整知识体系。
          </p>
        </div>
        <div
          class="animate-float rounded-xl bg-gradient-to-br from-purple-900/50 to-blue-900/50 p-6 transition-all duration-300 hover:scale-105 hover:from-purple-800/50 hover:to-blue-800/50 hover:shadow-xl"
          style="animation-delay: 0.2s"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="mb-4 size-12 text-purple-400 transition-transform hover:rotate-12"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
            <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
          </svg>
          <h3 class="mb-2 text-2xl font-semibold text-white">
            一键格式化导入
          </h3>
          <p class="text-gray-300">
            支持中英对照文本批量导入，教材、作业内容秒变练习素材。
          </p>
        </div>
        <div
          class="animate-float rounded-xl bg-gradient-to-br from-purple-900/50 to-blue-900/50 p-6 transition-all duration-300 hover:scale-105 hover:from-purple-800/50 hover:to-blue-800/50 hover:shadow-xl"
          style="animation-delay: 0.3s"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="mb-4 size-12 text-purple-400 transition-transform hover:rotate-12"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
          </svg>
          <h3 class="mb-2 text-2xl font-semibold text-white">
            自由编辑
          </h3>
          <p class="text-gray-300">
            支持灵活编辑翻译、音标等内容，让教学素材完全符合您的需求
          </p>
        </div>
        <div
          class="animate-float rounded-xl bg-gradient-to-br from-purple-900/50 to-blue-900/50 p-6 transition-all duration-300 hover:scale-105 hover:from-purple-800/50 hover:to-blue-800/50 hover:shadow-xl"
          style="animation-delay: 0.4s"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="mb-4 size-12 text-purple-400 transition-transform hover:rotate-12"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
          </svg>
          <h3 class="mb-2 text-2xl font-semibold text-white">
            多维度语言信息
          </h3>
          <p class="text-gray-300">
            自动生成音标、翻译、重点词组标注，打造全方位学习体验。
          </p>
        </div>
        <div
          class="animate-float rounded-xl bg-gradient-to-br from-purple-900/50 to-blue-900/50 p-6 transition-all duration-300 hover:scale-105 hover:from-purple-800/50 hover:to-blue-800/50 hover:shadow-xl"
          style="animation-delay: 0.5s"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="mb-4 size-12 text-purple-400 transition-transform hover:rotate-12"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
          </svg>
          <h3 class="mb-2 text-2xl font-semibold text-white">
            智能练习生成
          </h3>
          <p class="text-gray-300">
            基于AI智能分析目标单词，自动生成包含这些单词的趣味小故事，让练习更加生动有趣。
          </p>
        </div>
      </div>
    </main>

    <footer class="mt-20 border-t border-gray-800 bg-[#0A0B1A]">
      <div class="container mx-auto px-4 py-3">
        <div class="flex flex-col items-center justify-center">
          <p class="text-gray-400">
            &copy; 2024 句乐部. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}
.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out forwards;
}
.animate-pulse {
  animation: pulse 2s infinite;
}
.animate-float {
  animation: float 4s ease-in-out infinite;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.feature-card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease-in-out;
}
.feature-card:hover {
  box-shadow:
    0 20px 25px -5px rgba(124, 58, 237, 0.1),
    0 10px 10px -5px rgba(124, 58, 237, 0.04);
}
.bg-gradient-radial {
  background-image: radial-gradient(circle at center, rgba(124, 58, 237, 0.15) 0%, rgba(10, 11, 26, 0) 80%);
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.bg-animate {
  background-size: 200% 200%;
  animation: gradientShift 15s ease infinite;
}
</style>
