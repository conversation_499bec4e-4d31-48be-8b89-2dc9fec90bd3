<script setup lang="ts">
import Loading from 'vue-loading-overlay'
import 'vue-loading-overlay/dist/css/index.css'

const loadingTips = [
  '我叫小句子，正在努力加载中...',
  '别着急，小句子在飞速运转中~',
  '马上就好，再等等小句子一下下',
  '小句子正在为您准备精彩内容，稍等片刻',
  '小句子正在开动小脑筋，马上就来咯~',
  '让小句子再蹦跶一会儿，很快就好啦',
  '小句子正在施展魔法，请稍等片刻',
  '小句子在打包精彩内容，马上就送到~',
  '小句子正在加速冲刺，即将抵达终点！',
  '让小句子再蹦跶一会儿，马上就好啦~',
]

const { isLoading, loadingTip } = useLoadingSpinner()

// 监听 isLoading 变化,当为 true 时重新计算 tips
const defaultLoadingTip = ref('')

watch(
  () => isLoading.value,
  (newValue) => {
    if (newValue) {
      defaultLoadingTip.value
        = loadingTips[Math.floor(Math.random() * loadingTips.length)]
    }
  },
)
</script>

<template>
  <Transition
    leave-active-class="transition-all duration-500 ease-in"
    leave-to-class="opacity-0 scale-150"
  >
    <Loading
      v-model:active="isLoading"
      background-color="#000"
      :can-cancel="false"
      :is-full-page="true"
    >
      <template #default>
        <div class="flex flex-col items-center">
          <img
            src="/logo.svg"
            alt="Logo"
            class="animate-pulse-scale size-16"
          >
        </div>
      </template>
      <template #after>
        <div
          class="absolute left-1/2 mt-3 w-screen -translate-x-1/2 text-center"
        >
          {{ loadingTip || defaultLoadingTip }}
        </div>
      </template>
    </Loading>
  </Transition>
</template>

<style>
@keyframes pulse-scale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.animate-pulse-scale {
  animation: pulse-scale 1.5s ease-in-out infinite;
}

:deep(.vl-overlay) {
  transition: opacity 0.4s ease-out;
  transform-origin: center center;
}

:deep(.vl-overlay.vl-active) {
  opacity: 1;
}

:deep(.vl-overlay.vl-inactive) {
  opacity: 0;
}
</style>
