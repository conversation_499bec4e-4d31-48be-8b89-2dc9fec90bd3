<script setup lang="ts">
const props = defineProps<{
  modelValue: boolean
  title?: string
  width?: string
  preventClose?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'close'): void
}>()

function handleClose() {
  if (props.preventClose)
    return
  emit('update:modelValue', false)
  emit('close')
}
</script>

<template>
  <Transition
    enter-active-class="transition duration-200 ease-out"
    enter-from-class="transform scale-95 opacity-0"
    enter-to-class="transform scale-100 opacity-100"
    leave-active-class="transition duration-150 ease-in"
    leave-from-class="transform scale-100 opacity-100"
    leave-to-class="transform scale-95 opacity-0"
  >
    <div v-if="modelValue" class="fixed inset-0 z-50 overflow-y-auto">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-black/50 backdrop-blur-sm" @click="handleClose" />

      <!-- 模态框内容 -->
      <div class="flex min-h-full items-center justify-center p-4">
        <div
          class="relative w-full overflow-hidden rounded-2xl border border-purple-500/20 bg-gray-900/95 p-6 text-left align-middle shadow-xl backdrop-blur-xl transition-all"
          :class="[width]"
        >
          <!-- 标题栏 -->
          <div class="flex items-center justify-between border-b border-slate-700/50 pb-4">
            <div class="flex items-center gap-3">
              <slot name="title">
                <h3 class="text-xl font-medium text-slate-200">
                  {{ title }}
                </h3>
              </slot>
            </div>

            <button
              class="flex items-center justify-center rounded-lg p-2 text-slate-400 transition-colors hover:bg-slate-700/50"
              :disabled="preventClose"
              @click="handleClose"
            >
              <UIcon name="i-heroicons-x-mark" class="size-5" />
            </button>
          </div>

          <!-- 内容区域 -->
          <div class="mt-4">
            <slot />
          </div>

          <!-- 底部按钮区域 -->
          <div v-if="$slots.footer" class="mt-6 flex justify-end gap-3 border-t border-slate-700/50 pt-4">
            <slot name="footer" />
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>
