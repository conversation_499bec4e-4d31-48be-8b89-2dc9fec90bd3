<script setup lang="ts">
const props = defineProps<{
  show: boolean
  text?: string
}>()

defineEmits<{
  afterLeave: []
}>()

// 只有在初始状态为 true 时才渲染过渡组件
const shouldRender = ref(props.show)

watch(() => props.show, (newVal) => {
  if (newVal) {
    shouldRender.value = true
  }
  else {
    // 如果是从未显示状态直接设置为 false，则不需要渲染过渡组件
    if (!shouldRender.value)
      return

    // 如果之前显示过，等待过渡动画结束后再移除组件
    setTimeout(() => {
      shouldRender.value = false
    }, 500) // 与过渡动画持续时间相同
  }
}, { immediate: true })
</script>

<template>
  <Transition
    v-if="shouldRender"
    enter-active-class="transition-all duration-500 ease-out"
    leave-active-class="transition-all duration-500 ease-in"
    enter-from-class="opacity-0 scale-50"
    leave-to-class="opacity-0 scale-150"
    @after-leave="$emit('afterLeave')"
  >
    <div
      v-if="show"
      class="absolute inset-0 z-50 flex flex-col items-center justify-center"
    >
      <div class="absolute inset-0 bg-gray-900/80 backdrop-blur-sm" />

      <div class="relative z-10 flex flex-col items-center">
        <img
          src="/logo.svg"
          alt="Logo"
          class="animate-pulse-scale size-16"
        >
        <p v-if="text" class="mt-4 text-sm text-slate-300">
          {{ text }}
        </p>
      </div>
    </div>
  </Transition>
</template>

<style scoped>
@keyframes pulse-scale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.animate-pulse-scale {
  animation: pulse-scale 1.5s ease-in-out infinite;
}
</style>
