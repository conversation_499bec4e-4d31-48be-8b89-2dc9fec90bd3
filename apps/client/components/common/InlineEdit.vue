<script setup lang="ts">
import { NInput } from 'naive-ui'

const props = defineProps<{
  value: string
  textClass?: string
  inputClass?: string
  placeholder?: string
  saving?: boolean
}>()

const emit = defineEmits<{
  save: [value: string]
}>()

const isEditing = ref(false)
const editValue = ref('')
const editInput = ref<HTMLInputElement | null>(null)

function startEdit() {
  editValue.value = props.value
  isEditing.value = true
  nextTick(() => {
    editInput.value?.focus()
  })
}

function handleBlur() {
  editValue.value = props.value // 恢复原值
  isEditing.value = false // 退出编辑模式
}

function handleEnter(e: KeyboardEvent) {
  // 如果正在输入法编辑中,则不处理回车事件
  if (e.isComposing)
    return

  e.preventDefault() // 阻止默认行为
  e.stopPropagation() // 阻止冒泡

  if (editValue.value !== props.value) {
    emit('save', editValue.value)
  }
  isEditing.value = false
}

watch(
  () => props.value,
  (newValue) => {
    if (!isEditing.value) {
      editValue.value = newValue
    }
  },
)

// 暴露方法供外部使用
defineExpose({
  startEdit,
})
</script>

<template>
  <div class="group relative inline-flex items-start">
    <span
      v-if="!isEditing"
      class="flex cursor-pointer items-start gap-2 rounded pr-1 group-hover:bg-slate-700/50"
      :class="[textClass, { 'text-slate-500': !value }]"
      @click="startEdit"
    >
      <span class="break-all">{{ value || placeholder }}</span>
      <UIcon
        name="i-ph-pencil-simple"
        class="size-4 shrink-0 opacity-0 transition-opacity group-hover:opacity-100"
      />
    </span>
    <NInput
      v-else
      ref="editInput"
      v-model:value="editValue"
      type="textarea"
      :placeholder="placeholder"
      :autosize="{
        minRows: 1,
        maxRows: 5,
      }"
      class="min-w-[200px]"
      :disabled="saving"
      @blur="handleBlur"
      @keydown.enter="handleEnter"
    />
  </div>
</template>
