<script setup lang="ts">
import { toast } from 'vue-sonner'

import { injectHttpAPIStatusErrorHandler } from '~/api/http'
import { injectHttpTRPCStatusErrorHandler } from '~/plugins/trpc'
import { signIn, signOut } from '~/services/auth'

useHttpStatusError()

function useHttpStatusError() {
  function handler(errMessage: string, statusCode: number) {
    switch (statusCode) {
      case 401:
        toast.error(errMessage, {
          duration: 2000,
          onAutoClose() {
            signIn()
          },
        })
        break
      case 403:
        // 禁止进入
        toast.error(errMessage, {
          duration: 2000,
          onAutoClose() {
            signOut()
          },
        })
        break
      default:
        toast.error(errMessage)
        break
    }
  }

  injectHttpAPIStatusErrorHandler(handler)
  injectHttpTRPCStatusErrorHandler(handler)
}
</script>

<template>
  <slot />
</template>

<style scoped></style>
