<script setup lang="ts">
interface Props {
  title?: string
  subtitle?: string
}

withDefaults(defineProps<Props>(), {
  title: '',
  subtitle: '',
})

// 定义插槽
defineSlots<{
  'left-content'?: any
  'right-content'?: any
  'center-content'?: any
}>()
</script>

<template>
  <div
    class="mx-6 mb-2 mt-4 rounded-md bg-slate-800 p-4 transition-colors"
  >
    <div class="flex items-center justify-between">
      <!-- 左侧内容插槽 -->
      <div class="flex items-center space-x-2">
        <slot name="left-content">
          <!-- 默认标题区域 -->
          <div v-if="title || subtitle" class="flex flex-col">
            <div v-if="title" class="text-lg font-semibold text-white">
              {{ title }}
            </div>
            <div v-if="subtitle" class="text-sm text-slate-400">
              {{ subtitle }}
            </div>
          </div>
        </slot>
      </div>

      <!-- 中间内容插槽 -->
      <div class="mx-4 flex-1">
        <slot name="center-content" />
      </div>

      <!-- 右侧内容插槽 -->
      <div class="flex items-center space-x-2">
        <slot name="right-content" />
      </div>
    </div>
  </div>
</template>
