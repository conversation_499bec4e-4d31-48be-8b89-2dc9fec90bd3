<script lang="ts" setup>
import type { PropType } from 'vue'
import { onMounted, onUnmounted } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  content: {
    type: String,
    default: '',
  },
  showCancel: {
    type: Boolean,
    default: false,
  },
  cancelText: {
    type: String,
    default: '取消',
  },
  showConfirm: {
    type: Boolean,
    default: false,
  },
  confirmText: {
    type: String,
    default: '确认',
  },
  showThirdButton: {
    type: Boolean,
    default: false,
  },
  thirdButtonText: {
    type: String,
    default: '第三个选项',
  },
  thirdButtonColor: {
    type: String as PropType<'primary' | 'gray' | 'red' | 'orange' | 'amber' | 'yellow' | 'lime' | 'green' | 'emerald' | 'teal' | 'cyan' | 'sky' | 'blue' | 'indigo' | 'violet' | 'purple' | 'fuchsia' | 'pink' | 'rose'>,
    default: 'amber',
  },
})

const emit = defineEmits(['cancel', 'confirm', 'thirdButton'])
const modal = useModal()
async function onCancel() {
  await modal.close()
  emit('cancel')
}
async function onConfirm() {
  await modal.close()
  emit('confirm')
}
async function onThirdButton() {
  await modal.close()
  emit('thirdButton')
}

function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'Enter' && props.showConfirm) {
    event.preventDefault()
    event.stopPropagation()
    onConfirm()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <UModal :ui="{ width: 'w-full sm:max-w-lg' }">
    <div class="relative flex max-h-[85vh] flex-col overflow-hidden rounded-2xl border border-purple-500/20 bg-gray-900/95 backdrop-blur-xl">
      <!-- 装饰性背景 -->
      <div class="absolute inset-0">
        <div class="animate-float absolute -right-32 -top-32 size-64 rounded-full bg-purple-500/10 blur-3xl" />
        <div class="animate-float-delay absolute -bottom-32 -left-32 size-64 rounded-full bg-blue-500/10 blur-3xl" />
      </div>

      <!-- 标题区域 -->
      <div class="relative z-10 shrink-0 p-8 pb-0">
        <h2 class="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-2xl font-bold text-transparent">
          {{ title }}
        </h2>
      </div>

      <!-- 内容区域 - 可滚动 -->
      <div class="relative z-10 min-h-0 flex-1 overflow-y-auto px-8 py-6">
        <p class="whitespace-pre-line break-words text-base leading-relaxed text-gray-300">
          {{ content }}
        </p>
      </div>

      <!-- 按钮区域 -->
      <div class="relative z-10 shrink-0 p-8 pt-0">
        <div class="flex w-full justify-end space-x-3">
          <UButton
            v-if="showCancel"
            color="gray"
            class="px-8 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-gray-500/20"
            @click="onCancel"
          >
            {{ cancelText || "取消" }}
          </UButton>
          <UButton
            v-if="showThirdButton"
            :color="thirdButtonColor"
            class="px-8 transition-all duration-300 hover:scale-105 hover:shadow-lg"
            :class="`hover:shadow-${thirdButtonColor}-500/20`"
            @click="onThirdButton"
          >
            {{ thirdButtonText || "第三个选项" }}
          </UButton>
          <UButton
            v-if="showConfirm"
            class="px-8 transition-all duration-300 hover:scale-105"
            @click="onConfirm"
          >
            {{ confirmText || "确认" }}
          </UButton>
        </div>
      </div>
    </div>
  </UModal>
</template>

<style scoped>
@keyframes float {
  0% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(10px, 10px);
  }
  100% {
    transform: translate(0, 0);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}
</style>
