<script setup lang="ts">
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  text: {
    type: String,
    default: '',
  },
})

// 只有在初始状态为 true 时才渲染过渡组件
const shouldRender = ref(props.show)

watch(() => props.show, (newVal) => {
  if (newVal) {
    shouldRender.value = true
  }
  else {
    // 如果是从未显示状态直接设置为 false，则不需要渲染过渡组件
    if (!shouldRender.value)
      return

    // 如果之前显示过，等待过渡动画结束后再移除组件
    setTimeout(() => {
      shouldRender.value = false
    }, 500) // 与过渡动画持续时间相同
  }
}, { immediate: true })
</script>

<template>
  <Transition
    v-if="shouldRender"
    enter-active-class="transition-all duration-500 ease-out"
    leave-active-class="transition-all duration-500 ease-in"
    enter-from-class="opacity-0 scale-50"
    leave-to-class="opacity-0 scale-150"
  >
    <div
      v-if="show"
      class="fixed inset-0 z-50 flex items-center justify-center bg-slate-900"
    >
      <div class="text-center">
        <img
          src="/logo.svg"
          alt="Loading"
          class="mx-auto size-20 animate-[zoom_2s_ease-in-out_infinite]"
        >
        <p v-if="text" class="mt-4 text-lg text-slate-200">
          {{ text }}
        </p>
      </div>
    </div>
  </Transition>
</template>

<style scoped>
@keyframes zoom {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
}
</style>
