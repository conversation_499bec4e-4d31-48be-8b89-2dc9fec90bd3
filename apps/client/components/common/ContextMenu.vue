<script setup lang="ts">
interface ContextMenuItem {
  label: string
  icon?: string
  color?: string
  onSelect?: () => void
}

defineProps<{
  items: ContextMenuItem[][]
}>()

const isVisible = ref(false)
const position = ref({ x: 0, y: 0 })

// 显示菜单
function show(event: MouseEvent) {
  event.preventDefault()
  event.stopPropagation()
  position.value = {
    x: event.clientX,
    y: event.clientY,
  }
  isVisible.value = true
}

// 隐藏菜单
function hide() {
  isVisible.value = false
}

// 处理选择
function handleSelect(item: ContextMenuItem) {
  if (item.onSelect) {
    item.onSelect()
  }
  hide()
}

// 点击其他地方时隐藏菜单
function handleClickOutside(event: MouseEvent) {
  const target = event.target as HTMLElement
  if (target.closest('.context-menu')) {
    return
  }

  if (isVisible.value) {
    hide()
  }
}

// 监听点击事件
onMounted(() => {
  document.addEventListener('mousedown', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('mousedown', handleClickOutside)
})

// 暴露方法给父组件
defineExpose({
  show,
})
</script>

<template>
  <div
    v-if="isVisible"
    class="context-menu fixed z-50 min-w-[160px] rounded-lg border border-slate-700/50 bg-slate-800/90 p-1 shadow-lg backdrop-blur-sm"
    :style="{
      top: `${position.y}px`,
      left: `${position.x}px`,
    }"
  >
    <div v-for="(group, groupIndex) in items" :key="groupIndex">
      <div
        v-for="(item, index) in group"
        :key="index"
        class="flex cursor-pointer items-center gap-2 rounded-md px-2 py-1.5 text-sm text-slate-200 transition-colors hover:bg-slate-700/50"
        :class="{
          'text-red-400 hover:text-red-300': item.color === 'red',
        }"
        @click="handleSelect(item)"
      >
        <UIcon v-if="item.icon" :name="item.icon" class="size-4" />
        <span>{{ item.label }}</span>
      </div>
      <div
        v-if="groupIndex < items.length - 1"
        class="my-1 h-px bg-slate-700/50"
      />
    </div>
  </div>
</template>
