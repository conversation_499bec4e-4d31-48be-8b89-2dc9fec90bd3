<script setup lang="ts">
import { useLoadingBar } from 'naive-ui'
import { toast } from 'vue-sonner'
import { isMac } from '~/utils/os'
import { useContentState } from './useContentState'

const emit = defineEmits(['cancel'])
const loadingSpinner = useLoadingSpinner()
const { addViewContent } = useContentState()
const isSubmitting = ref(false)
const loadingFormatting = ref(false)
const courseStore = useCourseStore()
const loadingBar = useLoadingBar()
const { formatAddViewContent, lockTab, preventCloseModal } = useContentState()

const formatButtons = ref([
  {
    label: '词汇格式化(全英)',
    icon: 'heroicons:document-text',
    type: 'vocabularyToEnglish' as const,
    active: false,
  },
  {
    label: '词汇格式化(中英)',
    icon: 'heroicons:language',
    type: 'vocabularyToEnglishAndChinese' as const,
    active: false,
  },
  {
    label: '句子格式化(全英)',
    icon: 'heroicons:chat-bubble-left-right',
    type: 'sentenceToEnglish' as const,
    active: false,
  },
  {
    label: '句子格式化(中英)',
    icon: 'heroicons:pencil-square',
    type: 'sentenceToEnglishAndChinese' as const,
    active: false,
  },
])

// 添加快捷键处理函数
function handleKeydown(e: KeyboardEvent) {
  // Mac: Cmd+Enter, Windows: Ctrl+Enter
  if (
    e.key === 'Enter'
    && ((isMac() && e.metaKey) || (!isMac() && e.ctrlKey))
  ) {
    e.preventDefault()
    if (!isSubmitting.value && !loadingFormatting.value) {
      onConfirm()
    }
  }
}

async function handleFormattingContent(button: typeof formatButtons.value[0]) {
  if (addViewContent.value.length === 0) {
    toast.error('请输入内容')
    return
  }

  loadingBar.start()
  loadingFormatting.value = true
  button.active = true
  lockTab(true)
  preventCloseModal(true)

  try {
    await formatAddViewContent(button.type)
    toast.success('格式化成功')
  }
  finally {
    button.active = false
    loadingFormatting.value = false
    lockTab(false)
    preventCloseModal(false)
    loadingBar.finish()
  }
}

async function onConfirm() {
  if (isSubmitting.value)
    return

  if (!addViewContent.value.trim()) {
    toast.warning('请输入句子内容')
    return
  }

  const linesCount = addViewContent.value.split('\n').length
  if (linesCount > 1000) {
    toast.error('一个课程最多只能添加1000个句子，请拆分多个课程来录入')
    return
  }

  try {
    isSubmitting.value = true
    preventCloseModal(true)
    loadingSpinner.startLoading(['小句子正在添加，请稍候...', '请耐心等待，需要大模型预先解析句子哦。'])
    await courseStore.addSentences(addViewContent.value)

    toast.success('添加成功')
    addViewContent.value = ''
    emit('cancel')
  }
  finally {
    loadingSpinner.finishLoading()
    isSubmitting.value = false
    preventCloseModal(false)
  }
}
</script>

<template>
  <div class="flex flex-1 flex-col">
    <!-- 格式化按钮组 -->
    <div class="mb-4 flex flex-wrap gap-3">
      <UButton
        v-for="btn in formatButtons"
        :key="btn.label"
        :disabled="loadingFormatting"
        color="gray"
        class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95 disabled:cursor-not-allowed disabled:opacity-50"
        :loading="btn.active"
        :icon="btn.icon"
        @click="() => handleFormattingContent(btn)"
      >
        {{ btn.label }}
      </UButton>
    </div>

    <!-- 输入区域 -->
    <div class="flex flex-1 flex-col">
      <textarea
        v-model="addViewContent"
        placeholder="请输入句子内容... 支持批量添加，每行一个句子"
        class="w-full flex-1 resize-none rounded-lg border border-slate-700 bg-slate-800/50 p-4 text-gray-200 placeholder:text-gray-500 focus:border-purple-500 focus:outline-none focus:ring-1 focus:ring-purple-500"
        @keydown="handleKeydown"
      />
    </div>

    <!-- 操作按钮 -->
    <div class="mt-6 flex justify-end space-x-4">
      <UButton
        color="gray"
        :disabled="isSubmitting || loadingFormatting"
        class="px-8 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-gray-500/20"
        @click="$emit('cancel')"
      >
        取消
      </UButton>
      <UButton
        :disabled="isSubmitting || loadingFormatting"
        class="px-8 transition-all duration-300 hover:scale-105"
        @click="onConfirm"
      >
        添加
      </UButton>
    </div>
  </div>
</template>
