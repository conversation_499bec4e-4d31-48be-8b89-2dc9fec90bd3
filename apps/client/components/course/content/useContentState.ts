// 定义标签页枚举
export enum TabType {
  Add = 'add',
  Generate = 'generate',
}

// 共享状态
const activeTab = ref<TabType>(TabType.Add)
const addViewContent = ref('')
const isLockedTab = ref(false)
const isPreventCloseModal = ref(false)

export function useContentState() {
  const { $trpc } = useNuxtApp()
  // 切换标签页
  const switchTab = (tab: TabType) => {
    activeTab.value = tab
  }

  // 设置 AddView 的内容
  const setAddViewContent = (content: string) => {
    addViewContent.value = content
  }

  async function formatAddViewContent(
    type:
      | 'sentenceToEnglish'
      | 'sentenceToEnglishAndChinese'
      | 'vocabularyToEnglish'
      | 'vocabularyToEnglishAndChinese',
  ) {
    const result = await $trpc.content.format.mutate({
      content: addViewContent.value,
      type,
    })

    if (result) {
      addViewContent.value = result
    }
  }

  function lockTab(lock: boolean) {
    isLockedTab.value = lock
  }

  function preventCloseModal(prevent: boolean) {
    isPreventCloseModal.value = prevent
  }

  return {
    activeTab,
    addViewContent,
    isLockTab: isLockedTab,
    isPreventCloseModal,
    switchTab,
    setAddViewContent,
    formatAddViewContent,
    lockTab,
    preventCloseModal,
  }
}
