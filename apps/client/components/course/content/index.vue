<script setup lang="ts">
import AddView from './AddView.vue'
import GenerateView from './GenerateView.vue'
import { TabType, useContentState } from './useContentState'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const { activeTab, switchTab, isLockTab, isPreventCloseModal }
  = useContentState()

// 使用拆分的组合式函数
const tabs = [
  { key: 'add', label: '手动添加' },
  { key: 'generate', label: '智能生成' },
]

const isLocked = ref(false)
const preventClose = ref(false)

// 添加 watch 来监听模态框的开启状态
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      // 当模态框打开时，重置 isLocked 状态
      isLocked.value = false
      preventClose.value = false
      switchTab(TabType.Add)
    }
  },
)

function onCancel() {
  emit('update:modelValue', false)
}
</script>

<template>
  <UModal
    :model-value="modelValue"
    :prevent-close="isPreventCloseModal"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <UContainer
      :ui="{
        base: 'flex flex-col p-0 w-[55vw] h-[90vh] ',
        constrained: 'p-0 sm:px-0 lg:px-0 max-w-[780px] max-h-[700px]',
      }"
    >
      <div
        class="relative size-full overflow-hidden rounded-2xl border border-purple-500/20 bg-gray-900/95 backdrop-blur-xl"
      >
        <!-- 装饰性背景 -->
        <div class="absolute inset-0">
          <div
            class="animate-float absolute -right-32 -top-32 size-64 rounded-full bg-purple-500/10 blur-3xl"
          />
          <div
            class="animate-float-delay absolute -bottom-32 -left-32 size-64 rounded-full bg-blue-500/10 blur-3xl"
          />
        </div>

        <!-- 内容层 -->
        <div class="relative z-10 flex h-full flex-col p-8">
          <!-- 标签页切换 -->
          <div class="mb-6 flex space-x-4">
            <button
              v-for="tab in tabs"
              :key="tab.key"
              :disabled="isLockTab"
              class="relative px-4 py-2 text-xl font-semibold transition-all duration-300"
              :class="[
                activeTab === tab.key
                  ? 'text-purple-400'
                  : 'text-gray-400 hover:text-gray-300',
              ]"
              @click="switchTab(tab.key as TabType)"
            >
              {{ tab.label }}
              <div
                v-if="activeTab === tab.key"
                class="absolute bottom-0 left-0 h-0.5 w-full bg-gradient-to-r from-purple-400 to-blue-400"
              />
            </button>
          </div>

          <!-- 修改内容视图部分，添加 KeepAlive -->
          <KeepAlive>
            <template v-if="activeTab === TabType.Add">
              <AddView @cancel="onCancel" />
            </template>

            <template v-else-if="activeTab === TabType.Generate">
              <GenerateView @cancel="onCancel" />
            </template>
          </KeepAlive>
        </div>
      </div>
    </UContainer>
  </UModal>
</template>

<style scoped>
@keyframes float {
  0% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(10px, 10px);
  }
  100% {
    transform: translate(0, 0);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-delay {
  animation: float 3s ease-in-out infinite;
  animation-delay: 1.5s;
}
</style>
