<script setup lang="ts">
import { useLoadingBar } from 'naive-ui'
import { toast } from 'vue-sonner'
import { TabType, useContentState } from './useContentState'

defineEmits(['cancel'])

const { $trpc } = useNuxtApp()

const keywords = ref('')
const generatedContent = ref('')
const isSubmitting = ref(false)
const hasGenerated = ref(false)
const loadingBar = useLoadingBar()
const isFormatting = ref(false)
const {
  switchTab,
  setAddViewContent,
  formatAddViewContent,
  lockTab,
  preventCloseModal,
} = useContentState()

const englishLevelOptions = [
  { label: '小学低年级 (1-3年级)', value: '小学低年级 (1-3年级)' },
  { label: '小学高年级 (4-6年级)', value: '小学高年级 (4-6年级)' },
  { label: '初中水平', value: '初中水平' },
  { label: '高中水平', value: '高中水平' },
  { label: '大学入门', value: '大学入门' },
  { label: '大学中级', value: '大学中级' },
  { label: '大学高级', value: '大学高级' },
  { label: '研究生水平', value: '研究生水平' },
  { label: '初学者 (A1)', value: '初学者 (A1)' },
  { label: '基础 (A2)', value: '基础 (A2)' },
  { label: '中级 (B1)', value: '中级 (B1)' },
  { label: '中高级 (B2)', value: '中高级 (B2)' },
  { label: '高级 (C1)', value: '高级 (C1)' },
  { label: '精通 (C2)', value: '精通 (C2)' },
]

const englishLevel = ref(englishLevelOptions[0])

watch(keywords, (newValue) => {
  if (newValue.length === 0) {
    hasGenerated.value = false
  }
})

async function onGenerate() {
  if (isSubmitting.value)
    return
  if (!keywords.value.trim()) {
    toast.warning('请输入关键词')
    return
  }

  try {
    isSubmitting.value = true
    loadingBar.start()
    lockTab(true)
    preventCloseModal(true)
    const result = await $trpc.content.generateWithWords.mutate({
      words: keywords.value,
      englishLevel: englishLevel.value.value,
    })

    generatedContent.value = result?.content || ''
    hasGenerated.value = true
    toast.success('生成成功')
  }
  catch {
    toast.error('生成失败，请重试')
  }
  finally {
    loadingBar.finish()
    isSubmitting.value = false
    lockTab(false)
    preventCloseModal(false)
  }
}

async function useGeneratedContent() {
  try {
    isFormatting.value = true
    loadingBar.start()
    lockTab(true)
    preventCloseModal(true)
    setAddViewContent(generatedContent.value)
    await formatAddViewContent('sentenceToEnglish')
    // 格式化完成后再切换到 AddView 标签页
    switchTab(TabType.Add)
    // 重置本地状态
    resetForm()
  }
  catch {
    toast.error('内容格式化失败，请重试')
  }
  finally {
    isFormatting.value = false
    lockTab(false)
    preventCloseModal(false)
    loadingBar.finish()
  }
}

function resetForm() {
  keywords.value = ''
  generatedContent.value = ''
  hasGenerated.value = false
  englishLevel.value = englishLevelOptions[0]
}
</script>

<template>
  <div class="flex flex-1 flex-col">
    <!-- 英语水平选择 -->
    <div class="mb-4">
      <div class="mb-3 text-base font-medium text-gray-300">
        选择英语水平
      </div>
      <UInputMenu
        v-model="englishLevel"
        :options="englishLevelOptions"
        size="md"
        class="w-full"
      />

      <!-- 关键词输入 -->
      <div class="mb-5 mt-6">
        <div class="mb-3 text-base font-medium text-gray-300">
          输入单词（建议不超过5个单词）
        </div>
        <input
          v-model="keywords"
          type="text"
          placeholder="例如: cat garden sunny"
          class="w-full rounded-lg border border-slate-700 bg-slate-800/50 p-4 text-base text-gray-200 placeholder:text-gray-400 focus:border-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
        >
      </div>
    </div>

    <!-- 生成的内容显示区域 -->
    <div class="flex flex-1 flex-col">
      <textarea
        v-model="generatedContent"
        placeholder="生成的内容将显示在这里..."
        readonly
        class="w-full flex-1 resize-none rounded-lg border border-slate-700 bg-slate-800/50 p-5 text-base leading-relaxed text-gray-200 placeholder:text-gray-400"
      />
    </div>

    <!-- 显示词数统计 -->
    <div v-if="hasGenerated" class="mt-5 space-y-2 text-base text-gray-300">
      <p>
        故事总词数：<span class="font-medium text-blue-400">{{
          generatedContent.split(" ").length
        }}</span>
        个
      </p>
      <p>
        不同单词数：<span class="font-medium text-green-400">{{
          [...new Set(generatedContent.split(" "))].length
        }}</span>
        个
      </p>
    </div>

    <!-- 操作按钮 -->
    <div class="mt-8 flex justify-end space-x-5">
      <UButton
        color="gray"
        :disabled="isSubmitting || isFormatting"
        class="px-8 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-gray-500/20"
        @click="$emit('cancel')"
      >
        取消
      </UButton>
      <UButton
        color="gray"
        :disabled="
          isSubmitting
            || (!keywords.trim() && !generatedContent)
            || isFormatting
        "
        class="px-8 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-gray-500/20"
        @click="resetForm"
      >
        重置
      </UButton>
      <UButton
        :disabled="!generatedContent.trim()"
        :loading="isFormatting"
        class="px-8 transition-all duration-300 hover:scale-105"
        @click="useGeneratedContent"
      >
        使用内容
      </UButton>
      <UButton
        :disabled="isSubmitting || !keywords.trim() || isFormatting"
        :loading="isSubmitting"
        class="px-8 transition-all duration-300 hover:scale-105"
        @click="onGenerate"
      >
        {{ hasGenerated ? "重新生成" : "生成" }}
      </UButton>
    </div>
  </div>
</template>

<style scoped></style>
