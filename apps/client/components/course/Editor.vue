<script setup lang="ts">
import type { FormError } from '#ui/types'
import { useLoadingBar } from 'naive-ui'
import { onMounted } from 'vue'
import { toast } from 'vue-sonner'
import { z } from 'zod'
import { useCourseStore } from '~/stores/course'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const isOpen = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

const courseStore = useCourseStore()
const loadingBar = useLoadingBar()
const isLoading = ref(false)

const state = reactive({
  title: '',
  description: '',
})

onMounted(() => {
  initState()
})

function initState() {
  state.title = courseStore.currentCourse?.title || ''
  state.description = courseStore.currentCourse?.description || ''
}

// 定义Zod schema
const zodSchema = z.object({
  title: z.string().min(2, '标题至少需要2个字符'),
  description: z.string().min(2, '描述至少需要2个字符'),
})

// 使用Zod进行验证，但通过validate函数与UForm集成
function validate(state: any): FormError[] {
  try {
    // 尝试验证数据
    zodSchema.parse(state)
    return [] // 验证通过，返回空错误数组
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      // 将Zod错误转换为UForm期望的格式
      return error.errors.map(err => ({
        path: err.path.join('.'),
        message: err.message,
      }))
    }
    return [] // 其他类型错误，返回空数组
  }
}

function noUpdate() {
  return (
    state.title === courseStore.currentCourse?.title
    && state.description === courseStore.currentCourse?.description
  )
}

async function onSubmit() {
  if (noUpdate()) {
    isOpen.value = false
    return
  }

  loadingBar.start()
  isLoading.value = true

  try {
    await courseStore.editCourse(
      state.title,
      state.description,
    )
    await delay(700)
    isOpen.value = false
    toast.success('更新成功')
  }
  catch (error) {
    if (error instanceof Error) {
      toast.error(error.message)
    }
  }
  finally {
    isLoading.value = false
    loadingBar.finish()
  }
}

watch(
  () => isOpen.value,
  async (val) => {
    if (!val) {
      onClose()
    }
    else {
      initState()
    }
  },
)

function onClose() {
  // 清理状态
}
</script>

<template>
  <div>
    <UModal
      :model-value="isOpen"
      :ui="{ width: 'w-full sm:max-w-lg' }"
      @update:model-value="isOpen = $event"
    >
      <div
        class="relative overflow-hidden rounded-2xl border border-purple-500/20 bg-gray-900/95 backdrop-blur-xl"
      >
        <div class="absolute inset-0">
          <div
            class="absolute -right-32 -top-32 size-64 rounded-full bg-purple-500/10 blur-3xl"
          />
          <div
            class="absolute -bottom-32 -left-32 size-64 rounded-full bg-blue-500/10 blur-3xl"
          />
        </div>

        <div class="relative z-10 p-8">
          <h2
            class="mb-6 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-2xl font-bold text-transparent"
          >
            编辑课程
          </h2>

          <UForm
            :validate="validate"
            :state="state"
            class="space-y-6"
            @submit="onSubmit"
          >
            <UFormGroup label="标题" name="title" class="text-gray-300">
              <UInput
                v-model="state.title"
                placeholder="输入标题"
                class="border-purple-500/20 bg-gray-800/50 focus:border-purple-500/50"
              />
            </UFormGroup>

            <UFormGroup label="描述" name="description">
              <UTextarea
                v-model="state.description"
                placeholder="输入详情..."
              />
            </UFormGroup>

            <div class="mt-8 flex justify-end space-x-4">
              <UButton
                color="gray"
                class="px-8 transition-all duration-300 hover:scale-105"
                @click="isOpen = false"
              >
                取消
              </UButton>
              <UButton
                type="submit"
                :loading="isLoading"
                class="bg-gradient-to-r px-8 transition-all duration-300 hover:scale-105"
              >
                更新
              </UButton>
            </div>
          </UForm>
        </div>
      </div>
    </UModal>
  </div>
</template>
