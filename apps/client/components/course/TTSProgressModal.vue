<script setup lang="ts">
import type { TrpcClient } from '~/plugins/trpc'
import { useLoadingBar } from 'naive-ui'
import { computed, watch } from 'vue'
import { toast } from 'vue-sonner'

const props = defineProps<{
  show: boolean
  courseId: string
  action: 'publish' | 'update'
}>()

const emit = defineEmits<{
  (e: 'update:show', value: boolean): void
  (e: 'success'): void
}>()

const loadingBar = useLoadingBar()
const { $trpc } = useNuxtApp()

const actionText = computed(() => {
  return props.action === 'publish' ? '发布' : '更新'
})

async function generateTTS() {
  try {
    loadingBar.start()
    await ($trpc as TrpcClient).tts.generateTTSForCourse.mutate({
      courseId: props.courseId,
    })

    emit('success')
  }
  catch (error) {
    console.error('Error generating TTS:', error)
    toast.error(`音频文件生成失败，${actionText.value}无法完成`)
  }
  finally {
    loadingBar.finish()
    emit('update:show', false)
  }
}

function handleClose() {
  emit('update:show', false)
}

watch(() => props.show, (newVal) => {
  if (newVal) {
    generateTTS()
  }
})
</script>

<template>
  <UModal
    :model-value="show"
    :ui="{ width: 'w-full sm:max-w-lg' }"
    :prevent-close="true"
    @update:model-value="handleClose"
  >
    <div
      class="relative rounded-2xl border border-purple-500/20 bg-gray-900/95 backdrop-blur-xl"
    >
      <div class="relative z-10 p-8">
        <div class="space-y-6">
          <h2
            class="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-2xl font-bold text-transparent"
          >
            音频生成
          </h2>

          <!-- 加载动画 -->
          <div class="flex justify-center py-4">
            <div class="relative size-20">
              <div class="absolute size-full animate-ping rounded-full bg-purple-400 opacity-20" />
              <div class="absolute inset-2 animate-pulse rounded-full bg-gradient-to-r from-purple-500 to-pink-500 opacity-70" />
              <div class="absolute inset-0 flex items-center justify-center">
                <UIcon name="i-heroicons-musical-note" class="size-8 text-white" />
              </div>
            </div>
          </div>

          <div class="space-y-4 text-slate-300">
            <p class="font-medium">
              正在为您生成课程所需的所有音频文件，请不要关闭页面...
            </p>

            <div class="rounded-lg bg-slate-800/50 p-4 text-sm">
              <p class="mb-2 font-medium text-purple-300">
                为什么需要等待？
              </p>
              <p class="text-slate-400">
                在{{ actionText }}课程前，我们需要提前生成该课程所需的所有音频文件，这样在游戏中就可以顺利播放，无需额外等待，为您提供流畅的学习体验。
              </p>
            </div>

            <div class="mt-2 text-xs text-slate-500">
              <p>生成时间取决于课程内容长度，请耐心等待</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </UModal>
</template>

<style scoped>
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes ping {
  75%,
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: ping 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}
</style>
