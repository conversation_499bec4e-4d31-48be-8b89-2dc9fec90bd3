<script setup lang="ts">
import { CourseType } from '@julebu/shared'
import { useLoadingBar } from 'naive-ui'
import { toast } from 'vue-sonner'
import Dialog from '~/components/common/Dialog.vue'
import CourseEditor from '~/components/course/Editor.vue'
import { useChangeDetection } from '~/composables/subtitle/useChangeDetection'
import { useCourseStore } from '~/stores/course'
import { useGameUpdateStore } from '~/stores/gameUpdate'
import { formatDate } from '~/utils/date'
import TTSProgressModal from './TTSProgressModal.vue'

const props = defineProps<{
  modelValue: boolean
  beforePublish?: () => Promise<boolean> | boolean
  beforeUpdate?: () => Promise<boolean> | boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const isOpen = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

const router = useRouter()
const route = useRoute()
const courseStore = useCourseStore()
const coursePackStore = useCoursePackStore()
const showEditor = ref(false)
const gameUpdateStore = useGameUpdateStore()
const loadingSpinner = useLoadingSpinner()
const modal = useModal()
const config = useRuntimeConfig()
const loadingBar = useLoadingBar()
const showTTSProgress = ref(false)
const ttsAction = ref<'publish' | 'update'>('publish')
const { hasUnsavedChanges } = useChangeDetection()

// 处理编辑按钮点击
function handleEdit() {
  isOpen.value = false
  showEditor.value = true
}

// 添加处理编辑器关闭的函数
function handleEditorClose() {
  showEditor.value = false
  isOpen.value = true
}

watch(
  () => isOpen.value,
  (val) => {
    if (!val) {
      onClose()
    }
  },
)

function onClose() {
  // 清理状态
}

// 显示TTS生成模态框
function showTTSGeneration(action: 'publish' | 'update') {
  ttsAction.value = action
  showTTSProgress.value = true
}

async function handlePublish() {
  // 如果有发布前回调，先执行回调
  if (props.beforePublish) {
    // 如果有未保存的更改，先关闭详情弹窗避免弹窗重叠
    if (hasUnsavedChanges.value) {
      isOpen.value = false
    }

    const shouldContinue = await props.beforePublish()
    if (!shouldContinue) {
      return
    }
  }
  await executePublish()
}

// 执行发布逻辑的私有方法
async function executePublish() {
  try {
    if (!coursePackStore.currentCoursePack?.gameId) {
      toast.warning(
        '课程包没有发布过 请先发布课程包哦(回到课程包页面点击发布)',
      )
      return
    }

    // 检查课程类型，只有默认课程需要TTS
    if (courseStore.currentCourse?.type === CourseType.normal) {
      // 显示 TTS 进度 Modal，设置为发布操作
      showTTSGeneration('publish')
    }
    else {
      // 其他类型课程直接发布
      loadingSpinner.startLoading()
      await publishCourse()
    }
  }
  catch (error) {
    console.error('Error in handlePublish:', error)
    toast.error('发布失败')
  }
  finally {
    loadingSpinner.finishLoading()
  }
}

// 发布课程的公共逻辑
async function publishCourse() {
  await courseStore.publishToGame()
  toast.success('课程发布成功')
}

// 更新课程的公共逻辑
async function updateCourse() {
  await courseStore.updateToGame()
  toast.success('课程更新成功')
  isOpen.value = false
  gameUpdateStore.markCourseUpdated()
}

async function handleTTSSuccess() {
  try {
    if (ttsAction.value === 'publish') {
      await publishCourse()
    }
    else {
      await updateCourse()
    }
  }
  catch (error) {
    console.error(`Error in ${ttsAction.value} success:`, error)
    toast.error(`${ttsAction.value === 'publish' ? '发布' : '更新'}失败`)
  }
}

async function handleUnpublish() {
  // 因为 弹出 2 个 modal 的话 会有 bug  所以我们关闭一个
  isOpen.value = false
  modal.open(Dialog, {
    title: '下架课程',
    content: '你确定要下架该课程吗？下架后该课程将会在游戏端删除',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      try {
        loadingSpinner.startLoading()
        await courseStore.unpublishFromGame()
        toast.success('课程下架成功')
        gameUpdateStore.markCoursePackForUpdate()
      }
      finally {
        loadingSpinner.finishLoading()
      }
    },
    onCancel() {
      isOpen.value = true
    },
  })
}

async function handleUpdate() {
  // 如果有更新前回调，先执行回调
  if (props.beforeUpdate) {
    // 如果有未保存的更改，先关闭详情弹窗避免弹窗重叠
    if (hasUnsavedChanges.value) {
      isOpen.value = false
    }

    const shouldContinue = await props.beforeUpdate()
    if (!shouldContinue) {
      return
    }
  }
  await executeUpdate()
}

// 执行更新逻辑的私有方法
async function executeUpdate() {
  try {
    if (!gameUpdateStore.needsCourseUpdate(courseStore.currentCourse?.id || '')) {
      toast.success('课程没有变化 无需更新')
      return
    }

    // 检查课程类型，只有默认课程需要TTS
    if (courseStore.currentCourse?.type === CourseType.normal) {
      // 显示 TTS 进度 Modal，设置为更新操作
      showTTSGeneration('update')
    }
    else {
      // 其他类型课程直接更新
      loadingSpinner.startLoading()
      await updateCourse()
    }
  }
  catch (error) {
    console.error('Error in handleUpdate:', error)
    toast.error('更新失败')
  }
  finally {
    loadingSpinner.finishLoading()
  }
}

function handlePlayGame() {
  const url = `${config.public.gameURL}/game/course/${coursePackStore.currentCoursePack?.gameId}/${courseStore.currentCourse?.gameId}`
  window.open(url, '_blank')
}

function handleDelete() {
  isOpen.value = false

  modal.open(Dialog, {
    title: '警告',
    content: '确定要删除这个课程吗？删除后将无法恢复',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      if (courseStore.currentCourse?.gameId) {
        toast.warning('需要先下架再删除哦')
        return
      }

      // 实现删除课程的逻辑
      loadingBar.start()
      await courseStore.deleteCourse(courseStore.currentCourse?.id || '')
      loadingBar.finish()
      toast.success('课程删除成功')
      await delay(700)
      const coursePackId = route.query.coursePackId ? route.query.coursePackId : route.params.id
      router.push(`/dashboard/course-packs/${coursePackId}`)
    },
    onCancel() {
      isOpen.value = true
    },
  })
}

// 添加关闭处理函数
function handleClose() {
  isOpen.value = false
  onClose() // 调用现有的清理函数
}

// 获取课程类型显示信息
function getCourseTypeInfo(type: CourseType) {
  switch (type) {
    case CourseType.music:
      return {
        text: '音乐课程',
        bgClass: 'bg-gradient-to-r from-purple-500/20 to-pink-500/20',
        textClass: 'text-purple-300',
        borderClass: 'border-purple-500/30',
        icon: '🎵',
      }
    case CourseType.normal:
      return {
        text: '常规课程',
        bgClass: 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20',
        textClass: 'text-blue-300',
        borderClass: 'border-blue-500/30',
        icon: '📚',
      }
    case CourseType.audio:
      return {
        text: '音频课程',
        bgClass: 'bg-gradient-to-r from-orange-500/20 to-amber-500/20',
        textClass: 'text-orange-300',
        borderClass: 'border-orange-500/30',
        icon: '🎧',
      }
    case CourseType.video:
      return {
        text: '视频课程',
        bgClass: 'bg-gradient-to-r from-green-500/20 to-emerald-500/20',
        textClass: 'text-green-300',
        borderClass: 'border-green-500/30',
        icon: '🎬',
      }
    default:
      return {
        text: '常规课程',
        bgClass: 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20',
        textClass: 'text-blue-300',
        borderClass: 'border-blue-500/30',
        icon: '📚',
      }
  }
}
</script>

<template>
  <div>
    <UModal
      :model-value="isOpen"
      :ui="{ width: 'w-full sm:max-w-lg' }"
      @update:model-value="isOpen = $event"
    >
      <div
        class="relative rounded-2xl border border-purple-500/20 bg-gray-900/95 backdrop-blur-xl"
      >
        <button
          class="absolute right-4 top-4 z-20 flex size-8 items-center justify-center rounded-lg border border-slate-700/50 bg-slate-800/50 text-slate-400 hover:border-red-500/50 hover:bg-red-500/20 hover:text-red-400"
          @click="handleClose"
        >
          <UIcon name="i-heroicons-x-mark" class="size-5" />
        </button>

        <div class="relative z-10 p-8">
          <div class="space-y-4">
            <h2
              class="bg-gradient-to-r from-purple-400 via-pink-400 to-purple-400 bg-clip-text text-2xl font-bold text-transparent"
            >
              {{ courseStore.currentCourse?.title }}
            </h2>

            <p class="mb-2 leading-relaxed text-slate-400">
              {{ courseStore.currentCourse?.description }}
            </p>

            <div
              class="space-y-3.5 rounded-lg bg-gradient-to-br from-slate-800/70 to-slate-800/40 p-5 ring-1 ring-slate-700/50 backdrop-blur-lg"
            >
              <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-2">
                  <UIcon
                    name="i-heroicons-calendar"
                    class="size-4 text-slate-400"
                  />
                  <span class="font-medium text-slate-400">创建时间</span>
                </div>
                <span class="font-medium text-slate-300">{{
                  formatDate(courseStore.currentCourse?.createdAt ?? "")
                }}</span>
              </div>

              <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-2">
                  <UIcon
                    name="i-heroicons-clock"
                    class="size-4 text-slate-400"
                  />
                  <span class="font-medium text-slate-400">更新时间</span>
                </div>
                <span class="font-medium text-slate-300">{{
                  formatDate(courseStore.currentCourse?.updatedAt ?? "")
                }}</span>
              </div>

              <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-2">
                  <UIcon
                    name="i-heroicons-book-open"
                    class="size-4 text-slate-400"
                  />
                  <span class="font-medium text-slate-400">练习数量</span>
                </div>
                <span
                  class="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text font-semibold text-transparent"
                >
                  {{ courseStore.totalElementsCount }}
                </span>
              </div>

              <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-2">
                  <UIcon
                    name="i-heroicons-tag"
                    class="size-4 text-slate-400"
                  />
                  <span class="font-medium text-slate-400">课程类型</span>
                </div>
                <span
                  v-if="courseStore.currentCourse?.type"
                  class="inline-flex items-center gap-1.5 rounded-full px-3 py-1 text-xs font-medium ring-1 transition-colors"
                  :class="[
                    getCourseTypeInfo(courseStore.currentCourse.type).bgClass,
                    getCourseTypeInfo(courseStore.currentCourse.type).textClass,
                    getCourseTypeInfo(courseStore.currentCourse.type).borderClass,
                  ]"
                >
                  <span class="select-none text-xs">
                    {{ getCourseTypeInfo(courseStore.currentCourse.type).icon }}
                  </span>
                  {{ getCourseTypeInfo(courseStore.currentCourse.type).text }}
                </span>
                <span v-else class="font-medium text-slate-500">暂无类型</span>
              </div>

              <div class="flex items-center justify-between text-sm">
                <div class="flex items-center gap-2">
                  <UIcon
                    name="i-heroicons-signal"
                    class="size-4 text-slate-400"
                  />
                  <span class="font-medium text-slate-400">发布状态</span>
                </div>
                <span
                  class="rounded-full px-3.5 py-1.5 text-xs font-medium shadow-sm" :class="[
                    courseStore.currentCourse?.gameId
                      ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-400 ring-1 ring-green-500/30'
                      : 'bg-gradient-to-r from-amber-500/20 to-orange-500/20 text-amber-400 ring-1 ring-amber-500/30',
                  ]"
                >
                  <div class="flex items-center gap-1.5">
                    <div
                      class="size-2 rounded-full" :class="[
                        courseStore.currentCourse?.gameId
                          ? 'animate-pulse bg-green-400 shadow-green-400/50'
                          : 'bg-amber-400 shadow-amber-400/50',
                      ]"
                    />
                    {{
                      courseStore.currentCourse?.gameId ? "已发布" : "未发布"
                    }}
                  </div>
                </span>
              </div>
            </div>

            <div class="space-y-2">
              <div class="flex gap-2">
                <button
                  class="inline-flex flex-1 items-center justify-center rounded-lg bg-gradient-to-br from-slate-700/90 to-slate-800/90 px-4 py-2.5 text-sm font-medium text-slate-200 shadow-lg transition-all duration-300 hover:-translate-y-px hover:from-slate-600/90 hover:to-slate-700/90 hover:text-white hover:shadow-slate-500/30 active:scale-[0.98]"
                  @click="handleDelete"
                >
                  <UIcon name="i-heroicons-trash" class="mr-1.5 size-4" />
                  删除
                </button>
                <button
                  class="inline-flex flex-1 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500/90 to-purple-600/90 px-4 py-2.5 text-sm font-medium text-white shadow-lg shadow-purple-500/20 transition-all duration-300 hover:-translate-y-px hover:from-purple-600 hover:to-purple-700 hover:shadow-purple-500/30 active:scale-[0.98]"
                  @click="handleEdit"
                >
                  <UIcon name="i-heroicons-pencil" class="mr-1.5 size-4" />
                  编辑
                </button>
              </div>

              <div v-if="courseStore.currentCourse?.gameId" class="flex gap-2">
                <button
                  class="inline-flex flex-1 items-center justify-center rounded-lg bg-gradient-to-br from-slate-700/90 to-slate-800/90 px-4 py-2.5 text-sm font-medium text-slate-200 shadow-lg transition-all duration-300 hover:-translate-y-px hover:from-slate-600/90 hover:to-slate-700/90 hover:text-white hover:shadow-slate-500/30 active:scale-[0.98]"
                  @click="handleUnpublish"
                >
                  <UIcon
                    name="i-heroicons-arrow-down-circle"
                    class="mr-1.5 size-4"
                  />
                  下架
                </button>
                <button
                  class="inline-flex flex-1 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500/90 to-pink-500/90 px-4 py-2.5 text-sm font-medium text-white shadow-lg shadow-purple-500/20 transition-all duration-300 hover:-translate-y-px hover:from-purple-600 hover:to-pink-600 hover:shadow-purple-500/30 active:scale-[0.98]"
                  @click="handleUpdate"
                >
                  <UIcon name="i-heroicons-arrow-path" class="mr-1.5 size-4" />
                  更新
                </button>
                <button
                  class="inline-flex flex-1 items-center justify-center rounded-lg bg-gradient-to-br from-indigo-500/90 to-purple-600/90 px-4 py-2.5 text-sm font-medium text-white shadow-lg shadow-indigo-500/20 transition-all duration-300 hover:-translate-y-px hover:from-indigo-600 hover:to-purple-700 hover:shadow-indigo-500/30 active:scale-[0.98]"
                  @click="handlePlayGame"
                >
                  <UIcon name="i-heroicons-play" class="mr-1.5 size-4" />
                  玩起来
                </button>
              </div>

              <button
                v-else
                class="inline-flex w-full items-center justify-center rounded-lg bg-gradient-to-br from-purple-500/90 to-pink-500/90 px-5 py-2.5 text-sm font-medium text-white shadow-lg shadow-purple-500/20 transition-all duration-300 hover:-translate-y-px hover:from-purple-600 hover:to-pink-600 hover:shadow-purple-500/30 active:scale-[0.98]"
                @click="handlePublish"
              >
                <UIcon
                  name="i-heroicons-paper-airplane"
                  class="mr-1.5 size-4"
                />
                发布
              </button>
            </div>
          </div>
        </div>
      </div>
    </UModal>
    <CourseEditor
      v-model="showEditor"
      @update:model-value="handleEditorClose"
    />
    <TTSProgressModal
      v-model:show="showTTSProgress"
      :course-id="courseStore.currentCourse?.id || ''"
      :action="ttsAction"
      @success="handleTTSSuccess"
    />
  </div>
</template>

<style scoped>
/* 确保过渡效果生效 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}
</style>
