<script setup lang="ts">
import { useLoadingBar } from 'naive-ui'
import { computed } from 'vue'
import { toast } from 'vue-sonner'
import { useCourseStore } from '~/stores/course'

defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

// 词性枚举
enum PartOfSpeech {
  NOUN = 'NOUN',
  VERB = 'VERB',
  ADJ = 'ADJ',
  ADV = 'ADV',
  PRON = 'PRON',
  ADP = 'ADP',
  CCONJ = 'CCONJ',
  SCONJ = 'SCONJ',
  INTJ = 'INTJ',
  DET = 'DET',
  AUX = 'AUX',
  PROPN = 'PROPN',
  PROPN_PERSON = 'PROPN_PERSON',
  NUM = 'NUM',
  PART = 'PART',
}

// 词性代码和对应的中文名称
const POS_TEXT_MAP: Record<PartOfSpeech, string> = {
  [PartOfSpeech.NOUN]: '名词',
  [PartOfSpeech.VERB]: '动词',
  [PartOfSpeech.ADJ]: '形容词',
  [PartOfSpeech.ADV]: '副词',
  [PartOfSpeech.PRON]: '代词',
  [PartOfSpeech.ADP]: '介词',
  [PartOfSpeech.CCONJ]: '并列连词',
  [PartOfSpeech.SCONJ]: '从属连词',
  [PartOfSpeech.INTJ]: '感叹词',
  [PartOfSpeech.DET]: '限定词',
  [PartOfSpeech.AUX]: '助动词',
  [PartOfSpeech.PROPN]: '专有名词',
  [PartOfSpeech.PROPN_PERSON]: '人名',
  [PartOfSpeech.NUM]: '数词',
  [PartOfSpeech.PART]: '助词',
}

const courseStore = useCourseStore()
const loadingBar = useLoadingBar()
const loadingSpinner = useLoadingSpinner()
const isPreventCloseModal = ref(false)
const isGenerating = ref(false)

const wordDetails = computed(() => {
  return courseStore.selectedSentence?.wordDetails || []
})

const posOptions = computed(() => {
  return Object.entries(POS_TEXT_MAP).map(([value, label]) => ({
    value,
    label,
  }))
})

function handleClose() {
  if (isPreventCloseModal.value) {
    return
  }
  emit('update:modelValue', false)
}

async function handlePosChange(word: string, value: string) {
  try {
    loadingBar.start()
    await courseStore.selectedSentence?.updateWordDetail(word, {
      pos: value,
    })
    toast.success('更新成功')
  }
  catch {
    toast.error('更新失败')
  }
  finally {
    loadingBar.finish()
  }
}

async function handleDefinitionChange(word: string, definition: string) {
  try {
    loadingBar.start()
    await courseStore.selectedSentence?.updateWordDetail(word, {
      definition,
    })
    toast.success('更新成功')
  }
  catch {
    toast.error('更新失败')
  }
  finally {
    loadingBar.finish()
  }
}

async function handleAnalyzeSentence() {
  if (isGenerating.value)
    return

  try {
    isGenerating.value = true
    loadingSpinner.startLoading([
      '生成依赖大模型 时间会久一点',
      '请耐心等待哦',
    ])
    isPreventCloseModal.value = true
    await courseStore.selectedSentence?.analyzeSentence()
    toast.success('生成成功')
  }
  catch {
    toast.error('生成失败')
  }
  finally {
    isGenerating.value = false
    loadingSpinner.finishLoading()
    isPreventCloseModal.value = false
  }
}
</script>

<template>
  <UModal
    :model-value="modelValue"
    :ui="{ width: 'w-[45vw] max-w-[600px]' }"
    :prevent-close="isPreventCloseModal"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <div
      class="relative h-[70vh] max-h-[768px] overflow-hidden rounded-2xl border border-purple-500/20 bg-gray-900/95 backdrop-blur-xl"
    >
      <!-- 背景装饰 -->
      <div class="absolute inset-0">
        <div
          class="absolute -right-32 -top-32 size-64 rounded-full bg-purple-500/10 blur-3xl"
        />
        <div
          class="absolute -bottom-32 -left-32 size-64 rounded-full bg-purple-500/10 blur-3xl"
        />
      </div>

      <!-- 内容区域 -->
      <div class="relative z-10 flex h-full flex-col">
        <!-- 标题栏 -->
        <div
          class="flex items-center justify-between border-b border-purple-500/20 px-6 py-4"
        >
          <h2
            class="bg-gradient-to-r from-purple-400 to-purple-300 bg-clip-text text-xl font-bold text-transparent"
          >
            单词表
          </h2>

          <button
            class="flex items-center justify-center rounded-lg p-2 text-gray-400 transition-colors hover:bg-purple-500/10 hover:text-gray-300"
            @click="handleClose"
          >
            <UIcon name="i-heroicons-x-mark" class="size-5" />
          </button>
        </div>

        <!-- 单词列表 -->
        <div class="flex-1 overflow-y-auto px-6 pb-6 pt-4">
          <div class="grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4">
            <div
              v-for="(detail, index) in wordDetails"
              :key="index"
              class="rounded-lg bg-gray-800/50 p-3 transition-all duration-200 hover:bg-gray-800/70"
            >
              <!-- 单词 -->
              <div class="mb-0.5">
                <h3 class="text-lg font-medium text-purple-400">
                  {{ detail.word }}
                </h3>
              </div>

              <!-- 音标 -->
              <div class="mb-1.5 h-[18px]">
                <span
                  v-if="detail.phonetic?.us"
                  class="font-mono text-xs text-gray-400"
                >
                  /{{ detail.phonetic.us }}/
                </span>
                <span v-else class="font-mono text-xs text-gray-400/30" />
              </div>

              <!-- 释义 -->
              <div class="w-full">
                <CommonInlineEdit
                  :value="detail.definition || ''"
                  input-class="w-full rounded-md bg-gray-700/50 border-0 px-2 py-1 text-xs text-gray-300 focus:ring-1 focus:ring-purple-500/30 focus:outline-none"
                  placeholder="点击添加翻译..."
                  :saving="false"
                  @save="(val: string) => handleDefinitionChange(detail.word, val)"
                >
                  <template #empty>
                    <div
                      class="flex min-h-[32px] w-full items-center gap-1.5 rounded-md bg-gray-700/50 px-2 py-1 text-xs text-gray-500 transition-colors hover:bg-gray-700/70"
                    >
                      <UIcon name="i-heroicons-pencil" class="size-3" />
                      <span>点击添加翻译</span>
                    </div>
                  </template>
                </CommonInlineEdit>
              </div>

              <!-- 词性选择 -->
              <div class="mt-1.5">
                <select
                  v-model="detail.pos"
                  class="w-full rounded-md border-0 bg-gray-700/50 px-2 py-1 text-xs text-gray-300 transition-colors hover:bg-gray-700/70 focus:outline-none focus:ring-1 focus:ring-purple-500/30"
                  @change="
                    (e: Event) =>
                      handlePosChange(
                        detail.word,
                        (e.target as HTMLSelectElement).value,
                      )
                  "
                >
                  <option
                    v-for="pos in posOptions"
                    :key="pos.value"
                    :value="pos.value"
                  >
                    {{ pos.label }}
                  </option>
                </select>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div
            v-if="wordDetails.length === 0"
            class="flex h-[calc(70vh-120px)] items-center justify-center rounded-lg border border-dashed border-purple-500/20 bg-gray-800/30"
          >
            <div class="text-center">
              <UIcon
                name="i-heroicons-book-open"
                class="mx-auto mb-4 size-12 text-gray-500/80"
              />
              <div class="mb-2 text-gray-400">
                当前句子还没有单词表
              </div>
              <div class="mb-6 text-sm text-gray-500">
                点击下方按钮开始生成单词详情
              </div>
              <button
                class="inline-flex items-center gap-2 rounded-lg bg-gradient-to-r from-purple-500 to-purple-600 px-6 py-2.5 text-sm font-medium text-white shadow-lg shadow-purple-500/20 transition-all duration-300 hover:-translate-y-px hover:from-purple-600 hover:to-purple-700 hover:shadow-purple-500/30 active:scale-[0.98] disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:translate-y-0"
                :disabled="isGenerating"
                @click="handleAnalyzeSentence"
              >
                <UIcon
                  :name="isGenerating ? 'i-heroicons-arrow-path' : 'i-heroicons-sparkles'"
                  class="size-4"
                  :class="{ 'animate-spin': isGenerating }"
                />
                {{ isGenerating ? '生成中...' : '开始生成' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </UModal>
</template>

<style scoped>
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: theme('colors.gray.600') transparent;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: theme('colors.gray.600');
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: theme('colors.gray.500');
}
</style>
