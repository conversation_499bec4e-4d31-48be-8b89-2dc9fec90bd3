<script setup lang="ts">
import { useCourseStore } from '~/stores/course'
import { isMac } from '~/utils/os'

const courseStore = useCourseStore()
const searchInput = ref<HTMLInputElement | null>(null)

// 监听搜索查询变化
watch(
  () => courseStore.searchSentencesQuery,
  (value: string) => {
    courseStore.updateSentencesSearchQuery(value)
  },
)

// 添加键盘快捷键
onMounted(() => {
  window.addEventListener('keydown', handleShortcut)
})

onUnmounted(() => {
  courseStore.searchSentencesQuery = ''
  window.removeEventListener('keydown', handleShortcut)
})

function handleShortcut(event: KeyboardEvent) {
  if ((event.metaKey || event.ctrlKey) && event.key.toLowerCase() === 'k') {
    event.preventDefault()
    searchInput.value?.focus()
  }
}
</script>

<template>
  <div class="relative flex-1">
    <input
      ref="searchInput"
      v-model="courseStore.searchSentencesQuery"
      type="search"
      :placeholder="isMac() ? '搜索 ⌘ + K' : '搜索 Ctrl + K'"
      class="w-full rounded-lg border border-slate-700 bg-slate-800/50 px-4 py-2 pl-10 text-slate-200 placeholder:text-slate-400 focus:border-purple-500 focus:outline-none focus:ring-1 focus:ring-purple-500"
    >
    <UIcon
      name="i-heroicons-magnifying-glass"
      class="absolute left-3 top-1/2 size-5 -translate-y-1/2 text-slate-400"
    />
  </div>
</template>
