<script setup lang="ts">
import type {
  Element,
  ImageSource,
  ImageStyle,
} from '@julebu/shared'
import type CommonInlineEdit from '~/components/common/InlineEdit.vue'
import { useLoadingBar } from 'naive-ui'
import { toast } from 'vue-sonner'
import Dialog from '~/components/common/Dialog.vue'
import { generatePhonetic } from '~/services/phonetic'
import { useCourseStore } from '~/stores/course'
import { getImageUrl } from '~/utils/url'
import ElementImageSelector from './ElementImageSelector.vue'

const props = defineProps<{
  element: Element
  index: number
  isCollapsed: boolean
}>()
const emit = defineEmits<{
  newElement: [elementId: string]
}>()
const loadingSpinner = useLoadingSpinner()
const loadingBar = useLoadingBar()
const courseStore = useCourseStore()

const modal = useModal()

const contentEditRef = ref<InstanceType<typeof CommonInlineEdit> | null>(null)

defineExpose({
  startEdit: () => {
    contentEditRef.value?.startEdit()
  },
})

// 添加图片选择器状态
const showImageSelector = ref(false)
// 添加图片预览状态
const showImagePreview = ref(false)
const isImageUploading = ref(false)

const elementActions = computed(() => [
  {
    name: '删除',
    icon: 'ph:trash',
    click: () => {
      modal.open(Dialog, {
        title: '警告',
        content: '你确定？删除了可就没法恢复了',
        showCancel: true,
        showConfirm: true,
        async onConfirm() {
          loadingBar.start()
          await courseStore.selectedSentence?.deleteElement(props.element.id)
          loadingBar.finish()
        },
      })
    },
  },
  {
    name: '向上插入',
    icon: 'ph:arrow-up',
    click: async () => {
      const newElementId
        = await courseStore.selectedSentence?.insertElementAbove(
          props.element.id,
        )
      if (newElementId) {
        emit('newElement', newElementId)
      }
    },
  },
  {
    name: '向下插入',
    icon: 'ph:arrow-down',
    click: async () => {
      const newElementId
        = await courseStore.selectedSentence?.insertElementBelow(
          props.element.id,
        )
      if (newElementId) {
        emit('newElement', newElementId)
      }
    },
  },
  {
    name: '复制',
    icon: 'ph:copy',
    click: async () => {
      const newElementId = await courseStore.selectedSentence?.copyElement(
        props.element.id,
      )
      if (newElementId) {
        emit('newElement', newElementId)
      }
    },
  },
  {
    name: '加工',
    icon: 'ph:wrench',
    click: async () => {
      loadingSpinner.startLoading()
      await generatePhonetic(props.element.content)
      await courseStore.selectedSentence?.processElement(props.element.id)
      loadingSpinner.finishLoading()
    },
  },
  {
    name: props.element.image ? '更换图片' : '添加图片',
    icon: 'ph:image-square',
    click: () => {
      showImageSelector.value = true
    },
  },
])

const saving = ref({
  chinese: false,
  phonetic: false,
  english: false,
})

async function handleChineseSave(value: string) {
  saving.value.chinese = true
  loadingBar.start()

  try {
    await courseStore.selectedSentence?.changeElementProp(
      props.element.id,
      'chinese',
      value,
    )
  }
  finally {
    saving.value.chinese = false
    loadingBar.finish()
  }
}

async function handlePhoneticSave(value: string) {
  saving.value.phonetic = true
  loadingBar.start()

  try {
    await courseStore.selectedSentence?.changeElementProp(
      props.element.id,
      'phonetic',
      value,
    )
  }
  finally {
    saving.value.phonetic = false
    loadingBar.finish()
  }
}

async function handleEnglishSave(value: string) {
  saving.value.english = true
  loadingBar.start()

  try {
    await courseStore.selectedSentence?.changeElementProp(
      props.element.id,
      'content',
      value,
    )
  }
  finally {
    saving.value.english = false
    loadingBar.finish()
  }
}

async function handleImageConfirm({
  file,
  description,
  source,
  style,
}: {
  file: File
  description: string
  source: ImageSource
  style?: ImageStyle
}) {
  try {
    isImageUploading.value = true

    await courseStore.selectedSentence?.changeImage({
      file,
      elementId: props.element.id,
      description,
      source,
      style,
    })

    toast.success('图片添加成功！')
    showImageSelector.value = false
  }
  catch (error) {
    console.error('保存图片失败:', error)
  }
  finally {
    isImageUploading.value = false
  }
}

// 处理移除图片（带确认对话框）
function handleRemoveImageWithConfirm() {
  modal.open(Dialog, {
    title: '确认移除',
    content: '确定要移除这张图片吗？',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      await handleRemoveImage()
    },
  })
}

// 处理移除图片
async function handleRemoveImage() {
  try {
    loadingBar.start()
    await courseStore.selectedSentence?.removeImage(props.element.id)
    toast.success('图片已删除')
  }
  catch {
    toast.error('删除图片失败，请重试')
  }
  finally {
    loadingBar.finish()
  }
}
</script>

<template>
  <div
    class="group relative flex gap-4 rounded-lg border border-slate-700/50 bg-slate-800/30 p-4 transition-colors hover:border-purple-500/50 hover:bg-slate-700/30"
  >
    <!-- 序号标记 -->
    <div
      class="absolute right-1 top-0 text-sm font-medium text-slate-400 transition-opacity group-hover:opacity-0"
    >
      #{{ index + 1 }}
    </div>

    <!-- 拖拽把手 -->
    <div
      class="drag-handle flex cursor-move items-center self-stretch text-slate-500 hover:text-slate-400"
    >
      <UIcon name="i-heroicons-bars-3" class="size-5" />
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1">
      <!-- 标题区域 -->
      <div class="flex min-h-[40px] items-center justify-between">
        <div class="flex items-center gap-3">
          <!-- 图片缩略图 - 在标题旁边显示 -->
          <div v-if="element.image" class="group/image relative">
            <img
              :src="getImageUrl(element.image, { w: 48, h: 48 })"
              :alt="element.content"
              class="size-[48px] cursor-pointer rounded-lg border border-slate-600 object-cover transition-all duration-200 hover:scale-110 hover:border-purple-500"
              @click="showImagePreview = true"
            >
            <!-- 删除图片按钮 -->
            <button
              class="absolute -right-1 -top-1 flex size-5 items-center justify-center rounded-full bg-red-500 text-white opacity-0 transition-all duration-200 hover:bg-red-600 group-hover/image:opacity-100"
              @click.stop="handleRemoveImageWithConfirm"
            >
              <UIcon name="i-heroicons-x-mark" class="size-3" />
            </button>
          </div>

          <CommonInlineEdit
            ref="contentEditRef"
            :value="element.content"
            text-class="text-2xl font-medium text-slate-200 leading-none"
            input-class="text-2xl font-medium leading-none"
            :saving="saving.english"
            @save="handleEnglishSave"
          />
        </div>

        <!-- 操作按钮 -->
        <div
          class="flex space-x-1 opacity-0 transition-opacity duration-200 group-hover:opacity-100"
        >
          <UTooltip
            v-for="action in elementActions"
            :key="action.name"
            :text="action.name"
          >
            <button
              class="inline-flex size-8 items-center justify-center rounded-lg hover:bg-slate-600/50"
              @click="action.click"
            >
              <UIcon :name="action.icon" class="size-4 text-slate-200" />
            </button>
          </UTooltip>
        </div>
      </div>

      <!-- 内容区域 -->
      <transition name="expand">
        <div v-if="!isCollapsed" class="mt-3 space-y-2">
          <!-- 中文翻译 -->
          <div class="flex min-h-[35px] items-center gap-x-2 text-base">
            <span class="w-12 shrink-0 font-medium text-slate-400">
              中文：
            </span>
            <CommonInlineEdit
              :value="element.chinese"
              text-class="text-slate-200 leading-normal flex-1"
              input-class="leading-normal w-full"
              :saving="saving.chinese"
              placeholder="点击添加中文翻译..."
              @save="handleChineseSave"
            />
          </div>
          <!-- 音标 -->
          <div class="flex min-h-[35px] items-center gap-x-2 text-base">
            <span class="w-12 shrink-0 font-medium text-slate-400">
              音标：
            </span>
            <CommonInlineEdit
              :value="element.phonetic"
              text-class="text-slate-200 leading-normal flex-1"
              input-class="leading-normal w-full"
              :saving="saving.phonetic"
              placeholder="点击添加音标..."
              @save="handlePhoneticSave"
            />
          </div>
        </div>
      </transition>
    </div>
  </div>

  <!-- 图片选择器模态框 -->
  <ElementImageSelector
    v-model="showImageSelector"
    :element-id="element.id"
    :is-image-uploading="isImageUploading"
    @confirm="handleImageConfirm"
  />

  <!-- 图片预览模态框 -->
  <template v-if="element.image">
    <UModal v-model="showImagePreview" :ui="{ width: 'w-full sm:max-w-4xl' }">
      <div class="p-6">
        <!-- 只有标题和关闭 -->
        <div class="mb-4 flex items-center justify-between">
          <h3 class="text-lg font-semibold text-slate-200">
            {{ element.content }}
          </h3>
          <UButton
            color="gray"
            variant="ghost"
            icon="i-heroicons-x-mark-20-solid"
            @click="showImagePreview = false"
          />
        </div>

        <!-- 图片 -->
        <div class="mb-4 flex justify-center">
          <img
            :src="getImageUrl(element.image!)"
            :alt="element.content"
            class="max-h-96 max-w-full rounded-lg border border-slate-600"
          >
        </div>

        <!-- 操作按钮移到底部 -->
        <div class="flex justify-center border-t border-slate-600 pt-4">
          <UButton
            color="red"
            variant="outline"
            icon="i-heroicons-trash-20-solid"
            @click="handleRemoveImageWithConfirm"
          >
            删除图片
          </UButton>
        </div>
      </div>
    </UModal>
  </template>
</template>

<style scoped>
/* 添加展开/折叠过渡动画 */
.expand-enter-active,
.expand-leave-active {
  transition: all 0.3s ease-out;
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-10px);
}

.expand-enter-to,
.expand-leave-from {
  opacity: 1;
  max-height: 200px; /* 根据实际内容高度调整 */
  transform: translateY(0);
}
</style>
