<script setup lang="ts">
import type { GenerateImageSize, ImageSource, ImageStyle } from '@julebu/shared'
import {
  ALLOWED_IMAGE_MIME_TYPES,

  IMAGE_SIZE_OPTIONS,
  IMAGE_STYLES_OPTIONS,

} from '@julebu/shared'
import { toast } from 'vue-sonner'
import { generateImage } from '~/api/image'
import { base64ToFile } from '~/utils/file'

const props = defineProps<{
  modelValue: boolean
  elementId: string
  isImageUploading: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [
    {
      file: File | undefined
      source: ImageSource
      description: string
      style?: ImageStyle
    },
  ]
}>()
// 选项卡状态
const activeTab = ref<'ai' | 'upload' | 'url'>('ai')

const tabs = [
  { key: 'ai' as const, label: 'AI生成' },
  { key: 'upload' as const, label: '上传图片' },
]

// AI生成相关状态
const isGenerating = ref(false)
const generatedImage = ref<{
  b64_json: string
  dataUrl: string
  description: string
} | null>(null)

const selectedStyle = ref<ImageStyle>(IMAGE_STYLES_OPTIONS[0].value)
const selectedSize = ref<GenerateImageSize>(IMAGE_SIZE_OPTIONS[0].value)

// 上传相关状态
const fileInput = ref<HTMLInputElement | null>(null)
const uploadedImage = ref<{ file: File, previewUrl: string } | null>(null)

// 计算属性
const hasSelectedImage = computed(() => {
  return (
    (activeTab.value === 'ai' && generatedImage.value !== null)
    || (activeTab.value === 'upload' && uploadedImage.value !== null)
  )
})

// 基础方法
function triggerFileInput() {
  fileInput.value?.click()
}

function handleFileUpload(event: Event) {
  const input = event.target as HTMLInputElement
  if (!input.files?.length)
    return

  const file = input.files[0]

  if (file.size > 1 * 1024 * 1024) {
    toast.error('图片大小不能超过 1MB')
    return
  }

  if (!ALLOWED_IMAGE_MIME_TYPES.includes(file.type as typeof ALLOWED_IMAGE_MIME_TYPES[number])) {
    toast.error('请选择图片文件，支持 WebP、JPG、PNG、GIF 格式')
    return
  }

  const previewUrl = URL.createObjectURL(file)
  uploadedImage.value = { file, previewUrl }

  input.value = ''
}

function clearUploadedImage() {
  if (uploadedImage.value?.previewUrl) {
    URL.revokeObjectURL(uploadedImage.value.previewUrl)
  }
  uploadedImage.value = null
}

async function generateAIImage() {
  isGenerating.value = true

  try {
    const { base64, description } = await generateImage({
      elementId: props.elementId,
      style: selectedStyle.value,
      size: selectedSize.value || 'auto',
    })

    if (base64 && description) {
      const dataUrl = `data:image/webp;base64,${base64}`

      generatedImage.value = {
        b64_json: base64,
        dataUrl,
        description,
      }

      // 清除其他选择
      clearUploadedImage()
    }
  }
  catch (error) {
    toast.error('图片生成失败，请重试')
    console.error('AI图片生成错误:', error)
  }
  finally {
    isGenerating.value = false
  }
}

async function onConfirm() {
  if (!hasSelectedImage.value)
    return

  try {
    let file: File | undefined
    let source: ImageSource = 'ai_generated'
    let style: ImageStyle | undefined

    switch (activeTab.value) {
      case 'ai':
        if (generatedImage.value) {
          file = base64ToFile(generatedImage.value.b64_json)
          style = selectedStyle.value
        }
        break
      case 'upload':
        if (uploadedImage.value) {
          file = uploadedImage.value.file
          source = 'user_upload'
        }
        break
    }

    emit('confirm', {
      file,
      description: generatedImage.value?.description || '',
      source,
      style,
    })
  }
  catch {
    toast.error('图片处理失败，请重试')
  }
}

// 处理模态框关闭事件
function handleModalClose(value: boolean) {
  // 如果正在生成图片，阻止关闭
  if (!value && isGenerating.value) {
    toast.warning('图片生成中，请稍候...')
    return
  }

  // 如果正在上传图片，也阻止关闭
  if (!value && props.isImageUploading) {
    toast.warning('图片上传中，请稍候...')
    return
  }

  emit('update:modelValue', value)
}

function onCancel() {
  // 如果正在生成或上传，不允许取消
  if (isGenerating.value) {
    toast.warning('图片生成中，请稍候...')
    return
  }

  if (props.isImageUploading) {
    toast.warning('图片上传中，请稍候...')
    return
  }

  emit('update:modelValue', false)
}

// 监听模态框关闭，清理状态
watch(
  () => props.modelValue,
  (newValue) => {
    if (!newValue) {
      // 重置状态
      activeTab.value = 'ai'
      generatedImage.value = null
      clearUploadedImage()
    }
  },
)

// 组件卸载时清理URL
onUnmounted(() => {
  clearUploadedImage()
})
</script>

<template>
  <UModal
    :model-value="modelValue"
    :ui="{ width: 'w-full sm:max-w-2xl' }"
    @update:model-value="handleModalClose"
  >
    <div
      class="relative overflow-hidden rounded-2xl border border-purple-500/20 bg-gray-900/95 backdrop-blur-xl"
    >
      <div class="absolute inset-0">
        <div
          class="absolute -right-32 -top-32 size-64 rounded-full bg-purple-500/10 blur-3xl"
        />
        <div
          class="absolute -bottom-32 -left-32 size-64 rounded-full bg-blue-500/10 blur-3xl"
        />
      </div>

      <div class="relative z-10 p-8">
        <h2
          class="mb-6 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-2xl font-bold text-transparent"
        >
          添加图片
        </h2>

        <!-- 标签页切换 -->
        <div class="mb-6 flex space-x-4">
          <button
            v-for="tab in tabs"
            :key="tab.key"
            class="relative px-4 py-2 text-lg font-semibold transition-all duration-300"
            :class="[
              activeTab === tab.key
                ? 'text-purple-400'
                : 'text-gray-400 hover:text-gray-300',
            ]"
            @click="activeTab = tab.key"
          >
            {{ tab.label }}
            <div
              v-if="activeTab === tab.key"
              class="absolute bottom-0 left-0 h-0.5 w-full bg-gradient-to-r from-purple-400 to-blue-400"
            />
          </button>
        </div>

        <!-- AI生成图片选项卡 -->
        <div v-if="activeTab === 'ai'" class="space-y-6">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="mb-2 block text-sm font-medium text-gray-300">
                图片风格
              </label>
              <USelect
                v-model="selectedStyle"
                required
                :options="IMAGE_STYLES_OPTIONS as unknown as unknown[]"
                placeholder="选择风格"
              />
            </div>
            <div>
              <label class="mb-2 block text-sm font-medium text-gray-300">
                图片尺寸
              </label>
              <USelect
                v-model="selectedSize"
                required
                :options="IMAGE_SIZE_OPTIONS as unknown as unknown[]"
                placeholder="选择尺寸"
              />
            </div>
          </div>

          <!-- 生成的图片预览区域 -->
          <div v-if="generatedImage" class="space-y-4">
            <h3 class="text-lg font-medium text-gray-300">
              生成的图片
            </h3>
            <div class="flex justify-center">
              <div class="relative max-w-md">
                <img
                  :src="generatedImage.dataUrl"
                  alt="生成的图片"
                  class="max-h-80 w-full rounded-lg border-2 border-purple-500 object-contain shadow-lg shadow-purple-500/25"
                >
                <!-- 选中状态指示器 -->
                <div
                  class="absolute right-2 top-2 flex size-8 items-center justify-center rounded-full bg-purple-500 shadow-lg"
                >
                  <UIcon
                    name="i-heroicons-check-20-solid"
                    class="size-4 text-white"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- 生成按钮 -->
          <div class="flex justify-center">
            <UButton
              :disabled="isGenerating"
              size="lg"
              class="relative w-full overflow-hidden rounded-lg border-0 bg-gradient-to-r from-purple-600 via-purple-500 to-blue-500 px-6 py-3 shadow-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-purple-500/25"
              @click="generateAIImage"
            >
              <UIcon
                v-if="!isGenerating"
                name="i-heroicons-sparkles-20-solid"
                class="mr-2 size-5 text-white"
              />
              <UIcon
                v-else
                name="i-heroicons-arrow-path-20-solid"
                class="mr-2 size-5 animate-spin text-white"
              />
              <span class="font-semibold text-white">
                {{
                  isGenerating
                    ? "正在生成..."
                    : generatedImage
                      ? "重新生成"
                      : "生成图片"
                }}
              </span>
            </UButton>
          </div>
        </div>

        <!-- 上传图片选项卡 -->
        <div v-if="activeTab === 'upload'" class="space-y-6">
          <!-- 上传区域 - 只在没有上传图片时显示 -->
          <div
            v-if="!uploadedImage"
            class="flex cursor-pointer items-center justify-center rounded-lg border-2 border-dashed border-purple-500/30 p-12 transition-all duration-300 hover:border-purple-500/70 hover:bg-purple-500/10"
            @click="triggerFileInput"
          >
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              class="sr-only"
              @change="handleFileUpload"
            >
            <div class="text-center">
              <UIcon
                name="i-mingcute-folder-upload-line"
                class="mx-auto size-16 text-purple-400"
              />
              <p class="mt-4 text-lg font-medium text-gray-300">
                点击上传图片
              </p>
              <p class="mt-2 text-sm text-gray-500">
                支持 WebP、JPG、PNG、GIF 格式，最大 1MB
              </p>
            </div>
          </div>

          <!-- 上传的图片预览 -->
          <div v-if="uploadedImage" class="space-y-4">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-300">
                已上传的图片
              </h3>
            </div>
            <div class="group relative">
              <img
                :src="uploadedImage.previewUrl"
                alt="上传的图片"
                class="h-48 w-full rounded-lg border-2 border-purple-500/20 object-contain"
              >
              <!-- 删除按钮 -->
              <div
                class="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 backdrop-blur-sm transition-all duration-300 group-hover:opacity-100"
              >
                <UButton
                  icon="i-heroicons-trash-20-solid"
                  variant="solid"
                  color="red"
                  size="sm"
                  class="rounded-lg shadow-lg"
                  @click="clearUploadedImage"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="mt-8 flex justify-end space-x-4">
          <UButton
            :disabled="isGenerating"
            color="gray"
            class="px-8 transition-all duration-300 hover:scale-105"
            @click="onCancel"
          >
            取消
          </UButton>
          <UButton
            :disabled="!hasSelectedImage || isGenerating"
            :loading="isImageUploading"
            class="bg-gradient-to-r px-8 transition-all duration-300 hover:scale-105"
            @click="onConfirm"
          >
            {{ isImageUploading ? "上传中..." : "确认" }}
          </UButton>
        </div>
      </div>
    </div>
  </UModal>
</template>
