<script setup lang="ts">
import type { SortableEvent } from 'vue-draggable-plus'
import { VueDraggable } from 'vue-draggable-plus'
import ContextMenu from '~/components/common/ContextMenu.vue'
import Dialog from '~/components/common/Dialog.vue'
import { useCourseStore } from '~/stores/course'
import SentenceSearch from './Search.vue'

interface Sentence {
  id: string
  content: string
  // 添加其他必要的字段
}

interface ContextMenuItem {
  label: string
  icon?: string
  color?: string
  onSelect?: () => void
}

const sortDirection = ref<'asc' | 'desc'>('asc')
const courseStore = useCourseStore()
const contextMenuRef = ref()
const currentSentence = ref<Sentence | null>(null)
const loadingSpinner = useLoadingSpinner()
const modal = useModal()

// 处理函数
async function handleInsertAbove(sentence: Sentence) {
  const newSentence = await courseStore.insertSentenceAbove(sentence.id)
  if (newSentence) {
    courseStore.selectSentence(newSentence.id)
  }
}

async function handleInsertBelow(sentence: Sentence) {
  const newSentence = await courseStore.insertSentenceBelow(sentence.id)
  if (newSentence) {
    courseStore.selectSentence(newSentence.id)
  }
}

async function handleDelete(sentence: Sentence) {
  modal.open(Dialog, {
    title: '警告',
    content: '你确定？删除了可就没法恢复了',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      loadingSpinner.startLoading()
      await courseStore.deleteSentence(
        sentence.id,
      )
      loadingSpinner.finishLoading()
    },
  })
}

async function handleMoveToTop(sentence: Sentence) {
  await courseStore.moveSentenceToTop(sentence.id)
}

async function handleMoveToBottom(sentence: Sentence) {
  await courseStore.moveSentenceToBottom(sentence.id)
}

// 定义右键菜单项
function getContextMenuItems(sentence: Sentence): ContextMenuItem[][] {
  return [
    [
      {
        label: '向上插入',
        icon: 'i-heroicons-plus',
        onSelect: () => handleInsertAbove(sentence),
      },
      {
        label: '向下插入',
        icon: 'i-heroicons-plus',
        onSelect: () => handleInsertBelow(sentence),
      },
      {
        label: '删除',
        icon: 'i-heroicons-trash',
        color: 'red',
        onSelect: () => handleDelete(sentence),
      },
    ],
    [
      {
        label: '移动到顶部',
        icon: 'i-heroicons-arrow-up',
        onSelect: () => handleMoveToTop(sentence),
      },
      {
        label: '移动到底部',
        icon: 'i-heroicons-arrow-down',
        onSelect: () => handleMoveToBottom(sentence),
      },
    ],
  ]
}

// 处理右键点击事件
function handleContextMenu(event: MouseEvent, sentence: Sentence) {
  // 阻止默认的右键菜单
  event.preventDefault()

  // 只设置当前右键菜单的目标句子，不改变选中状态
  currentSentence.value = sentence

  // 显示自定义右键菜单
  nextTick(() => {
    contextMenuRef.value?.show(event)
  })
}

function toggleSortDirection() {
  const newDirection = sortDirection.value === 'asc' ? 'desc' : 'asc'
  courseStore.updateSentencesSortDirection(newDirection)
  sortDirection.value = newDirection
}

// 添加拖拽结束处理函数
function handleDragEnd({ newIndex, oldIndex }: SortableEvent) {
  if (newIndex === undefined || oldIndex === undefined)
    return
  courseStore.moveSentencePosition(newIndex, oldIndex)
}

function handleSelectSentence(sentenceId: string) {
  courseStore.selectSentence(sentenceId)
}

// 添加清空选择句子的函数
function clearSelectedSentence() {
  courseStore.selectSentence(null)
}
</script>

<template>
  <div
    class="flex h-full w-[400px] flex-col rounded-xl border border-slate-700/50 bg-slate-800/30"
  >
    <!-- 搜索区域 -->
    <div class="p-6">
      <div class="relative flex items-center gap-2">
        <SentenceSearch @input="clearSelectedSentence" />
        <button
          class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
          @click="toggleSortDirection"
        >
          <UIcon
            name="ph:sort-ascending"
            class="size-5 text-purple-400 transition-transform duration-200"
            :class="{ 'rotate-180': sortDirection === 'desc' }"
          />
          {{ sortDirection === "asc" ? "正序" : "倒序" }}
        </button>
      </div>
    </div>

    <!-- 列表滚动区域 -->
    <div class="flex-1 overflow-y-auto px-6 pb-6">
      <template v-if="courseStore.searchSentencesQuery.length > 0">
        <div class="space-y-2">
          <div
            v-for="(sentence, index) in courseStore.searchSentences"
            :key="sentence.id"
            class="group cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 hover:bg-slate-700/50"
            :class="{
              'border-slate-700/30 bg-slate-800/30':
                courseStore.selectedSentence?.id !== sentence.id,
              'border-purple-500 bg-slate-700/50 !shadow-[0_0_0_1px_rgb(168,85,247,0.3)]':
                courseStore.selectedSentence?.id === sentence.id,
            }"
            @click="handleSelectSentence(sentence.id)"
            @contextmenu="(e: MouseEvent) => handleContextMenu(e, sentence)"
          >
            <!-- 添加与拖拽列表相同的布局，但使用不同图标 -->
            <div class="flex items-center gap-3">
              <div
                class="drag-handle flex cursor-not-allowed items-center self-stretch text-slate-500 hover:text-slate-400"
              >
                <UIcon name="i-heroicons-bars-3" class="size-5" />
              </div>
              <div class="relative flex-1">
                <span
                  class="absolute -top-1 right-0 text-xs font-medium text-purple-400/80"
                >#{{ index + 1 }}</span>
                <div class="pr-6 text-slate-200">
                  {{ sentence.content }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <template v-else>
        <VueDraggable
          v-model="courseStore.sentences"
          class="space-y-2"
          :animation="300"
          handle=".drag-handle"
          @end="handleDragEnd"
        >
          <div
            v-for="(sentence, index) in courseStore.sentences"
            :key="sentence.id"
            class="group cursor-pointer rounded-lg border-2 p-4 transition-all duration-200 hover:bg-slate-700/50"
            :class="{
              'border-slate-700/30 bg-slate-800/30':
                courseStore.selectedSentence?.id !== sentence.id,
              'border-purple-500 bg-slate-700/50 !shadow-[0_0_0_1px_rgb(168,85,247,0.3)]':
                courseStore.selectedSentence?.id === sentence.id,
            }"
            @click="handleSelectSentence(sentence.id)"
            @contextmenu="(e: MouseEvent) => handleContextMenu(e, sentence)"
          >
            <!-- 添加拖拽手柄 -->
            <div class="flex items-center gap-3">
              <div
                class="drag-handle flex cursor-move items-center self-stretch text-slate-500 hover:text-slate-400"
              >
                <UIcon name="i-heroicons-bars-3" class="size-5" />
              </div>
              <div class="relative flex-1">
                <span
                  class="absolute -top-1 right-0 text-xs font-medium text-slate-400/80"
                >#{{ index + 1 }}</span>
                <div class="pr-6 text-slate-200">
                  {{ sentence.content }}
                </div>
              </div>
            </div>
          </div>
        </VueDraggable>
      </template>
    </div>
  </div>

  <ContextMenu
    v-if="currentSentence"
    ref="contextMenuRef"
    :items="getContextMenuItems(currentSentence)"
  />
</template>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: rgba(139, 92, 246, 0.3);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background-color: rgba(139, 92, 246, 0.5);
}

/* 列表动画 */
.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.list-move {
  transition: transform 0.3s ease;
}
</style>
