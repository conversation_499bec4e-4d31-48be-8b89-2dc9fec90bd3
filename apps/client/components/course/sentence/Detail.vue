<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import Dialog from '~/components/common/Dialog.vue'
import LearningContent from '~/components/course/sentence/LearningContent.vue'
import WordDetails from '~/components/course/sentence/WordDetails.vue'
import { useCourseStore } from '~/stores/course'

const modal = useModal()
const loadingSpinner = useLoadingSpinner()

// 使用 store
const courseStore = useCourseStore()

// 修改计算属性，只读取不设置
const content = computed(() => courseStore.selectedSentence?.content || '')
// 修改 chinese 的计算属性,改为只读
const chinese = computed(() => courseStore.selectedSentence?.chinese || '')
const sentenceOrder = computed(() => courseStore.sentences.findIndex(s => s.id === courseStore.selectedSentence?.id) + 1)

// 检查内容是否真的发生变化（去除前后空格）
function isContentChanged(newValue: string, oldValue: string) {
  return newValue.trim() !== oldValue.trim()
}

// 添加 ref 用于控制 LearningContent 组件的显示
const showLearningContent = ref(false)

// 添加 ref 用于控制 WordDetails 组件的显示
const showWordDetails = ref(false)

// 操作按钮配置
const actions = [
  {
    name: '拆分',
    icon: 'ph:scissors',
    async handler() {
      if (!courseStore.selectedSentence?.isEmpty()) {
        toast.warning('句子里面有内容，如果想重新拆分的话，请先清空哦')
        return
      }

      loadingSpinner.startLoading([
        '拆分的时长会比较久 请耐心等待',
        '拆分功能依赖大模型 所以时间会久一点',
        '如果报错 那重新再试一次😄',
        '一节课的练习数不要超过 200 否则练习时长会非常久！',
      ])

      await courseStore.selectedSentence?.split()
      loadingSpinner.finishLoading()
    },
  },
  {
    name: '加工',
    icon: 'ph:wrench',
    async handler() {
      if (!courseStore.selectedSentence?.needsProcessing()) {
        toast.warning('所有内容都已经完整,不需要再加工了')
        return
      }

      loadingSpinner.startLoading()
      await courseStore.selectedSentence?.process()
      loadingSpinner.finishLoading()
    },
  },
  {
    name: computed(() =>
      courseStore.selectedSentence?.learningContent ? '查看讲解' : '生成讲解',
    ),
    icon: 'ph:sparkle',
    handler: async () => {
      if (courseStore.selectedSentence?.learningContent) {
        // 打开查看讲解的弹窗
        showLearningContent.value = true
        return
      }

      loadingSpinner.startLoading([
        '学习内容的生成依赖大模型 时间会久一点',
        '请耐心等待哦',
      ])

      try {
        await courseStore.selectedSentence?.generateLearningContent()
        // 生成完成后打开弹窗
        showLearningContent.value = true
      }
      finally {
        loadingSpinner.finishLoading()
      }
    },
  },
  {
    name: '单词表',
    icon: 'ph:book-bookmark',
    handler: () => {
      showWordDetails.value = true
    },
  },
  {
    name: '删除',
    icon: 'ph:trash',
    handler: () => {
      modal.open(Dialog, {
        title: '警告',
        content: '你确定？删除了可就没法恢复了',
        showCancel: true,
        showConfirm: true,
        async onConfirm() {
          loadingSpinner.startLoading()
          await courseStore.deleteSentence(
            courseStore.selectedSentence?.id || '',
          )
          loadingSpinner.finishLoading()
        },
      })
    },
  },
]

// 添加对滚动容器的引用
const scrollContainer = ref<HTMLElement | null>(null)

// 监听 selectedSentence 的变化
watch(
  () => courseStore.selectedSentence,
  () => {
    // 确保 DOM 更新后再滚动
    nextTick(() => {
      scrollContainer.value?.scrollTo({
        top: 0,
      })
    })
  },
)

async function handleContentSave(value: string) {
  if (!courseStore.selectedSentence)
    return
  if (!isContentChanged(value, courseStore.selectedSentence.content))
    return

  if (value.trim().length === 0) {
    toast.warning('句子不可以为空呀')
    return
  }

  modal.open(Dialog, {
    title: '警告',
    content: '改变句子会清空所有的 elements',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      loadingSpinner.startLoading('正在重新解析句子...')
      await courseStore.selectedSentence?.changeContent(value.trim())
      loadingSpinner.finishLoading()
    },
  })
}

async function handleChineseSave(value: string) {
  if (!courseStore.selectedSentence)
    return
  if (!isContentChanged(value, courseStore.selectedSentence.chinese))
    return

  modal.open(Dialog, {
    title: '警告',
    content: '改变中文会清空所有的 elements',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      loadingSpinner.startLoading()
      await courseStore.selectedSentence?.changeChinese(value.trim())
      loadingSpinner.finishLoading()
    },
  })
}
</script>

<template>
  <div
    class="flex h-full flex-1 flex-col rounded-xl border border-slate-700/50 bg-slate-800/30"
  >
    <!-- 标题和操作按钮 -->
    <div class="p-6">
      <div class="flex min-h-[40px] items-center justify-between">
        <div class="flex items-center gap-4">
          <h3 class="flex items-center gap-2 text-2xl font-medium text-white">
            详情
            <span v-if="courseStore.selectedSentence" class="text-slate-400">
              #{{ sentenceOrder }}
            </span>
          </h3>
        </div>
        <!-- 其他操作按钮 -->
        <div v-if="courseStore.selectedSentence" class="flex gap-2">
          <button
            v-for="action in actions"
            :key="action.name"
            class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2.5 text-sm font-medium text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
            @click="action.handler"
          >
            <UIcon :name="action.icon" class="size-4" />
            {{ action.name }}
          </button>
        </div>
      </div>
    </div>

    <!-- 详情滚动区域 -->
    <div ref="scrollContainer" class="flex-1 overflow-y-auto px-6 pb-6">
      <Transition
        mode="out-in"
        enter-active-class="transition duration-200 ease-out"
        enter-from-class="opacity-0 translate-y-2"
        enter-to-class="opacity-100 translate-y-0"
        leave-active-class="transition duration-200 ease-in"
        leave-from-class="opacity-100 translate-y-0"
        leave-to-class="opacity-0 translate-y-2"
      >
        <div v-if="courseStore.selectedSentence" key="detail" class="space-y-8">
          <!-- 句子内容编辑区 -->
          <div
            class="space-y-6 rounded-xl border border-slate-700/50 bg-slate-800/50 p-6"
          >
            <!-- 英文输入区 -->
            <div class="flex min-h-[44px] items-start">
              <CommonInlineEdit
                :value="content"
                text-class="text-2xl font-medium text-purple-400 group-hover:text-white transition-colors break-words whitespace-pre-wrap"
                input-class="text-2xl font-medium text-purple-100 min-w-[300px] max-w-full"
                placeholder="输入英文句子..."
                :saving="false"
                @save="handleContentSave"
              />
            </div>

            <!-- 中文输入区 -->
            <div class="flex min-h-[40px] items-start">
              <CommonInlineEdit
                :value="chinese"
                text-class="text-lg text-slate-200 group-hover:text-white transition-colors break-words whitespace-pre-wrap"
                input-class="text-lg text-slate-200 min-w-[300px] max-w-full"
                placeholder="点击添加中文翻译..."
                :saving="false"
                @save="handleChineseSave"
              />
            </div>
          </div>

          <!-- Elements 列表 -->
          <CourseSentenceElements />
        </div>
        <div v-else key="empty" class="flex h-full items-center justify-center">
          <div class="text-center">
            <UIcon
              name="ph:book-bookmark-bold"
              class="mx-auto mb-3 size-10 text-slate-500"
            />
            <div class="text-lg text-slate-400">
              请选择一个句子查看详情
            </div>
          </div>
        </div>
      </Transition>
    </div>

    <!-- 添加 LearningContent 组件 -->
    <LearningContent v-model="showLearningContent" />

    <!-- 添加 WordDetails 组件 -->
    <WordDetails v-model="showWordDetails" />
  </div>
</template>
