<script setup lang="ts">
import type { ComponentPublicInstance } from 'vue'
import type { SortableEvent } from 'vue-draggable-plus'
import { VueDraggable } from 'vue-draggable-plus'
import { toast } from 'vue-sonner'
import Dialog from '~/components/common/Dialog.vue'
import { useCourseStore } from '~/stores/course'
import ElementItem from './ElementItem.vue'

type ElementItemInstance = ComponentPublicInstance & {
  startEdit: () => void
}

const loadingSpinner = useLoadingSpinner()

const courseStore = useCourseStore()
const modal = useModal()

// 空状态操作按钮
const emptyStateActions = [
  {
    name: '拆分',
    icon: 'ph:scissors',
    handler: async () => {
      if (!courseStore.selectedSentence?.isEmpty()) {
        toast.warning('句子里面有内容，如果想重新拆分的话，请先清空哦')
        return
      }

      loadingSpinner.startLoading([
        '拆分的时长会比较久 请耐心等待',
        '拆分功能依赖大模型 所以时间会久一点',
        '如果报错 那重新再试一次😄',
        '一节课的练习数不要超过 200 否则练习时长会非常久！',
      ])

      try {
        await courseStore.selectedSentence?.split()
      }
      finally {
        loadingSpinner.finishLoading()
      }
    },
  },
  {
    name: '加工',
    icon: 'ph:wrench',
    handler: async () => {
      loadingSpinner.startLoading()
      await courseStore.selectedSentence?.process()
      loadingSpinner.finishLoading()
    },
  },
]

// 提示信息
const tips = [
  {
    icon: 'ph:scissors',
    text: '拆分会将句子拆分成多个练习点',
  },
  {
    icon: 'ph:wrench',
    text: '加工会直接将当前内容转换为练习点(用于单词、词组、不需要拆分的)',
  },
]

function handleClearElements() {
  modal.open(Dialog, {
    title: '警告',
    content: '你确定？清空了可就没法恢复了',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      loadingSpinner.startLoading()
      await courseStore.selectedSentence?.clearElements()
      loadingSpinner.finishLoading()
    },
  })
}

// 删除 Map 的定义，改用简单的 boolean ref
const isCollapsed = ref(false)

// 添加对选中句子ID的监听
watch(
  () => courseStore.selectedSentence,
  () => {
    isCollapsed.value = false
  },
)

// 简化切换折叠状态的函数
function toggleCollapse() {
  isCollapsed.value = !isCollapsed.value
}

// 添加拖拽结束处理函数
function handleElementDragEnd({ newIndex, oldIndex }: SortableEvent) {
  if (newIndex === undefined || oldIndex === undefined)
    return
  courseStore.selectedSentence?.moveElementPosition(newIndex, oldIndex)
}

const elementRefs = ref<Record<string, ElementItemInstance | null>>({})

function handleNewElement(elementId: string) {
  nextTick(() => {
    // 聚焦到新创建 element 的 input 上 ，方便用户直接可以输入
    elementRefs.value[elementId]?.startEdit()
  })
}
</script>

<template>
  <div class="space-y-4">
    <!-- Elements 标题 -->
    <div class="flex h-10 items-center justify-between">
      <h3 class="flex items-center gap-2 text-xl font-semibold text-white">
        练习元素
        <span
          class="rounded-full bg-purple-500/20 px-2 py-0.5 text-sm font-medium text-purple-400"
        >
          {{ courseStore.selectedSentence?.elements.length }}
        </span>
      </h3>
      <!-- 操作按钮组 -->
      <div class="flex min-h-[40px] items-center gap-2">
        <template
          v-if="
            courseStore.selectedSentence?.elements.length
              && courseStore.selectedSentence?.elements.length > 0
          "
        >
          <button
            class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
            @click="handleClearElements"
          >
            <UIcon name="ph:eraser" class="size-4" />
            清空
          </button>
          <button
            class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
            @click="toggleCollapse"
          >
            <UIcon
              :name="isCollapsed ? 'ph:caret-down' : 'ph:caret-up'"
              class="size-4"
            />
            {{ isCollapsed ? "展开" : "折叠" }}
          </button>
        </template>
      </div>
    </div>

    <!-- 使用 transition 包裹需要动画的内容 -->
    <transition name="fade-slide" mode="out-in">
      <!-- 空状态提示 -->
      <div
        v-if="courseStore.selectedSentence?.elements.length === 0"
        key="empty"
        class="flex min-h-[405px] items-center justify-center rounded-lg border border-slate-700/50 bg-slate-800/30 p-6"
      >
        <div class="text-center">
          <UIcon
            name="ph:puzzle-piece"
            class="mx-auto mb-4 size-12 text-slate-500"
          />
          <div class="mb-6 text-lg font-medium text-slate-300">
            当前句子还没有练习元素
          </div>
          <div class="flex flex-col items-center gap-6">
            <!-- 主要操作按钮 -->
            <div class="flex justify-center gap-4">
              <button
                v-for="action in emptyStateActions"
                :key="action.name"
                class="inline-flex items-center gap-2 rounded-lg bg-purple-500 px-6 py-3 text-base font-medium text-white transition-all duration-200 hover:bg-purple-600 hover:shadow-lg active:scale-95"
                @click="action.handler"
              >
                <UIcon :name="action.icon" class="size-5" />
                {{ action.name }}
              </button>
            </div>

            <!-- 说明文字 -->
            <div class="mx-auto max-w-md space-y-2 text-xs opacity-80">
              <div
                v-for="tip in tips"
                :key="tip.text"
                class="flex items-center gap-3 rounded-lg border border-slate-700/30 bg-slate-800/20 px-4 py-2"
              >
                <UIcon
                  :name="tip.icon"
                  class="size-4 shrink-0 text-purple-400/80"
                />
                <span class="text-center text-slate-400">{{ tip.text }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Elements 列表 -->
      <div v-else key="list" class="space-y-3">
        <VueDraggable
          v-model="courseStore.selectedSentence!.elements"
          class="space-y-2"
          :animation="300"
          handle=".drag-handle"
          @end="handleElementDragEnd"
        >
          <div
            v-for="(element, index) in courseStore.selectedSentence!.elements"
            :key="element.id"
          >
            <ElementItem
              :ref="(el: any) => {
                if (el) elementRefs[element.id] = el as ElementItemInstance;
              }"
              :element="element"
              :index="index"
              :is-collapsed="isCollapsed"
              @new-element="handleNewElement"
            />
          </div>
        </VueDraggable>
      </div>
    </transition>
  </div>
</template>

<style scoped>
/* 添加过渡动画样式 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 现有的动画样式保持不变 */

/* 添加拖拽相关样式 */
.ghost-class {
  @apply bg-slate-700/50 opacity-50;
}

.drag-class {
  @apply cursor-grabbing;
}

/* 确保拖拽容器有正确的定位 */
.sortable-drag {
  position: relative !important;
}
</style>
