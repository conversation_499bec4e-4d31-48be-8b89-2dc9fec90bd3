<script setup lang="ts">
import type { LearningContent } from '@julebu/shared'
import { schemas } from '@julebu/shared'
import { useLoadingBar } from 'naive-ui'
import Dialog from '~/components/common/Dialog.vue'
import { useLoadingSpinner } from '~/composables/loadingSpinner'
import { useCourseStore } from '~/stores/course'

// 添加 v-model 相关的 props 和 emit
defineProps<{
  modelValue: boolean
}>()

defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const modal = useModal()
const loadingBar = useLoadingBar()
const courseStore = useCourseStore()
const loadingSpinner = useLoadingSpinner()

const rawLearningContent = computed(() => {
  return courseStore.selectedSentence?.learningContent
})
const isValidContent = computed(() =>
  schemas.validateLearningContent(rawLearningContent.value),
)
const learningContent = computed(() =>
  isValidContent.value ? (rawLearningContent.value as LearningContent) : null,
)

async function handleRegenerate() {
  loadingSpinner.startLoading([
    '学习内容的生成依赖大模型 时间会久一点',
    '请耐心等待哦',
  ])

  try {
    await courseStore.selectedSentence?.generateLearningContent()
  }
  finally {
    loadingSpinner.finishLoading()
  }
}

async function handleDelete() {
  modal.open(Dialog, {
    title: '警告',
    content: '确定要删除这个学习内容吗？',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      loadingBar.start()
      await courseStore.selectedSentence?.deleteLearningContent()
      loadingBar.finish()
    },
  })
}
</script>

<template>
  <UModal
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <UContainer
      :ui="{
        base: 'w-[90vw] h-[80vh] flex flex-col',
        constrained: 'max-w-[880px] max-h-[880px] p-0 sm:px-0 lg:px-0',
      }"
    >
      <div
        class="relative flex h-full flex-col overflow-hidden rounded-2xl bg-gray-900/95 backdrop-blur-xl"
      >
        <div class="absolute inset-0">
          <div
            class="animate-float absolute -right-32 -top-32 size-64 rounded-full bg-purple-500/10 blur-3xl"
          />
          <div
            class="animate-float-delay absolute -bottom-32 -left-32 size-64 rounded-full bg-blue-500/10 blur-3xl"
          />
        </div>

        <div class="relative z-10 flex h-full flex-col">
          <div class="shrink-0 p-6">
            <h2
              class="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-2xl font-bold text-transparent"
            >
              知识点讲解
            </h2>
          </div>

          <div class="flex-1 overflow-y-auto px-6 pb-6">
            <div v-if="!rawLearningContent">
              <p class="text-center text-gray-400">
                暂无学习内容
              </p>
            </div>
            <div v-else-if="!isValidContent" class="p-6">
              <div class="rounded-lg bg-red-950/50 p-6">
                <h3 class="mb-4 text-xl font-bold text-red-400">
                  数据格式错误
                </h3>
                <div class="flex flex-col items-center gap-4">
                  <p class="text-gray-300">
                    当前学习内容的数据格式不完整或有错误
                  </p>
                  <UButton
                    color="purple"
                    class="px-8 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/20"
                    @click="handleRegenerate"
                  >
                    重新生成
                  </UButton>
                </div>
              </div>
            </div>
            <div v-else class="space-y-6">
              <div v-if="learningContent" class="space-y-6">
                <div class="mb-6">
                  <p class="text-xl font-medium leading-relaxed text-gray-100">
                    {{ courseStore.selectedSentence?.content }}
                  </p>
                </div>

                <div class="mb-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
                  <div class="rounded-lg bg-gray-700/30 p-6 backdrop-blur-sm">
                    <h3 class="mb-3 text-lg font-medium text-purple-300">
                      中文
                    </h3>
                    <p class="text-lg leading-relaxed text-gray-200">
                      {{ learningContent.overview.chineseTranslation }}
                    </p>
                  </div>
                  <div class="rounded-lg bg-gray-700/30 p-6 backdrop-blur-sm">
                    <h3 class="mb-3 text-lg font-medium text-blue-300">
                      英英
                    </h3>
                    <p class="text-lg leading-relaxed text-gray-200">
                      {{ learningContent.overview.englishExplanation }}
                    </p>
                  </div>
                </div>

                <div class="mb-8">
                  <h3
                    class="mb-4 inline-block bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-xl font-bold text-transparent"
                  >
                    单词短语注解
                  </h3>
                  <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
                    <div
                      v-for="word in learningContent.vocabularyAnalysis"
                      :key="word.word"
                      class="group relative rounded-lg border border-purple-500/10 bg-gray-700/30 p-6 transition-all duration-300 hover:border-purple-500/30 hover:bg-gray-700/50"
                    >
                      <div class="mb-3 flex items-baseline justify-between">
                        <h4 class="text-xl font-bold text-purple-300">
                          {{ word.word }}
                        </h4>
                        <span class="text-sm text-gray-400">{{
                          word.phonetic
                        }}</span>
                      </div>
                      <div class="space-y-2 text-gray-200">
                        <div class="mt-3 space-y-2">
                          <p>
                            <span class="font-medium text-purple-300">中文翻译：</span>{{ word.chineseTranslation }}
                          </p>
                          <p>
                            <span class="font-medium text-purple-300">基本含义：</span>{{ word.basicMeaning }}
                          </p>
                          <p>
                            <span class="font-medium text-purple-300">上下文含义：</span>{{ word.contextualMeaning }}
                          </p>
                          <p>
                            <span class="font-medium text-purple-300">常用短语：</span>{{ word.commonPhrases }}
                          </p>
                          <p>
                            <span class="font-medium text-purple-300">同义词：</span>{{ word.synonyms || "无" }}
                          </p>
                          <p>
                            <span class="font-medium text-purple-300">反义词：</span>{{ word.antonyms || "无" }}
                          </p>
                          <p>
                            <span class="font-medium text-purple-300">记忆技巧：</span>{{ word.memorizationTip }}
                          </p>
                          <p>
                            <span class="font-medium text-purple-300">例句：</span>{{ word.exampleSentence }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mb-8">
                  <h3
                    class="mb-6 inline-block bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-2xl font-bold text-transparent"
                  >
                    语法分析
                  </h3>

                  <!-- 句子类型和时态卡片 -->
                  <div class="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div
                      class="rounded-xl bg-gradient-to-br from-blue-500/10 to-purple-500/10 p-px"
                    >
                      <div
                        class="h-full rounded-xl bg-gray-800/90 p-4 backdrop-blur-sm"
                      >
                        <h4 class="mb-2 text-blue-300">
                          句子类型
                        </h4>
                        <p class="text-lg font-medium text-gray-200">
                          {{ learningContent.grammarAnalysis.sentenceType }}
                        </p>
                      </div>
                    </div>

                    <div
                      class="rounded-xl bg-gradient-to-br from-purple-500/10 to-blue-500/10 p-px"
                    >
                      <div
                        class="h-full rounded-xl bg-gray-800/90 p-4 backdrop-blur-sm"
                      >
                        <h4 class="mb-2 text-purple-300">
                          时态和语气
                        </h4>
                        <p class="text-lg font-medium text-gray-200">
                          {{ learningContent.grammarAnalysis.tenseAndMood }}
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- 句子成分分析 -->
                  <div class="mb-6">
                    <h4 class="mb-4 text-lg font-semibold text-blue-300">
                      句子成分解析
                    </h4>
                    <div class="space-y-3">
                      <div
                        v-for="component in learningContent.grammarAnalysis
                          .sentenceComponents"
                        :key="component.component"
                        class="group rounded-lg border border-blue-500/10 bg-gray-800/70 p-4 transition-all duration-300 hover:border-blue-500/30"
                      >
                        <div class="flex flex-col space-y-3">
                          <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-blue-400">
                              {{ component.component }}
                            </span>
                          </div>
                          <p class="text-gray-300">
                            {{ component.grammaticalExplanation }}
                          </p>
                          <p
                            class="rounded-lg bg-gray-700/50 p-3 text-gray-200"
                          >
                            {{ component.meaningInSentence }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 语法重点 -->
                  <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
                    <div class="space-y-4">
                      <div class="rounded-lg bg-gray-800/70 p-4">
                        <h4 class="mb-3 text-purple-300">
                          关键语法点
                        </h4>
                        <p class="text-gray-200">
                          {{ learningContent.grammarAnalysis.keyGrammarPoints }}
                        </p>
                      </div>

                      <div class="rounded-lg bg-gray-800/70 p-4">
                        <h4 class="mb-3 text-purple-300">
                          特殊结构
                        </h4>
                        <p class="text-gray-200">
                          {{
                            learningContent.grammarAnalysis.specialStructures
                              || "无"
                          }}
                        </p>
                      </div>

                      <div class="rounded-lg bg-gray-800/70 p-4">
                        <h4 class="mb-3 text-purple-300">
                          词序
                        </h4>
                        <p class="text-gray-200">
                          {{ learningContent.grammarAnalysis.wordOrder }}
                        </p>
                      </div>
                    </div>

                    <div class="space-y-4">
                      <div class="rounded-lg bg-gray-800/70 p-4">
                        <h4 class="mb-3 text-purple-300">
                          语法规则应用
                        </h4>
                        <p class="text-gray-200">
                          {{
                            learningContent.grammarAnalysis
                              .grammarRulesApplication
                          }}
                        </p>
                      </div>

                      <div class="rounded-lg bg-gray-800/70 p-4">
                        <h4 class="mb-3 text-purple-300">
                          常见错误
                        </h4>
                        <p class="text-gray-200">
                          {{ learningContent.grammarAnalysis.commonErrors }}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  class="mb-6 rounded-lg border border-purple-500/10 bg-gray-800/50 p-4 backdrop-blur-sm"
                >
                  <h3
                    class="mb-3 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-xl font-semibold text-transparent"
                  >
                    文化和实用知识
                  </h3>
                  <p class="mb-2 dark:text-gray-300">
                    <strong>文化元素：</strong>{{
                      learningContent.culturalAndPracticalKnowledge
                        .culturalElements
                    }}
                  </p>
                  <p class="mb-2 dark:text-gray-300">
                    <strong>背景信息：</strong>{{
                      learningContent.culturalAndPracticalKnowledge
                        .backgroundInformation
                    }}
                  </p>
                  <p class="dark:text-gray-300">
                    <strong>实际应用：</strong>{{
                      learningContent.culturalAndPracticalKnowledge
                        .practicalApplication
                    }}
                  </p>
                </div>

                <div
                  class="mb-6 rounded-lg border border-purple-500/10 bg-gray-800/50 p-4 backdrop-blur-sm"
                >
                  <h4 class="mb-2 text-lg font-semibold text-purple-300">
                    功能和使用场景
                  </h4>
                  <p class="text-gray-200">
                    {{ learningContent.overview.functionAndContext }}
                  </p>
                </div>

                <div class="mb-6">
                  <h3
                    class="mb-3 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-xl font-semibold text-transparent"
                  >
                    相关例句
                  </h3>
                  <ul class="space-y-2">
                    <li
                      v-for="(
                        example, index
                      ) in learningContent.relatedExamples"
                      :key="index"
                      class="rounded-lg border border-purple-500/10 bg-gray-800/50 p-4 backdrop-blur-sm"
                    >
                      <p class="font-medium dark:text-gray-100">
                        {{ example.example }}
                      </p>
                      <p class="mt-2 text-sm text-gray-500 dark:text-gray-300">
                        {{ example.explanation }}
                      </p>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div
            class="flex shrink-0 justify-end space-x-4 border-t border-gray-800 p-4"
          >
            <UButton
              color="gray"
              class="px-8 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-gray-500/20"
              @click="$emit('update:modelValue', false)"
            >
              关闭
            </UButton>
            <UButton
              color="purple"
              class="px-8 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-gray-500/20"
              icon="i-heroicons-trash"
              @click="handleDelete"
            >
              删除
            </UButton>
          </div>
        </div>
      </div>
    </UContainer>
  </UModal>
</template>

<style scoped>
@keyframes float {
  0% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(10px, 10px);
  }
  100% {
    transform: translate(0, 0);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-float-delay {
  animation: float 3s ease-in-out infinite;
  animation-delay: 1.5s;
}
</style>
