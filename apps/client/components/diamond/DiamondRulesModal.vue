<script setup lang="ts">
defineProps<{
  modelValue: boolean
}>()

defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()
</script>

<template>
  <UModal
    :model-value="modelValue"
    :ui="{ width: 'w-[75vw] max-w-[800px]' }"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <div
      class="relative h-[80vh] max-h-[800px] overflow-y-auto rounded-2xl border border-purple-500/20 bg-gray-900/95 backdrop-blur-xl"
    >
      <!-- 背景装饰 -->
      <div class="absolute inset-0">
        <div
          class="absolute -right-32 -top-32 size-64 rounded-full bg-purple-500/10 blur-3xl"
        />
        <div
          class="absolute -bottom-32 -left-32 size-64 rounded-full bg-blue-500/10 blur-3xl"
        />
      </div>

      <!-- 内容区域 -->
      <div class="relative z-10 flex h-full flex-col">
        <!-- 标题栏 -->
        <div
          class="sticky top-0 z-20 flex items-center justify-between border-b border-purple-500/20 bg-gray-900/95 px-6 py-4 backdrop-blur-xl"
        >
          <h2
            class="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-xl font-bold text-transparent"
          >
            钻石消耗与免费额度说明
          </h2>

          <button
            class="flex items-center justify-center rounded-lg p-2 text-gray-400 transition-colors hover:bg-purple-500/10 hover:text-gray-300"
            @click="$emit('update:modelValue', false)"
          >
            <UIcon name="i-heroicons-x-mark" class="size-5" />
          </button>
        </div>

        <!-- 内容部分 -->
        <div class="p-6">
          <div class="space-y-6 text-slate-300">
            <div>
              <h3 class="mb-3 text-lg font-medium text-blue-400">
                句乐部 AI 功能钻石消耗与免费额度说明
              </h3>
              <p class="mb-4">
                欢迎使用句乐部！为了给您提供强大而智能的 AI 辅助学习功能，部分操作会消耗"钻石"。钻石的消耗主要用于支持背后先进的 AI 技术计算资源。
              </p>
            </div>

            <div class="rounded-xl bg-slate-800/50 p-5">
              <h3 class="mb-3 text-lg font-medium text-blue-400">
                每月免费钻石与重置规则
              </h3>
              <ul class="list-inside list-disc space-y-2">
                <li>
                  <span class="font-medium text-slate-200">免费额度：</span>
                  我们为 每位会员用户提供 每月 10,000 颗 的免费钻石额度，让您可以充分体验句乐部的 AI 功能。
                </li>
                <li>
                  <span class="font-medium text-slate-200">按月重置：</span>
                  您的免费钻石额度会在您会员周期的开始日自动重置回 10,000 颗。例如，如果您的会员资格是从某月 5 日开始生效的，那么您的免费钻石将在之后每个月的 5 日进行重置。
                </li>
                <li>
                  <span class="font-medium text-slate-200">额度不累积：</span>
                  请注意，上一个周期的免费钻石若未使用完，将在重置时清零，不会累积到下一个月。
                </li>
                <li>
                  <span class="font-medium text-slate-200">额度充足性：</span>
                  对于大多数用户的日常学习需求而言，这每月 10,000 颗的免费钻石通常是足够使用的。
                </li>
              </ul>
            </div>

            <div class="rounded-xl bg-slate-800/50 p-5">
              <h3 class="mb-3 text-lg font-medium text-blue-400">
                钻石充值
              </h3>
              <p class="mb-2">
                如果您的学习强度较大，每月免费额度不足，可以联系我们的客服 "阿崔" 进行钻石充值。
              </p>
              <p class="mb-2">
                目前的充值比例为：<span class="text-lg font-medium text-blue-400">1 元人民币 = 1,000 颗钻石</span>。
              </p>
              <!-- <p>
                这个功能还没有实现呢  等到下一个版本有人有需求了在说
                充值获得的钻石没有使用期限，会累加在您的账户中，并且系统会优先消耗您充值的钻石，再消耗免费额度钻石。
              </p> -->
            </div>

            <div class="rounded-xl bg-slate-800/50 p-5">
              <h3 class="mb-3 text-lg font-medium text-blue-400">
                AI 功能钻石消耗细则
              </h3>
              <p class="mb-3">
                我们主要依赖 DeepSeek 等业界领先的 AI 模型来驱动这些智能功能。以下是各项 AI 功能的具体钻石消耗：
              </p>

              <div class="space-y-4">
                <div class="rounded-lg bg-slate-700/30 p-4">
                  <h4 class="mb-2 font-medium text-slate-200">
                    1. 拆分功能
                  </h4>
                  <ul class="list-inside list-disc space-y-1 text-sm">
                    <li>单个单词: 1 颗钻石。</li>
                    <li>多个单词句子: 每 10 个单词（不足按10个计）消耗 10 颗钻石。</li>
                    <li>示例: 25 个单词的句子消耗 30 颗钻石。</li>
                  </ul>
                </div>

                <div class="rounded-lg bg-slate-700/30 p-4">
                  <h4 class="mb-2 font-medium text-slate-200">
                    2. 加工功能
                  </h4>
                  <ul class="list-inside list-disc space-y-1 text-sm">
                    <li>每个单词消耗 0.1 颗钻石，向上取整。</li>
                    <li>最低消耗 1 颗钻石。</li>
                    <li>示例: 15 词句子消耗 2 颗；5 词句子消耗 1 颗。</li>
                  </ul>
                </div>

                <div class="rounded-lg bg-slate-700/30 p-4">
                  <h4 class="mb-2 font-medium text-slate-200">
                    3. 格式化输入内容
                  </h4>
                  <ul class="list-inside list-disc space-y-1 text-sm">
                    <li>每 700 个字符（不足按700个计）消耗 1 颗钻石。</li>
                    <li>示例: 1000 字符消耗 2 颗钻石。</li>
                  </ul>
                </div>

                <div class="rounded-lg bg-slate-700/30 p-4">
                  <h4 class="mb-2 font-medium text-slate-200">
                    4. 生成句子知识点内容
                  </h4>
                  <ul class="list-inside list-disc space-y-1 text-sm">
                    <li>每个句子固定消耗 50 颗钻石。</li>
                  </ul>
                </div>

                <div class="rounded-lg bg-slate-700/30 p-4">
                  <h4 class="mb-2 font-medium text-slate-200">
                    5. 单词故事生成
                  </h4>
                  <ul class="list-inside list-disc space-y-1 text-sm">
                    <li>固定消耗 5 颗钻石。</li>
                  </ul>
                </div>

                <div class="rounded-lg bg-slate-700/30 p-4">
                  <h4 class="mb-2 font-medium text-slate-200">
                    6. AI 图片生成
                  </h4>
                  <ul class="list-inside list-disc space-y-1 text-sm">
                    <li>每生成一张图片固定消耗 259 颗钻石。</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="rounded-xl bg-slate-800/50 p-5">
              <h3 class="mb-3 text-lg font-medium text-blue-400">
                重要提示
              </h3>
              <p class="mb-2">
                我们希望这份说明能帮助您更好地了解句乐部各项 AI 功能的钻石消耗情况以及免费额度的使用规则。
              </p>
              <p class="mb-2">
                为了持续为您提供优质且可持续的 AI 服务，钻石的消耗标准、免费额度政策以及充值比例 可能会根据我们所使用的 DeepSeek 等底层 AI 模型的实际成本变化而进行适时调整。我们会努力在提供高质量服务和保持合理定价之间取得平衡。
              </p>
              <p>
                句乐部保留对以上所有钻石消耗规则、免费额度政策、重置机制、充值方案及其未来调整的最终解释权。
              </p>
              <p class="mt-4 font-medium text-blue-400">
                感谢您的理解与支持！
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </UModal>
</template>
