<script setup lang="ts">
interface InboxItem {
  id: string
  content: string
  remark?: string
  tags: string[]
  sourceUrl?: string
  createdAt: string
}

interface Props {
  item: InboxItem
  isSelectionMode: boolean
  isSelected: boolean
}

defineProps<Props>()

defineEmits<{
  (e: 'delete' | 'toggleSelection', id: string): void
  (e: 'addToCourse', item: InboxItem): void
}>()
</script>

<template>
  <div
    class="group relative rounded-xl border border-slate-700/50 bg-slate-800/30 px-6 py-3 transition-all hover:translate-y-[-2px] hover:border-slate-600/50 hover:bg-slate-700/40 hover:shadow-lg"
  >
    <!-- 选择框 -->
    <div
      v-if="isSelectionMode"
      class="absolute inset-y-0 left-4 flex items-center"
    >
      <input
        type="checkbox"
        :checked="isSelected"
        class="size-5 rounded border-gray-600 bg-gray-700 text-purple-500 focus:ring-purple-500"
        @change="$emit('toggleSelection', item.id)"
      >
    </div>

    <!-- 内容区域 -->
    <div :class="{ 'ml-8': isSelectionMode }">
      <div class="flex items-center gap-6">
        <!-- 左侧内容区域 -->
        <div class="min-w-0 flex-1">
          <!-- 主要内容 -->
          <p class="text-lg font-medium text-white">
            {{ item.content }}
          </p>

          <!-- 备注和标签区域 -->
          <div
            v-if="item.remark || (item.tags && item.tags.length > 0)"
            class="mt-2 space-y-2"
          >
            <!-- 备注 -->
            <p v-if="item.remark" class="text-sm text-slate-400">
              {{ item.remark }}
            </p>

            <!-- 标签区域 -->
            <div
              v-if="item.tags && item.tags.length > 0"
              class="-ml-[0.6rem] flex items-center"
            >
              <div class="flex flex-wrap items-center gap-2">
                <span
                  v-for="tag in item.tags"
                  :key="tag"
                  class="rounded-full bg-purple-500/10 px-2.5 py-1 text-xs font-medium text-purple-400"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧按钮区域 -->
        <div class="flex h-full shrink-0 items-center gap-1.5">
          <button
            class="rounded-lg p-2 text-slate-400 transition-colors hover:bg-slate-700 hover:text-white"
            title="添加到课程"
            @click="$emit('addToCourse', item)"
          >
            <UIcon name="ph:plus" class="size-5" />
          </button>

          <button
            class="rounded-lg p-2 text-red-400 transition-colors hover:bg-red-400/10"
            title="删除"
            @click="$emit('delete', item.id)"
          >
            <UIcon name="ph:trash" class="size-5" />
          </button>

          <a
            v-if="item.sourceUrl"
            :href="item.sourceUrl"
            target="_blank"
            class="rounded-lg p-2 text-slate-400 transition-colors hover:bg-slate-700/50"
            title="查看原文"
          >
            <UIcon name="ph:link" class="size-5" />
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
