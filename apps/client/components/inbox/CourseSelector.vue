<script setup lang="ts">
import type { CoursePack } from '@julebu/shared'
import Fuse from 'fuse.js'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import ContainerLoading from '~/components/common/ContainerLoading.vue'
import { useCoursePackStore } from '~/stores/coursePack'
import { useCoursePacksStore } from '~/stores/coursePacks'
import { isMac } from '~/utils/os'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'select': [courseId: string, coursePackId: string]
}>()

const coursePacksStore = useCoursePacksStore()
const coursePackStore = useCoursePackStore()
const selectedPack = ref<CoursePack | null>(null)
const isLoading = ref(false)

// 添加课程缓存
const coursesCache = ref<
  Record<
    string,
    {
      id: string
      title: string
      description: string
      gameId: string
      createdAt: string
      updatedAt: string
      position: number
    }[]
  >
>({})

// 添加课程加载状态
const isCoursesLoading = ref(false)

// 添加搜索和排序状态
const searchQuery = ref('')
const searchInput = ref<HTMLInputElement | null>(null)
const sortDirection = ref<'asc' | 'desc'>('asc')

// 添加课程排序状态
const courseSortDirection = ref<'asc' | 'desc'>('asc')

// 添加 Fuse.js 配置
const fuseOptions = {
  keys: ['title', 'description'],
  threshold: 0.3,
  location: 0,
  distance: 100,
  minMatchCharLength: 1,
  shouldSort: true,
  includeScore: true,
  useExtendedSearch: true,
}

// 修改计算属性，使用 position 排序
const filteredAndSortedPacks = computed(() => {
  let result = [...coursePacksStore.coursePacks]

  // 使用 Fuse.js 进行搜索
  if (searchQuery.value) {
    const fuse = new Fuse(result, fuseOptions)
    const searchResults = fuse.search(searchQuery.value)
    result = searchResults.map(result => result.item)
  }

  // 修改为按 position 排序
  result.sort((a, b) => {
    const comparison = a.position - b.position
    return sortDirection.value === 'asc' ? comparison : -comparison
  })

  return result
})

// 切换排序方向
function toggleSortDirection() {
  sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
}

// 修改排序后的课程列表计算属性
const sortedCourses = computed(() => {
  if (!coursePackStore.courses)
    return []

  const courses = [...coursePackStore.courses]

  // 始终按 position 排序，只是改变方向
  courses.sort((a, b) => {
    const comparison = a.position - b.position
    return courseSortDirection.value === 'asc' ? comparison : -comparison
  })

  return courses
})

// 简化切换排序方向的方法
function toggleCourseSortDirection() {
  courseSortDirection.value
    = courseSortDirection.value === 'asc' ? 'desc' : 'asc'
}

// 修改选择课程包的方法
async function selectCoursePack(pack: CoursePack) {
  selectedPack.value = pack

  // 如果缓存中存在，直接使用缓存的数据
  if (coursesCache.value[pack.id]) {
    coursePackStore.courses = coursesCache.value[pack.id]
    return
  }

  // 否则请求 API 并缓存结果
  isCoursesLoading.value = true
  try {
    await coursePackStore.init(pack.id)
    coursesCache.value[pack.id] = coursePackStore.courses
  }
  finally {
    isCoursesLoading.value = false
  }
}

// 修改 watch 来监听 modal 的打开/关闭
watch(
  () => props.modelValue,
  async (newVal) => {
    if (newVal) {
      if (!coursePacksStore.isInitialized) {
        isLoading.value = true
        try {
          await coursePacksStore.init()
        }
        finally {
          isLoading.value = false
        }
      }
    }
    else {
      nextTick(() => {
        selectedPack.value = null
        coursePackStore.currentCoursePack = undefined
        // 清空缓存
        coursesCache.value = {}
      })
    }
  },
)

// 修改选择课程的方法
async function selectCourse(courseId: string) {
  if (!selectedPack.value)
    return

  // 发出选择事件，同时返回 courseId 和 coursePackId
  emit('select', courseId, selectedPack.value.id)
  emit('update:modelValue', false)
}

// 添加键盘快捷键
onMounted(() => {
  window.addEventListener('keydown', handleShortcut)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleShortcut)
})

function handleShortcut(event: KeyboardEvent) {
  if ((event.metaKey || event.ctrlKey) && event.key.toLowerCase() === 'k') {
    event.preventDefault()
    searchInput.value?.focus()
  }
}
</script>

<template>
  <UModal
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <UContainer
      :ui="{
        base: 'w-[90vw] h-[80vh] flex flex-col',
        constrained: 'max-w-[900px] max-h-[800px] p-0 sm:px-0 lg:px-0',
      }"
    >
      <div
        class="relative h-full overflow-hidden rounded-2xl border border-purple-500/20 bg-gray-900/95 backdrop-blur-xl"
      >
        <ContainerLoading :show="isLoading" text="正在加载课程包..." />

        <div v-show="!isLoading" class="relative z-10 flex h-full">
          <div class="relative w-[300px] shrink-0 border-r border-slate-700/50">
            <div
              class="flex items-center justify-between border-b border-slate-700/50 p-6"
            >
              <h2
                class="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-xl font-bold text-transparent"
              >
                课程包
              </h2>
              <span class="text-xs text-slate-500">
                {{ coursePacksStore.coursePacks.length }} 个课程包
              </span>
            </div>

            <div class="px-6 py-3">
              <div class="relative flex items-center gap-2">
                <div class="relative flex-1">
                  <UIcon
                    name="heroicons:magnifying-glass"
                    class="absolute left-3 top-1/2 size-5 -translate-y-1/2 text-slate-400"
                  />
                  <input
                    ref="searchInput"
                    v-model="searchQuery"
                    type="search"
                    :placeholder="isMac() ? '搜索 ⌘ + K' : '搜索 Ctrl + K'"
                    class="w-full rounded-lg border border-slate-700 bg-slate-800/50 px-4 py-2 pl-10 text-slate-200 placeholder:text-slate-400 focus:border-purple-500 focus:outline-none focus:ring-1 focus:ring-purple-500"
                  >
                </div>

                <button
                  class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
                  @click="toggleSortDirection"
                >
                  <UIcon
                    name="ph:sort-ascending"
                    class="size-5 text-purple-400 transition-transform duration-200"
                    :class="{
                      'rotate-180': sortDirection === 'desc',
                    }"
                  />
                </button>
              </div>
            </div>

            <div
              class="custom-scrollbar h-[calc(100%-144px)] overflow-y-auto px-6 pb-6"
            >
              <div class="space-y-2">
                <button
                  v-for="pack in filteredAndSortedPacks"
                  :key="pack.id"
                  class="group flex w-full items-center gap-4 rounded-lg border-2 p-4 text-left transition-all hover:bg-slate-700/50"
                  :class="{
                    'border-slate-700/30 bg-slate-800/30':
                      selectedPack?.id !== pack.id,
                    'border-purple-500 bg-slate-700/50 !shadow-[0_0_0_1px_rgb(168,85,247,0.3)]':
                      selectedPack?.id === pack.id,
                  }"
                  @click="selectCoursePack(pack)"
                >
                  <div
                    class="relative h-12 w-20 shrink-0 overflow-hidden rounded-lg bg-slate-800/50"
                  >
                    <img
                      v-if="pack.cover"
                      :alt="pack.title"
                      class="size-full object-cover transition-transform duration-300 group-hover:scale-110"
                      :src="pack.cover"
                    >
                    <UIcon
                      v-else
                      name="i-heroicons-photo"
                      class="absolute left-1/2 top-1/2 size-6 -translate-x-1/2 -translate-y-1/2 text-slate-600"
                    />
                  </div>

                  <div class="min-w-0 flex-1">
                    <h3
                      class="truncate font-medium text-white transition-colors group-hover:text-purple-400"
                    >
                      {{ pack.title }}
                    </h3>
                    <p class="mt-0.5 line-clamp-1 text-xs text-slate-400">
                      {{ pack.description }}
                    </p>
                  </div>
                </button>
              </div>
            </div>
          </div>

          <div class="flex-1">
            <div
              class="flex items-center justify-between border-b border-slate-700/50 p-6"
            >
              <h2
                class="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-xl font-bold text-transparent"
              >
                {{ selectedPack?.title }}
              </h2>
              <span
                v-if="
                  selectedPack && !isCoursesLoading && coursePackStore.courses
                "
                class="text-xs text-slate-500"
              >
                {{ coursePackStore.courses.length }} 个课程
              </span>
            </div>

            <div
              v-if="selectedPack"
              class="mx-6 mb-2 mt-4 rounded-md bg-slate-800 p-4"
            >
              <div class="flex items-center gap-4">
                <button
                  class="inline-flex items-center gap-2 rounded-md bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
                  @click="toggleCourseSortDirection"
                >
                  <UIcon
                    name="ph:sort-ascending"
                    class="size-5 transition-transform duration-200"
                    :class="{
                      'rotate-180': courseSortDirection === 'desc',
                    }"
                  />
                  <span class="text-sm">
                    {{ courseSortDirection === "asc" ? "正序" : "倒序" }}
                  </span>
                </button>

                <NuxtLink
                  v-if="selectedPack"
                  :to="`/dashboard/course-packs/${selectedPack.id}`"
                  class="inline-flex items-center gap-2 rounded-md bg-slate-700/30 px-4 py-2 text-sm text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
                >
                  <UIcon
                    name="i-heroicons-arrow-top-right-on-square"
                    class="size-4"
                  />
                  进入课程包
                </NuxtLink>
              </div>
            </div>

            <div
              class="custom-scrollbar relative h-[calc(100%-180px)] overflow-y-auto px-6 py-1 pb-6"
            >
              <ContainerLoading
                :show="isCoursesLoading"
                text="正在加载课程列表..."
              />

              <div v-show="!isCoursesLoading" class="mb-4 space-y-4">
                <template v-if="selectedPack && coursePackStore.courses">
                  <div
                    v-if="coursePackStore.courses.length === 0"
                    class="flex h-[400px] flex-col items-center justify-center text-center"
                  >
                    <UIcon
                      name="i-heroicons-folder-open"
                      class="mb-4 size-12 text-slate-600"
                    />
                    <p class="text-slate-400">
                      当前课程包还没有任何课程
                    </p>
                  </div>

                  <template v-else>
                    <button
                      v-for="course in sortedCourses"
                      :key="course.id"
                      class="group flex w-full items-center justify-between rounded-lg border border-slate-700/50 bg-slate-800/30 p-4 text-left transition-all hover:border-purple-500/30 hover:bg-gradient-to-br hover:from-purple-500/10 hover:to-slate-800/50"
                      @click="selectCourse(course.id)"
                    >
                      <div class="min-w-0 flex-1">
                        <h3
                          class="font-medium text-white transition-colors group-hover:text-purple-400"
                        >
                          {{ course.title }}
                        </h3>
                        <p
                          class="mt-1 line-clamp-1 text-sm text-slate-400 transition-colors group-hover:text-slate-300"
                        >
                          {{ course.description }}
                        </p>
                      </div>

                      <div class="ml-4 shrink-0">
                        <span
                          class="inline-flex items-center gap-1.5 rounded-lg bg-purple-500/10 px-3 py-1.5 text-sm font-medium text-purple-400 group-hover:bg-purple-500/20"
                        >
                          <UIcon
                            name="i-heroicons-plus-small"
                            class="size-4"
                          />
                          选择
                        </span>
                      </div>
                    </button>
                  </template>
                </template>

                <template v-else>
                  <div
                    class="flex h-[600px] flex-col items-center justify-center text-center"
                  >
                    <UIcon
                      name="i-heroicons-arrow-left-circle"
                      class="mb-4 size-12 text-slate-600"
                    />
                    <p class="text-slate-400">
                      请先从左侧选择一个课程包
                    </p>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </UContainer>
  </UModal>
</template>

<style scoped>
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(139, 92, 246, 0.3) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(139, 92, 246, 0.3);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(139, 92, 246, 0.5);
}

/* 更新滚动区域高度 */
.custom-scrollbar {
  height: calc(100% - 144px);
}
</style>
