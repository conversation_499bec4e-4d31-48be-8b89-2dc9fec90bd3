<script setup lang="ts">
import { findLastIndex, isEmpty } from 'lodash-es'
import { useLrcStore } from '~/composables/subtitle/stores/lrcStore'
import { usePlayerStore } from '~/composables/subtitle/stores/playerStore'
import { useSubtitleStore } from '~/composables/subtitle/stores/subtitleStore'
import { lrcToSrt } from '~/composables/subtitle/useFormatProcessing'
import { formatTimeToString } from '~/composables/subtitle/useTimeUtils'

const lrcStore = useLrcStore()
const playerStore = usePlayerStore()
const subtitleStore = useSubtitleStore()

// Refs
const containerRef = ref<HTMLDivElement | null>(null)
const fileInputRef = ref<HTMLInputElement | null>(null)
const showTranslations = ref(true)
const copyFeedback = ref<{
  type: 'time' | 'text' | 'translation'
  index: number
} | null>(null)

// 计算当前活动行索引
const activeLineIndex = computed(() => {
  return findLastIndex(lrcStore.lrcLines.value, line => line.time <= playerStore.currentTime.value)
})

function handleConvertToSrt() {
  if (!lrcStore.lrcLines.value.length) {
    console.warn('没有可转换的 LRC 行')
    return
  }
  const srtSubtitles = lrcToSrt(lrcStore.lrcLines.value)
  subtitleStore.setSubtitles(srtSubtitles)
}

// 文件上传处理
async function handleLrcFileUpload(event: Event) {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    await lrcStore.uploadLrc(file)

    handleConvertToSrt()
  }
}

// 复制到剪贴板
async function copyToClipboard(text: string, type: 'time' | 'text' | 'translation', index: number) {
  if (!text) {
    return
  }

  try {
    await navigator.clipboard.writeText(text)
    copyFeedback.value = { type, index }
    setTimeout(() => {
      copyFeedback.value = null
    }, 200)
  }
  catch (error) {
    console.error('复制失败:', error)
  }
}

// 监听活动行变化，自动滚动
watch(
  () => activeLineIndex.value,
  async (newIndex) => {
    if (newIndex === -1 || !containerRef.value) {
      return
    }

    await nextTick()
    const activeElement = containerRef.value.children[newIndex] as HTMLElement
    if (activeElement) {
      activeElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  },
)

// 处理行点击
function handleLineClick(time: number) {
  playerStore.setSeekRequest(time)
}
</script>

<template>
  <div class="relative flex h-full flex-col">
    <input ref="fileInputRef" type="file" accept=".lrc" class="hidden" @change="handleLrcFileUpload">
    <p v-if="!lrcStore.hasLrc" class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 select-none text-sm text-gray-500">
      文本-预览区
    </p>

    <!-- 显示 LRC 内容 -->
    <template v-if="lrcStore.hasLrc && !isEmpty(lrcStore.lrcLines)">
      <div ref="containerRef" class="group flex w-full flex-1 flex-col items-center overflow-y-auto p-4">
        <!-- 显示带时间戳的歌词 -->
        <div
          v-for="(line, index) in lrcStore.lrcLines.value"
          :key="`lrc-${index}`"
          class="my-2 flex w-full max-w-3xl items-center transition-all duration-300"
          :class="{
            'text-primary-500 font-bold': index === activeLineIndex,
            'text-gray-300': index !== activeLineIndex,
          }"
          @click="handleLineClick(line.time)"
        >
          <!-- 时间戳区域 -->
          <div class="relative mr-4 flex min-w-[120px] items-center pl-6">
            <button
              class="absolute left-0 p-1 opacity-0 transition-all group-hover:opacity-100"
              :class="
                copyFeedback?.type === 'time' && copyFeedback?.index === index ? 'text-green-500' : 'text-gray-400 hover:text-primary-400'
              "
              :title="copyFeedback?.type === 'time' && copyFeedback?.index === index ? '已复制!' : '复制时间戳'"
              @click="copyToClipboard(formatTimeToString(line.time), 'time', index)"
            >
              <UIcon v-if="copyFeedback?.type === 'time' && copyFeedback?.index === index" name="i-heroicons-check" class="size-3.5" />
              <UIcon v-else name="i-heroicons-clipboard" class="size-3.5" />
            </button>
            <span class="cursor-pointer font-mono text-xs text-gray-500" @click="handleLineClick(line.time)">
              {{ formatTimeToString(line.time) }}
            </span>
          </div>

          <!-- 歌词文本区域 -->
          <div class="flex flex-1 flex-col">
            <div class="flex items-center">
              <!-- 普通文本显示 -->
              <span class="flex-1">{{ line.text }}</span>
              <button
                class="ml-2 p-1 opacity-0 transition-all group-hover:opacity-100"
                :class="
                  copyFeedback?.type === 'text' && copyFeedback?.index === index ? 'text-green-500' : 'text-gray-400 hover:text-primary-400'
                "
                :title="copyFeedback?.type === 'text' && copyFeedback?.index === index ? '已复制!' : '复制文本'"
                @click="copyToClipboard(line.text || '', 'text', index)"
              >
                <UIcon v-if="copyFeedback?.type === 'text' && copyFeedback?.index === index" name="i-heroicons-check" class="size-3.5" />
                <UIcon v-else name="i-heroicons-clipboard" class="size-3.5" />
              </button>
            </div>

            <!-- 翻译文本 -->
            <div v-if="showTranslations && line.translationText" class="mt-1 flex items-center text-sm text-gray-400">
              <span class="flex-1">{{ line.translationText }}</span>
              <button
                class="ml-2 p-1 opacity-0 transition-all group-hover:opacity-100"
                :class="
                  copyFeedback?.type === 'translation' && copyFeedback?.index === index
                    ? 'text-green-500'
                    : 'text-gray-400 hover:text-primary-400'
                "
                :title="copyFeedback?.type === 'translation' && copyFeedback?.index === index ? '已复制!' : '复制翻译'"
                @click="copyToClipboard(line.translationText, 'translation', index)"
              >
                <UIcon
                  v-if="copyFeedback?.type === 'translation' && copyFeedback?.index === index"
                  name="i-heroicons-check"
                  class="size-3.5"
                />
                <UIcon v-else name="i-heroicons-clipboard" class="size-3.5" />
              </button>
            </div>
          </div>
        </div>

        <!-- 显示普通文本行 -->
        <div v-for="(text, index) in lrcStore.plainTextLines.value" :key="`plain-${index}`" class="my-2 flex w-full max-w-3xl items-center">
          <div class="flex-1">
            <span class="text-gray-400">{{ text }}</span>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
