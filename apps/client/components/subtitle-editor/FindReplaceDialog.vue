<script setup lang="ts">
import { filter, includes, map } from 'lodash-es'
import BaseModal from '~/components/common/BaseModal.vue'
import { useSubtitleStore } from '~/composables/subtitle/stores/subtitleStore'

interface ResultItem {
  uuid: string
  id: number
  originalText: string
  translatedText: string
  originalPreview: string
  translatedPreview: string
  textMatch: boolean
  translationMatch: boolean
}

const props = defineProps<{ isOpen: boolean }>()
const emit = defineEmits(['close', 'replace'])
const subtitleStore = useSubtitleStore()
const findText = ref('')
const replaceText = ref('')
const caseSensitive = ref(false)
const wholeWord = ref(false)
const useRegex = ref(false)
const selected = ref<string[]>([])
const isOpen = computed({
  get: () => props.isOpen,
  set: (v) => {
    if (!v)
      emit('close')
  },
})

const results = computed(() => {
  if (!findText.value)
    return []
  try {
    const flags = caseSensitive.value ? 'g' : 'gi'
    const pattern = useRegex.value ? findText.value : findText.value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    const word = wholeWord.value ? `\\b${pattern}\\b` : pattern
    const re = new RegExp(word, flags)
    return filter(
      map(subtitleStore.subtitles.value, (sub) => {
        const textMatch = re.test(sub.text)
        const translationMatch = sub.translationText ? re.test(sub.translationText) : false
        if (!textMatch && !translationMatch)
          return undefined
        return {
          uuid: sub.uuid,
          id: sub.id,
          originalText: sub.text,
          translatedText: sub.translationText || '',
          originalPreview: textMatch ? sub.text.replace(re, replaceText.value) : sub.text,
          translatedPreview:
            translationMatch && sub.translationText ? sub.translationText.replace(re, replaceText.value) : sub.translationText || '',
          textMatch,
          translationMatch,
        }
      }),
      (item): item is ResultItem => !!item,
    )
  }
  catch {
    return []
  }
})

const allSelected = computed(() => results.value.length > 0 && results.value.length === selected.value.length)

function toggleAll() {
  if (allSelected.value)
    selected.value = []
  else selected.value = map(results.value, r => r.uuid)
}

function emitClose() {
  findText.value = ''
  replaceText.value = ''
  selected.value = []
  caseSensitive.value = false
  wholeWord.value = false
  useRegex.value = false
  emit('close')
}

function highlightMatch(text: string) {
  if (!findText.value)
    return text
  try {
    const flags = caseSensitive.value ? 'g' : 'gi'
    const pattern = useRegex.value ? findText.value : findText.value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    const word = wholeWord.value ? `\\b${pattern}\\b` : pattern
    return text.replace(new RegExp(word, flags), m => `<span class='bg-primary-900/80'>${m}</span>`)
  }
  catch {
    return text
  }
}

function highlightReplace(text: string) {
  if (!findText.value)
    return text
  try {
    const flags = caseSensitive.value ? 'g' : 'gi'
    const pattern = useRegex.value ? findText.value : findText.value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    const word = wholeWord.value ? `\\b${pattern}\\b` : pattern
    return text.replace(new RegExp(word, flags), m => `<span class='bg-green-700/80'>${m}</span>`)
  }
  catch {
    return text
  }
}

function doReplace() {
  const replacements = map(
    filter(results.value, r => includes(selected.value, r.uuid)),
    (r) => {
      const rep: {
        uuid: string
        newOriginalText?: string
        newTranslatedText?: string
      } = { uuid: r.uuid }
      if (r.textMatch)
        rep.newOriginalText = r.originalPreview
      if (r.translationMatch)
        rep.newTranslatedText = r.translatedPreview
      return rep
    },
  )
  emit('replace', replacements)
  emit('close')
}

watch(
  () => props.isOpen,
  (v) => {
    if (!v) {
      selected.value = []
      findText.value = ''
      replaceText.value = ''
      caseSensitive.value = false
      wholeWord.value = false
      useRegex.value = false
    }
  },
)
</script>

<template>
  <BaseModal v-model="isOpen" title="查找和替换" @close="emitClose">
    <div class="flex flex-1 flex-col">
      <div>
        <UInput v-model="findText" placeholder="输入要查找的文本" />
        <div class="mt-2 flex gap-4">
          <UCheckbox v-model="caseSensitive" label="区分大小写" />
          <UCheckbox v-model="wholeWord" label="匹配完整单词" />
          <UCheckbox v-model="useRegex" label="使用正则表达式" />
        </div>
        <UInput v-model="replaceText" class="mt-2" placeholder="输入要替换的文本" />
      </div>
      <div class="my-4 h-[20vh] overflow-y-auto rounded-lg border border-gray-700 sm:h-[26vh] md:h-[32vh] lg:h-[30vh] xl:h-[30vh] 2xl:h-[35vh]">
        <table class="w-full text-sm">
          <thead>
            <tr class="bg-gray-800">
              <th class="w-8">
                <UCheckbox :checked="allSelected" @change="toggleAll" />
              </th>
              <th class="w-12">
                ID
              </th>
              <th class="w-1/5">
                原文
              </th>
              <th class="w-1/5">
                原文预览
              </th>
              <th class="w-1/5">
                翻译
              </th>
              <th class="w-1/5">
                翻译预览
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in results" :key="item.uuid">
              <td><UCheckbox v-model="selected" :value="item.uuid" /></td>
              <td>{{ item.id }}</td>
              <td v-dompurify-html="highlightMatch(item.originalText)" class="whitespace-pre-line" />
              <td v-dompurify-html="highlightReplace(item.originalPreview)" class="whitespace-pre-line" />
              <td v-dompurify-html="highlightMatch(item.translatedText)" class="whitespace-pre-line" />
              <td v-dompurify-html="highlightReplace(item.translatedPreview)" class="whitespace-pre-line" />
            </tr>
            <tr v-if="results.length === 0">
              <td colspan="6" class="py-4 text-center text-gray-400">
                未找到匹配项
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <template #footer>
      <UButton :disabled="selected.length === 0" @click="doReplace">
        替换
      </UButton>
    </template>
  </BaseModal>
</template>
