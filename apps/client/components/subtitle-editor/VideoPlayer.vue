<script setup lang="ts">
import { forEachRight, isEmpty } from 'lodash-es'
import videojs from 'video.js'

import { PlayerStatusEnum, usePlayerStore } from '~/composables/subtitle/stores/playerStore'
import { useSubtitleStore } from '~/composables/subtitle/stores/subtitleStore'
import { subtitlesToVttString } from '~/composables/subtitle/useFormatProcessing'
import 'video.js/dist/video-js.css'

const playerStore = usePlayerStore()
const subtitlesStore = useSubtitleStore()

// 视频播放器引用
const videoPlayer = ref<HTMLVideoElement>()
const playerContainerRef = ref<HTMLDivElement>()

// video.js 播放器实例
let player: any = null
let resizeObserver: ResizeObserver | null = null

// VTT 字幕相关
const vttUrl = ref<string>('')
const vttTrackRef = ref<HTMLTrackElement | null>(null)

// 清除 vtt 轨道
function clearVttTrack() {
  if (!player) {
    return
  }

  if (vttTrackRef.value) {
    try {
      const existingTracks = (player as any)?.remoteTextTracks()
      if (existingTracks && existingTracks.length > 0) {
        // 从后向前遍历并移除，因为移除元素会改变列表长度和索引
        forEachRight(existingTracks, (track) => {
          if (track.kind === 'subtitles') {
            // 移除指定的远程文本轨道
            ;(player as any).removeRemoteTextTrack(track)
          }
        })
      }
    }
    catch (error) {
      console.error('Failed to clear vtt track:', error)
    }
    vttTrackRef.value = null
  }
  if (vttUrl.value) {
    URL.revokeObjectURL(vttUrl.value)
    vttUrl.value = ''
  }
}

// 生成 vtt blob url
function createBlobUrlFromVttPath() {
  const vttString = subtitlesToVttString(subtitlesStore.subtitles.value)
  const blob = new Blob([vttString], { type: 'text/vtt' })
  return URL.createObjectURL(blob)
}

// 应用 vtt 轨道
function generateVttAndApplyTrack() {
  // 检查播放器实例是否存在且未被销毁
  if (!player || player.isDisposed())
    return

  // 清除旧字幕
  clearVttTrack()

  // 没有字幕则不添加
  if (isEmpty(subtitlesStore.subtitles.value)) {
    return
  }

  vttUrl.value = createBlobUrlFromVttPath()

  const trackOptions = {
    kind: 'subtitles' as const,
    src: vttUrl.value,
    srclang: 'zh',
    label: '用户字幕',
    default: true,
  }

  const htmlTrackElement = player.addRemoteTextTrack(trackOptions, false)

  if (htmlTrackElement) {
    htmlTrackElement.addEventListener('load', () => {
      const track = (htmlTrackElement as any).track
      vttTrackRef.value = track
      if (track) {
        track.mode = 'showing' // 设置字幕为显示状态
      }
    })
  }
  else {
    console.warn('Failed to add remote text track or track element is invalid.')
  }
}

// 监听播放状态
watch(
  () => playerStore.status.value,
  (newStatus, oldStatus) => {
    // 避免重复处理
    if (!player || newStatus === oldStatus)
      return

    if (newStatus === PlayerStatusEnum.PLAYING) {
      player.play()
    }
    else if (newStatus === PlayerStatusEnum.PAUSED) {
      // 暂停播放
      try {
        player.pause()
      }
      catch (error) {
        console.warn('Failed to pause player:', error)
        // 忽略暂停错误
      }
    }
  },
)

// 监听全局 seekRequest，实现事件驱动跳转
watch(
  () => playerStore.seekRequest.value,
  (req, oldReq) => {
    // 有媒体文件或媒体URL时，才允许跳转
    if (!playerStore.mediaFile.value && !playerStore.mediaUrl.value)
      return
    if (req && (!oldReq || req.key !== oldReq.key) && player) {
      // 确保时间在持续时间之内
      const duration = player.duration()
      if (typeof duration === 'number' && req.time >= 0 && req.time <= duration) {
        player.currentTime(req.time)
      }
      else {
        console.warn(`Seek request ${req.time} is out of bounds (duration: ${duration})`)
      }
    }
  },
)

// 生成 vtt 文件
watch(
  () => subtitlesStore.subtitles.value,
  () => {
    generateVttAndApplyTrack()
  },
  { deep: true },
)

// 监听播放速率
watch(
  () => playerStore.playbackRate.value,
  () => {
    if (player) {
      player.playbackRate(playerStore.playbackRate.value)
    }
  },
)

function adjustPlayerSize() {
  if (!player || !playerContainerRef.value)
    return
  const containerWidth = playerContainerRef.value.clientWidth
  const containerHeight = playerContainerRef.value.clientHeight
  const videoEl = player.el() as HTMLElement
  if (!videoEl)
    return
  if (containerWidth > containerHeight) {
    videoEl.style.height = `${containerHeight}px`
    videoEl.style.width = '100%'
  }
  else if (containerWidth < containerHeight) {
    videoEl.style.height = '100%'
    videoEl.style.width = `${containerWidth}px`
  }
  else {
    videoEl.style.height = '100%'
    videoEl.style.width = '100%'
  }
}

function initPlayer() {
  if (!videoPlayer.value || !playerStore.mediaUrl.value)
    return

  const options = {
    controls: false,
    preload: 'auto',
    fluid: false,
    bigPlayButton: false,
  }

  player = videojs(videoPlayer.value, options, () => {
    if (!player)
      return

    // 添加关键事件监听器
    player.on('play', () => {
      playerStore.setStatus(PlayerStatusEnum.PLAYING)
    })

    player.on('pause', () => {
      playerStore.setStatus(PlayerStatusEnum.PAUSED)
    })

    player.on('timeupdate', () => {
      if (player && typeof player.currentTime === 'function') {
        const currentTime = player.currentTime()
        playerStore.currentTime.value = currentTime
      }
    })

    player.on('error', (error: any) => {
      console.error('[VideoPlayer] 播放器错误:', error)
    })

    // 设置媒体源
    player.src({
      src: playerStore.mediaUrl.value,
      type: playerStore.mediaFile.value?.type || 'video/mp4',
    })
    player.load()

    // 字幕和 UI 逻辑
    generateVttAndApplyTrack()
    if (playerContainerRef.value) {
      adjustPlayerSize()
      if (!resizeObserver) {
        resizeObserver = new ResizeObserver(() => {
          if (player)
            adjustPlayerSize()
        })
        resizeObserver.observe(playerContainerRef.value)
      }
    }
  })
}

onUnmounted(() => {
  if (resizeObserver && playerContainerRef.value) {
    resizeObserver.unobserve(playerContainerRef.value)
  }
  resizeObserver = null

  if (player && !player.isDisposed()) {
    player.dispose()
    player = null
  }
  clearVttTrack()
})

// 监听 playerStore.mediaUrl 变化，在 DOM 重建后重新初始化
watch(
  () => playerStore.mediaUrl.value,
  () => {
    nextTick(() => {
      if (playerStore.mediaUrl.value && videoPlayer.value) {
        initPlayer()
      }
    })
  },
)
</script>

<template>
  <div ref="playerContainerRef" class="relative flex size-full select-none items-center justify-center bg-black">
    <div
      v-if="!playerStore.mediaFile.value"
      class="absolute left-1/2 top-1/2 z-10 -translate-x-1/2 -translate-y-1/2 select-none text-sm text-gray-500"
    >
      媒体预览区
    </div>

    <!-- Video 元素，音频和视频都需要 -->
    <video
      v-if="playerStore.mediaUrl.value"
      :key="playerStore.mediaUrl.value"
      ref="videoPlayer"
      class="mx-auto block"
    >
      <source v-if="playerStore.mediaUrl.value" :src="playerStore.mediaUrl.value" type="video/mp4">
      <!-- 初始字幕轨道将通过 JS 添加 -->
      <p class="vjs-no-js">
        要观看此视频，请启用JavaScript，并考虑升级到web浏览器
        <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>
      </p>
    </video>

    <!-- 音频文件的提示文本 -->
  </div>
</template>
