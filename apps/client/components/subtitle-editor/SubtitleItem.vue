<script setup lang="ts">
import type { Subtitle } from '~/types/subtitle/subtitle'
import { find, findIndex } from 'lodash-es'
import { usePlayerStore } from '~/composables/subtitle/stores/playerStore'
import { useSubtitleStore } from '~/composables/subtitle/stores/subtitleStore'
import { useSubtitleTimeValidation } from '~/composables/subtitle/useSubtitleProcessing'
import { useSubtitleActions, useSubtitleEditing } from '~/composables/subtitle/useSubtitleUI'
import { parseSrtTime } from '~/composables/subtitle/useTimeUtils'
import TimeInput from './TimeInput.vue'

const props = defineProps<{
  subtitle: Subtitle
  nextSubtitle: Subtitle | null
  isLast: boolean
  id: string
  isActive: boolean
}>()

const emit = defineEmits(['mergeButtonHover', 'mergeButtonLeave', 'rowBlankClick'])
const subtitleStore = useSubtitleStore()
const playerStore = usePlayerStore()

// 获取最新字幕对象
const currentSubtitle = computed(() => find(subtitleStore.subtitles.value, s => s.uuid === props.subtitle.uuid) || props.subtitle)

// 计算上一行结束时间和下一行开始时间
const prevSubtitleEndTime = computed(() => {
  const idx = findIndex(subtitleStore.subtitles.value, s => s.uuid === props.subtitle.uuid)
  if (idx > 0) {
    return subtitleStore.subtitles.value[idx - 1].endTime
  }
  return null
})
const nextSubtitleStartTime = computed(() => props.nextSubtitle?.startTime || null)

// 编辑相关逻辑
const {
  editing,
  editingText,
  editingTranslationText,
  editingType,
  startTime,
  endTime,
  textareaRef,
  translationTextareaRef,
  startTimeInputRef,
  endTimeInputRef,
  startEdit,
  confirmEdit,
  cancelEdit,
  onStartTime,
  onEndTime,
} = useSubtitleEditing(currentSubtitle)

// 时间校验相关逻辑
const { isAddDisabled, isStartTimeValid, isEndTimeValid } = useSubtitleTimeValidation(
  startTime,
  endTime,
  nextSubtitleStartTime,
  prevSubtitleEndTime,
)

// 传递给 TimeInput 的顺序校验类型（歌词模式优化）
// 开始时间验证：只检查同行内时间关系，不再检查跨行关系
const startTimeOrderType = computed(() => {
  // 只检查开始时间与同行结束时间的关系
  if (!isStartTimeValid.value)
    return 'endTime'
  // 验证通过
  return true
})

// 结束时间验证：只检查同行内时间关系，不再检查跨行关系
const endTimeOrderType = computed(() => {
  // 只检查结束时间与同行开始时间的关系
  if (!isEndTimeValid.value)
    return false
  // 验证通过
  return true
})

// 操作相关逻辑
const { addTooltipContent, handleAddSubtitle, handleDeleteSubtitle, handleMergeSubtitle } = useSubtitleActions(
  ref(props.subtitle),
  isAddDisabled,
  ref(props.isLast),
)

// 计算当前字幕是否在循环播放
const isLooping = computed(() => playerStore.loopingSubtitleUuid.value === props.subtitle.uuid)

// 计算当前字幕是否在练习播放
const isPracticePlaying = computed(() => playerStore.practicePlayingUuid.value === props.subtitle.uuid)

function handleRowClick() {
  emit('rowBlankClick', currentSubtitle.value.startTime)
}

// 处理循环播放
function handleLoopToggle(event: MouseEvent) {
  event.stopPropagation()
  if (isLooping.value) {
    playerStore.stopLoop()
  }
  else {
    const startSeconds = parseSrtTime(props.subtitle.startTime)
    const endSeconds = parseSrtTime(props.subtitle.endTime)
    if (startSeconds !== null && endSeconds !== null) {
      playerStore.startLoop(props.subtitle.uuid, startSeconds, endSeconds)
    }
  }
}

// 处理练习点播放
function handlePracticeToggle(event: MouseEvent) {
  event.stopPropagation()
  if (isPracticePlaying.value) {
    playerStore.stopPracticePlay()
  }
  else {
    const startSeconds = parseSrtTime(props.subtitle.startTime)
    const endSeconds = parseSrtTime(props.subtitle.endTime)
    if (startSeconds !== null && endSeconds !== null) {
      playerStore.startPracticePlay(props.subtitle.uuid, startSeconds, endSeconds)
    }
  }
}

// 监听字幕时间变化，更新循环时间
watch(
  () => [props.subtitle.startTime, props.subtitle.endTime],
  ([newStartTime, newEndTime]) => {
    if (playerStore.loopingSubtitleUuid.value === props.subtitle.uuid) {
      const startSeconds = parseSrtTime(newStartTime)
      const endSeconds = parseSrtTime(newEndTime)
      playerStore.updateLoopTime(startSeconds, endSeconds)
    }
  },
)
</script>

<template>
  <div
    :id="props.id"
    class="subtitle-hover-row relative flex min-h-28 cursor-pointer flex-col border-b border-gray-700 transition-all duration-200 hover:bg-gray-800/50 hover:shadow-[0_0_15px_rgba(255,255,255,0.05)]"
    :class="{
      'is-active': props.isActive,
      'is-looping': isLooping,
      'is-practice-playing': isPracticePlaying,
    }"
    @click="handleRowClick"
  >
    <div class="flex grow items-center gap-2 px-2 py-6">
      <span class="w-6 text-center text-xs text-gray-500">
        {{ props.subtitle.id }}
      </span>
      <div class="flex flex-col justify-center">
        <div class="cursor-pointer" @click.stop="(startTimeInputRef as any)?.focus?.()">
          <TimeInput
            ref="startTimeInputRef"
            v-model="startTime"
            label="开始时间"
            :is-order-valid="startTimeOrderType"
            class="w-28"
            @update:model-value="onStartTime"
          />
        </div>
        <div class="mt-1 cursor-pointer" @click.stop="(endTimeInputRef as any)?.focus()">
          <TimeInput
            ref="endTimeInputRef"
            v-model="endTime"
            label="结束时间"
            :is-order-valid="endTimeOrderType"
            class="w-28"
            :max-seconds="playerStore.mediaFile.value && playerStore.duration.value > 0 ? playerStore.duration.value : undefined"
            @update:model-value="onEndTime"
          />
        </div>
      </div>
      <div class="flex-1">
        <div>
          <span
            v-if="!editing || editingType !== 'text'"
            class="inline-block w-full cursor-pointer rounded-md bg-gray-800 px-2 py-1"
            :class="{ 'placeholder:text-text': !props.subtitle.text }"
            @click.stop="startEdit('text')"
          >
            {{ props.subtitle.text || '（点击编辑文本）' }}
          </span>
          <textarea
            v-else-if="editing && editingType === 'text'"
            ref="textareaRef"
            v-model="editingText"
            class="min-h-12 w-full rounded border border-gray-700 bg-gray-900 px-1 py-0.5 text-sm text-gray-100"
            rows="1"
            @blur="confirmEdit"
            @keydown.enter.exact.prevent="confirmEdit"
            @keydown.esc="cancelEdit"
            @click.stop
          />
        </div>
        <div class="mt-1">
          <span
            v-if="(!editing || editingType !== 'translation') && props.subtitle.translationText"
            class="inline-block w-full cursor-pointer rounded-md bg-gray-800 px-2 py-1 text-sm text-gray-400"
            @click.stop="startEdit('translation')"
          >
            {{ props.subtitle.translationText }}
          </span>
          <button
            v-else-if="(!editing || editingType !== 'translation') && !props.subtitle.translationText"
            class="cursor-pointer border-none bg-transparent p-0 text-xs text-blue-400 hover:underline"
            @click.stop="startEdit('translation')"
          >
            + 添加翻译
          </button>
          <textarea
            v-else-if="editing && editingType === 'translation'"
            ref="translationTextareaRef"
            v-model="editingTranslationText"
            class="min-h-12 w-full rounded border border-gray-700 bg-gray-900 px-1 py-0.5 text-sm text-gray-400"
            rows="1"
            @blur="confirmEdit"
            @keydown.enter.exact.prevent="confirmEdit"
            @keydown.esc="cancelEdit"
            @click.stop
          />
        </div>
      </div>
      <div class="flex gap-1">
        <div class="relative">
          <UTooltip v-if="playerStore.mediaFile.value" :text="isPracticePlaying ? '停止播放' : '播放一次当前片段'" class="tooltip-fixed">
            <UButton
              :icon="isPracticePlaying ? 'i-heroicons-pause-solid' : 'i-heroicons-play-solid'"
              :color="isPracticePlaying ? 'green' : 'primary'"
              size="xs"
              variant="solid"
              @click="handlePracticeToggle"
            />
          </UTooltip>
        </div>
        <div class="relative">
          <UTooltip v-if="playerStore.mediaFile.value" text="循环播放当前片段" class="tooltip-fixed">
            <UButton
              icon="i-heroicons-arrow-path-solid"
              :color="isLooping ? 'blue' : 'primary'"
              size="xs"
              variant="solid"
              @click="handleLoopToggle"
            />
          </UTooltip>
        </div>
        <div class="relative">
          <UTooltip text="删除当前行" class="tooltip-fixed">
            <UButton icon="i-heroicons-trash" color="red" size="xs" variant="soft" @click.stop="handleDeleteSubtitle" />
          </UTooltip>
        </div>
      </div>
    </div>
    <div class="pointer-events-none z-10">
      <div class="pointer-events-auto -my-4 flex w-full items-center justify-center gap-20">
        <!-- 添加按钮 -->
        <div class="relative">
          <UTooltip :text="addTooltipContent" class="tooltip-fixed">
            <UButton
              icon="i-heroicons-plus-circle"
              color="primary"
              size="xs"
              variant="soft"
              :disabled="isAddDisabled"
              @click.stop="handleAddSubtitle"
            />
          </UTooltip>
        </div>
        <!-- 合并按钮 -->
        <div class="relative">
          <UTooltip :text="props.isLast ? '没有合并的行' : '合并'" class="tooltip-fixed">
            <UButton
              :disabled="props.isLast"
              icon="i-heroicons-sparkles"
              color="yellow"
              size="xs"
              variant="soft"
              @click.stop="handleMergeSubtitle"
              @mouseover="emit('mergeButtonHover', props.subtitle.uuid)"
              @mouseout="emit('mergeButtonLeave')"
            />
          </UTooltip>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 渐变+淡色内阴影 */
.subtitle-hover-row:hover {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.08) 0%, rgba(236, 72, 153, 0.08) 100%);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.15) inset;
}

/* 蓝粉渐变+白色柔和叠加 */
.subtitle-hover-row.is-active {
  background:
    linear-gradient(90deg, rgba(59, 130, 246, 0.16) 0%, rgba(236, 72, 153, 0.16) 100%), rgba(255, 255, 255, 0.08);
}

/* 循环播放状态样式 */
.subtitle-hover-row.is-looping {
  background:
    linear-gradient(90deg, rgba(59, 130, 246, 0.16) 0%, rgba(151, 68, 246, 0.16) 100%), rgba(255, 255, 255, 0.08);
  border-left: 2px solid #3b82f6;
}

/* 练习点播放状态样式 */
.subtitle-hover-row.is-practice-playing {
  background:
    linear-gradient(90deg, rgba(34, 197, 94, 0.16) 0%, rgba(251, 146, 60, 0.16) 100%), rgba(255, 255, 255, 0.08);
  border-left: 2px solid #22c55e;
}

/* 占位文本样式 */
.placeholder-text {
  color: rgba(156, 163, 175, 0.6);
  font-style: italic;
  opacity: 0.8;
  transition: all 0.2s ease;
}

.placeholder-text:hover {
  opacity: 1;
  color: rgba(156, 163, 175, 0.8);
}

/* 强制UTooltip正确定位 */
.tooltip-fixed :deep([data-popper-placement='bottom']) {
  position: fixed !important;
  transform: translateX(0%) !important;
  z-index: 9999 !important;
  inset: auto !important;
  margin: 0 !important;
  margin-top: 70px !important;
}
</style>
