<script setup lang="ts">
import { isEmpty } from 'lodash-es'
import { usePlayerStore } from '~/composables/subtitle/stores/playerStore'
import { useSubtitleStore } from '~/composables/subtitle/stores/subtitleStore'
import { useWaveformRegions, useWaveSurfer } from '~/composables/subtitle/useAudio'

// Refs
const waveformRef = ref<HTMLDivElement | null>(null)
const timelineRef = ref<HTMLDivElement | null>(null)

// 使用 WaveSurfer composable
const { wavesurfer, status, error, initialize, loadAudio, play, destroy, setPlaybackRate, regionsPluginInstance } = useWaveSurfer()

// 使用自定义的 region 管理 composable
const { registerRegionEvents, unregisterRegionEvents, syncRegions } = useWaveformRegions(regionsPluginInstance)

const playerStore = usePlayerStore()
const subtitleStore = useSubtitleStore()

/**
 * 封装 WaveSurfer 的初始化和音频加载逻辑。
 * @param {File} file - 需要加载的媒体文件。
 * @returns {Promise<void>} - 异步操作，无返回值。
 */
async function setupWaveSurfer(file: File) {
  if (!file) {
    return
  }

  try {
    await initialize({
      container: waveformRef.value!,
      timelineContainer: timelineRef.value || undefined,
      onSeek: time => playerStore.seek(time),
      onError: err => playerStore.setError(err),
      onDurationAvailable: duration => playerStore.setDuration(duration),
      onReady: () => {
        playerStore.updatePauseStatus()
        if (playerStore.isPlaying.value) {
          play()
        }
        // 设置初始播放速度
        setPlaybackRate(playerStore.playbackRate.value)
        // 注册区域事件
        registerRegionEvents()
      },
      playbackRate: playerStore.playbackRate.value,
    })

    await loadAudio(file)
  }
  catch (err) {
    console.error('设置 WaveSurfer 时出错:', err)
    playerStore.setError(err as Error)
  }
}

// 在组件挂载后执行初始化
onMounted(async () => {
  await nextTick()
  if (playerStore.mediaFile.value && waveformRef.value) {
    await setupWaveSurfer(playerStore.mediaFile.value)
  }
})

// 监听媒体文件变化
watch(
  () => playerStore.mediaFile.value,
  async (newFile, oldFile) => {
    if (newFile === oldFile) {
      return
    }
    try {
      if (newFile) {
        if (wavesurfer.value) {
          await loadAudio(newFile)
        }
        else if (waveformRef.value) {
          await setupWaveSurfer(newFile)
        }
      }
      else if (wavesurfer.value) {
        destroy()
      }
    }
    catch (err) {
      playerStore.setError(err as Error)
    }
  },
)

// 监听字幕变化并同步 region
watch(
  () => subtitleStore.subtitles.value,
  () => {
    syncRegions(wavesurfer.value, status.value)
  },
  { deep: true },
)

watch(
  () => status.value,
  (newStatus, oldStatus) => {
    if (newStatus === 'ready' && oldStatus !== 'ready') {
      if (!isEmpty(subtitleStore.subtitles.value)) {
        syncRegions(wavesurfer.value, newStatus)
      }
    }
  },
)

onBeforeUnmount(() => {
  unregisterRegionEvents()
})
</script>

<template>
  <div class="relative size-full">
    <!-- 波形容器 -->
    <div v-show="status === 'ready'" ref="waveformRef" class="size-full rounded-lg" />

    <!-- 时间轴容器 -->
    <div v-show="status === 'ready'" ref="timelineRef" class="h-5 w-full" />

    <!-- 状态指示器 -->
    <div v-if="status === 'initializing' || status === 'loading'" class="absolute inset-0 flex items-center justify-center bg-gray-800/50">
      <div class="flex flex-col items-center gap-2">
        <div class="border-primary-500 size-8 animate-spin rounded-full border-4 border-t-transparent" />
        <div class="text-gray-600">
          {{ status === 'initializing' ? '初始化中...' : '加载音频...' }}
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="status === 'error' && error" class="absolute inset-0 flex items-center justify-center bg-gray-900/80">
      <div class="p-4 text-center text-red-400">
        <div class="font-bold">
          加载失败
        </div>
        <div class="mt-2">
          {{ error.message }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
