<script setup lang="ts">
import type { Subtitle, SubtitleFilterType } from '~/types/subtitle/subtitle'
import { filter } from 'lodash-es'
import BaseModal from '~/components/common/BaseModal.vue'
import { useSubtitleStore } from '~/composables/subtitle/stores/subtitleStore'
import { hasTimestampError } from '~/composables/subtitle/useTimeUtils'

interface Props {
  isOpen: boolean
}

interface Emits {
  (e: 'update:isOpen', value: boolean): void
  (e: 'jumpTo', subtitle: Subtitle): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const subtitleStore = useSubtitleStore()

const filterOptions = [
  { value: 'all' as SubtitleFilterType, label: '显示全部', description: '显示所有字幕条目' },
  { value: 'textEmpty' as SubtitleFilterType, label: '原文为空', description: '仅显示原文为空的条目' },
  { value: 'textOnly' as SubtitleFilterType, label: '仅有原文', description: '仅显示有原文但无翻译的条目' },
  { value: 'translationOnly' as SubtitleFilterType, label: '仅有翻译', description: '仅显示有翻译但无原文的条目' },
  { value: 'bothEmpty' as SubtitleFilterType, label: '都为空', description: '仅显示原文和翻译都为空的条目' },
  { value: 'hasContent' as SubtitleFilterType, label: '有原文或翻译', description: '仅显示原文或翻译至少有一个不为空的条目' },
  { value: 'timestampError' as SubtitleFilterType, label: '时间戳错误', description: '显示时间戳格式错误或时间逻辑错误的条目' },
]

const selectedFilter = ref<SubtitleFilterType>(subtitleStore.currentFilter.value)

watch(
  () => subtitleStore.currentFilter.value,
  (newValue) => {
    selectedFilter.value = newValue
  },
)

function handleApplyFilter() {
  subtitleStore.setFilter(selectedFilter.value)
  emit('update:isOpen', false)
}

function handleClose() {
  selectedFilter.value = subtitleStore.currentFilter.value // 重置为当前筛选状态
  emit('update:isOpen', false)
}

// 计算每种筛选条件的数量
const filterCounts = computed(() => {
  const counts = {} as Record<SubtitleFilterType, number>

  filterOptions.forEach((option) => {
    if (option.value === 'all') {
      counts[option.value] = subtitleStore.subtitles.value.length
    }
    else {
      counts[option.value] = filter(subtitleStore.subtitles.value, (subtitle, index) => {
        const hasText = subtitle.text.trim() !== ''
        const hasTranslation = subtitle.translationText.trim() !== ''

        switch (option.value) {
          case 'textEmpty':
            return !hasText
          case 'textOnly':
            return hasText && !hasTranslation
          case 'translationOnly':
            return !hasText && hasTranslation
          case 'bothEmpty':
            return !hasText && !hasTranslation
          case 'hasContent':
            return hasText || hasTranslation
          case 'timestampError': {
            const prevSubtitle = index > 0 ? subtitleStore.subtitles.value[index - 1] : undefined
            const nextSubtitle = index < subtitleStore.subtitles.value.length - 1 ? subtitleStore.subtitles.value[index + 1] : undefined
            return hasTimestampError(subtitle, prevSubtitle, nextSubtitle)
          }
          default:
            return true
        }
      }).length
    }
  })

  return counts
})

// 计算筛选结果
const previewSubtitles = computed(() => {
  if (selectedFilter.value === 'all') {
    return subtitleStore.subtitles.value
  }

  return filter(subtitleStore.subtitles.value, (subtitle, index) => {
    const hasText = subtitle.text.trim() !== ''
    const hasTranslation = subtitle.translationText.trim() !== ''

    switch (selectedFilter.value) {
      case 'textEmpty':
        return !hasText
      case 'textOnly':
        return hasText && !hasTranslation
      case 'translationOnly':
        return !hasText && hasTranslation
      case 'bothEmpty':
        return !hasText && !hasTranslation
      case 'hasContent':
        return hasText || hasTranslation
      case 'timestampError': {
        const prevSubtitle = index > 0 ? subtitleStore.subtitles.value[index - 1] : undefined
        const nextSubtitle = index < subtitleStore.subtitles.value.length - 1 ? subtitleStore.subtitles.value[index + 1] : undefined
        return hasTimestampError(subtitle, prevSubtitle, nextSubtitle)
      }
      default:
        return true
    }
  })
})

// 限制预览显示前5条
const previewItems = computed(() => {
  return previewSubtitles.value.slice(0, 5)
})

// 计算总的筛选结果数量
const filteredCount = computed(() => {
  return previewSubtitles.value.length
})

// 判断是否有更多结果
const hasMoreResults = computed(() => {
  return previewSubtitles.value.length > 5
})

// 文本截断显示
function truncateText(text: string, maxLength: number): string {
  if (!text || text.length <= maxLength) {
    return text
  }
  return `${text.substring(0, maxLength)}...`
}

// 格式化时间戳显示
function formatTimeForPreview(time: string): string {
  return time
}

// 时间戳错误的视觉指示
function getErrorIndicator(_subtitle: Subtitle): string {
  return '时间戳错误'
}

// 跳转到指定字幕
function jumpToSubtitle(subtitle: Subtitle): void {
  emit('jumpTo', subtitle)

  // 关闭弹窗
  emit('update:isOpen', false)
}
</script>

<template>
  <BaseModal :model-value="props.isOpen" title="筛选字幕" @close="handleClose" @update:model-value="emit('update:isOpen', $event)">
    <div class="flex h-[50vh] gap-4 py-2">
      <div class="w-80 space-y-2 overflow-y-auto pr-2">
        <div
          v-for="option in filterOptions"
          :key="option.value"
          class="flex cursor-pointer items-start gap-3 rounded-lg border p-3 transition-all"
          :class="{
            'border-primary-500 bg-primary-900/20': selectedFilter === option.value,
            'border-gray-700 hover:border-gray-600': selectedFilter !== option.value,
          }"
          @click="selectedFilter = option.value"
        >
          <input v-model="selectedFilter" :value="option.value" type="radio" name="filter-option" class="mt-1">
          <div class="flex-1">
            <div class="mb-1 flex items-center justify-between">
              <span class="font-medium text-white">{{ option.label }}</span>
              <span class="rounded bg-gray-800 px-2 py-1 text-sm text-gray-400">
                {{ filterCounts[option.value] }}
              </span>
            </div>
            <p class="text-xs leading-relaxed text-gray-400">
              {{ option.description }}
            </p>
          </div>
        </div>
      </div>
      <div class="flex flex-1 flex-col rounded-lg border border-gray-700 bg-gray-800/50 p-4">
        <div class="mb-4 flex items-center justify-between">
          <h4 class="font-medium text-white">
            筛选预览
          </h4>
          <span class="text-sm text-gray-400">{{ previewItems.length }} / {{ filteredCount }} 条</span>
        </div>

        <div v-if="previewItems.length === 0" class="flex flex-1 items-center justify-center text-gray-400">
          <div class="text-center">
            <div class="mb-2 text-lg">
              📝
            </div>
            <div>暂无匹配结果</div>
          </div>
        </div>

        <div v-else class="flex-1 space-y-3 overflow-y-auto">
          <div
            v-for="subtitle in previewItems"
            :key="subtitle.uuid"
            class="cursor-pointer rounded-lg border border-gray-600 bg-gray-900/50 p-3 transition-all hover:border-gray-500"
            @click="jumpToSubtitle(subtitle)"
          >
            <div class="mb-2 flex items-center justify-between">
              <span class="text-xs text-gray-400">#{{ subtitle.id }}</span>
              <span class="text-xs text-gray-400">{{ formatTimeForPreview(subtitle.startTime) }} → {{ formatTimeForPreview(subtitle.endTime) }}</span>
            </div>
            <div class="space-y-1">
              <div class="text-sm text-gray-200">
                原文: {{ truncateText(subtitle.text, 30) || '(空)' }}
              </div>
              <div class="text-sm text-gray-300">
                翻译: {{ truncateText(subtitle.translationText, 30) || '(空)' }}
              </div>
            </div>
            <div v-if="selectedFilter === 'timestampError'" class="mt-2">
              <span class="rounded bg-red-900/30 px-2 py-1 text-xs text-red-400">
                {{ getErrorIndicator(subtitle) }}
              </span>
            </div>
          </div>

          <div v-if="hasMoreResults" class="py-2 text-center text-sm text-gray-400">
            还有 {{ filteredCount - previewItems.length }} 条...
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <UButton color="primary" @click="handleApplyFilter">
        应用筛选
      </UButton>
    </template>
  </BaseModal>
</template>
