<script setup lang="ts">
import type { Subtitle } from '~/types/subtitle/subtitle'
import { useSubtitleStore } from '~/composables/subtitle/stores/subtitleStore'

// const props = defineProps<{
//   onExport: () => void
//   onFindReplace: () => void
// }>()

const subtitleStore = useSubtitleStore()
const showJumpDialog = ref(false)
const showFilterDialog = ref(false)

function handleJumpTo(subtitle: Subtitle) {
  const el = document.getElementById(`subtitle-item-${subtitle.uuid}`)
  if (el) {
    el.scrollIntoView({ behavior: 'smooth', block: 'center' })
  }
}

// 获取当前筛选的显示文本
// const currentFilterText = computed(() => {
//   switch (subtitleStore.currentFilter.value) {
//     case 'all':
//       return '全部'
//     case 'textEmpty':
//       return '文本为空'
//     case 'textOnly':
//       return '仅有文本'
//     case 'translationOnly':
//       return '仅有翻译'
//     case 'bothEmpty':
//       return '都为空'
//     case 'hasContent':
//       return '有内容'
//     default:
//       return '全部'
//   }
// })

// 判断是否有激活的筛选
// const hasActiveFilter = computed(() => subtitleStore.currentFilter.value !== 'all')
</script>

<template>
  <div>
    <div class="sticky top-0 z-20 bg-gray-800/80 px-4 py-2 shadow-sm backdrop-blur-sm">
      <div class="mx-auto flex max-w-screen-xl items-center gap-2">
        <div class="relative">
          <UTooltip text="后退" class="tooltip-fixed">
            <UButton icon="i-heroicons-arrow-uturn-left" size="xs" :disabled="!subtitleStore.canUndo.value" @click="subtitleStore.undo()" />
          </UTooltip>
        </div>
        <UTooltip text="前进" class="tooltip-fixed">
          <UButton icon="i-heroicons-arrow-uturn-right" size="xs" :disabled="!subtitleStore.canRedo.value" @click="subtitleStore.redo()" />
        </UTooltip>
        <!-- <UButton icon="i-heroicons-magnifying-glass" size="sm" :disabled="isEmpty(subtitleStore.subtitles.value)" @click="props.onFindReplace">
          查找/替换
        </UButton>
        <UButton icon="i-heroicons-arrow-down-circle" size="sm" :disabled="isEmpty(subtitleStore.subtitles.value)" @click="showJumpDialog = true">
          跳转
        </UButton> -->
        <!-- 筛选按钮 -->
        <!-- <UButton
          icon="i-heroicons-funnel"
          size="sm"
          :disabled="isEmpty(subtitleStore.subtitles.value)"
          :color="hasActiveFilter ? 'primary' : 'gray'"
          @click="showFilterDialog = true"
        >
          筛选{{ hasActiveFilter ? `(${currentFilterText})` : '' }}
        </UButton> -->
      </div>
    </div>
    <SubtitleJumpDialog v-model:is-open="showJumpDialog" :subtitles="subtitleStore.filteredSubtitles.value" @jump-to="handleJumpTo" />
    <SubtitleFilterDialog v-model:is-open="showFilterDialog" @jump-to="handleJumpTo" />
  </div>
</template>

<style scoped>
/* 强制UTooltip正确定位 */
.tooltip-fixed :deep([data-popper-placement='bottom']) {
  position: fixed !important;
  transform: translateX(0%) !important;
  z-index: 9999 !important;
  inset: auto !important;
  margin: 0 !important;
  margin-top: 70px !important;
}
</style>
