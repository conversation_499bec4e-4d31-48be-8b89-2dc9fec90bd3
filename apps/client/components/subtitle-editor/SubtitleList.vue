<script setup lang="ts">
import { findIndex, isEmpty } from 'lodash-es'
import SubtitleItem from '~/components/subtitle-editor/SubtitleItem.vue'
import { usePlayerStore } from '~/composables/subtitle/stores/playerStore'
import { useSubtitleStore } from '~/composables/subtitle/stores/subtitleStore'
import { useMergeHighlight } from '~/composables/subtitle/useSubtitleUI'
import { parseSrtTime } from '~/composables/subtitle/useTimeUtils'

const scrollContainerRef = ref<HTMLDivElement | null>(null)

const subtitleStore = useSubtitleStore()
const playerStore = usePlayerStore()

const { highlightBoxStyle, showHighlightBox, handleMergeButtonHover, handleMergeButtonLeave } = useMergeHighlight(scrollContainerRef)

// 滚动相关
const activeSubtitleIndex = ref(-1)
const userLastScrolledTime = ref(0)
const isProgrammaticScroll = ref(false)

// 计算当前活动字幕索引（基于筛选后的列表）
const currentActiveSubtitleIndex = computed(() => {
  const currentTime = playerStore.currentTime.value
  const filteredSubtitles = subtitleStore.filteredSubtitles.value
  if (!filteredSubtitles || filteredSubtitles.length === 0)
    return -1
  return findIndex(filteredSubtitles, (s) => {
    const start = parseSrtTime(s.startTime)
    const end = parseSrtTime(s.endTime)
    return start !== null && end !== null && currentTime >= start && currentTime < end
  })
})

// 监听活动字幕变化，自动滚动
watch(currentActiveSubtitleIndex, async (newIndex) => {
  activeSubtitleIndex.value = newIndex
  if (newIndex === -1)
    return
  await nextTick()
  const uuid = subtitleStore.filteredSubtitles.value[newIndex]?.uuid
  if (!uuid)
    return
  const targetElement = document.getElementById(`subtitle-item-${uuid}`)
  if (targetElement && scrollContainerRef.value) {
    isProgrammaticScroll.value = true
    targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' })
    setTimeout(() => {
      isProgrammaticScroll.value = false
    }, 100)
  }
})

// 用户手动滚动处理
function handleUserScroll() {
  if (isProgrammaticScroll.value)
    return
  userLastScrolledTime.value = Date.now()
}

function handleRowBlankClick(startTime: string) {
  // SRT 时间字符串转秒
  const seconds = parseSrtTime(startTime)
  if (typeof seconds === 'number' && !Number.isNaN(seconds)) {
    playerStore.setSeekRequest(seconds)
  }
}
</script>

<template>
  <div class="relative h-full">
    <!-- 高亮框 -->
    <div
      v-if="showHighlightBox"
      :style="highlightBoxStyle"
      class="highlight-overlay-box pointer-events-none absolute z-10 box-border rounded-xl"
    />

    <!-- 字幕 item 列表，滚动容器 -->
    <div ref="scrollContainerRef" class="relative h-full overflow-y-auto" @scroll.passive="handleUserScroll">
      <SubtitleItem
        v-for="(sub, index) in subtitleStore.filteredSubtitles.value"
        :id="`subtitle-item-${sub.uuid}`"
        :key="sub.uuid"
        :subtitle="sub"
        :next-subtitle="subtitleStore.filteredSubtitles.value[index + 1] || null"
        :is-last="index === subtitleStore.filteredSubtitles.value.length - 1"
        :is-active="index === activeSubtitleIndex && activeSubtitleIndex !== -1"
        @merge-button-hover="handleMergeButtonHover"
        @merge-button-leave="handleMergeButtonLeave"
        @row-blank-click="handleRowBlankClick"
      />
    </div>

    <!-- 筛选状态提示 -->
    <div
      v-if="subtitleStore.currentFilter.value !== 'all' && isEmpty(subtitleStore.filteredSubtitles.value)"
      class="absolute inset-0 flex items-center justify-center"
    >
      <div class="text-center text-gray-400">
        <div class="mb-2 text-lg">
          没有符合筛选条件的字幕
        </div>
        <div class="text-sm">
          当前筛选：{{ subtitleStore.currentFilter.value === 'textEmpty' ? '文本为空'
            : subtitleStore.currentFilter.value === 'textOnly' ? '仅有文本'
              : subtitleStore.currentFilter.value === 'translationOnly' ? '仅有翻译'
                : subtitleStore.currentFilter.value === 'bothEmpty' ? '都为空'
                  : subtitleStore.currentFilter.value === 'hasContent' ? '有内容' : '全部' }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.highlight-overlay-box {
  border: 2px solid transparent;
  transition:
    box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    background 0.3s cubic-bezier(0.4, 0, 0.2, 1),
    border 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%);
  box-shadow: 0 4px 24px 0 rgba(59, 130, 246, 0.1);
  border-image: linear-gradient(90deg, #3b82f6, #ec4899) 1;
  border-image-slice: 1;
}
</style>
