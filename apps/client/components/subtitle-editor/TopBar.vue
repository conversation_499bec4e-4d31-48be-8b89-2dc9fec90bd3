<script setup lang="ts">
import DashboardTopBar from '~/components/common/DashboardTopBar.vue'
import { useTopBarLogic } from '~/composables/subtitle/useSubtitleUI'

defineEmits<{
  showDetail: []
}>()

const {
  audioFileInputRef,
  subtitleFileInputRef,
  canSaveChanges,
  totalElementsCount,

  triggerAudioFileInput,
  handleAudioFileUpload,
  handleFinish,
  triggerSubtitleFileInput,
  handleSubtitleFileUpload,
  // handleSubtitleAlign,
  // handleSubtitleFormat,
} = useTopBarLogic()
</script>

<template>
  <DashboardTopBar variant="subtitle" class="mx-0 mt-0">
    <template #left-content>
      <div class="flex items-center space-x-2">
        <!-- Elements 统计 -->
        <div
          class="flex items-center space-x-4 rounded-lg bg-slate-700/30 px-4 py-2"
        >
          <div class="flex items-center">
            <span class="text-sm text-slate-300">练习数</span>
            <span class="mx-2 text-lg font-semibold text-purple-400">{{ totalElementsCount }}</span>
          </div>
        </div>

        <!-- 分隔线 -->
        <div class="h-8 w-px bg-slate-700/50" />
        <!-- 字幕预处理 -->
        <!-- <button
          class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
          @click="handleSubtitleFormat"
        >
          <UIcon name="i-heroicons-adjustments-horizontal" class="size-5" />
          字幕格式化
        </button> -->

        <!-- 合并字幕导入按钮 -->
        <input ref="subtitleFileInputRef" type="file" accept=".vtt,.srt,.lrc" class="hidden" @change="handleSubtitleFileUpload">
        <button
          class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
          @click="triggerSubtitleFileInput"
        >
          <UIcon name="i-heroicons-document-arrow-up" class="size-5" />
          导入字幕
        </button>

        <!-- 获取媒体文件 -->
        <input ref="audioFileInputRef" type="file" accept="audio/*" class="hidden" @change="handleAudioFileUpload">
        <button
          class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95"
          @click="triggerAudioFileInput"
        >
          <UIcon name="i-heroicons-film" class="size-5" />
          导入媒体
        </button>

        <!-- 有音频和歌词时才可以点击完成按钮 -->
        <button
          class="inline-flex items-center gap-2 rounded-lg px-4 py-2 text-white transition-all duration-200 hover:shadow-lg active:scale-95"
          :class="canSaveChanges ? 'bg-emerald-500 hover:bg-emerald-600' : 'bg-slate-600 cursor-not-allowed'"
          :disabled="!canSaveChanges"
          @click="handleFinish"
        >
          <UIcon name="i-heroicons-check-circle" class="size-5" />
          保存
        </button>
      </div>
    </template>

    <template #right-content>
      <div class="flex items-center space-x-2">
        <!-- 课程详情按钮 -->
        <button
          class="inline-flex items-center gap-2 rounded-lg bg-purple-500 px-4 py-2 text-white transition-all duration-200 hover:bg-purple-600 hover:shadow-lg active:scale-95"
          @click="$emit('showDetail')"
        >
          <UIcon name="i-heroicons-information-circle" class="size-5" />
          课程详情
        </button>
      </div>
    </template>
  </DashboardTopBar>
</template>
