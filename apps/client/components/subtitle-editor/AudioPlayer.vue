<script setup lang="ts">
import { MAX_AUDIO_SIZE, SUPPORTED_FORMATS } from '@julebu/shared'
import { toast } from 'vue-sonner'
import AudioWaveform from '~/components/subtitle-editor/AudioWaveform.vue'
import { PLAYBACK_RATES, PlayerStatusEnum, usePlayerStore } from '~/composables/subtitle/stores/playerStore'
import { formatTimeToString } from '~/composables/subtitle/useTimeUtils'

const playerStore = usePlayerStore()
const audioFileInput = ref<HTMLInputElement | null>(null)

// 文件选择处理
function handleFileSelect(event: Event) {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file)
    return

  // 文件格式验证
  const fileExtension = file.name.split('.').pop()?.toLowerCase()
  if (!fileExtension || !SUPPORTED_FORMATS.includes(fileExtension)) {
    // 变量
    const supportedFormats = SUPPORTED_FORMATS.map(format => format.toUpperCase()).join('、')
    const errorMessage = `媒体文件仅支持 ${supportedFormats} 格式`
    toast.warning(errorMessage)

    playerStore.setError(new Error(errorMessage))
    ;(event.target as HTMLInputElement).value = ''
    return
  }

  if (file.size > MAX_AUDIO_SIZE) {
    const errorMessage = `媒体文件大小超出限制，请选择小于 ${MAX_AUDIO_SIZE / 1024 / 1024}MB 的文件`
    toast.warning(errorMessage)

    playerStore.setError(new Error(errorMessage))
    ;(event.target as HTMLInputElement).value = ''
    return
  }

  playerStore.setMediaFile(file)

  // 清空 input 以便连续上传同一文件
  ;(event.target as HTMLInputElement).value = ''
}

// 计算进度百分比
const progress = computed(() => {
  if (playerStore.duration.value === 0)
    return 0
  return (playerStore.currentTime.value / playerStore.duration.value) * 100
})

// 处理进度条点击
function handleProgressClick(event: MouseEvent) {
  const progressBar = event.currentTarget as HTMLDivElement
  const rect = progressBar.getBoundingClientRect()
  const percentage = (event.clientX - rect.left) / rect.width
  const newTime = percentage * playerStore.duration.value
  playerStore.setSeekRequest(newTime)

  // TODO: 更新波形进度 wavesurfer.value.setTime
}

function cyclePlaybackRate() {
  // 获取当前速率在PLAYBACK_RATES中的索引
  const idx = PLAYBACK_RATES.indexOf(playerStore.playbackRate.value)
  const nextIdx = (idx + 1) % PLAYBACK_RATES.length
  playerStore.playbackRate.value = PLAYBACK_RATES[nextIdx]
}

// 悬浮时间
const hoverTime = ref<number | null>(null)
const hoverX = ref<number | null>(null)

// 处理进度条鼠标移动
function handleProgressMouseMove(event: MouseEvent) {
  const progressBar = event.currentTarget as HTMLDivElement
  const rect = progressBar.getBoundingClientRect()
  const x = event.clientX - rect.left
  const percentage = Math.min(Math.max(x / rect.width, 0), 1)
  hoverTime.value = percentage * playerStore.duration.value
  hoverX.value = x
}

// 处理进度条鼠标离开
function handleProgressMouseLeave() {
  hoverTime.value = null
  hoverX.value = null
}

function triggerAudioFileInput() {
  audioFileInput.value?.click()
}
</script>

<template>
  <div class="relative flex h-52 flex-col gap-1 px-3">
    <input ref="audioFileInput" type="file" accept="audio/*,video/*" class="hidden" @change="handleFileSelect">
    <div v-if="!playerStore.mediaFile.value" class="absolute left-1/2 top-1/2 flex -translate-x-1/2 -translate-y-1/2 flex-col items-center">
      <p class="select-none text-xs text-gray-500">
        支持 MP3、WAV、M4A、OGG，文件小于 50MB
      </p>
      <p class="select-none text-xs text-gray-500">
        媒体时长需小于一小时，推荐使用 MP3 格式以获得最佳兼容性
      </p>
      <div class="mt-4 flex h-10 items-center gap-4">
        <UButton color="indigo" icon="i-heroicons-film" @click="triggerAudioFileInput">
          导入媒体
        </UButton>
      </div>
    </div>
    <!-- 播放控制栏部分 -->
    <div v-if="playerStore.mediaFile.value" class="mt-3 flex h-12 w-full items-center gap-4 bg-slate-700/20 px-3">
      <!-- 播放/暂停图标按钮 -->
      <UButton
        class="h-7 w-9"
        :disabled="playerStore.status.value === PlayerStatusEnum.ERROR || !playerStore.mediaFile"
        @click="playerStore.togglePlay"
      >
        <UIcon :name="playerStore.isPlaying.value ? 'heroicons:pause-solid' : 'heroicons:play-solid'" class="size-6 text-gray-200" />
      </UButton>

      <!-- 播放速度切换 -->
      <button
        class="w-14 rounded px-2 py-1 text-sm font-medium text-gray-200 hover:bg-gray-700 focus:outline-none"
        @click="cyclePlaybackRate"
      >
        <!-- 显示当前播放速率，点击切换 -->
        {{ playerStore.playbackRate.value }}x
      </button>

      <!-- 进度条 -->
      <div class="mx-2 grow">
        <div
          class="relative h-3 cursor-pointer rounded-full bg-gray-800"
          @click="handleProgressClick"
          @mousemove="handleProgressMouseMove"
          @mouseleave="handleProgressMouseLeave"
        >
          <!-- 已播放进度条 -->
          <div
            class="absolute left-0 top-0 h-3 rounded-full bg-purple-500 transition-all duration-100"
            :style="{ width: `${progress}%` }"
          />
          <!-- 悬浮时显示对应时间提示 -->
          <div
            v-if="hoverTime !== null && hoverX !== null"
            class="pointer-events-none absolute -top-7 z-20 select-none whitespace-nowrap rounded bg-black/80 px-2 py-1 text-xs text-white"
            :style="{ left: `${hoverX}px`, transform: 'translateX(-50%)' }"
          >
            {{ formatTimeToString(hoverTime) }}
          </div>
        </div>
      </div>

      <!-- 时间显示，垂直堆叠，显示当前时间和总时长 -->
      <div class="flex min-w-[110px] flex-col items-start text-xs text-gray-400">
        <span>{{ formatTimeToString(playerStore.currentTime.value) }} /</span>
        <span>{{ formatTimeToString(playerStore.duration.value) }}</span>
      </div>
    </div>

    <!-- 波形显示部分 -->
    <div v-if="playerStore.mediaFile.value" class="relative h-28 bg-slate-700/10">
      <!-- 波形显示，只有选择音频文件后才显示波形组件 -->
      <AudioWaveform />

      <!-- 错误提示 -->
      <div v-if="playerStore.error.value" class="absolute inset-0 z-10 flex items-center justify-center bg-black/60 text-red-600">
        {{ playerStore.error.value.message }}
      </div>
    </div>
  </div>
</template>
