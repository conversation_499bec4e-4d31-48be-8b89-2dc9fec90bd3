<script setup lang="ts">
import { debounce } from 'lodash-es'
import { parseSrtTime, validateSrtTimeFormat } from '~/composables/subtitle/useTimeUtils'

// 时间验证类型（歌词模式优化）：
// - boolean: 基本的有效/无效状态
// - 'endTime': 与同行结束时间的关系验证失败（用于开始时间）
// 移除 'prev' 和 'next' 类型，因为歌词模式允许时间重叠
type OrderValidType = boolean | 'endTime'
interface Props {
  modelValue: string
  label?: string
  isOrderValid?: OrderValidType
  maxSeconds?: number
}

const props = withDefaults(defineProps<Props>(), {
  label: '00:00:00,000',
  isOrderValid: true,
  maxSeconds: undefined,
})

const emit = defineEmits(['update:modelValue'])

const inputValue = ref('')
const isFormatValid = ref(true)
const inputRef = ref<HTMLInputElement | null>(null)

// 结束时间不允许超过媒体总时长
const isMaxValid = computed(() => {
  if (props.maxSeconds === undefined)
    return true
  const seconds = parseSrtTime(inputValue.value)
  if (seconds === null)
    return true // 格式无效时不做最大值校验
  return seconds <= props.maxSeconds
})

const isComponentValid = computed(() => {
  if (typeof props.isOrderValid === 'boolean') {
    return isFormatValid.value && props.isOrderValid && isMaxValid.value
  }
  // 只检查 'endTime' 错误，移除 'prev' 和 'next' 检查（歌词模式优化）
  return isFormatValid.value && props.isOrderValid !== 'endTime' && isMaxValid.value
})

watch(
  () => props.modelValue,
  (val) => {
    inputValue.value = val
    isFormatValid.value = validateSrtTimeFormat(val)
  },
  { immediate: true },
)

function onInputHandler(event: Event) {
  let newValue = (event.target as HTMLInputElement).value
  // 将中文的句号、点号、逗号替换为英文的句号、点号、逗号
  newValue = newValue.replace(/[。.,，]/g, ',')
  inputValue.value = newValue
  isFormatValid.value = validateSrtTimeFormat(newValue)
  emit('update:modelValue', newValue)
}

const onInput = debounce(onInputHandler, 700)
</script>

<template>
  <div>
    <UInput
      ref="inputRef"
      :model-value="inputValue"
      :placeholder="props.label"
      color="sky"
      :ui="{
        variant: {
          outline: isComponentValid
            ? 'shadow-sm bg-transparent text-gray-900 dark:text-white ring-1 ring-inset ring-{color}-500 dark:ring-{color}-400 focus:ring-2 focus:ring-{color}-500 dark:focus:ring-{color}-400'
            : 'shadow-sm bg-transparent text-gray-900 dark:text-white ring-2 ring-inset ring-red-500 dark:ring-red-400 focus:ring-2 focus:ring-red-500 dark:focus:ring-red-400',
        },
      }"
      style="color: #e5e7ebe3;"
      maxlength="12"
      @input="onInput"
    />
    <div v-if="!isComponentValid" class="mt-1 text-xs text-red-500">
      <span v-if="!isFormatValid" class="whitespace-nowrap">
        HH:MM:SS,mmm
      </span>
      <span v-else-if="props.isOrderValid === false">结束时间必须晚于开始时间</span>
      <span v-else-if="props.isOrderValid === 'endTime'">开始时间必须早于结束时间</span>
      <span v-else-if="!isMaxValid">不能超过音频总时长</span>
    </div>
  </div>
</template>
