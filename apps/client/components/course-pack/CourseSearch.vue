<script setup lang="ts">
import { useCoursePackStore } from '~/stores/coursePack'
import { isMac } from '~/utils/os'

const coursePackStore = useCoursePackStore()
const searchQuery = ref(coursePackStore.searchCoursesQuery)

// 监听变化并更新
watch(searchQuery, (value) => {
  coursePackStore.updateSearchQuery(value)
})

const searchInput = ref<HTMLInputElement | null>(null)

// 键盘快捷键监听
onMounted(() => {
  window.addEventListener('keydown', handleShortcut)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleShortcut)
})

function handleShortcut(event: KeyboardEvent) {
  if ((event.metaKey || event.ctrlKey) && event.key.toLowerCase() === 'k') {
    event.preventDefault()
    searchInput.value?.focus()
  }
}
</script>

<template>
  <div class="relative w-64">
    <UIcon
      name="heroicons:magnifying-glass"
      class="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-slate-400"
    />
    <input
      ref="searchInput"
      v-model="searchQuery"
      type="search"
      :placeholder="`搜索课程 ${isMac() ? '⌘' : 'Ctrl'} + K`"
      class="w-full rounded-md border border-slate-600 bg-slate-700 px-4 py-2.5 pl-10 text-sm text-white outline-none transition-colors duration-200 placeholder:text-slate-400 focus:border-purple-500 focus:ring-1 focus:ring-purple-500"
    >
  </div>
</template>
