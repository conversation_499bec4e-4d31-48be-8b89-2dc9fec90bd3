<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import BaseModal from '~/components/common/BaseModal.vue'
import { useCourseStore } from '~/stores/course'
import { useCoursePackStore } from '~/stores/coursePack'

// 定义组件属性
const props = defineProps<{
  modelValue: boolean
  coursePackId: string
}>()

// 定义事件
const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'publish-complete'): void
}>()

// 组件状态
const isOpen = computed(() => props.modelValue)
const loadingSpinner = useLoadingSpinner()

// Store
const coursePackStore = useCoursePackStore()
const courseStore = useCourseStore()

// 课程列表
const courses = ref<
  Array<{
    id: string
    title: string
    gameId?: string
    published: boolean
    isProcessing: boolean
    processStatus?:
      | 'generating_audio'
      | 'syncing_to_game'
      | 'completed'
      | 'failed'
    stepProgress: number
    publishError?: boolean
  }>
>([])

// 批量选择模式
const isBatchMode = ref(false)
const selectedCourseIds = ref<string[]>([])

// 监听对话框打开状态
watch(isOpen, async (newValue) => {
  if (newValue) {
    isBatchMode.value = false
    selectedCourseIds.value = []
    await loadCourses()
  }
})

// 加载课程
async function loadCourses() {
  try {
    // 这里我们假设已经有课程包信息
    if (!coursePackStore.currentCoursePack) {
      throw new Error('找不到课程包信息')
    }

    courses.value = coursePackStore.currentCoursePack.courses.map(course => ({
      id: course.id,
      title: course.title,
      published: !!course.gameId,
      gameId: course.gameId,
      processStatus: course.gameId ? 'completed' : 'syncing_to_game',
      stepProgress: course.gameId ? 100 : 0,
      isProcessing: false,
    }))
  }
  catch (error) {
    console.error('加载课程失败:', error)
    toast.error('加载课程失败')
  }
}

// 处理单个课程的发布
async function handlePublishCourse(course: (typeof courses.value)[0], skipLoadingManagement = false) {
  try {
    // 如果课程包未发布，先发布课程包
    if (!coursePackStore.currentCoursePack?.gameId) {
      await handlePublishCoursePack()
    }

    // 设置处理状态
    const courseIndex = courses.value.findIndex(c => c.id === course.id)
    if (courseIndex === -1)
      return

    if (!skipLoadingManagement) {
      loadingSpinner.startLoading()
    }

    // 处理课程发布
    await processCourse(courseIndex)

    if (!skipLoadingManagement) {
      toast.success(`课程《${course.title}》发布成功`)
    }
  }
  catch (error) {
    console.error('发布课程失败:', error)
    if (!skipLoadingManagement) {
      toast.error(`课程《${course.title}》发布失败，请重试`)
    }
  }
  finally {
    if (!skipLoadingManagement) {
      loadingSpinner.finishLoading()
    }
  }
}

// 处理单个课程的更新
async function handleUpdateCourse(course: (typeof courses.value)[0], skipLoadingManagement = false) {
  try {
    const courseIndex = courses.value.findIndex(c => c.id === course.id)
    if (courseIndex === -1)
      return

    if (!skipLoadingManagement) {
      loadingSpinner.startLoading([
        '需要提前生成音频哦',
        '时间会久一点哦',
        '请耐心等待',
      ])
    }

    // 处理课程更新
    await processCourse(courseIndex)

    if (!skipLoadingManagement) {
      toast.success(`课程《${course.title}》更新成功`)
    }
  }
  catch (error) {
    console.error('更新课程失败:', error)
    if (!skipLoadingManagement) {
      toast.error(`课程《${course.title}》更新失败，请重试`)
    }
  }
  finally {
    if (!skipLoadingManagement) {
      loadingSpinner.finishLoading()
    }
  }
}

// 发布课程包（仅发布课程包本身）
async function handlePublishCoursePack() {
  if (coursePackStore.currentCoursePack?.gameId) {
    toast.info('课程包已经发布')
    return
  }

  loadingSpinner.startLoading()

  try {
    // 发布课程包
    await coursePackStore.publishToGame()

    // 刷新课程包信息
    await coursePackStore.updateCourses()

    toast.success('课程包发布成功')
  }
  finally {
    loadingSpinner.finishLoading()
  }
}

// 批量发布所有未发布的课程
async function handleBatchPublish() {
  loadingSpinner.startLoading([
    '正在批量发布课程',
    '需要提前生成音频哦',
    '时间会久一点哦',
    '请耐心等待',
  ])

  try {
    // 1. 检查并发布课程包
    if (!coursePackStore.currentCoursePack?.gameId) {
      await handlePublishCoursePack()
    }

    // 2. 找出所有未发布的课程
    const unpublishedCourses = courses.value.filter(course => !course.gameId)

    if (unpublishedCourses.length === 0) {
      toast.info('没有需要发布的课程')
      return
    }

    // 3. 依次发布每个课程（跳过 loading 管理）
    for (const course of unpublishedCourses) {
      await handlePublishCourse(course, true)
    }

    // 4. 刷新课程包中的课程列表
    await coursePackStore.updateCourses()

    toast.success(`成功发布 ${unpublishedCourses.length} 个课程`)
  }
  finally {
    loadingSpinner.finishLoading()
  }
}

// 批量更新所有已发布的课程
async function handleBatchUpdate() {
  loadingSpinner.startLoading([
    '正在批量更新课程',
    '需要提前生成音频哦',
    '时间会久一点哦',
    '请耐心等待',
  ])

  try {
    // 找出所有已发布的课程
    const publishedCourses = courses.value.filter(course => !!course.gameId)

    if (publishedCourses.length === 0) {
      toast.info('没有需要更新的课程')
      return
    }

    // 依次更新每个课程（跳过 loading 管理）
    for (const course of publishedCourses) {
      await handleUpdateCourse(course, true)
    }

    // 刷新课程包中的课程列表
    await coursePackStore.updateCourses()

    toast.success(`成功更新 ${publishedCourses.length} 个课程`)
  }
  finally {
    loadingSpinner.finishLoading()
  }
}

// 下架课程包（包括所有课程）
async function handleUnpublishCoursePack() {
  if (!coursePackStore.currentCoursePack?.gameId) {
    toast.info('课程包尚未发布')
    return
  }

  loadingSpinner.startLoading()

  try {
    // 找出所有已发布的课程
    const publishedCourses = courses.value.filter(course => !!course.gameId)

    if (publishedCourses.length > 0) {
      // 依次下架每个课程
      for (const course of publishedCourses) {
        await courseStore.init(props.coursePackId, course.id)
        await courseStore.unpublishFromGame()

        // 更新课程的本地状态
        course.published = false
        course.gameId = undefined
        course.processStatus = 'completed'
        course.stepProgress = 100
      }
    }

    // 下架课程包
    await coursePackStore.unpublishFromGame()

    // 刷新课程包中的课程列表
    await coursePackStore.updateCourses()

    // 更新所有课程的 UI 状态
    courses.value = courses.value.map(course => ({
      ...course,
      published: false,
      gameId: undefined,
      processStatus: 'completed',
      stepProgress: 100,
      isProcessing: false,
    }))

    toast.success('课程包下架成功')
  }
  finally {
    loadingSpinner.finishLoading()
  }
}

// 批量下架所有课程（不影响课程包状态）
async function handleBatchUnpublish() {
  loadingSpinner.startLoading([
    '正在批量下架课程',
    '请稍候...',
  ])

  try {
    // 找出所有已发布的课程
    const publishedCourses = courses.value.filter(course => !!course.gameId)

    if (publishedCourses.length === 0) {
      toast.info('没有需要下架的课程')
      return
    }

    // 依次下架每个课程
    for (const course of publishedCourses) {
      // 初始化课程
      await courseStore.init(props.coursePackId, course.id)
      // 下架课程
      await courseStore.unpublishFromGame()
      // 更新课程状态
      course.published = false
      course.gameId = undefined
      course.processStatus = 'completed'
      course.stepProgress = 100
      course.isProcessing = false
    }

    // 刷新课程包中的课程列表
    await coursePackStore.updateCourses()

    toast.success(`成功下架 ${publishedCourses.length} 个课程`)
  }
  finally {
    loadingSpinner.finishLoading()
  }
}

// 关闭对话框
function handleClose() {
  emit('update:modelValue', false)
}

// 下架单个课程
async function handleUnpublishCourse(course: (typeof courses.value)[0], skipLoadingManagement = false) {
  if (!skipLoadingManagement) {
    loadingSpinner.startLoading()
  }

  try {
    const courseIndex = courses.value.findIndex(c => c.id === course.id)
    if (courseIndex === -1) {
      throw new Error('找不到要处理的课程')
    }

    await courseStore.init(props.coursePackId, course.id)
    await courseStore.unpublishFromGame()

    // 更新课程的本地状态
    course.published = false
    course.gameId = undefined
    course.processStatus = 'completed'
    course.stepProgress = 100
    course.isProcessing = false

    // 更新课程包中的课程列表
    await coursePackStore.updateCourses()

    if (!skipLoadingManagement) {
      toast.success(`课程《${course.title}》下架成功`)
    }
  }
  catch (error) {
    console.error('下架课程失败:', error)
    if (!skipLoadingManagement) {
      toast.error(`课程《${course.title}》下架失败，请重试`)
    }
  }
  finally {
    if (!skipLoadingManagement) {
      loadingSpinner.finishLoading()
    }
  }
}

// 处理单个课程的发布/更新
async function processCourse(courseIndex: number): Promise<boolean> {
  const course = courses.value[courseIndex]

  if (!course) {
    return false
  }

  // 步骤1：初始化课程
  await courseStore.init(props.coursePackId, course.id)

  if (courseStore.isEmpty()) {
    toast.error('课程内容不完整，请先完善课程内容')
    return false
  }

  // 步骤2：生成音频
  await courseStore.generateAudio()

  // 步骤3：发布/更新到游戏端
  if (course.gameId) {
    // 有 gameId 表示课程已发布，需要更新
    await courseStore.updateToGame()
  }
  else {
    // 没有 gameId 表示课程未发布，需要发布
    const result = await courseStore.publishToGame()
    course.gameId = result.courseId
  }

  // 更新课程状态
  course.published = true
  return true
}

// 切换批量选择模式
function toggleBatchMode() {
  isBatchMode.value = !isBatchMode.value
  if (!isBatchMode.value) {
    selectedCourseIds.value = []
  }
}

// 处理课程选择
function handleCourseSelect(courseId: string) {
  if (!isBatchMode.value)
    return

  const index = selectedCourseIds.value.indexOf(courseId)
  if (index === -1) {
    selectedCourseIds.value.push(courseId)
  }
  else {
    selectedCourseIds.value.splice(index, 1)
  }
}

// 全选
function selectAll() {
  selectedCourseIds.value = courses.value.map(course => course.id)
}

// 取消全选
function unselectAll() {
  selectedCourseIds.value = []
}

// 处理选中课程的发布
async function handleSelectedPublish() {
  if (selectedCourseIds.value.length === 0)
    return

  const isNeedPublish = selectedCourseIds.value.some((id) => {
    const course = courses.value.find(c => c.id === id)
    return course && !course.published
  })

  if (!isNeedPublish) {
    toast.info('没有需要发布的课程')
    return
  }

  loadingSpinner.startLoading([
    '正在发布选中课程',
    '需要提前生成音频哦',
    '时间会久一点哦',
    '请耐心等待',
  ])

  try {
    for (const courseId of selectedCourseIds.value) {
      const course = courses.value.find(c => c.id === courseId)
      if (!course || course.published)
        continue

      await handlePublishCourse(course, true)
    }
    toggleBatchMode()
  }
  finally {
    loadingSpinner.finishLoading()
  }
}

// 处理选中课程的更新
async function handleSelectedUpdate() {
  if (selectedCourseIds.value.length === 0)
    return

  const isNeedUpdate = selectedCourseIds.value.some((id) => {
    const course = courses.value.find(c => c.id === id)
    return course && course.published
  })

  if (!isNeedUpdate) {
    toast.info('没有需要更新的课程')
    return
  }

  loadingSpinner.startLoading([
    '正在更新选中课程',
    '需要提前生成音频哦',
    '时间会久一点哦',
    '请耐心等待',
  ])

  try {
    for (const courseId of selectedCourseIds.value) {
      const course = courses.value.find(c => c.id === courseId)
      if (!course || !course.published)
        continue

      await handleUpdateCourse(course, true)
    }
    toggleBatchMode()
  }
  finally {
    loadingSpinner.finishLoading()
  }
}

// 处理选中课程的下架
async function handleSelectedUnpublish() {
  if (selectedCourseIds.value.length === 0)
    return

  const isNeedUnpublish = selectedCourseIds.value.some((id) => {
    const course = courses.value.find(c => c.id === id)
    return course && course.published
  })

  if (!isNeedUnpublish) {
    toast.info('没有需要下架的课程')
    return
  }

  loadingSpinner.startLoading([
    '正在下架选中课程',
    '请稍候...',
  ])

  try {
    for (const courseId of selectedCourseIds.value) {
      const course = courses.value.find(c => c.id === courseId)
      if (!course || !course.published)
        continue

      await handleUnpublishCourse(course, true)
    }
    toggleBatchMode()
  }
  finally {
    loadingSpinner.finishLoading()
  }
}

const config = useRuntimeConfig()

// 处理课程包的游戏跳转
function handlePlayGameCoursePack() {
  const url = `${config.public.gameURL}/my-course-packs/${coursePackStore.currentCoursePack?.gameId}`
  window.open(url, '_blank')
}

// 处理单个课程的游戏跳转
function handlePlayGameCourse(course: (typeof courses.value)[0]) {
  const url = `${config.public.gameURL}/game/course/${coursePackStore.currentCoursePack?.gameId}/${course.gameId}`
  window.open(url, '_blank')
}

// 更新课程包（仅更新课程包本身）
async function handleUpdateCoursePack() {
  if (!coursePackStore.currentCoursePack?.gameId) {
    toast.info('课程包尚未发布')
    return
  }

  loadingSpinner.startLoading()

  try {
    // 更新课程包
    await coursePackStore.updateToGame()

    // 刷新课程包信息
    await coursePackStore.updateCourses()

    toast.success('课程包更新成功')
  }
  finally {
    loadingSpinner.finishLoading()
  }
}
</script>

<template>
  <BaseModal
    v-model="isOpen"
    width="w-[75vw] max-w-[1000px]"
    title="同步游戏端"
    @close="handleClose"
  >
    <div class="flex h-[60vh] max-h-[768px] flex-col space-y-4">
      <!-- 课程包信息区域 -->
      <div
        class="flex items-center justify-between rounded-xl border border-slate-700/40 bg-slate-800/20 px-6 py-4"
      >
        <div class="flex min-w-0 flex-1 items-center gap-3">
          <h3 class="text-lg font-medium text-slate-200">
            {{ coursePackStore.currentCoursePack?.title }}
          </h3>
          <span
            class="inline-flex items-center rounded-full px-2.5 py-1 text-xs font-medium" :class="[
              coursePackStore.currentCoursePack?.gameId
                ? 'bg-emerald-400/10 text-emerald-400'
                : 'bg-amber-400/10 text-amber-400',
            ]"
          >
            {{
              coursePackStore.currentCoursePack?.gameId ? "已发布" : "未发布"
            }}
          </span>
        </div>

        <!-- 课程包操作按钮 -->
        <div class="flex items-center gap-2">
          <button
            v-if="!coursePackStore.currentCoursePack?.gameId"
            class="inline-flex items-center gap-2 rounded-lg bg-purple-500 px-4 py-2 text-sm font-medium text-white transition-all duration-200 hover:bg-purple-600 hover:shadow-lg active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:shadow-none disabled:active:scale-100"
            @click="handlePublishCoursePack"
          >
            <UIcon name="i-heroicons-paper-airplane" class="size-4" />
            发布课程包
          </button>
          <template v-else>
            <button
              class="inline-flex items-center gap-2 rounded-lg bg-purple-500 px-4 py-2 text-sm font-medium text-white transition-all duration-200 hover:bg-purple-600 hover:shadow-lg active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:shadow-none disabled:active:scale-100"
              @click="handleUpdateCoursePack"
            >
              <UIcon name="i-heroicons-arrow-path" class="size-4" />
              更新课程包
            </button>
            <button
              class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-sm font-medium text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:shadow-none disabled:active:scale-100"
              @click="handleUnpublishCoursePack"
            >
              <UIcon name="i-heroicons-arrow-down-circle" class="size-4" />
              下架课程包
            </button>
            <button
              class="inline-flex items-center gap-2 rounded-lg bg-slate-700/30 px-4 py-2 text-sm font-medium text-slate-200 transition-all duration-200 hover:bg-slate-700/50 hover:shadow-lg active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:shadow-none disabled:active:scale-100"
              @click="handlePlayGameCoursePack"
            >
              <UIcon name="i-heroicons-play" class="size-4" />
              玩起来
            </button>
          </template>
        </div>
      </div>

      <!-- 课程列表区域 -->
      <div
        class="flex flex-1 flex-col overflow-hidden rounded-xl border border-slate-700/40 bg-slate-800/20"
      >
        <!-- 批量操作工具栏 -->
        <div
          class="flex items-center justify-between border-b border-slate-700/50 px-6 py-3"
        >
          <div class="flex items-center gap-4">
            <!-- 非批量选择模式下显示的按钮 -->
            <div v-if="!isBatchMode" class="flex items-center gap-2">
              <button
                class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
                @click="handleBatchPublish"
              >
                <UIcon name="i-heroicons-paper-airplane" class="size-4" />
                全部发布
              </button>
              <button
                class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
                @click="handleBatchUpdate"
              >
                <UIcon name="i-heroicons-arrow-path" class="size-4" />
                全部更新
              </button>
              <button
                class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
                @click="handleBatchUnpublish"
              >
                <UIcon name="i-heroicons-arrow-down-circle" class="size-4" />
                全部下架
              </button>
            </div>
            <!-- 批量选择模式下显示的按钮 -->
            <div v-else class="flex items-center gap-2">
              <button
                class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
                :disabled="selectedCourseIds.length === 0"
                @click="handleSelectedPublish"
              >
                <UIcon name="i-heroicons-paper-airplane" class="size-4" />
                发布选中
              </button>
              <button
                class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
                :disabled="selectedCourseIds.length === 0"
                @click="handleSelectedUpdate"
              >
                <UIcon name="i-heroicons-arrow-path" class="size-4" />
                更新选中
              </button>
              <button
                class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
                :disabled="selectedCourseIds.length === 0"
                @click="handleSelectedUnpublish"
              >
                <UIcon name="i-heroicons-arrow-down-circle" class="size-4" />
                下架选中
              </button>
              <button
                class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
                @click="selectAll"
              >
                <UIcon name="i-heroicons-check-circle" class="size-4" />
                全选
              </button>
              <button
                class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
                :disabled="selectedCourseIds.length === 0"
                @click="unselectAll"
              >
                <UIcon name="i-heroicons-x-circle" class="size-4" />
                取消全选
              </button>
            </div>
          </div>
          <button
            class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
            @click="toggleBatchMode"
          >
            <UIcon
              :name="
                isBatchMode ? 'i-heroicons-x-mark' : 'i-heroicons-squares-2x2'
              "
              class="size-4"
            />
            {{ isBatchMode ? "取消选择" : "批量选择" }}
          </button>
        </div>

        <!-- 课程列表内容 -->
        <div class="flex-1 overflow-y-auto p-4">
          <div class="space-y-2">
            <div
              v-for="course in courses"
              :key="course.id"
              class="group flex items-center justify-between rounded-lg border border-slate-700/40 bg-slate-800/20 px-4 py-3 transition-all duration-200 hover:border-purple-500/30 hover:bg-slate-800/40"
              :class="[
                isBatchMode && 'cursor-pointer',
                isBatchMode
                  && selectedCourseIds.includes(course.id)
                  && 'border-purple-500/50 bg-slate-800/40',
              ]"
              @click="
                isBatchMode
                  && !course.isProcessing
                  && handleCourseSelect(course.id)
              "
            >
              <!-- 左侧内容 -->
              <div class="flex min-w-0 flex-1 items-center">
                <!-- 复选框 -->
                <div v-if="isBatchMode" class="mr-4 shrink-0" @click.stop>
                  <input
                    type="checkbox"
                    :checked="selectedCourseIds.includes(course.id)"
                    :disabled="course.isProcessing"
                    class="size-4 rounded border-slate-600 bg-slate-800 text-purple-500 focus:ring-purple-500 focus:ring-offset-slate-900"
                    @change="handleCourseSelect(course.id)"
                  >
                </div>

                <!-- 标题和状态 -->
                <div class="min-w-0 flex-1">
                  <div class="flex items-center gap-3">
                    <h3 class="truncate font-medium text-slate-200">
                      {{ course.title }}
                    </h3>
                    <span
                      class="inline-flex items-center rounded-full px-2.5 py-1 text-xs font-medium" :class="[
                        course.published
                          ? 'bg-emerald-400/10 text-emerald-400'
                          : 'bg-amber-400/10 text-amber-400',
                      ]"
                    >
                      {{ course.published ? "已发布" : "未发布" }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 右侧操作按钮 -->
              <div class="flex items-center gap-2">
                <button
                  v-if="!course.gameId"
                  class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
                  :disabled="course.isProcessing"
                  @click="handlePublishCourse(course)"
                >
                  <UIcon name="i-heroicons-paper-airplane" class="size-4" />
                  发布
                </button>
                <template v-else>
                  <button
                    class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
                    :disabled="course.isProcessing"
                    @click="handleUpdateCourse(course)"
                  >
                    <UIcon name="i-heroicons-arrow-path" class="size-4" />
                    更新
                  </button>
                  <button
                    class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
                    :disabled="course.isProcessing"
                    @click="handleUnpublishCourse(course)"
                  >
                    <UIcon
                      name="i-heroicons-arrow-down-circle"
                      class="size-4"
                    />
                    下架
                  </button>
                  <button
                    class="inline-flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm font-medium text-slate-200 hover:bg-slate-600/50"
                    :disabled="course.isProcessing"
                    @click="handlePlayGameCourse(course)"
                  >
                    <UIcon name="i-heroicons-play" class="size-4" />
                    玩起来
                  </button>
                </template>
              </div>

              <!-- 处理状态遮罩层 -->
              <div
                v-if="course.isProcessing"
                class="absolute inset-0 flex items-center justify-center rounded-lg bg-slate-900/70 backdrop-blur-sm transition-all duration-300"
              >
                <div
                  class="rounded-lg border border-purple-500/20 bg-slate-800/70 p-4 text-center shadow-lg backdrop-blur-sm"
                >
                  <div class="flex items-center justify-center gap-2">
                    <UIcon
                      name="i-heroicons-arrow-path"
                      class="size-5 animate-spin text-purple-400"
                    />
                    <span class="text-sm font-medium text-purple-400">
                      {{
                        course.processStatus === "generating_audio"
                          ? "生成音频"
                          : "同步游戏端"
                      }}
                    </span>
                  </div>
                  <!-- 进度条 -->
                  <div class="mt-3 w-56">
                    <div class="h-2 w-full rounded-full bg-slate-700/50">
                      <div
                        class="h-2 rounded-full bg-purple-500 transition-all duration-300"
                        :style="{
                          width: `${course.stepProgress}%`,
                        }"
                      />
                    </div>
                    <div class="mt-1.5 text-xs text-purple-400">
                      {{ course.stepProgress }}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BaseModal>
</template>
