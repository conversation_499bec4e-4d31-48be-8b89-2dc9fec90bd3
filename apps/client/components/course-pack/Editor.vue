<script setup lang="ts">
import type { FormError } from '#ui/types'
import { CoursePackShareLevel, CourseType } from '@julebu/shared'
import { some } from 'lodash-es'
import { useLoadingBar } from 'naive-ui'
import { toast } from 'vue-sonner'
import { z } from 'zod'
import { useCourseCover } from '~/components/course-packs/Creator/useCourseCover'
import CascadeSelect from '~/components/ui/CascadeSelect.vue'
import { useCategoriesStore } from '~/stores/categories'
import { useCoursePackStore } from '~/stores/coursePack'

const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const isOpen = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

const coursePackStore = useCoursePackStore()
const categoriesStore = useCategoriesStore()
const loadingBar = useLoadingBar()
const isLoading = ref(false)
const { shareLevelOptions, shareLevelSelected } = useShareLevel()

const state = reactive({
  title: '',
  description: '',
})

const selectedCategoryId = ref<string>('')

// 处理分类选择变化
function handleCategoryChange(value: string | null) {
  selectedCategoryId.value = value || ''
}

const { previewImage, handleImageSelect, clearPreview, uploadPreviewImage }
  = useCourseCover()

const cover = computed(() => {
  if (previewImage.value) {
    return previewImage.value.previewUrl
  }
  return coursePackStore.currentCoursePack?.cover
})

// 确保分类数据已加载
onMounted(async () => {
  try {
    await categoriesStore.fetchCategories()
    initState()
  }
  catch (error) {
    console.error('Failed to load categories:', error)
  }
})

function initState() {
  state.title = coursePackStore.currentCoursePack?.title || ''
  state.description = coursePackStore.currentCoursePack?.description || ''
  selectedCategoryId.value = coursePackStore.currentCoursePack?.categoryId || ''
  shareLevelSelected.value
    = coursePackStore.currentCoursePack?.shareLevel
      || CoursePackShareLevel.Private
}

// 定义Zod schema
const zodSchema = z.object({
  title: z.string().min(2, '标题至少需要2个字符'),
  description: z.string().min(2, '描述至少需要2个字符'),
})

// 使用Zod进行验证，但通过validate函数与UForm集成
function validate(state: { title: string, description: string }): FormError[] {
  try {
    // 尝试验证数据
    zodSchema.parse(state)
    return [] // 验证通过，返回空错误数组
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      // 将Zod错误转换为UForm期望的格式
      return error.errors.map(err => ({
        path: err.path.join('.'),
        message: err.message,
      }))
    }
    return [] // 其他类型错误，返回空数组
  }
}

function noUpdate() {
  return (
    state.title === coursePackStore.currentCoursePack?.title
    && state.description === coursePackStore.currentCoursePack.description
    && shareLevelSelected.value === coursePackStore.currentCoursePack.shareLevel
    && selectedCategoryId.value === (coursePackStore.currentCoursePack?.categoryId || '')
    && !previewImage.value
  )
}

async function onSubmit() {
  if (noUpdate()) {
    isOpen.value = false
    return
  }

  const hasMusicCourse = some(coursePackStore.currentCoursePack?.courses, course => course.type === CourseType.music)
  // 如果勾选了共享，则不允许更新
  if (hasMusicCourse && shareLevelSelected.value === CoursePackShareLevel.FounderOnly) {
    toast.error('课程包中包含音乐课程，不支持会员共享')
    return
  }

  loadingBar.start()
  isLoading.value = true

  try {
    let finalCover = coursePackStore.currentCoursePack?.cover || ''
    if (previewImage.value) {
      finalCover = await uploadPreviewImage()
    }

    await coursePackStore.editCoursePack(
      state.title,
      state.description,
      finalCover,
      shareLevelSelected.value,
      selectedCategoryId.value,
    )

    toast.success('课程包更新成功')
    await delay(700)
    isOpen.value = false
  }
  catch (error) {
    if (error instanceof Error) {
      toast.error(error.message)
    }
  }
  finally {
    isLoading.value = false
    loadingBar.finish()
  }
}

watch(
  () => isOpen.value,
  async (val) => {
    if (!val) {
      onClose()
    }
    else {
      initState()
      // 获取分类列表
      try {
        await categoriesStore.fetchCategories()
      }
      catch (error) {
        console.error('获取分类失败:', error)
      }
    }
  },
)

function onClose() {
  clearPreview()
}

async function handleUploadChange(event: Event) {
  const input = event.target as HTMLInputElement
  if (!input.files?.length)
    return

  const file = input.files[0]

  try {
    await handleImageSelect(file)
    input.value = ''
  }
  catch (error: unknown) {
    if (error instanceof Error) {
      toast.error(error.message)
    }
    else {
      toast.error('图片选择失败')
    }
  }
}

function useShareLevel() {
  const shareLevelOptions = [
    {
      value: CoursePackShareLevel.Private,
      label: '私有',
    },
    {
      value: CoursePackShareLevel.FounderOnly,
      label: '会员共享',
    },
  ]

  const shareLevelSelected = ref<CoursePackShareLevel>(
    CoursePackShareLevel.Private,
  )

  return {
    shareLevelOptions,
    shareLevelSelected,
  }
}
</script>

<template>
  <div>
    <UModal
      :model-value="isOpen"
      :ui="{ width: 'w-full sm:max-w-lg' }"
      @update:model-value="isOpen = $event"
    >
      <!-- 添加渐变背景效果 -->
      <div
        class="relative overflow-hidden rounded-2xl border border-purple-500/20 bg-gray-900/95 backdrop-blur-xl"
      >
        <div class="absolute inset-0">
          <div
            class="absolute -right-32 -top-32 size-64 rounded-full bg-purple-500/10 blur-3xl"
          />
          <div
            class="absolute -bottom-32 -left-32 size-64 rounded-full bg-blue-500/10 blur-3xl"
          />
        </div>

        <div class="relative z-10 p-8">
          <!-- 渐变标题 -->
          <h2
            class="mb-6 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-2xl font-bold text-transparent"
          >
            编辑课程包
          </h2>

          <UForm
            :validate="validate"
            :state="state"
            class="space-y-6"
            @submit="onSubmit"
          >
            <UFormGroup label="标题" name="title" class="text-gray-300">
              <UInput
                v-model="state.title"
                placeholder="输入标题"
                class="border-purple-500/20 bg-gray-800/50 focus:border-purple-500/50"
              />
            </UFormGroup>

            <UFormGroup label="描述" name="description">
              <UTextarea
                v-model="state.description"
                placeholder="输入详情..."
              />
            </UFormGroup>

            <!-- 级联分类选择 -->
            <UFormGroup label="分类" class="text-gray-300">
              <CascadeSelect
                v-model="selectedCategoryId"
                :options="[...categoriesStore.categories]"
                :loading="categoriesStore.isLoading"
                placeholder="选择分类（可选）"
                key-field="id"
                label-field="label"
                parent-field="parentId"
                description-field="description"
                @change="handleCategoryChange"
              />
            </UFormGroup>

            <!-- 封面上传区域 -->
            <UFormGroup label="封面" class="text-gray-300">
              <div class="group relative">
                <img
                  :src="cover"
                  class="aspect-video h-48 w-full rounded-lg border-2 border-purple-500/20 bg-gray-800 object-contain"
                >
                <div
                  class="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 backdrop-blur-sm transition-all duration-300 group-hover:opacity-100"
                >
                  <label class="cursor-pointer">
                    <input
                      type="file"
                      accept="image/*"
                      class="sr-only"
                      @change="handleUploadChange"
                    >
                    <div class="text-center">
                      <Icon
                        name="i-mingcute-folder-upload-line"
                        class="text-4xl text-purple-400"
                      />
                      <span class="block text-sm text-gray-400">更换封面</span>
                    </div>
                  </label>
                </div>
              </div>

              <div class="mt-2 text-sm text-gray-500">
                提示：封面图必须是 16:9 的格式，size 小于 1mb
              </div>
            </UFormGroup>

            <!-- 共享级别选择 -->
            <div
              v-if="coursePackStore.currentCoursePack?.gameId"
              class="mt-6"
            >
              <h4 class="mb-3 text-sm font-medium text-gray-300">
                共享级别
              </h4>
              <div class="grid grid-cols-2 gap-4">
                <label
                  v-for="option in shareLevelOptions"
                  :key="option.value"
                  class="group relative cursor-pointer"
                >
                  <input
                    v-model="shareLevelSelected"
                    type="radio"
                    :value="option.value"
                    class="peer sr-only"
                  >
                  <div
                    class="flex items-center justify-center rounded-lg border-2 border-purple-500/20 bg-gray-800/50 p-4 transition-all duration-300 hover:border-purple-500/50 peer-checked:border-purple-500 peer-checked:bg-purple-500/10"
                  >
                    <span
                      class="text-sm text-gray-300 group-hover:text-white"
                    >{{ option.label }}</span>
                  </div>
                  <div
                    class="absolute right-2 top-2 hidden text-purple-400 peer-checked:block"
                  >
                    <Icon name="i-heroicons-check-circle-20-solid" />
                  </div>
                </label>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="mt-8 flex justify-end space-x-4">
              <UButton
                color="gray"
                class="px-8 transition-all duration-300 hover:scale-105"
                @click="isOpen = false"
              >
                取消
              </UButton>
              <UButton
                type="submit"
                :loading="isLoading"
                class="bg-gradient-to-r px-8 transition-all duration-300 hover:scale-105"
              >
                更新
              </UButton>
            </div>
          </UForm>
        </div>
      </div>
    </UModal>
  </div>
</template>
