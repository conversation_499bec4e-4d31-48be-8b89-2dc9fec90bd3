<script setup lang="ts">
import type { FormError } from '#ui/types'
import { CourseType } from '@julebu/shared'
import { useLoadingBar } from 'naive-ui'
import { toast } from 'vue-sonner'
import { z } from 'zod'
import { usePermissions } from '~/composables/usePermissions'
import { useCoursePackStore } from '~/stores/coursePack'

const props = defineProps<{
  modelValue: boolean
}>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const coursePackStore = useCoursePackStore()
const { hasFeaturePermission } = usePermissions()
const loadingBar = useLoadingBar()
const isLoading = ref(false)

const state = reactive({
  title: '',
  description: '',
  type: CourseType.normal,
})

// 课程类型选项
const courseTypeOptions = [
  { value: CourseType.normal, label: '常规课程' },
  { value: CourseType.music, label: '音乐课程' },
]

// 定义Zod schema
const zodSchema = z.object({
  title: z.string().min(2, '标题至少需要2个字符'),
  description: z.string().min(2, '描述至少需要2个字符'),
})

// 使用Zod进行验证，但通过validate函数与UForm集成
function validate(state: any): FormError[] {
  try {
    // 尝试验证数据
    zodSchema.parse(state)
    return [] // 验证通过，返回空错误数组
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      // 将Zod错误转换为UForm期望的格式
      return error.errors.map(err => ({
        path: err.path.join('.'),
        message: err.message,
      }))
    }
    return [] // 其他类型错误，返回空数组
  }
}

async function onSubmit() {
  loadingBar.start()
  isLoading.value = true

  try {
    await coursePackStore.createCourse(state.title, state.description, state.type)
    await delay(300)
    toast.success('课程创建成功')
    emit('update:modelValue', false)
  }
  catch (error) {
    console.error('表单提交错误:', error)
    toast.error('课程创建失败，请重试')
  }
  finally {
    isLoading.value = false
    loadingBar.finish()
  }
}

watch(
  () => props.modelValue,
  async (val) => {
    if (!val) {
      onClose()
    }
  },
)

function onClose() {
  clearForm()
  emit('update:modelValue', false)
}

function clearForm() {
  state.title = ''
  state.description = ''
}
</script>

<template>
  <div>
    <UModal
      :model-value="modelValue"
      :ui="{ width: 'w-full sm:max-w-lg' }"
      @update:model-value="$emit('update:modelValue', $event)"
    >
      <div
        class="relative overflow-hidden rounded-2xl border border-purple-500/20 bg-gray-900/95 backdrop-blur-xl"
      >
        <div class="absolute inset-0">
          <div
            class="absolute -right-32 -top-32 size-64 rounded-full bg-purple-500/10 blur-3xl"
          />
          <div
            class="absolute -bottom-32 -left-32 size-64 rounded-full bg-blue-500/10 blur-3xl"
          />
        </div>

        <div class="relative z-10 p-8">
          <h2
            class="mb-6 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-2xl font-bold text-transparent"
          >
            创建课程
          </h2>

          <UForm
            :validate="validate"
            :state="state"
            class="space-y-6"
            @submit="onSubmit"
          >
            <UFormGroup label="标题" name="title" class="text-gray-300">
              <UInput
                v-model="state.title"
                placeholder="输入标题"
                class="border-purple-500/20 bg-gray-800/50 focus:border-purple-500/50"
              />
            </UFormGroup>

            <UFormGroup v-if="hasFeaturePermission" label="课程类型" name="type" class="text-gray-300">
              <USelectMenu
                v-model="state.type"
                :options="courseTypeOptions"
                placeholder="选择课程类型"
                value-attribute="value"
                option-attribute="label"
              />
            </UFormGroup>

            <UFormGroup label="描述" name="description">
              <UTextarea
                v-model="state.description"
                placeholder="输入详情..."
              />
            </UFormGroup>

            <div class="mt-8 flex justify-end space-x-4">
              <UButton
                color="gray"
                class="px-8 transition-all duration-300 hover:scale-105"
                @click="onClose"
              >
                取消
              </UButton>
              <UButton
                type="submit"
                :loading="isLoading"
                class="bg-gradient-to-r px-8 transition-all duration-300 hover:scale-105"
              >
                创建
              </UButton>
            </div>
          </UForm>
        </div>
      </div>
    </UModal>
  </div>
</template>
