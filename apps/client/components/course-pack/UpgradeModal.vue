<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { toast } from 'vue-sonner'
import { useCourseStore } from '~/stores/course'
import { useCoursePackStore } from '~/stores/coursePack'

// 定义课程类型
interface Course {
  id: string
  title: string
  description: string
  gameId: string
  createdAt: string
  updatedAt: string
  position: number
  needsUpgrade?: boolean
  upgraded?: boolean
  upgradeError?: boolean
}

// 定义升级错误类型
interface UpgradeError {
  courseTitle: string
  message: string
}

const props = defineProps<{
  coursePackId: string
}>()

const courseStore = useCourseStore()
const coursePackStore = useCoursePackStore()

// 所有课程（包括需要升级和不需要升级的）
const allCourses = ref<Course[]>([])
// 需要升级的课程
const needUpgradeCourses = ref<Course[]>([])

// 升级相关的状态
const isUpgrading = ref(false)
const isUpgradeComplete = ref(false)
const upgradeProgress = ref({ completed: 0, total: 0 })
const upgradeErrors = ref<UpgradeError[]>([])
const currentUpgradingCourse = ref<Course | null>(null)
const upgradeStats = ref({
  success: 0,
  skipped: 0,
  failed: 0,
  words: 0,
})
const isLoading = ref(true)
const hasCheckedUpgrade = ref(false)
const isOpen = ref(false)
const gameUpdateStore = useGameUpdateStore()

// 在组件挂载时，如果autoCheck为true，自动检查升级状态
onMounted(async () => {
  await checkUpgradeStatus()

  // 如果有需要升级的课程，自动打开模态框
  if (needUpgradeCourses.value.length > 0) {
    isOpen.value = true
  }
})

// 检查课程升级状态
async function checkUpgradeStatus() {
  try {
    isLoading.value = true
    hasCheckedUpgrade.value = true

    // 确保课程包数据已加载
    if (
      !coursePackStore.currentCoursePack
      || coursePackStore.currentCoursePack.id !== props.coursePackId
    ) {
      await coursePackStore.init(props.coursePackId)
    }

    // 获取课程包中的所有课程
    if (coursePackStore.currentCoursePack) {
      // 使用 coursePackStore 的 checkCoursesNeedUpgrade 方法检查所有课程的升级状态
      const upgradeCheckResult = await coursePackStore.checkCoursesNeedUpgrade(
        props.coursePackId,
      )

      // 直接使用接口返回的课程数据，其中已包含 needsUpgrade 属性
      allCourses.value = upgradeCheckResult.courses

      // 筛选出需要升级的课程
      needUpgradeCourses.value = allCourses.value.filter(
        course => course.needsUpgrade,
      )
    }
  }
  catch (error) {
    console.error('检查课程升级状态时出错:', error)
  }
  finally {
    isLoading.value = false
  }
}

// 开始升级过程
async function startUpgrade() {
  try {
    isUpgrading.value = true
    isUpgradeComplete.value = false

    // 只升级需要升级的课程
    const coursesToUpgrade = allCourses.value.filter(
      course => course.needsUpgrade,
    )
    upgradeProgress.value = { completed: 0, total: coursesToUpgrade.length }
    upgradeErrors.value = []
    upgradeStats.value = { success: 0, skipped: 0, failed: 0, words: 0 }

    for (const course of coursesToUpgrade) {
      await upgradeCourse(course)
    }

    isUpgradeComplete.value = true
    // 标记整个课程包里面的课程都需要更新同步
    gameUpdateStore.markCurrentCoursesForUpdate()
  }
  catch (error) {
    console.error('升级过程中出现错误:', error)
    const errorMessage = error instanceof Error ? error.message : String(error)
    upgradeErrors.value.push({
      courseTitle: '整体升级过程',
      message: errorMessage,
    })
  }
  finally {
    isUpgrading.value = false
  }
}

// 升级单个课程
async function upgradeCourse(course: Course) {
  currentUpgradingCourse.value = course

  try {
    // 调用课程升级接口
    const result = await courseStore.upgradeCourse(course.id)

    // 更新统计信息
    upgradeStats.value.success += result.updatedSentences
    upgradeStats.value.skipped += result.skippedSentences
    upgradeStats.value.failed += result.failedSentences
    upgradeStats.value.words += result.skippedWords

    // 处理错误
    if (result.errors && result.errors.length > 0) {
      result.errors.forEach((error) => {
        upgradeErrors.value.push({
          courseTitle: `${course.title} - 句子: ${error.content}`,
          message: error.error,
        })
      })

      // 如果有错误，标记课程为升级失败
      if (result.failedSentences > 0) {
        const courseIndex = allCourses.value.findIndex(
          c => c.id === course.id,
        )
        if (courseIndex !== -1) {
          allCourses.value[courseIndex].upgradeError = true
        }
      }
      else {
        // 如果没有失败的句子，仍然标记为升级成功
        const courseIndex = allCourses.value.findIndex(
          c => c.id === course.id,
        )
        if (courseIndex !== -1) {
          allCourses.value[courseIndex].upgraded = true
          allCourses.value[courseIndex].needsUpgrade = false
        }
      }
    }
    else {
      // 标记课程为已升级
      const courseIndex = allCourses.value.findIndex(c => c.id === course.id)
      if (courseIndex !== -1) {
        allCourses.value[courseIndex].upgraded = true
        allCourses.value[courseIndex].needsUpgrade = false
      }
    }

    // 更新进度
    upgradeProgress.value.completed++
  }
  catch (error) {
    console.error(`升级课程 ${course.title} 失败:`, error)
    const errorMessage = error instanceof Error ? error.message : String(error)

    upgradeErrors.value.push({
      courseTitle: course.title,
      message: errorMessage,
    })

    // 标记课程为升级失败
    const courseIndex = allCourses.value.findIndex(c => c.id === course.id)
    if (courseIndex !== -1) {
      allCourses.value[courseIndex].upgradeError = true
    }

    // 即使失败也更新进度
    upgradeProgress.value.completed++
  }
}

// 关闭升级模态框
function handleClose() {
  if (isUpgrading.value) {
    toast.warning('升级过程中，请勿关闭页面')
    return
  }

  // 重置状态
  isUpgradeComplete.value = false
  upgradeErrors.value = []
  currentUpgradingCourse.value = null

  isOpen.value = false
}
</script>

<template>
  <UModal
    :model-value="isOpen"
    :ui="{ width: 'w-[70vw] max-w-[900px]' }"
    :prevent-close="isUpgrading"
    @update:model-value="handleClose"
  >
    <div
      class="relative h-[80vh] max-h-[768px] overflow-hidden rounded-2xl border border-purple-500/20 bg-gray-900/95 backdrop-blur-xl"
    >
      <!-- 背景装饰 -->
      <div class="absolute inset-0">
        <div
          class="absolute -right-32 -top-32 size-64 rounded-full bg-purple-500/10 blur-3xl"
        />
        <div
          class="absolute -bottom-32 -left-32 size-64 rounded-full bg-purple-500/10 blur-3xl"
        />
      </div>

      <!-- 内容区域 -->
      <div class="relative z-10 flex h-full flex-col">
        <!-- 标题栏 -->
        <div
          class="flex items-center justify-between border-b border-purple-500/20 px-6 py-4"
        >
          <h2
            class="bg-gradient-to-r from-purple-400 to-purple-300 bg-clip-text text-xl font-bold text-transparent"
          >
            课程包升级
          </h2>

          <button
            class="flex items-center justify-center rounded-lg p-2 text-gray-400 transition-colors hover:bg-purple-500/10 hover:text-gray-300"
            @click="handleClose"
          >
            <UIcon name="i-heroicons-x-mark" class="size-5" />
          </button>
        </div>

        <!-- 主要内容区域 -->
        <div class="custom-scrollbar flex-1">
          <div class="px-6 pb-24 pt-4">
            <!-- 加载状态 -->
            <div
              v-if="isLoading"
              class="flex flex-col items-center justify-center py-12"
            >
              <div
                class="mb-4 flex size-16 items-center justify-center rounded-full bg-slate-800/70"
              >
                <UIcon
                  name="i-heroicons-arrow-path"
                  class="size-8 animate-spin text-slate-400"
                />
              </div>
              <p class="text-slate-400">
                正在检查课程升级状态...
              </p>
            </div>

            <div
              v-else-if="!isUpgrading && !isUpgradeComplete"
              class="space-y-6"
            >
              <!-- 状态说明 -->
              <div
                class="flex flex-col items-center justify-center text-center"
              >
                <div
                  class="mb-4 flex size-16 items-center justify-center rounded-full bg-purple-500/20"
                >
                  <UIcon
                    name="i-heroicons-arrow-path"
                    class="size-8 text-purple-400"
                  />
                </div>
                <h3 class="mb-2 text-lg font-medium text-slate-200">
                  课程包升级助手
                </h3>
                <p class="max-w-md text-slate-400">
                  共有
                  <span class="font-medium text-purple-400">{{
                    needUpgradeCourses.length
                  }}</span>
                  个课程需要升级
                </p>
              </div>

              <!-- 课程列表 -->
              <div class="space-y-3">
                <div
                  v-for="course in allCourses"
                  :key="course.id"
                  class="rounded-lg border border-slate-700/50 bg-slate-800/50 p-4 transition-all duration-200 hover:border-slate-600/70"
                >
                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="font-medium text-slate-200">
                        {{ course.title }}
                      </h3>
                      <p class="mt-1 line-clamp-1 text-sm text-slate-400">
                        {{ course.description }}
                      </p>
                    </div>
                    <div class="flex items-center">
                      <span
                        v-if="course.needsUpgrade"
                        class="inline-flex items-center rounded-full bg-amber-500/20 px-2.5 py-1 text-xs font-medium text-amber-400 ring-1 ring-amber-500/30"
                      >
                        <div
                          class="mr-1 size-1.5 rounded-full bg-amber-400"
                        />
                        需要升级
                      </span>
                      <span
                        v-else
                        class="inline-flex items-center rounded-full bg-green-500/20 px-2.5 py-1 text-xs font-medium text-green-400 ring-1 ring-green-500/30"
                      >
                        <div
                          class="mr-1 size-1.5 rounded-full bg-green-400"
                        />
                        已是最新
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 升级进行中状态 -->
            <div v-else-if="isUpgrading" class="space-y-6">
              <div
                class="flex flex-col items-center justify-center text-center"
              >
                <div
                  class="mb-4 flex size-16 items-center justify-center rounded-full bg-purple-500/20"
                >
                  <UIcon
                    name="i-heroicons-arrow-path"
                    class="size-8 animate-spin text-purple-400"
                  />
                </div>
                <h3 class="mb-2 text-lg font-medium text-slate-200">
                  正在升级课程(不要关闭页面 请耐心等待)
                </h3>
                <p class="text-slate-400">
                  已完成: {{ upgradeProgress.completed }}/{{
                    upgradeProgress.total
                  }}
                </p>
                <p
                  v-if="currentUpgradingCourse"
                  class="mt-1 text-sm text-purple-400"
                >
                  当前: {{ currentUpgradingCourse.title }}
                </p>
              </div>

              <div class="mb-8 h-2.5 w-full rounded-full bg-slate-700/50">
                <div
                  class="h-2.5 rounded-full bg-gradient-to-r from-purple-500 to-pink-500"
                  :style="{
                    width: `${
                      (upgradeProgress.completed / upgradeProgress.total) * 100
                    }%`,
                  }"
                />
              </div>

              <div class="custom-scrollbar max-h-[40vh] space-y-3 pr-2">
                <div
                  v-for="course in allCourses"
                  :key="course.id"
                  class="rounded-lg border p-4 transition-all duration-200" :class="[
                    currentUpgradingCourse
                      && currentUpgradingCourse.id === course.id
                      ? 'border-purple-500/50 bg-purple-900/20'
                      : course.upgraded
                        ? 'border-green-500/30 bg-slate-800/50'
                        : course.upgradeError
                          ? 'border-red-500/30 bg-slate-800/50'
                          : 'border-slate-700/50 bg-slate-800/50',
                  ]"
                >
                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="font-medium text-slate-200">
                        {{ course.title }}
                      </h3>
                      <p class="mt-1 line-clamp-1 text-sm text-slate-400">
                        {{ course.description }}
                      </p>
                    </div>
                    <div class="flex items-center">
                      <span
                        v-if="
                          currentUpgradingCourse
                            && currentUpgradingCourse.id === course.id
                        "
                        class="inline-flex items-center rounded-full bg-purple-500/20 px-2.5 py-1 text-xs font-medium text-purple-400 ring-1 ring-purple-500/30"
                      >
                        <UIcon
                          name="i-heroicons-arrow-path"
                          class="mr-1 size-3 animate-spin"
                        />
                        升级中
                      </span>
                      <span
                        v-else-if="course.upgraded"
                        class="inline-flex items-center rounded-full bg-green-500/20 px-2.5 py-1 text-xs font-medium text-green-400 ring-1 ring-green-500/30"
                      >
                        <UIcon name="i-heroicons-check" class="mr-1 size-3" />
                        已升级
                      </span>
                      <span
                        v-else-if="course.upgradeError"
                        class="inline-flex items-center rounded-full bg-red-500/20 px-2.5 py-1 text-xs font-medium text-red-400 ring-1 ring-red-500/30"
                      >
                        <UIcon name="i-heroicons-x-mark" class="mr-1 size-3" />
                        升级失败
                      </span>
                      <span
                        v-else-if="course.needsUpgrade"
                        class="inline-flex items-center rounded-full bg-amber-500/20 px-2.5 py-1 text-xs font-medium text-amber-400 ring-1 ring-amber-500/30"
                      >
                        <div
                          class="mr-1 size-1.5 rounded-full bg-amber-400"
                        />
                        等待升级
                      </span>
                      <span
                        v-else
                        class="inline-flex items-center rounded-full bg-slate-500/20 px-2.5 py-1 text-xs font-medium text-slate-400 ring-1 ring-slate-500/30"
                      >
                        <div
                          class="mr-1 size-1.5 rounded-full bg-slate-400"
                        />
                        无需升级
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 升级完成状态 -->
            <div v-else-if="isUpgradeComplete" class="space-y-6">
              <div
                class="flex flex-col items-center justify-center text-center"
              >
                <div
                  class="mb-4 flex size-16 items-center justify-center rounded-full bg-green-500/20"
                >
                  <UIcon
                    name="i-heroicons-check-circle"
                    class="size-8 text-green-400"
                  />
                </div>
                <h3 class="mb-2 text-lg font-medium text-slate-200">
                  升级完成
                </h3>
                <p class="text-slate-400">
                  所有课程升级已完成，共处理 {{ upgradeProgress.total }} 个课程
                </p>
              </div>

              <div class="mb-8 grid grid-cols-4 gap-4">
                <div
                  class="flex flex-col items-center justify-center rounded-lg border border-green-500/30 bg-slate-800/70 p-4"
                >
                  <div class="mb-1 text-sm text-slate-400">
                    成功
                  </div>
                  <div class="text-2xl font-bold text-green-400">
                    {{ upgradeStats.success }}
                    <span class="text-sm font-normal">个句子</span>
                  </div>
                </div>
                <div
                  class="flex flex-col items-center justify-center rounded-lg border border-amber-500/30 bg-slate-800/70 p-4"
                >
                  <div class="mb-1 text-sm text-slate-400">
                    跳过
                  </div>
                  <div class="text-2xl font-bold text-amber-400">
                    {{ upgradeStats.skipped }}
                    <span class="text-sm font-normal">个句子</span>
                  </div>
                </div>
                <div
                  class="flex flex-col items-center justify-center rounded-lg border border-purple-500/30 bg-slate-800/70 p-4"
                >
                  <div class="mb-1 text-sm text-slate-400">
                    单词
                  </div>
                  <div class="text-2xl font-bold text-purple-400">
                    {{ upgradeStats.words }}
                    <span class="text-sm font-normal">个</span>
                  </div>
                </div>
                <div
                  class="flex flex-col items-center justify-center rounded-lg border border-red-500/30 bg-slate-800/70 p-4"
                >
                  <div class="mb-1 text-sm text-slate-400">
                    失败
                  </div>
                  <div class="text-2xl font-bold text-red-400">
                    {{ upgradeStats.failed }}
                    <span class="text-sm font-normal">个句子</span>
                  </div>
                </div>
              </div>

              <div v-if="upgradeErrors.length > 0" class="mb-6">
                <h3 class="mb-2 flex items-center font-medium text-red-400">
                  <UIcon
                    name="i-heroicons-exclamation-triangle"
                    class="mr-1 size-4"
                  />
                  错误信息
                </h3>
                <div
                  class="custom-scrollbar max-h-40 rounded-lg border border-red-500/30 bg-slate-800/70 p-3"
                >
                  <div
                    v-for="(error, index) in upgradeErrors"
                    :key="index"
                    class="mb-2 border-b border-red-500/20 p-2 last:mb-0 last:border-0"
                  >
                    <div class="font-medium text-slate-300">
                      {{ error.courseTitle }}
                    </div>
                    <div class="mt-1 text-sm text-red-400">
                      {{ error.message }}
                    </div>
                  </div>
                </div>
              </div>

              <div class="custom-scrollbar max-h-[30vh] space-y-3 pr-2">
                <div
                  v-for="course in allCourses"
                  :key="course.id"
                  class="rounded-lg border p-4 transition-all duration-200" :class="[
                    course.upgraded
                      ? 'border-green-500/30 bg-slate-800/50'
                      : course.upgradeError
                        ? 'border-red-500/30 bg-slate-800/50'
                        : 'border-slate-700/50 bg-slate-800/50',
                  ]"
                >
                  <div class="flex items-center justify-between">
                    <div>
                      <h3 class="font-medium text-slate-200">
                        {{ course.title }}
                      </h3>
                      <p class="mt-1 line-clamp-1 text-sm text-slate-400">
                        {{ course.description }}
                      </p>
                    </div>
                    <div class="flex items-center">
                      <span
                        v-if="course.upgraded"
                        class="inline-flex items-center rounded-full bg-green-500/20 px-2.5 py-1 text-xs font-medium text-green-400 ring-1 ring-green-500/30"
                      >
                        <UIcon name="i-heroicons-check" class="mr-1 size-3" />
                        已升级
                      </span>
                      <span
                        v-else-if="course.upgradeError"
                        class="inline-flex items-center rounded-full bg-red-500/20 px-2.5 py-1 text-xs font-medium text-red-400 ring-1 ring-red-500/30"
                      >
                        <UIcon name="i-heroicons-x-mark" class="mr-1 size-3" />
                        升级失败
                      </span>
                      <span
                        v-else
                        class="inline-flex items-center rounded-full bg-slate-500/20 px-2.5 py-1 text-xs font-medium text-slate-400 ring-1 ring-slate-500/30"
                      >
                        <div
                          class="mr-1 size-1.5 rounded-full bg-slate-400"
                        />
                        无需升级
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部固定按钮区域 -->
        <div
          v-if="
            (!isLoading && !isUpgrading && !isUpgradeComplete)
              || isUpgradeComplete
          "
          class="absolute inset-x-0 bottom-0 flex justify-center border-t border-purple-500/20 bg-gray-900/95 px-6 py-4 backdrop-blur-xl"
        >
          <!-- 未开始升级状态的按钮 -->
          <template v-if="!isLoading && !isUpgrading && !isUpgradeComplete">
            <div v-if="needUpgradeCourses.length > 0">
              <button
                class="inline-flex items-center justify-center rounded-lg bg-gradient-to-br from-purple-500/90 to-pink-500/90 px-6 py-3 text-sm font-medium text-white shadow-lg shadow-purple-500/20 transition-all duration-300 hover:-translate-y-px hover:from-purple-600 hover:to-pink-600 hover:shadow-purple-500/30 active:scale-[0.98]"
                @click="startUpgrade"
              >
                <UIcon name="i-heroicons-arrow-path" class="mr-2 size-5" />
                开始升级全部课程
              </button>
            </div>
            <div v-else>
              <div
                class="inline-flex items-center justify-center rounded-lg bg-green-500/20 px-6 py-3 text-sm font-medium text-green-400 ring-1 ring-green-500/30"
              >
                <UIcon name="i-heroicons-check-circle" class="mr-2 size-5" />
                所有课程均为最新版本
              </div>
            </div>
          </template>

          <!-- 升级完成状态的按钮 -->
          <template v-if="isUpgradeComplete">
            <button
              class="inline-flex items-center justify-center rounded-lg bg-gradient-to-br from-purple-500/90 to-purple-600/90 px-6 py-3 text-sm font-medium text-white shadow-lg shadow-purple-500/20 transition-all duration-300 hover:-translate-y-px hover:from-purple-600 hover:to-purple-700 hover:shadow-purple-500/30 active:scale-[0.98]"
              @click="handleClose"
            >
              <UIcon name="i-heroicons-check" class="mr-2 size-5" />
              完成
            </button>
          </template>
        </div>
      </div>
    </div>
  </UModal>
</template>

<style scoped>
.custom-scrollbar {
  @apply overflow-y-auto;
  scrollbar-width: thin;
  scrollbar-color: theme('colors.gray.600') transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  @apply w-1.5;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  @apply bg-gray-600 rounded;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}
</style>
