<script setup lang="ts">
import type { CoursePackWithCourses } from '@julebu/shared'
import { CourseType } from '@julebu/shared'
import { useLoadingBar } from 'naive-ui'
import { toast } from 'vue-sonner'
import Dialog from '~/components/common/Dialog.vue'
import { useCoursePackStore } from '~/stores/coursePack'
import { formatYearMonthDay } from '~/utils/date'

const props = defineProps<{
  course: CoursePackWithCourses['courses'][number]
  draggable?: boolean
}>()
const loadingBar = useLoadingBar()
const modal = useModal()
const coursePackStore = useCoursePackStore()

function handleViewCourse() {
  // 如果是音乐课程，跳转到字幕编辑器
  if (props.course.type === CourseType.music) {
    navigateTo(`/dashboard/course-packs/subtitle-editor?coursePackId=${coursePackStore.currentCoursePack?.id}&courseId=${props.course.id}`)
    return
  }

  // 常规课程保持原有跳转逻辑
  navigateTo(
    `/dashboard/course-packs/${coursePackStore.currentCoursePack?.id}/${props.course.id}`,
  )
}

function handleDelete() {
  modal.open(Dialog, {
    title: '删除课程',
    content: '确定要删除这个课程吗？删除后将无法恢复。',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      if (props.course.gameId) {
        toast.warning('需要先下架再删除哦')
        return
      }

      loadingBar.start()
      await coursePackStore.deleteCourse(props.course.id)
      loadingBar.finish()
    },
  })
}
</script>

<template>
  <div
    class="group relative flex cursor-pointer items-start gap-6 rounded-xl border border-slate-700/50 bg-slate-800/30 p-5 backdrop-blur-sm transition-all hover:translate-y-[-2px] hover:border-slate-600/50 hover:bg-slate-700/40 hover:shadow-lg"

    @click="handleViewCourse"
  >
    <!-- 添加拖拽把手 -->
    <div
      class="drag-handle flex items-center self-stretch text-slate-500 hover:text-slate-400" :class="[
        { 'cursor-move': draggable, 'cursor-not-allowed': !draggable },
      ]"
      @click.stop
    >
      <UIcon name="i-heroicons-bars-3" class="size-5" />
    </div>

    <!-- 课程信息 -->
    <div class="flex h-[100px] flex-1 flex-col justify-between">
      <div class="flex items-center gap-3">
        <h2
          class="text-lg font-medium text-white transition-colors group-hover:text-purple-400"
        >
          {{ course.title }}
        </h2>
        <CourseTypeBadge :types="[course.type]" />
        <span
          class="rounded-full px-3 py-1 text-xs font-medium transition-colors" :class="[
            course.gameId
              ? 'bg-green-500/15 text-green-400 group-hover:bg-green-500/20'
              : 'bg-amber-500/15 text-amber-400 group-hover:bg-amber-500/20',
          ]"
        >
          <div
            v-if="course.gameId"
            class="flex items-center gap-1.5"
          >
            <div class="size-2 animate-pulse rounded-full bg-green-400" />
            已发布
          </div>
          <div v-else class="flex items-center gap-1.5">
            <div class="size-2 rounded-full bg-amber-400" />
            未发布
          </div>
        </span>
      </div>
      <p
        class="text-sm text-slate-400 group-hover:relative"
        :title="course.description"
      >
        {{
          course.description?.length > 100
            ? `${course.description.slice(0, 100)}...`
            : course.description
        }}
      </p>
      <div class="flex items-center justify-start gap-4">
        <div class="flex items-center gap-1 text-sm text-slate-500">
          <UIcon name="ph:clock" class="size-4" />
          <span>创建于 {{ formatYearMonthDay(course.createdAt) }}</span>
        </div>
        <div class="flex items-center gap-1 text-sm text-slate-500">
          <UIcon name="ph:clock-clockwise" class="size-4" />
          <span>更新于 {{ formatYearMonthDay(course.updatedAt) }}</span>
        </div>
      </div>
    </div>

    <div class="ml-auto flex shrink-0 items-center gap-2 self-stretch">
      <button
        class="relative z-10 rounded p-2 text-red-400 hover:bg-red-400/10"
        @click.stop="handleDelete"
      >
        <UIcon name="ph:trash" class="size-5" />
      </button>
    </div>
  </div>
</template>
