<script setup lang="ts">
import type { CoursePack } from '@julebu/shared'
import { useLoadingBar } from 'naive-ui'
import { toast } from 'vue-sonner'
import Dialog from '~/components/common/Dialog.vue'
import { useCoursePacksStore } from '~/stores/coursePacks'
import { formatYearMonthDay } from '~/utils/date'

const props = defineProps<{
  pack: CoursePack
}>()
const loadingBar = useLoadingBar()
const modal = useModal()
const coursePacksStore = useCoursePacksStore()

async function handlePin() {
  await coursePacksStore.pinCoursePack(props.pack.id)
}

async function handleUnpin() {
  await coursePacksStore.unpinCoursePack(props.pack.id)
}

function handleViewCourse() {
  navigateTo(`/dashboard/course-packs/${props.pack.id}`)
}

function handleDelete() {
  modal.open(Dialog, {
    title: '删除课程包',
    content: '真的要和这个课程包说再见吗? 删除后它就再也回不来了哦~',
    showCancel: true,
    showConfirm: true,
    async onConfirm() {
      if (props.pack.gameId) {
        toast.warning('需要先下架 在删除哦')
        return
      }

      loadingBar.start()
      await coursePacksStore.deleteCoursePack(props.pack.id)
      loadingBar.finish()
    },
  })
}
</script>

<template>
  <div
    class="group relative flex cursor-pointer items-start gap-6 rounded-xl border border-slate-700/50 bg-slate-800/30 p-5 backdrop-blur-sm transition-all hover:translate-y-[-2px] hover:border-slate-600/50 hover:bg-slate-700/40 hover:shadow-lg"

    @click="handleViewCourse"
  >
    <!-- 课程包图片 -->
    <div class="relative h-[90px] w-[160px] overflow-hidden rounded-lg">
      <img
        v-if="pack.cover"
        :alt="pack.title"
        class="size-full object-cover transition-transform group-hover:scale-110"
        :src="pack.cover"
        style="aspect-ratio: 16/9; object-fit: cover"
      >
    </div>

    <!-- 课程包信息 -->
    <div class="flex h-[100px] flex-1 flex-col justify-between">
      <div class="flex items-center gap-3">
        <h2
          class="text-lg font-medium text-white transition-colors group-hover:text-purple-400"
        >
          {{ pack.title }}
        </h2>
        <span
          class="rounded-full px-3 py-1 text-xs font-medium transition-colors" :class="[
            pack.gameId
              ? 'bg-green-500/15 text-green-400 group-hover:bg-green-500/20'
              : 'bg-amber-500/15 text-amber-400 group-hover:bg-amber-500/20',
          ]"
        >
          <div v-if="pack.gameId" class="flex items-center gap-1.5">
            <div class="size-2 animate-pulse rounded-full bg-green-400" />
            已发布
          </div>
          <div v-else class="flex items-center gap-1.5">
            <div class="size-2 rounded-full bg-amber-400" />
            未发布
          </div>
        </span>
      </div>
      <p
        class="text-sm text-slate-400 group-hover:relative"
        :title="pack.description"
      >
        {{ pack.description }}
      </p>
      <div class="flex items-center justify-start gap-4">
        <div class="flex items-center gap-1 text-sm text-slate-500">
          <UIcon name="ph:clock" class="size-4" />
          <span>创建于 {{ formatYearMonthDay(pack.createdAt) }}</span>
        </div>
        <div class="flex items-center gap-1 text-sm text-slate-500">
          <UIcon name="ph:clock-clockwise" class="size-4" />
          <span>更新于 {{ formatYearMonthDay(pack.updatedAt) }}</span>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="ml-auto flex h-full shrink-0 items-center gap-2">
      <button
        v-if="pack.isPinned"
        class="relative z-10 rounded p-2 text-purple-400 hover:bg-purple-400/10"
        @click.stop="handleUnpin"
      >
        <UIcon name="ph:push-pin-fill" class="size-5" />
      </button>
      <button
        v-else
        class="relative z-10 rounded p-2 text-slate-400 hover:bg-slate-700 hover:text-white"
        @click.stop="handlePin"
      >
        <UIcon name="ph:push-pin-light" class="size-5" />
      </button>

      <button
        class="relative z-10 rounded p-2 text-red-400 hover:bg-red-400/10"
        @click.stop="handleDelete"
      >
        <UIcon name="ph:trash" class="size-5" />
      </button>
    </div>
  </div>
</template>
