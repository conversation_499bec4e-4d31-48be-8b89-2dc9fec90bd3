<script setup lang="ts">
import type { FormError } from '#ui/types'
import { useLoadingBar } from 'naive-ui'
import { toast } from 'vue-sonner'
import { z } from 'zod'
import CascadeSelect from '~/components/ui/CascadeSelect.vue'
import { useCategoriesStore } from '~/stores/categories'
import { useCoursePacksStore } from '~/stores/coursePacks'
import { useCourseCover } from './useCourseCover'
import { useDefaultCover } from './useDefaultCover'

const props = defineProps<{
  modelValue: boolean
}>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()
const coursePacksStore = useCoursePacksStore()
const categoriesStore = useCategoriesStore()
const loadingBar = useLoadingBar()
const isLoading = ref(false)

const { previewImage, handleImageSelect, clearPreview, uploadPreviewImage }
  = useCourseCover()

const {
  defaultCoverPath,
  selectedIndex,
  images,
  selectImage,
  resetSelectImage,
} = useDefaultCover()

const state = reactive({
  title: '',
  description: '',
})

const selectedCategoryId = ref<string>('')

// 处理分类选择变化
function handleCategoryChange(value: string | null) {
  selectedCategoryId.value = value || ''
}

// 获取分类列表
onMounted(async () => {
  try {
    await categoriesStore.fetchCategories()
  }
  catch (error) {
    console.error('获取分类失败:', error)
  }
})

// 定义Zod schema
const zodSchema = z.object({
  title: z.string().min(2, '标题至少需要2个字符'),
  description: z.string().min(2, '描述至少需要2个字符'),
})

// 使用Zod进行验证，但通过validate函数与UForm集成
function validate(state: { title: string, description: string }): FormError[] {
  try {
    // 尝试验证数据
    zodSchema.parse(state)
    return [] // 验证通过，返回空错误数组
  }
  catch (error) {
    if (error instanceof z.ZodError) {
      // 将Zod错误转换为UForm期望的格式
      return error.errors.map(err => ({
        path: err.path.join('.'),
        message: err.message,
      }))
    }
    return [] // 其他类型错误，返回空数组
  }
}

async function onSubmit() {
  if (!previewImage.value && !defaultCoverPath.value) {
    toast.error('需要上传封面哦')
    return
  }

  try {
    loadingBar.start()
    isLoading.value = true

    const cover = previewImage.value
      ? await uploadPreviewImage()
      : defaultCoverPath.value

    await coursePacksStore.createCoursePack(
      state.title,
      state.description,
      cover!,
      selectedCategoryId.value || undefined,
    )
    await delay(300)
    emit('update:modelValue', false)
  }
  catch (error) {
    console.error('表单提交错误:', error)
    toast.error('创建失败，请稍后重试')
  }
  finally {
    isLoading.value = false
    loadingBar.finish()
  }
}

watch(
  () => props.modelValue,
  async (val) => {
    if (val) {
      selectImage(0)
    }
    else {
      onClose()
    }
  },
)

function onClose() {
  clearForm()
  resetSelectImage()
  clearPreview()
  emit('update:modelValue', false)
}

function clearForm() {
  state.title = ''
  state.description = ''
  selectedCategoryId.value = ''
}

// 处理图片选择
async function handleUploadChange(event: Event) {
  const input = event.target as HTMLInputElement
  if (!input.files?.length)
    return

  const file = input.files[0]

  try {
    // 如果已经选择了默认封面，先重置
    if (defaultCoverPath.value) {
      resetSelectImage()
    }

    await handleImageSelect(file)
    input.value = ''
  }
  catch (error: unknown) {
    if (error instanceof Error) {
      toast.error(error.message)
    }
    else {
      toast.error('图片选择失败')
    }
  }
}

function handleDefaultImageSelect(index: number) {
  if (previewImage.value) {
    clearPreview()
  }
  selectImage(index)
}

function handleClearPreview() {
  handleDefaultImageSelect(0)
}
</script>

<template>
  <div>
    <UModal
      :model-value="modelValue"
      :ui="{ width: 'w-full sm:max-w-lg' }"
      @update:model-value="$emit('update:modelValue', $event)"
    >
      <div
        class="relative overflow-hidden rounded-2xl border border-purple-500/20 bg-gray-900/95 backdrop-blur-xl"
      >
        <div class="absolute inset-0">
          <div
            class="absolute -right-32 -top-32 size-64 rounded-full bg-purple-500/10 blur-3xl"
          />
          <div
            class="absolute -bottom-32 -left-32 size-64 rounded-full bg-blue-500/10 blur-3xl"
          />
        </div>

        <div class="relative z-10 p-8">
          <h2
            class="mb-6 bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-2xl font-bold text-transparent"
          >
            创建课程包
          </h2>

          <UForm
            :validate="validate"
            :state="state"
            class="space-y-6"
            @submit="onSubmit"
          >
            <UFormGroup label="标题" name="title" class="text-gray-300">
              <UInput
                v-model="state.title"
                placeholder="输入标题"
                class="border-purple-500/20 bg-gray-800/50 focus:border-purple-500/50"
              />
            </UFormGroup>

            <UFormGroup label="描述" name="description">
              <UTextarea
                v-model="state.description"
                placeholder="输入详情..."
              />
            </UFormGroup>

            <!-- 级联分类选择 -->
            <UFormGroup label="分类" class="text-gray-300">
              <CascadeSelect
                v-model="selectedCategoryId"
                :options="[...categoriesStore.categories]"
                :loading="categoriesStore.isLoading"
                placeholder="选择分类（可选）"
                key-field="id"
                label-field="label"
                parent-field="parentId"
                description-field="description"
                @change="handleCategoryChange"
              />
            </UFormGroup>

            <div class="mt-6">
              <!-- 封面选择区域 -->
              <UFormGroup label="封面" class="text-gray-300">
                <div v-if="!previewImage" class="grid grid-cols-3 gap-4">
                  <label
                    v-for="(image, index) in images"
                    :key="index"
                    class="group relative cursor-pointer"
                    @click="handleDefaultImageSelect(index)"
                  >
                    <img
                      :src="image.src"
                      :alt="`默认封面${index + 1}`"
                      class="h-auto w-full rounded-lg border-2 transition-all duration-300"
                      :class="
                        selectedIndex === index
                          ? 'border-purple-500'
                          : 'border-transparent group-hover:border-purple-500/50'
                      "
                    >
                    <div
                      v-if="selectedIndex === index"
                      class="absolute inset-0 flex items-center justify-center rounded-lg bg-black/50 backdrop-blur-sm"
                    >
                      <Icon
                        name="i-heroicons-check-20-solid"
                        class="text-purple-400"
                      />
                    </div>
                  </label>

                  <label
                    class="flex cursor-pointer items-center justify-center rounded-lg border-2 border-dashed border-purple-500/30 transition-all duration-300 hover:border-purple-500/70 hover:bg-purple-500/10"
                  >
                    <input
                      type="file"
                      accept="image/*"
                      class="sr-only"
                      @change="handleUploadChange"
                    >
                    <div class="text-center">
                      <Icon
                        name="i-mingcute-folder-upload-line"
                        class="text-4xl text-purple-400"
                      />
                      <span class="block text-sm text-gray-400">上传封面</span>
                    </div>
                  </label>
                </div>

                <div v-if="previewImage" class="group relative">
                  <img
                    :src="previewImage.previewUrl"
                    class="aspect-video h-48 w-full rounded-lg border-2 border-purple-500/20 bg-gray-800 object-contain"
                  >
                  <div
                    class="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 backdrop-blur-sm transition-all duration-300 group-hover:opacity-100"
                  >
                    <UButton
                      icon="i-heroicons-trash-20-solid"
                      variant="solid"
                      color="red"
                      size="sm"
                      class="rounded-lg shadow-lg"
                      @click="handleClearPreview"
                    />
                  </div>
                </div>

                <div class="mt-2 text-sm text-gray-500">
                  提示：封面图必须是 16:9 的格式，size 小于 1mb
                </div>
              </UFormGroup>

              <div class="mt-8 flex justify-end space-x-4">
                <UButton
                  color="gray"
                  class="px-8 transition-all duration-300 hover:scale-105"
                  @click="onClose"
                >
                  取消
                </UButton>
                <UButton
                  type="submit"
                  :loading="isLoading"
                  class="bg-gradient-to-r px-8 transition-all duration-300 hover:scale-105"
                >
                  创建
                </UButton>
              </div>
            </div>
          </UForm>
        </div>
      </div>
    </UModal>
  </div>
</template>
