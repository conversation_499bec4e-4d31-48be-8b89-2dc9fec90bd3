import COS from 'cos-js-sdk-v5'
import { v4 as uuidv4 } from 'uuid'
import { fetchCOSCredentials } from '~/api/cos'

interface COSConfig {
  tmpSecretId: string
  tmpSecretKey: string
  sessionToken: string
  bucket: string
  region: string
  allowPrefix: string
  startTime: number
  expiredTime: number
}

let cosConfig: COSConfig | null = null

export interface ImageValidationOptions {
  maxSize?: number // 默认 1MB
  aspectRatio?: number // 默认 16:9
  aspectRatioTolerance?: number // 默认 0.1
}

export interface PreviewImage {
  file: File
  previewUrl: string
  key: string
}

function processFileName(key: string): string {
  const parts = key.split('/')
  const fileName = parts[parts.length - 1]
  const [_, extension] = fileName.split('.')

  const uuid = uuidv4()
  const timestamp = Date.now()
  const newFileName = `${timestamp}_${uuid}.${extension}`

  parts[parts.length - 1] = newFileName
  return parts.join('/')
}

export function useCOS() {
  let cos: COS | null = null

  // 初始化 COS 实例
  async function initCOS() {
    if (cos)
      return cos
    cosConfig = await fetchCOSCredentials()

    cos = new COS({
      getAuthorization: (_options, callback) => {
        callback({
          TmpSecretId: cosConfig!.tmpSecretId,
          TmpSecretKey: cosConfig!.tmpSecretKey,
          SecurityToken: cosConfig!.sessionToken,
          StartTime: cosConfig!.startTime,
          ExpiredTime: cosConfig!.expiredTime,
        })
      },
    })

    return cos
  }

  // 验证图片
  async function validateImage(
    file: File,
    options: ImageValidationOptions = {},
  ) {
    const {
      maxSize = 1 * 1024 * 1024,
      aspectRatio = 16 / 9,
      aspectRatioTolerance = 0.1,
    } = options

    return new Promise<void>((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        const img = new Image()
        img.onload = () => {
          const ratio = img.width / img.height

          if (Math.abs(ratio - aspectRatio) > aspectRatioTolerance) {
            reject(new Error('图片必须是 16:9 的比例'))
            return
          }

          if (file.size > maxSize) {
            reject(new Error('图片大小不能超过 1MB'))
            return
          }

          resolve()
        }
        img.onerror = () => reject(new Error('图片加载失败'))
        img.src = e.target?.result as string
      }
      reader.readAsDataURL(file)
    })
  }

  // 上传文件到 COS
  async function uploadFile(file: File, key: string) {
    const cosInstance = await initCOS()

    if (!cosConfig) {
      throw new Error('COS配置未初始化')
    }

    return new Promise<string>((resolve, reject) => {
      const filePath = processFileName(key)

      cosInstance.uploadFile(
        {
          Bucket: cosConfig!.bucket,
          Region: cosConfig!.region,
          Key: filePath,
          Body: file,
          SliceSize: 1024 * 1024,
          Headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'origin,accept,content-type',
          },
          onTaskReady: (taskId) => {
            console.log('文件上传任务创建完成：', taskId)
          },
          onProgress: (progressData) => {
            console.log('上传进度：', progressData)
          },
        },
        (err) => {
          if (err) {
            reject(err)
            return
          }
          resolve(
            `https://${cosConfig!.bucket}.cos.${cosConfig!.region}.myqcloud.com/${filePath}`,
          )
        },
      )
    })
  }

  return {
    validateImage,
    uploadFile,
  }
}
