import type { PreviewImage } from './useCOS'
import { ref } from 'vue'
import { useCOS } from './useCOS'

export function useCourseCover() {
  const previewImage = ref<PreviewImage | null>(null)
  const { validateImage, uploadFile } = useCOS()

  // 处理图片选择
  async function handleImageSelect(file: File) {
    await validateImage(file)

    const previewUrl = URL.createObjectURL(file)
    const key = `course-packs/${Date.now()}_${file.name}`

    previewImage.value = {
      file,
      previewUrl,
      key,
    }
  }

  // 清理预览
  function clearPreview() {
    if (previewImage.value?.previewUrl) {
      URL.revokeObjectURL(previewImage.value.previewUrl)
    }
    previewImage.value = null
  }

  // 上传预览图片
  async function uploadPreviewImage() {
    if (!previewImage.value)
      return ''

    const { file, key } = previewImage.value
    return uploadFile(file, key)
  }

  return {
    previewImage,
    handleImageSelect,
    clearPreview,
    uploadPreviewImage,
  }
}
