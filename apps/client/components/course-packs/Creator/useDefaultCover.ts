import { reactive, ref, watchEffect } from 'vue'

export function useDefaultCover() {
  const selectedIndex = ref<number | null>(null)
  const defaultCoverPath = ref('')

  const images = reactive([
    {
      src: 'https://earthworm-prod-1312884695.cos.ap-beijing.myqcloud.com/course-packs/1720518714550_default_1.avif',
    },
    {
      src: 'https://earthworm-prod-1312884695.cos.ap-beijing.myqcloud.com/course-packs/1720518714550_default_2.avif',
    },
  ])

  watchEffect(() => {
    defaultCoverPath.value = selectedIndex.value !== null
      ? images[selectedIndex.value].src
      : ''
  })

  function selectImage(index: number) {
    console.log('selectImage', index, selectedIndex.value)
    if (selectedIndex.value === index) {
      selectedIndex.value = null
    } else {
      selectedIndex.value = index
    }
  }

  function resetSelectImage() {
    selectedIndex.value = null
    defaultCoverPath.value = ''
  }

  return {
    defaultCoverPath,
    selectedIndex,
    images,
    selectImage,
    resetSelectImage,
  }
}
