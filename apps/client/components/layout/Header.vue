<script setup lang="ts">
import { pathToRegexp } from 'path-to-regexp' // 引入 path-to-regexp
import { useRoute } from 'vue-router'
import { useCoursePackStore } from '~/stores/coursePack'

const route = useRoute()
const { currentItem } = useSidebar()

const { coursePackDetailLinks, coursePackDetailIsLoading }
  = useCoursePackDetail()
const { courseDetailLinks, courseDetailIsLoading } = useCourseDetail()
const { subtitleEditorLinks, subtitleEditorIsLoading } = useSubtitleEditor()

// 根据路由路径处理展示逻辑
enum RouteMatch {
  COURSE_PACK_DETAIL = 'course-pack-detail',
  COURSE_DETAIL = 'course-detail',
  SUBTITLE_EDITOR = 'subtitle-editor',
  DASHBOARD = 'dashboard',
  DEFAULT = 'default',
}

const routeMatch = computed(() => {
  const path = route.path

  // 使用 path-to-regexp 定义路径模式
  const coursePacksPattern = pathToRegexp('/dashboard/course-packs/:id')
  const coursePattern = pathToRegexp(
    '/dashboard/course-packs/:packId/:courseId',
  )

  if (path === '/dashboard/course-packs/subtitle-editor') {
    return RouteMatch.SUBTITLE_EDITOR
  }
  else if (coursePattern.regexp.test(path)) {
    return RouteMatch.COURSE_DETAIL
  }
  else if (coursePacksPattern.regexp.test(path)) {
    return RouteMatch.COURSE_PACK_DETAIL
  }
  else if (
    path === '/dashboard'
    || path === '/dashboard/settings'
    || path === '/dashboard/inbox'
    || path === '/dashboard/course-packs'
    || path === '/dashboard/currency'
  ) {
    return RouteMatch.DASHBOARD
  }
  else {
    return RouteMatch.DEFAULT
  }
})

function useCoursePackDetail() {
  const coursePackStore = useCoursePackStore()
  const isLoading = ref(false)
  const links = ref([
    {
      label: '课程包',
      to: '/dashboard/course-packs',
      icon: 'ph:books-light',
    },
    {
      label: '',
      to: '',
      icon: 'ph:book-open-light',
    },
  ])

  watch(
    () => coursePackStore.currentCoursePack?.title,
    async (newTitle) => {
      isLoading.value = true
      links.value[1].label = newTitle ?? ''
      await nextTick()
      isLoading.value = false
    },
    {
      immediate: true,
    },
  )

  return {
    coursePackDetailLinks: links.value,
    coursePackDetailIsLoading: isLoading,
  }
}

// 通用的面包屑链接方法
function useBreadcrumbLinks(thirdLinkIcon: string) {
  const coursePackStore = useCoursePackStore()
  const courseStore = useCourseStore()
  const isLoading = ref(false)
  const links = ref([
    {
      label: '课程包',
      to: '/dashboard/course-packs',
      icon: 'ph:books-light',
    },
    {
      label: '',
      to: '',
      icon: 'ph:book-open-light',
    },
    {
      label: '',
      to: '',
      icon: thirdLinkIcon,
    },
  ])

  watch(
    [
      () => coursePackStore.currentCoursePack?.title,
      () => courseStore.currentCourse?.title,
    ],
    async ([packTitle, courseTitle]) => {
      isLoading.value = true

      links.value[1].label = packTitle ?? ''
      links.value[1].to = `/dashboard/course-packs/${coursePackStore.currentCoursePack?.id}`
      links.value[2].label = courseTitle ?? ''
      await nextTick()
      isLoading.value = false
    },
    { immediate: true },
  )

  return {
    links: links.value,
    isLoading,
  }
}

function useCourseDetail() {
  const { links, isLoading } = useBreadcrumbLinks('ph:bookmark-simple-light')
  return {
    courseDetailLinks: links,
    courseDetailIsLoading: isLoading,
  }
}

function useSubtitleEditor() {
  const { links, isLoading } = useBreadcrumbLinks('ph:music-note')
  return {
    subtitleEditorLinks: links,
    subtitleEditorIsLoading: isLoading,
  }
}
</script>

<template>
  <header class="h-12 border-b border-slate-700 bg-slate-800">
    <div class="flex h-full items-center justify-between px-6">
      <template v-if="routeMatch === RouteMatch.COURSE_PACK_DETAIL">
        <template v-if="coursePackDetailIsLoading">
          <div class="flex animate-pulse space-x-4">
            <div class="h-6 w-20 rounded bg-slate-700" />
            <div class="h-6 w-40 rounded bg-slate-700" />
          </div>
        </template>
        <template v-else>
          <UBreadcrumb
            :links="coursePackDetailLinks"
            :ui="{
              li: 'flex items-center gap-x-1.5 text-gray-400 dark:text-gray-400 text-base leading-6 min-w-0',
              base: 'flex items-center gap-x-1.5 group font-semibold min-w-0',
              active: 'text-white',
              inactive: 'hover:text-gray-200',
            }"
          />
        </template>
      </template>
      <template v-else-if="routeMatch === RouteMatch.COURSE_DETAIL">
        <template v-if="courseDetailIsLoading">
          <div class="flex animate-pulse space-x-4">
            <div class="h-6 w-20 rounded bg-slate-700" />
            <div class="h-6 w-40 rounded bg-slate-700" />
            <div class="h-6 w-40 rounded bg-slate-700" />
          </div>
        </template>
        <template v-else>
          <UBreadcrumb
            :links="courseDetailLinks"
            :ui="{
              li: 'flex items-center gap-x-1.5 text-gray-400 dark:text-gray-400 text-base leading-6 min-w-0',
              base: 'flex items-center gap-x-1.5 group font-semibold min-w-0',
              active: 'text-white',
              inactive: 'hover:text-gray-200',
            }"
          />
        </template>
      </template>
      <template v-else-if="routeMatch === RouteMatch.SUBTITLE_EDITOR">
        <template v-if="subtitleEditorIsLoading">
          <div class="flex animate-pulse space-x-4">
            <div class="h-6 w-20 rounded bg-slate-700" />
            <div class="h-6 w-40 rounded bg-slate-700" />
            <div class="h-6 w-40 rounded bg-slate-700" />
          </div>
        </template>
        <template v-else>
          <UBreadcrumb
            :links="subtitleEditorLinks"
            :ui="{
              li: 'flex items-center gap-x-1.5 text-gray-400 dark:text-gray-400 text-base leading-6 min-w-0',
              base: 'flex items-center gap-x-1.5 group font-semibold min-w-0',
              active: 'text-white',
              inactive: 'hover:text-gray-200',
            }"
          />
        </template>
      </template>
      <div
        v-else-if="routeMatch === RouteMatch.DASHBOARD"
        class="flex items-center space-x-4"
      >
        <h1 class="text-xl font-medium text-white">
          {{ currentItem?.label }}
        </h1>
        <div class="h-5 w-px bg-slate-700" />
        <span class="text-sm text-slate-400">{{
          currentItem?.description
        }}</span>
      </div>
    </div>
  </header>
</template>

<style scoped></style>
