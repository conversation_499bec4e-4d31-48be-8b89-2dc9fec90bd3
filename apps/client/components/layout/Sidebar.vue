<script setup lang="ts">
import { signOut } from '~/services/auth'
import { useUserStore } from '~/stores/user'

const userStore = useUserStore()
const { navItems } = useSidebar()
const isSidebarCollapsed = ref(true)
// const showButton = ref(false);

// const toggleSidebar = () => {
//   isSidebarCollapsed.value = !isSidebarCollapsed.value;
//   if (isSidebarCollapsed.value) {
//     showButton.value = false;
//   } else {
//     setTimeout(() => {
//       showButton.value = true;
//     }, 300);
//   }
// };

// 初始化时设置按钮显示状态
// onMounted(() => {
//   showButton.value = !isSidebarCollapsed.value;
// });

const userMenuItems = computed(() => [
  [
    {
      label: userStore.userInfo?.username || '',
      slot: 'account',
    },
  ],
  [
    {
      label: '退出登录',
      icon: 'i-heroicons-arrow-left-on-rectangle',
      click: () => {
        signOut()
      },
    },
  ],
])
</script>

<template>
  <aside
    id="sidebar"
    class="flex flex-col justify-between bg-slate-800 px-3 text-white transition-all duration-300 ease-in-out" :class="[
      isSidebarCollapsed ? 'w-14' : 'w-60',
    ]"
  >
    <!-- 上半部分 -->
    <div class="flex flex-col">
      <!-- Logo -->
      <div class="mb-2 flex items-center justify-between py-2.5">
        <div class="flex items-center">
          <img src="/logo.svg" alt="句乐部 Logo" class="size-8">
        </div>

        <!-- 目前没有展开的必要性 后面再说 -->
        <!-- <button
          v-show="showButton"
          class="rounded text-white hover:bg-slate-700"
          @click="toggleSidebar"
        >
          <Icon icon="ph:sidebar-light" class="h-8 w-8" />
        </button> -->
      </div>

      <!-- 主导航菜单 -->
      <nav class="flex-1">
        <UVerticalNavigation
          :links="navItems"
          :ui="{
            base: 'group relative flex w-full items-center justify-center rounded-lg p-3 text-slate-400 transition-all duration-200 hover:bg-slate-700/50 hover:text-white',
            padding: 'py-3.5',
            active:
              'bg-gradient-to-r from-purple-500/20 to-blue-500/20 text-white hover:from-purple-500/30 hover:to-blue-500/30 before:absolute before:left-[-10px] before:top-0 before:h-full before:w-1 before:rounded-r-lg before:bg-gradient-to-b before:from-purple-500 before:to-blue-500 before:shadow-[0_0_15px_rgba(168,85,247,0.5)] transition-all duration-200',
            icon: {
              base: 'h-8 w-8',
            },
            label: 'hidden',
          }"
        />
      </nav>
    </div>

    <!-- 底部控制区 -->
    <div class="mt-auto space-y-4 py-4">
      <!-- <div class="flex">
        <button
          v-if="isSidebarCollapsed"
          class="rounded text-white hover:bg-slate-700"
          @click="toggleSidebar"
        >
          <Icon icon="ph:sidebar-light" class="h-8 w-8" />
        </button>
      </div> -->

      <!-- 用户信息 -->
      <div class="h-12">
        <UDropdown
          :items="userMenuItems"
          :popper="{ placement: 'bottom-start' }"
        >
          <div
            class="flex h-full items-center justify-start rounded-md hover:bg-slate-700"
          >
            <UAvatar
              :src="userStore.userInfo?.picture || ''"
              :alt="userStore.userInfo?.username || ''"
              size="sm"
            />
            <div
              v-show="!isSidebarCollapsed"
              class="ml-3 min-w-0 transition-opacity duration-300"
              :class="{ 'opacity-0': isSidebarCollapsed }"
            >
              <div class="truncate text-xs text-slate-400">
                {{ userStore.userInfo?.username }}
              </div>
            </div>
          </div>

          <template #account="{ item }">
            <div class="flex items-center gap-2 text-left">
              <UAvatar
                :src="userStore.userInfo?.picture || ''"
                :alt="userStore.userInfo?.username || ''"
                size="sm"
                class="ring-2 ring-white/30"
              />
              <p class="truncate font-medium text-white">
                {{ item.label }}
              </p>
            </div>
          </template>

          <template #item="{ item }">
            <span class="truncate text-white">{{ item.label }}</span>
            <UIcon
              :name="item.icon"
              class="ms-auto size-4 shrink-0 text-white"
            />
          </template>
        </UDropdown>
      </div>
    </div>
  </aside>
</template>
