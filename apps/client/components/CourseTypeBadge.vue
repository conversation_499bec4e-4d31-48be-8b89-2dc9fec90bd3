<script setup lang="ts">
import { CourseType } from '@julebu/shared'

interface Props {
  types: CourseType[]
}

defineProps<Props>()

// 获取主要类型和显示文本
function getDisplayInfo(types: CourseType[]) {
  if (types.length === 0) {
    return {
      text: '暂无课程',
      bgClass: 'bg-slate-500/20',
      textClass: 'text-slate-400',
      borderClass: 'border-slate-500/30',
      icon: '📚',
    }
  }

  const uniqueTypes = [...new Set(types)]

  if (uniqueTypes.length === 1) {
    const type = uniqueTypes[0]
    switch (type) {
      case CourseType.music:
        return {
          text: '音乐课程',
          bgClass: 'bg-gradient-to-r from-purple-500/20 to-pink-500/20',
          textClass: 'text-purple-300',
          borderClass: 'border-purple-500/30',
          icon: '🎵',
        }
      case CourseType.normal:
        return {
          text: '常规课程',
          bgClass: 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20',
          textClass: 'text-blue-300',
          borderClass: 'border-blue-500/30',
          icon: '📚',
        }
      case CourseType.audio:
        return {
          text: '音频课程',
          bgClass: 'bg-gradient-to-r from-orange-500/20 to-amber-500/20',
          textClass: 'text-orange-300',
          borderClass: 'border-orange-500/30',
          icon: '🎧',
        }
      case CourseType.video:
        return {
          text: '视频课程',
          bgClass: 'bg-gradient-to-r from-green-500/20 to-emerald-500/20',
          textClass: 'text-green-300',
          borderClass: 'border-green-500/30',
          icon: '🎬',
        }
      default:
        return {
          text: '常规课程',
          bgClass: 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20',
          textClass: 'text-blue-300',
          borderClass: 'border-blue-500/30',
          icon: '📚',
        }
    }
  }

  return {
    text: '混合类型',
    bgClass: 'bg-gradient-to-r from-indigo-500/20 to-purple-500/20',
    textClass: 'text-indigo-300',
    borderClass: 'border-indigo-500/30',
    icon: '🎯',
  }
}
</script>

<template>
  <span
    class="inline-flex items-center gap-1.5 rounded-full px-3 py-1 text-xs font-medium transition-colors"
    :class="[
      getDisplayInfo(types).bgClass,
      getDisplayInfo(types).textClass,
    ]"
  >
    <span class="select-none text-xs">{{ getDisplayInfo(types).icon }}</span>
    {{ getDisplayInfo(types).text }}
  </span>
</template>
