<template>
  <div class="relative">
    <!-- 选择按钮 -->
    <button
      type="button"
      class="relative w-full cursor-pointer rounded-md border border-purple-500/20 bg-gray-800/50 py-2 pl-3 pr-10 text-left text-gray-300 shadow-sm transition-all duration-300 hover:border-purple-500/50 focus:border-purple-500 focus:outline-none focus:ring-1 focus:ring-purple-500"
      :disabled="loading"
      @click="toggleOpen"
    >
      <span class="block truncate">
        {{ displayText || placeholder }}
      </span>
      <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
        <svg class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
        </svg>
      </span>
    </button>
    
    <!-- 下拉面板 -->
    <div
      v-if="isOpen"
      class="absolute z-50 mt-1 max-h-80 w-full overflow-hidden rounded-md border border-purple-500/20 bg-gray-800 shadow-lg"
    >
      <!-- 面包屑导航 -->
      <div v-if="breadcrumb.length > 0" class="border-b border-purple-500/20 px-3 py-2">
        <div class="flex items-center gap-2">
          <button
            type="button"
            class="rounded p-1 text-gray-400 hover:bg-gray-700 hover:text-gray-300"
            @click="goBack"
          >
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div class="flex items-center gap-1 text-sm text-gray-400">
            <span
              v-for="(item, index) in breadcrumb"
              :key="item[props.keyField]"
              class="flex items-center gap-1"
            >
              <span>{{ item[props.labelField] }}</span>
              <svg
                v-if="index < breadcrumb.length - 1"
                class="h-3 w-3"
                fill="currentColor" 
                viewBox="0 0 20 20"
              >
                <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
              </svg>
            </span>
          </div>
        </div>
      </div>
      
      <!-- 选项列表 -->
      <div class="max-h-60 overflow-y-auto">
        <div
          v-for="option in currentOptions"
          :key="option[props.keyField]"
          class="flex cursor-pointer items-center justify-between px-3 py-2 text-gray-300 transition-colors hover:bg-gray-700"
          @click="selectOption(option)"
        >
          <div class="flex-1">
            <div class="font-medium">{{ option[props.labelField] }}</div>
            <div v-if="props.descriptionField && option[props.descriptionField]" class="text-sm text-gray-500">
              {{ option[props.descriptionField] }}
            </div>
          </div>
          <svg
            v-if="getChildren(option).length > 0"
            class="h-4 w-4 text-gray-400"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
          </svg>
        </div>
        
        <!-- 无选项提示 -->
        <div v-if="currentOptions.length === 0" class="px-3 py-6 text-center text-gray-500">
          {{ loading ? '加载中...' : emptyText }}
        </div>
      </div>
      
      <!-- 清除选择 -->
      <div v-if="modelValue && clearable" class="border-t border-purple-500/20 p-2">
        <button
          type="button"
          class="w-full rounded px-3 py-1 text-sm text-red-400 hover:bg-red-500/10"
          @click="clearSelection"
        >
          {{ clearText }}
        </button>
      </div>
    </div>
    
    <!-- 点击外部关闭 -->
    <div
      v-if="isOpen"
      class="fixed inset-0 z-40"
      @click="isOpen = false"
    />
  </div>
</template>

<script setup lang="ts">
interface CascadeSelectProps {
  modelValue?: any; // 选中的值
  options: any[]; // 扁平化数据数组
  placeholder?: string; // 占位文本
  loading?: boolean; // 加载状态
  clearable?: boolean; // 是否可清除
  clearText?: string; // 清除按钮文本
  emptyText?: string; // 无数据文本
  // 字段映射
  keyField?: string; // 唯一标识字段
  labelField?: string; // 显示标签字段
  parentField?: string; // 父级ID字段
  descriptionField?: string; // 描述字段
}

const props = withDefaults(defineProps<CascadeSelectProps>(), {
  placeholder: '请选择',
  loading: false,
  clearable: true,
  clearText: '清除选择',
  emptyText: '暂无数据',
  keyField: 'id',
  labelField: 'label',
  parentField: 'parentId',
  descriptionField: 'description'
});

const emit = defineEmits<{
  'update:modelValue': [value: any];
  'change': [value: any, option: any, path: any[]];
}>();

// 状态管理
const isOpen = ref(false);
const currentOptions = ref<any[]>([]);
const breadcrumb = ref<any[]>([]);

// 根选项
const rootOptions = computed(() => {
  return props.options.filter(item => !item[props.parentField]);
});

// 获取子选项
function getChildren(parentItem: any) {
  const parentId = parentItem[props.keyField];
  return props.options.filter(item => item[props.parentField] === parentId);
}

// 获取选项路径
function getItemPath(value: any) {
  const path: any[] = [];
  let current = props.options.find(item => item[props.keyField] === value);
  
  while (current) {
    path.unshift(current);
    const parentId = current[props.parentField];
    current = parentId ? props.options.find(item => item[props.keyField] === parentId) : null;
  }
  
  return path;
}

// 显示文本
const displayText = computed(() => {
  if (!props.modelValue) return '';
  const path = getItemPath(props.modelValue);
  return path.map(item => item[props.labelField]).join(' > ');
});

// 初始化
function init() {
  currentOptions.value = [...rootOptions.value];
  breadcrumb.value = [];
}

// 切换打开状态
function toggleOpen() {
  if (isOpen.value) {
    isOpen.value = false;
  } else {
    isOpen.value = true;
    init();
  }
}

// 选择选项
function selectOption(option: any) {
  const children = getChildren(option);
  
  if (children.length > 0) {
    // 有子项，进入下一层
    breadcrumb.value.push(option);
    currentOptions.value = [...children];
  } else {
    // 叶子节点，完成选择
    const value = option[props.keyField];
    const path = [...breadcrumb.value, option];
    
    emit('update:modelValue', value);
    emit('change', value, option, path);
    isOpen.value = false;
  }
}

// 返回上一层
function goBack() {
  if (breadcrumb.value.length > 0) {
    breadcrumb.value.pop();
    
    if (breadcrumb.value.length === 0) {
      currentOptions.value = [...rootOptions.value];
    } else {
      const parent = breadcrumb.value[breadcrumb.value.length - 1];
      currentOptions.value = [...getChildren(parent)];
    }
  }
}

// 清除选择
function clearSelection() {
  emit('update:modelValue', null);
  emit('change', null, null, []);
  isOpen.value = false;
}

// 监听选项变化
watch(() => props.options, () => {
  if (isOpen.value) {
    init();
  }
}, { deep: true });
</script>