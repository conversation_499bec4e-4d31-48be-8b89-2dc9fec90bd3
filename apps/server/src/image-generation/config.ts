import { config } from '@/config/config'

export interface ModelConfig {
  name: string
  costDiamondPerImage: number
  apiKey: string
  baseURL: string
  responseFormat: 'url' | 'b64_json'
}

export const doubaoSeedreamModel: ModelConfig = {
  name: 'doubao-seedream-3-0-t2i-250415',
  costDiamondPerImage: 259,
  apiKey: config.openai.huoshanApiKey,
  baseURL: config.openai.huoshanBaseUrl,
  responseFormat: 'b64_json',
}
