import type { ModelConfig } from './config'
import { TRPCError } from '@trpc/server'
import OpenAI from 'openai'
import { createGenerateImagePromptModelLLM } from '@/llm/task'
import { currencyService } from '@/routers/currency/currency.service'

const systemPrompt = `
你是一个专业的AI图像生成prompt专家。你的任务是根据用户提供的英语单词/句子及其中文含义，生成一个详细的、高质量的图像描述prompt。
  要求：
  1. 生成的图像描述必须能够视觉化地表达英语文本的含义
  2. 图像中不能包含任何文字、字母、数字或符号
  3. 描述要具体、生动，包含场景、物体、动作、颜色、光线等细节
  4. 使用中文描述，适合AI图像生成模型理解
  5. 确保描述内容积极正面，符合一般审美标准
  6. 根据用户指定的风格和情绪调整描述
  请直接输出图像描述prompt，不要包含任何解释或额外文字。请确保prompt长度不超过500字符。`

// IG -> Image Generation
export class ModelIG {
  private config: ModelConfig
  private modelLLM = createGenerateImagePromptModelLLM()
  private modelIG: OpenAI
  constructor(config: ModelConfig) {
    this.config = config
    this.modelIG = new OpenAI({
      apiKey: this.config.apiKey,
      baseURL: this.config.baseURL,
    })
  }

  // 注意这里不会检查钻石余额
  private async generateImageFromPrompt({
    englishText,
    prompt,
    size,
    userId,
  }: {
    englishText: string
    prompt: string
    size: OpenAI.Images.ImageGenerateParams['size']
    userId: string
  }) {
    const image = await this.modelIG.images.generate({
      prompt,
      model: this.config.name,
      size,
      response_format: this.config.responseFormat,
    })

    await currencyService.consumeDiamonds(
      userId,
      this.config.costDiamondPerImage,
      `生成图片: ${englishText}`,
    )

    return image
  }

  private async generateImagePrompt({
    englishText,
    chineseText,
    style,
  }: {
    englishText: string
    chineseText: string
    size: OpenAI.Images.ImageGenerateParams['size']
    style: string
  }) {
    const userPrompt = this.buildUserPrompt(englishText, chineseText, style)

    const prompt = await this.modelLLM.invoke(
      `${systemPrompt}\n\n${userPrompt}`,
      'generate_image_prompt',
    )

    return prompt
  }

  async generateImage({
    englishText,
    chineseText,
    size,
    style,
    userId,
  }: {
    englishText: string
    chineseText: string
    size: OpenAI.Images.ImageGenerateParams['size']
    style: string
    userId: string
  }) {
    const isEnough = await currencyService.checkDiamondBalance(
      userId,
      this.config.costDiamondPerImage,
    )

    if (!isEnough) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: '钻石余额不足',
      })
    }

    const prompt = await this.generateImagePrompt({
      englishText,
      chineseText,
      size,
      style,
    })
    console.log('prompt', prompt)
    console.log('size', size)

    const image = await this.generateImageFromPrompt({
      englishText,
      prompt,
      size,
      userId,
    })

    return {
      prompt,
      image,
    }
  }

  private buildUserPrompt(
    englishText: string,
    chineseText: string,
    style: string,
  ): string {
    return `
    请为以下内容生成图像描述prompt：
    英语文本：${englishText}
    中文含义：${chineseText}
    风格要求：${style}
    请生成一个详细的图像描述prompt，确保：
    - 能够准确表达"${englishText}"的含义
    - 采用${style}
    - 图像中不包含任何文字或符号
    - 描述具体生动，适合AI图像生成`
  }
}
