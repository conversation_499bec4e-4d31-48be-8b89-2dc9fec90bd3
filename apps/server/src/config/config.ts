import path from 'node:path'

import dotenv from 'dotenv'
import { projectRoot } from '@/utils/paths'

// 根据当前环境加载对应的 .env 文件
const envFile
  = process.env.NODE_ENV === 'production'
    ? '.env.prod'
    : process.env.NODE_ENV === 'test'
      ? '.env.test'
      : '.env'

dotenv.config({ path: path.resolve(projectRoot, envFile) })

export const config = {
  nodeEnv: process.env.NODE_ENV || 'development',
  port: Number.parseInt(process.env.PORT || '3400'),
  database: {
    url: process.env.DATABASE_URL || 'mysql://root:password@localhost:3306/defaultdb',
  },
  gameURL: process.env.GAME_URL || '',
  redisUrl: process.env.REDIS_URL || 'redis://localhost:6379',
  logto: {
    endpoint: process.env.LOGTO_ENDPOINT || 'http://localhost:3000',
    resources: [process.env.BACKEND_ENDPOINT || 'http://localhost:3001'],
  },
  cos: {
    secretId: process.env.COS_SECRET_ID || '',
    secretKey: process.env.COS_SECRET_KEY || '',
    bucket: process.env.COS_BUCKET || '',
    region: process.env.COS_REGION || '',
    allowPrefix: 'course-packs/*',
    durationSeconds: 1800,
    proxy: '',
  },
  youdao: {
    appKey: process.env.YOUDAO_APP_KEY || '',
    secretKey: process.env.YOUDAO_APP_SECRET || '',
    from: process.env.YOUDAO_FROM || '',
    to: process.env.YOUDAO_TO || '',
  },
  // 火山引擎 openai 模型
  openai: {
    huoshanApiKey: process.env.OPENAI_API_KEY_HUOSHAN || '',
    huoshanBaseUrl: process.env.OPENAI_BASE_URL_HUOSHAN || '',
    tencentApiKey: process.env.OPENAI_API_KEY_TENCENT || '',
    tencentBaseUrl: process.env.OPENAI_BASE_URL_TENCENT || '',
    deepseekApiKey: process.env.OPENAI_API_KEY_DEEPSEEK || '',
    deepseekBaseUrl: process.env.OPENAI_BASE_URL_DEEPSEEK || '',
    qiniuApiKey: process.env.OPENAI_API_KEY_QINIU || '',
    qiniuBaseUrl: process.env.OPENAI_BASE_URL_QINIU || '',
  },
  // NLP服务配置
  nlp: {
    apiUrl: process.env.NLP_API_URL || '',
  },
  audioService: {
    url: process.env.AUDIO_SERVICE_URL,
    secret: process.env.AUDIO_SERVICE_SECRET || '',
  },
  // 服务间通信的认证密钥 和 game server 通信
  serviceSecret: process.env.SERVICE_SECRET || '',

  s3: {
    endpoint: process.env.S3_ENDPOINT || '',
    accessKeyId: process.env.S3_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || '',
    region: process.env.S3_REGION || '',
    bucketImages: process.env.S3_BUCKET_IMAGES || '',
    bucketGame: process.env.S3_BUCKET_GAME || '',
  },
  // 管理员接口临时密钥验证
  admin: {
    secretKey: process.env.ADMIN_SECRET_KEY || 'your-admin-secret-key-here',
  },
  // 音视频字幕生成
  speech: {
    appid: process.env.SPEECH_APP_ID || '',
    accessToken: process.env.SPEECH_ACCESS_TOKEN || '',
  },
}
