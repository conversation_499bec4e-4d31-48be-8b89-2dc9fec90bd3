import { Decimal } from 'decimal.js'

// 基础常量
const VP_UNIT = 10_000 // 1 RMB = 10,000 VP (1 VP = 0.0001 RMB)
const MILLION_TOKENS = 1_000_000

// 模型定价配置（单位：元/百万tokens）
export const MODEL_PRICING = {
  // DeepSeek
  'deepseek/deepseek-chat:free': {
    name: 'DeepSeek Chat',
    costPerMillion: { prompt: 2, completion: 8 }, // 元/百万tokens
  },
  'deepseek-v3-241226': {
    name: 'DeepSeek V3',
    costPerMillion: { prompt: 2, completion: 8 }, // 元/百万tokens
  },
  // Gemini
  'google/gemini-2.0-flash-001': {
    name: 'Gemini 2.0 Flash',
    costPerMillion: { prompt: 3, completion: 12 },
  },
  // Doubao
  'doubao-1-5-lite-32k-250115': {
    name: 'Doubao 1.5 Lite',
    costPerMillion: { prompt: 1.6, completion: 6.4 },
  },
} as const

export type ModelName = keyof typeof MODEL_PRICING

// VP 消耗计算函数
export function calculateVPConsumption(
  modelName: ModelName,
  tokens: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  },
) {
  const model = MODEL_PRICING[modelName]
  if (!model)
    throw new Error(`Unsupported model: ${modelName}`)

  const promptVP = new Decimal(tokens.promptTokens)
    .div(MILLION_TOKENS)
    .mul(model.costPerMillion.prompt)
    .mul(VP_UNIT)
    .round()
    .toNumber()

  const completionVP = new Decimal(tokens.completionTokens)
    .div(MILLION_TOKENS)
    .mul(model.costPerMillion.completion)
    .mul(VP_UNIT)
    .round()
    .toNumber()

  return promptVP + completionVP
}
