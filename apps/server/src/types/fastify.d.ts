import type { MySQLPromisePool } from '@fastify/mysql'
import 'fastify'

declare module 'fastify' {
  interface FastifyInstance {
    mysql: MySQLPromisePool
  }

  interface FastifyRequest {
    user?: {
      id: string
    }
    file: () => Promise<any>
    files: () => AsyncIterableIterator<any>
  }
}

declare module '@fastify/request-context' {
  interface RequestContextData {
    userId: string
  }
}
