import type { FastifyReply, FastifyRequest } from 'fastify'
import { requestContext } from '@fastify/request-context'
import { config } from '@/config/config'
import { extractTokenFromHeader, verifyJwtToken } from '@/services/auth'

const PUBLIC_PATHS: string[] = [
  // 添加其他不需要验证的路径
]

/**
 * 管理员密钥验证中间件
 * 用于临时验证管理员接口，通过 X-Admin-Secret 头部传递密钥
 */
export async function validateAdminSecret(
  request: FastifyRequest,
  reply: FastifyReply,
) {
  const adminSecret = request.headers['x-admin-secret'] as string

  if (!adminSecret) {
    reply.code(401).send({
      message: '缺少管理员密钥，请在请求头中添加 X-Admin-Secret',
    })
    return
  }

  if (adminSecret !== config.admin.secretKey) {
    reply.code(401).send({
      message: '管理员密钥验证失败',
    })
  }

  // 验证通过，可以继续处理请求
}

/**
 * JWT 令牌验证中间件
 * 用于验证普通用户的 JWT 令牌
 */
export async function validateToken(
  request: FastifyRequest,
  reply: FastifyReply,
) {
  // 如果是公开路径，直接放行
  if (PUBLIC_PATHS.some(path => request.url.startsWith(path))) {
    return
  }

  const token = extractTokenFromHeader(request.headers.authorization || '')

  if (!token) {
    reply.code(401).send({ message: '未提供认证令牌' })
    return
  }

  try {
    const { payload, isValid } = await verifyJwtToken(token)

    if (!isValid) {
      reply.code(401).send({ message: '无效的认证令牌' })
      return
    }

    const context = {
      user: {
        id: payload!.sub,
      },
      token,
    }

    requestContext.set('userId', context.user.id)
    // 将用户ID附加到请求对象上，供后续路由处理函数使用
    request.user = context.user
  } catch {
    reply.code(401).send({ message: '令牌验证失败' })
  }
}
