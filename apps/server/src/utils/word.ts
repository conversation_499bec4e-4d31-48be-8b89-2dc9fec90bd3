export function isSingleWord(sentence: string): boolean {
  // 如果是空字符串或者只包含空格，返回false
  if (!sentence || !sentence.trim()) {
    return false
  }

  // 匹配单词的正则表达式
  // 解释：
  // ^ - 开始
  // [#@$]? - 可选的特殊字符（@、#、$）
  // [a-zA-Z]+ - 一个或多个字母
  // (?:[''-][a-zA-Z]+)* - 零个或多个由撇号或连字符连接的字母组
  // \.? - 可选的句点（用于缩写）
  // $ - 结束
  const singleWordRegex = /^[#@$]?[a-z]+(?:['\-][a-z]+)*\.?$/i

  // 移除首尾空格
  sentence = sentence.trim()

  const items = sentence.split(' ')

  const result = items.filter((item) => {
    return singleWordRegex.test(item)
  })

  return result.length === 1
}

/**
 * 计算句子中单词的数量
 * @param sentence 要计算的句子
 * @returns 句子中单词的数量
 */
export function countWords(sentence: string): number {
  // 移除首尾空格
  sentence = sentence.trim()

  const items = sentence.split(' ')

  return items.length
}
