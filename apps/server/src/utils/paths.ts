import { existsSync } from 'node:fs'
import { dirname, resolve } from 'node:path'
import { fileURLToPath } from 'node:url'

export function getDirname(importMetaUrl: string): string {
  const __filename = fileURLToPath(importMetaUrl)
  return dirname(__filename)
}

export function getProjectRoot(importMetaUrl: string): string {
  let currentDir = getDirname(importMetaUrl)
  while (!existsSync(resolve(currentDir, 'package.json'))) {
    const parentDir = dirname(currentDir)
    if (parentDir === currentDir) {
      throw new Error('无法找到项目根目录')
    }
    currentDir = parentDir
  }
  return currentDir
}

const __dirname = getDirname(import.meta.url)
const projectRoot = getProjectRoot(import.meta.url)

export { __dirname, projectRoot }
