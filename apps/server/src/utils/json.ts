export function parseJsonFromCodeBlock<T>(content: string): T | undefined {
  if (!content || typeof content !== 'string') {
    console.error('解析JSON失败: 输入内容为空或不是字符串')
    return undefined
  }

  // 尝试匹配 ```json 格式
  const codeBlockRegex = /```json\n([\s\S]*?)\n```/
  const matches = content.match(codeBlockRegex)

  if (matches && matches[1]) {
    try {
      return JSON.parse(matches[1]) as T
    } catch {
      // console.error("解析JSON代码块时出错:", error);
      // 继续尝试其他方法
    }
  }

  // 尝试直接解析整个内容
  try {
    return JSON.parse(`${content}`) as T
  } catch (error) {
    console.error('直接解析JSON时出错:', error)
  }
}
