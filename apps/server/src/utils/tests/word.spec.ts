import { describe, it, expect } from 'vitest';
import { isSingleWord } from '../word';

describe('isSingleWord', () => {
  // 测试普通单词
  it('should return true for regular single words', () => {
    expect(isSingleWord('hello')).toBe(true);
    expect(isSingleWord('world')).toBe(true);
    expect(isSingleWord('test')).toBe(true);
  });

  // 测试带撇号的缩写
  it('should return true for contractions', () => {
    expect(isSingleWord("don't")).toBe(true);
    expect(isSingleWord("I'm")).toBe(true);
    expect(isSingleWord("won't")).toBe(true);
  });

  // 测试带连字符的复合词
  it('should return true for hyphenated words', () => {
    expect(isSingleWord('self-driving')).toBe(true);
    expect(isSingleWord('well-known')).toBe(true);
    expect(isSingleWord('up-to-date')).toBe(true);
  });

  // 测试带点的缩写
  it('should return true for abbreviated words', () => {
    expect(isSingleWord('Mr.')).toBe(true);
    expect(isSingleWord('Dr.')).toBe(true);
    expect(isSingleWord('Prof.')).toBe(true);
  });

  // 测试特殊字符
  it('should return true for words with special characters', () => {
    expect(isSingleWord('@hello')).toBe(true);
    expect(isSingleWord('#hashtag')).toBe(true);
    expect(isSingleWord('$dollar')).toBe(true);
  });

  // 测试多个单词的情况
  it('should return false for multiple words', () => {
    expect(isSingleWord('hello world')).toBe(false);
    expect(isSingleWord('this is test')).toBe(false);
    expect(isSingleWord('good morning everyone')).toBe(false);
  });

  // 测试空字符串和空格
  it('should handle empty strings and spaces', () => {
    expect(isSingleWord('')).toBe(false);
    expect(isSingleWord(' ')).toBe(false);
    expect(isSingleWord('  ')).toBe(false);
  });

  it("should return true for single word with special characters", () => {
    expect(isSingleWord("sometime ,")).toBe(true);
  });
});
