import { config } from '@/config/config'

// 模型配置类型
export interface ModelConfig {
  name: string
  costPerMillion: {
    prompt: number
    completion: number
  }
  openAIApiKey: string
  baseURL: string
}

export const deepseekHuoShanModel: ModelConfig = {
  costPerMillion: { prompt: 2, completion: 8 }, // 元/百万tokens
  name: 'deepseek-v3-250324',
  openAIApiKey: config.openai.huoshanApiKey,
  baseURL: config.openai.huoshanBaseUrl,
  // qiniu
  // name:"deepseek-v3-0324",
  // openAIApiKey: config.openai.qiniuApiKey,
  // baseURL: config.openai.qiniuBaseUrl,
}

export const deepseekTencentModel: ModelConfig = {
  name: 'deepseek-v3-0324',
  costPerMillion: { prompt: 2, completion: 8 }, // 元/百万tokens
  openAIApiKey: config.openai.tencentApiKey,
  baseURL: config.openai.tencentBaseUrl,
}

export const deepseekOfficialModel: ModelConfig = {
  name: 'deepseek-chat',
  costPerMillion: { prompt: 2, completion: 8 }, // 元/百万tokens
  openAIApiKey: config.openai.deepseekApiKey,
  baseURL: config.openai.deepseekBaseUrl,
}

export const doubaoProModel: ModelConfig = {
  name: 'doubao-1-5-pro-32k-250115',
  costPerMillion: { prompt: 8, completion: 2 }, // 元/百万tokens
  openAIApiKey: config.openai.huoshanApiKey,
  baseURL: config.openai.huoshanBaseUrl,
}

export const doubaoLiteModel: ModelConfig = {
  name: 'doubao-1-5-lite-32k-250115',
  costPerMillion: { prompt: 8, completion: 2 }, // 元/百万tokens
  openAIApiKey: config.openai.huoshanApiKey,
  baseURL: config.openai.huoshanBaseUrl,
}
