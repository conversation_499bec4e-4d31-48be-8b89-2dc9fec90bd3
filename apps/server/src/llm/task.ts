// 这里定义任务使用的模型
// 不同任务使用不同的模型，不同的模型有不同的参数
import {
  deepseekHuoShanModel,
  doubaoLiteModel,
} from './config'
import { ModelLLM } from './modelLLM'

// 任务类型
export type TaskType
  = | 'split_sentence'
    | 'split_chunk'
    | 'translate_content'
    | 'translate_chunk_combine'
    | 'generate_learning_content'
    | 'generate_word_story'
    | 'format_input'
    | 'analyze_sentence'
    | 'query_phonetic'
    | 'generate_image_prompt'

// 拆分句子的模型
export function createSplitSentenceModelLLM() {
  return new ModelLLM(deepseekHuoShanModel, 0.4)
}

// 解析句子生成词性的模型
export function createAnalyzeSentenceModelLLM() {
  return new ModelLLM(deepseekHuoShanModel, 0)
}

// 翻译模型
export function createTranslateModelLLM() {
  return new ModelLLM(deepseekHuoShanModel, 0.4)
}

// 生成学习内容模型
export function createGenerateLearningContentModelLLM() {
  return new ModelLLM(
    deepseekHuoShanModel,
    0.5,
  )
}

// 生成单词故事模型
export function createGenerateWordStoryModelLLM() {
  return new ModelLLM(deepseekHuoShanModel, 0.7)
}

// 格式化用户输入的内容模型
export function createFormatInputModelLLM() {
  return new ModelLLM(doubaoLiteModel, 0)
}

// 查询音标模型
export function createQueryPhoneticModelLLM() {
  return new ModelLLM(doubaoLiteModel, 0)
}

export function createGenerateImagePromptModelLLM() {
  return new ModelLLM(deepseekHuoShanModel, 0.4)
}
