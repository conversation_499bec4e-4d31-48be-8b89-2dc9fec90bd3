import type { LLMResult } from '@langchain/core/outputs'
import type { ModelConfig } from './config'
import type { TaskType } from './task'
import type { NewTaskTokenUsage } from '@/db/schema'
import type { TokenUsage } from '@/routers/currency/types'
import { ChatOpenAI } from '@langchain/openai'
import { getDB } from '@/db'
import { taskTokenUsage } from '@/db/schema'

// 定义元数据类型
export type ModelMetadata = Record<string, any>

export class ModelLLM {
  private config: ModelConfig
  private model: ChatOpenAI

  constructor(config: ModelConfig, temperature: number = 0.4) {
    this.config = config
    this.model = new ChatOpenAI({
      temperature,
      modelName: this.config.name,
      openAIApiKey: this.config.openAIApiKey,
      configuration: {
        baseURL: this.config.baseURL,
      },
    })
  }

  /**
   * 调用模型并跟踪 token 使用情况
   * @param prompt 提示词
   * @param taskType 任务类型
   * @param metadata 可选的元数据，用于在回调中传递额外信息
   * @returns 模型响应
   */
  async invoke(
    prompt: string,
    taskType: TaskType,
    metadata?: ModelMetadata,
  ): Promise<string> {
    let currentTokenUsage: TokenUsage = {
      promptTokens: 0,
      completionTokens: 0,
      totalTokens: 0,
    }

    // 创建一个临时的回调处理器来捕获 token 使用情况
    const tempCallbacks = [
      {
        handleLLMEnd: async (output: LLMResult) => {
          if (output.llmOutput?.tokenUsage) {
            currentTokenUsage = {
              promptTokens: output.llmOutput.tokenUsage.promptTokens,
              completionTokens: output.llmOutput.tokenUsage.completionTokens,
              totalTokens: output.llmOutput.tokenUsage.totalTokens,
            }

            // 直接记录 token 使用情况到数据库
            await this.recordTaskTokenUsage(
              taskType,
              currentTokenUsage,
              metadata,
            )
          }
        },
      },
    ]

    // 使用临时回调调用模型
    const response = await this.model.invoke(prompt, {
      callbacks: tempCallbacks,
    })

    // 将 LangChain 的响应转换为字符串
    return response.content.toString()
  }

  /**
   * 记录任务级别的 token 使用情况
   * @param taskType 任务类型
   * @param tokenUsage token 使用情况
   * @param metadata 元数据
   */
  async recordTaskTokenUsage(
    taskType: TaskType,
    tokenUsage: TokenUsage,
    metadata?: Record<string, any>,
  ): Promise<void> {
    // 计算当前年月
    const now = new Date()
    const year = now.getFullYear()
    const month = now.getMonth() + 1

    // 获取数据库实例
    const db = getDB()

    // 创建新记录
    const newRecord: NewTaskTokenUsage = {
      taskType,
      model: this.config.name,
      promptTokens: tokenUsage.promptTokens,
      completionTokens: tokenUsage.completionTokens,
      totalTokens: tokenUsage.totalTokens,
      metadata,
      year,
      month,
    }

    // 插入记录到数据库
    await db.insert(taskTokenUsage).values(newRecord)
  }

  getModelName(): string {
    return this.config.name
  }

  getConfig(): ModelConfig {
    return this.config
  }
}
