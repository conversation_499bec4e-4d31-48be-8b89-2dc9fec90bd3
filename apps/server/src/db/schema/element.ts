import { createId } from '@paralleldrive/cuid2'
import { relations, sql } from 'drizzle-orm'
import {
  index,
  int,
  mysqlTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/mysql-core'
import { image } from './image'
import { sentence } from './sentence'

export const element = mysqlTable(
  'elements',
  {
    id: varchar('id', { length: 128 })
      .primaryKey()
      .$defaultFn(() => createId()),
    content: text('content').notNull(),
    imageId: varchar('image_id', { length: 128 })
      .default(sql`NULL`)
      .references(() => image.id, {
        onDelete: 'set null',
      }),
    english: text('english').notNull().default(''),
    chinese: text('chinese').notNull().default(''),
    phonetic: text('phonetic').notNull().default(''),
    type: varchar('type', { length: 50 }).notNull().default(''),
    position: int('position').notNull().default(0),
    gameId: varchar('game_id', { length: 128 }).notNull().default(''), // 记录发布后的 id 可以在更新课程包的时候快速查找
    sentenceId: varchar('sentence_id', { length: 128 })
      .notNull()
      .references(() => sentence.id),
    createdAt: timestamp('created_at', {
      mode: 'string',
    })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp('updated_at', {
      mode: 'string',
    })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`)
      .$onUpdate(() => sql`CURRENT_TIMESTAMP`),
  },
  table => ({
    sentenceIdIdx: index('sentence_id_idx').on(table.sentenceId),
  }),
)

export const elementRelations = relations(element, ({ one }) => ({
  sentence: one(sentence, {
    fields: [element.sentenceId],
    references: [sentence.id],
  }),
  image: one(image, {
    fields: [element.imageId],
    references: [image.id],
  }),
}))
