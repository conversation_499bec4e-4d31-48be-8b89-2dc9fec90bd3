import { createId } from '@paralleldrive/cuid2'
import { relations, sql } from 'drizzle-orm'
import {
  index,
  mysqlTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/mysql-core'
import { image } from './image'

export const textImageLink = mysqlTable(
  'text_image_links',
  {
    id: varchar('id', { length: 128 })
      .primaryKey()
      .$defaultFn(() => createId()),
    textContent: text('text_content').notNull(),
    imageId: varchar('image_id', { length: 128 })
      .notNull()
      .references(() => image.id, {
        onDelete: 'cascade',
      }),
    // 目前只有 image 后续可能会有external link
    linkType: varchar('link_type', { length: 50 }).notNull(),
    userId: varchar('user_id', { length: 128 }),
    createdAt: timestamp('created_at')
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp('updated_at', {
      mode: 'string',
    })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`)
      .$onUpdate(() => sql`CURRENT_TIMESTAMP`),
  },
  (table) => {
    return {
      linkTypeIdx: index('link_type_idx').on(table.linkType),
      userIdIdx: index('user_id_idx').on(table.userId),
    }
  },
)

export const textImageLinkRelations = relations(textImageLink, ({ one }) => ({
  image: one(image, {
    fields: [textImageLink.imageId],
    references: [image.id],
  }),
}))
