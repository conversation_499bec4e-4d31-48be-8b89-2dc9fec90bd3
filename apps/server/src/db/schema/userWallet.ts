import { sql } from 'drizzle-orm'
import {
  bigint,
  mysqlTable,
  primaryKey,
  timestamp,
  varchar,
} from 'drizzle-orm/mysql-core'

const DIAMOND_CURRENCY = 'diamond'

/**
 * 用户钱包表 - 简化版本
 * 直接使用固定字符串作为货币类型
 */
export const userWallet = mysqlTable(
  'user_wallets',
  {
    userId: varchar('user_id', { length: 128 }).notNull(),
    // 固定为"diamond"，未来如果需要支持多种货币可以扩展
    currencyType: varchar('currency_type', { length: 50 })
      .notNull()
      .default(DIAMOND_CURRENCY),
    balance: bigint('balance', { mode: 'number' }).notNull().default(0),
    createdAt: timestamp('created_at', {
      mode: 'string',
    })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp('updated_at', {
      mode: 'string',
    })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`)
      .$onUpdate(() => sql`CURRENT_TIMESTAMP`),
  },
  (table) => {
    return {
      pk: primaryKey(table.userId, table.currencyType),
    }
  },
)
