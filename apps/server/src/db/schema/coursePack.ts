import { CoursePackShareLevel } from '@julebu/shared'
import { createId } from '@paralleldrive/cuid2'
import { relations, sql } from 'drizzle-orm'
import {
  boolean,
  index,
  int,
  mysqlTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/mysql-core'
import { course } from './course'

export const coursePack = mysqlTable('course_packs', {
  id: varchar('id', { length: 128 })
    .primaryKey()
    .$defaultFn(() => createId()),
  userId: varchar('user_id', { length: 128 }).notNull(),
  title: text('title').notNull().default(''),
  description: text('description').notNull().default(''),
  isFree: boolean('is_free').notNull().default(true),
  cover: text('cover').notNull().default(''),
  gameId: varchar('game_id', {
    length: 128,
  }).notNull().default(''),
  shareLevel: varchar('share_level', { length: 32 })
    .notNull()
    .default(CoursePackShareLevel.Private),
  categoryId: varchar('category_id', { length: 128 }),
  position: int('position').notNull().default(0),
  isPinned: boolean('is_pinned').notNull().default(false),
  createdAt: timestamp('created_at', {
    mode: 'string',
  })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
  updatedAt: timestamp('updated_at', {
    mode: 'string',
  })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`)
    .$onUpdate(() => sql`CURRENT_TIMESTAMP`),
}, table => ({
  categoryIdx: index('category_idx').on(table.categoryId),
}))

export const coursePackRelations = relations(coursePack, ({ many, one }) => ({
  courses: many(course),
}))
