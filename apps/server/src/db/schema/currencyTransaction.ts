import { createId } from '@paralleldrive/cuid2'
import { sql } from 'drizzle-orm'
import {
  bigint,
  mysqlEnum,
  mysqlTable,
  timestamp,
  varchar,
} from 'drizzle-orm/mysql-core'
import { DIAMOND_CURRENCY } from '../../routers/currency/constants'

export const TRANSACTION_TYPE = {
  PURCHASE: 'PURCHASE',
  REWARD: 'REWARD',
  CONSUME: 'CONSUME',
  TRANSFER: 'TRANSFER',
  SYSTEM: 'SYSTEM',
} as const

export const currencyTransaction = mysqlTable('currency_transactions', {
  id: varchar('id', { length: 128 })
    .primaryKey()
    .$defaultFn(() => createId()),
  userId: varchar('user_id', { length: 128 }).notNull(),
  currencyType: varchar('currency_type', { length: 50 })
    .notNull()
    .default(DIAMOND_CURRENCY),
  amount: bigint('amount', { mode: 'number' }).notNull(),
  transactionType: mysqlEnum('transaction_type', [
    TRANSACTION_TYPE.PURCHASE,
    TRANSACTION_TYPE.REWARD,
    TRANSACTION_TYPE.CONSUME,
    TRANSACTION_TYPE.TRANSFER,
    TRANSACTION_TYPE.SYSTEM,
  ]).notNull(),
  description: varchar('description', { length: 255 }),
  createdAt: timestamp('created_at', {
    mode: 'string',
  })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
  updatedAt: timestamp('updated_at', {
    mode: 'string',
  })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`)
    .$onUpdate(() => sql`CURRENT_TIMESTAMP`),
})
