import { createId } from '@paralleldrive/cuid2'
import { sql } from 'drizzle-orm'
import {
  index,
  json,
  mysqlTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/mysql-core'

// 内容类型枚举
export const InboxType = {
  TEXT: 'text',
  // 后续可以添加其他类型
  // IMAGE: 'image',
  // VIDEO: 'video',
  // LINK: 'link',
} as const

export type InboxTypeValue = (typeof InboxType)[keyof typeof InboxType]

export const inbox = mysqlTable(
  'inbox_items',
  {
    id: varchar('id', { length: 128 })
      .primaryKey()
      .$defaultFn(() => createId()),
    userId: varchar('user_id', { length: 128 }).notNull(),
    content: text('content').notNull(), // 主要内容
    remark: text('remark').default(''), // 备注（原 description）
    sourceUrl: text('source_url').notNull(), // 来源URL
    type: varchar('type', { length: 32 }) // 内容类型
      .notNull()
      .default(InboxType.TEXT),
    tags: json('tags') // 标签，使用 JSON 数组存储
      .$type<string[]>()
      .notNull()
      .default(sql`(JSON_ARRAY())`),
    createdAt: timestamp('created_at', {
      mode: 'string',
    })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp('updated_at', {
      mode: 'string',
    })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`)
      .$onUpdate(() => sql`CURRENT_TIMESTAMP`),
  },
  table => ({
    userIdIdx: index('user_id_idx').on(table.userId),
    typeIdx: index('type_idx').on(table.type),
  }),
)
