import { createId } from '@paralleldrive/cuid2'
import { sql } from 'drizzle-orm'
import {
  index,
  int,
  json,
  mysqlTable,
  timestamp,
  varchar,
} from 'drizzle-orm/mysql-core'

// 任务级别的 token 使用情况表
export const taskTokenUsage = mysqlTable(
  'task_token_usage',
  {
    id: varchar('id', { length: 128 })
      .primaryKey()
      .$defaultFn(() => createId()),

    // 任务类型
    taskType: varchar('task_type', { length: 50 }).notNull(),

    // 模型信息
    model: varchar('model', { length: 50 }).notNull(), // 使用的模型名称

    // Token 使用情况
    promptTokens: int('prompt_tokens').notNull(), // 提示词 token 数量
    completionTokens: int('completion_tokens').notNull(), // 完成 token 数量
    totalTokens: int('total_tokens').notNull(), // 总 token 数量

    // 元数据
    metadata: json('metadata').$type<Record<string, any>>(), // 存储任务相关的元数据

    // 时间信息
    year: int('year').notNull(), // 年份
    month: int('month').notNull(), // 月份
    createdAt: timestamp('created_at', {
      mode: 'string',
    })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp('updated_at', {
      mode: 'string',
    })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`)
      .$onUpdate(() => sql`CURRENT_TIMESTAMP`),
  },
  table => ({
    // 索引
    modelIdx: index('model_idx').on(table.model),
    yearMonthIdx: index('year_month_idx').on(table.year, table.month),
  }),
)

export type TaskTokenUsage = typeof taskTokenUsage.$inferSelect
export type NewTaskTokenUsage = typeof taskTokenUsage.$inferInsert
