import { createId } from '@paralleldrive/cuid2'
import { sql } from 'drizzle-orm'
import {
  index,
  int,
  json,
  mysqlTable,
  timestamp,
  varchar,
} from 'drizzle-orm/mysql-core'

export const image = mysqlTable(
  'images',
  {
    id: varchar('id', { length: 128 })
      .primaryKey()
      .$defaultFn(() => createId()),
    fileKey: varchar('file_key', { length: 255 }).notNull(),
    mimeType: varchar('mime_type', { length: 50 }).notNull(),
    fileSize: int('file_size').notNull(),
    width: int('width').notNull(),
    height: int('height').notNull(),
    // ai_generated | user_upload
    source: varchar('source', { length: 50 }).notNull(),
    description: varchar('description', { length: 500 }),
    userId: varchar('user_id', { length: 128 }),
    styleTags: json('style_tags').$type<string[]>().default([]),
    categoryTags: json('category_tags').$type<string[]>().default([]),
    createdAt: timestamp('created_at')
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp('updated_at', {
      mode: 'string',
    })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`)
      .$onUpdate(() => sql`CURRENT_TIMESTAMP`),
  },
  (table) => {
    return {
      sourceIdx: index('source_idx').on(table.source),
      userIdIdx: index('user_id_idx').on(table.userId),
      descriptionIdx: index('description_idx').on(table.description),
    }
  },
)
