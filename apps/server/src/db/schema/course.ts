import { CourseType } from '@julebu/shared'
import { createId } from '@paralleldrive/cuid2'
import { relations, sql } from 'drizzle-orm'

import {
  int,
  mysqlEnum,
  mysqlTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/mysql-core'
import { coursePack } from './coursePack'
import { sentence } from './sentence'

export const course = mysqlTable('courses', {
  id: varchar('id', { length: 128 })
    .primaryKey()
    .$defaultFn(() => createId()),
  title: text('title').notNull().default(''),
  description: text('description').notNull().default(''),
  cover: text('cover').notNull().default(''),
  video: text('video').notNull().default(''),
  coursePackId: varchar('course_pack_id', { length: 128 })
    .notNull()
    .references(() => coursePack.id),
  gameId: varchar('game_id', { length: 128 }).notNull().default(''),
  type: mysqlEnum('type', [CourseType.normal, CourseType.music, CourseType.audio, CourseType.video]).notNull().default(CourseType.normal),
  mediaUrl: text('media_url'),
  position: int('position').notNull().default(0),
  createdAt: timestamp('created_at', {
    mode: 'string',
  })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
  updatedAt: timestamp('updated_at', {
    mode: 'string',
  })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`)
    .$onUpdate(() => sql`CURRENT_TIMESTAMP`),
})

export const courseRelations = relations(course, ({ one, many }) => ({
  sentences: many(sentence),
  coursePack: one(coursePack, {
    fields: [course.coursePackId],
    references: [coursePack.id],
  }),
}))
