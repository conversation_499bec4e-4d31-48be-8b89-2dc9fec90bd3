import { createId } from '@paralleldrive/cuid2'
import { sql } from 'drizzle-orm'
import {
  int,
  mysqlTable,
  timestamp,
  uniqueIndex,
  varchar,
} from 'drizzle-orm/mysql-core'

// 后面增加用户额度表就可以实现用户付费购买额度了
// TODO 与用户额度表的关联: 这个表只是记录 token 消耗，你还需要一个用户额度表（user_quotas），记录用户的剩余额度，并在每次使用时进行扣减。
export const tokenUsage = mysqlTable('token_usage', {
  id: varchar('id', { length: 128 })
    .primaryKey()
    .$defaultFn(() => createId()),
  userId: varchar('user_id', { length: 255 }).notNull(),
  model: varchar('model', { length: 50 }).notNull(),
  promptTokens: int('prompt_tokens').notNull(),
  completionTokens: int('completion_tokens').notNull(),
  totalTokens: int('total_tokens').notNull(),
  year: int('year').notNull(),
  month: int('month').notNull(),
  createdAt: timestamp('created_at', {
    mode: 'string',
  })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
  updatedAt: timestamp('updated_at', {
    mode: 'string',
  })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`)
    .$onUpdate(() => sql`CURRENT_TIMESTAMP`),
}, table => ({
  userModelMonthIdx: uniqueIndex('user_model_month_idx').on(
    table.userId,
    table.model,
    table.year,
    table.month,
  ),
}))

export type TokenUsage = typeof tokenUsage.$inferSelect
export type NewTokenUsage = typeof tokenUsage.$inferInsert
