import { createId } from '@paralleldrive/cuid2'
import { relations, sql } from 'drizzle-orm'
import {
  decimal,
  index,
  int,
  json,
  mysqlTable,
  text,
  timestamp,
  varchar,
} from 'drizzle-orm/mysql-core'
import { course } from './course'
import { element } from './element'

export const sentence = mysqlTable(
  'sentences',
  {
    id: varchar('id', { length: 128 })
      .primaryKey()
      .$defaultFn(() => createId()),
    content: text('content').notNull().default(''),
    learningContent: json('learning_content'),
    wordDetails: json('word_details'),
    dependencyAnalysis: json('dependency_analysis'),
    chinese: text('chinese').notNull().default(''),
    english: text('english').notNull().default(''),
    position: int('position').notNull().default(0),
    courseId: varchar('course_id', { length: 128 })
      .notNull()
      .references(() => course.id),
    startTime: decimal('start_time', { precision: 10, scale: 3 }),
    endTime: decimal('end_time', { precision: 10, scale: 3 }),
    createdAt: timestamp('created_at', {
      mode: 'string',
    })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp('updated_at', {
      mode: 'string',
    })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`)
      .$onUpdate(() => sql`CURRENT_TIMESTAMP`),
  },
  table => ({
    courseIdIdx: index('course_id_idx').on(table.courseId),
  }),
)

export const sentenceRelations = relations(sentence, ({ one, many }) => ({
  course: one(course, {
    fields: [sentence.courseId],
    references: [course.id],
  }),
  elements: many(element),
}))
