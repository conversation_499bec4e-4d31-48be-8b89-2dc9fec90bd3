import type { MySql2Database } from 'drizzle-orm/mysql2'

import type * as mysql from 'mysql2/promise'
import { drizzle } from 'drizzle-orm/mysql2'

import * as schemas from './schema'

export * as schemas from './schema'

export type DbType = MySql2Database<typeof schemas>

let db: DbType | null = null
let pool: mysql.Pool | null = null

export async function setupDB(_pool: mysql.Pool) {
  if (!_pool) {
    throw new Error('必须先创建数据库连接池')
  }

  pool = _pool
  db = drizzle(pool, {
    schema: schemas,
    mode: 'default',
  })
}

export async function closeDB() {
  if (pool) {
    await pool.end()
    pool = null
    db = null
  }
}

export function getDB() {
  if (!db) {
    throw new Error('数据库连接未初始化， 必须要先调用 setupDB 初始化数据库连接')
  }
  return db
}
