import { z } from 'zod'
import { protectedProcedure, router } from '../../trpc'
import { userService } from './user.service'

export const userRouter = router({
  /**
   * 用户登录后的初始化接口
   * 前端在用户登录成功后调用
   * 执行各种初始化操作，例如检查会员状态和初始化VP记录
   */
  initializeAfterLogin: protectedProcedure
    .input(z.object({})) // 不需要额外输入，用户信息从上下文获取
    .mutation(async ({ ctx }) => {
      // 从上下文中获取用户ID和令牌
      const { user } = ctx
      // 调用用户服务的初始化方法
      await userService.initializeAfterLogin(user.id)

      return { success: true }
    }),
})
