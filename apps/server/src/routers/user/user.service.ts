import { currencyService } from '../currency/currency.service'

/**
 * 用户服务类，处理用户相关的业务逻辑
 */
class UserService {
  /**
   * 用户登录后的初始化操作
   * 检查会员状态，并确保VP记录的存在
   * @param userId 用户ID
   */
  async initializeAfterLogin(userId: string): Promise<void> {
    // 初始化用户的钻石余额
    await currencyService.initializeUserDiamondWallet(userId)

    // 这里可以添加其他用户初始化逻辑
    // 如：初始化用户偏好设置、记录登录历史等
  }
}

export const userService = new UserService()
