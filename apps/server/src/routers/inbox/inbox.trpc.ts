import { z } from 'zod'
import { protectedProcedure, router } from '../../trpc'
import { inboxService } from './inbox.service'

export const inboxTrpcRouter = router({
  // 获取用户的所有收集内容
  findAll: protectedProcedure.query(async ({ ctx }) => {
    return inboxService.findAll(ctx.user.id)
  }),

  // 获取单个收集内容
  findOne: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      return inboxService.findOne(input.id, ctx.user.id)
    }),

  // 删除单个收集内容
  deleteOne: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      return inboxService.deleteOne(input.id, ctx.user.id)
    }),

  // 批量删除收集内容
  batchDelete: protectedProcedure
    .input(z.object({ ids: z.array(z.string()) }))
    .mutation(async ({ input, ctx }) => {
      return inboxService.batchDelete(input.ids, ctx.user.id)
    }),

  // 删除所有收集内容
  deleteAll: protectedProcedure
    .mutation(async ({ ctx }) => {
      return inboxService.deleteAll(ctx.user.id)
    }),
})
