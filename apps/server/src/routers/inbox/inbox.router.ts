import type { FastifyInstance } from 'fastify'
import type { CreateInboxInput } from './inbox.service'
import { inboxService } from './inbox.service'

export async function inboxApiRoutes(server: FastifyInstance) {
  // 只提供创建接口给浏览器插件使用
  server.post<{ Body: CreateInboxInput }>('/inbox', {
    handler: async (request, reply) => {
      const item = await inboxService.create(request.body, request.user!.id)
      return reply.code(201).send(item)
    },
  })
}
