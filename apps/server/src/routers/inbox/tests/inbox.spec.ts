import { describe, expect, it, afterAll, beforeEach } from "vitest";
import { inboxService } from "../inbox.service";
import { cleanInboxDB } from "#/fixtures/inbox";
import type { CreateInboxInput } from "../inbox.service";
import { InboxType } from "@/db/schema/inbox";

describe("InboxService", () => {
  beforeEach(async () => {
    await cleanInboxDB();
  });

  afterAll(async () => {
    await cleanInboxDB();
  });

  const mockInboxItem: CreateInboxInput = {
    content: "这是一段测试内容",
    remark: "这是备注信息",
    sourceUrl: "https://example.com/article",
    type: InboxType.TEXT,
    tags: ["test", "example"],
  };

  const testUserId = "test-user-1";

  describe("create", () => {
    it("should create a new inbox item", async () => {
      const result = await inboxService.create(mockInboxItem, testUserId);

      expect(result).toBeDefined();
      expect(result.id).toBeDefined();
    });

    it("should create with default values", async () => {
      const minimalInput: CreateInboxInput = {
        content: "测试内容",
        sourceUrl: "https://example.com",
      };

      const result = await inboxService.create(minimalInput, testUserId);
      expect(result).toBeDefined();

      const item = await inboxService.findOne(result.id, testUserId);
      expect(item).toBeDefined();
      expect(item?.type).toBe(InboxType.TEXT);
      expect(item?.remark).toBe("");
      expect(item?.tags).toEqual([]);
    });
  });

  describe("findAll", () => {
    it("should return empty array when user has no items", async () => {
      const items = await inboxService.findAll("non-existent-user");
      expect(items).toEqual([]);
    });

    it("should return all items for a user", async () => {
      // 创建多个测试项目
      const mockItems = [
        mockInboxItem,
        {
          ...mockInboxItem,
          content: "第二条测试内容",
          remark: "第二条备注",
        },
        {
          ...mockInboxItem,
          content: "第三条测试内容",
          remark: "第三条备注",
        },
      ];

      await Promise.all(
        mockItems.map((item) => inboxService.create(item, testUserId))
      );

      const items = await inboxService.findAll(testUserId);
      expect(items).toHaveLength(3);
      expect(Array.isArray(items[0].tags)).toBe(true);
    });
  });

  describe("findOne", () => {
    it("should return null for non-existent item", async () => {
      const item = await inboxService.findOne(
        "non-existent-id",
        "test-user-1"
      );
      expect(item).toBeNull();
    });

    it("should return item by id", async () => {
      const created = await inboxService.create(mockInboxItem, testUserId);
      const found = await inboxService.findOne(created.id, testUserId);

      expect(found).toBeDefined();
      expect(found?.id).toBe(created.id);
      expect(found?.content).toBe(mockInboxItem.content);
      expect(found?.remark).toBe(mockInboxItem.remark);
      expect(found?.type).toBe(mockInboxItem.type);
      expect(found?.tags).toEqual(mockInboxItem.tags);
    });
  });

  describe("deleteOne", () => {
    it("should return false when deleting non-existent item", async () => {
      const result = await inboxService.deleteOne(
        "non-existent-id",
        "test-user-1"
      );
      expect(result).toBe(false);
    });

    it("should delete an existing item", async () => {
      const created = await inboxService.create(mockInboxItem, testUserId);
      const result = await inboxService.deleteOne(created.id, testUserId);
      expect(result).toBe(true);

      const found = await inboxService.findOne(created.id, testUserId);
      expect(found).toBeNull();
    });
  });

  describe("batchDelete", () => {
    it("should delete multiple items", async () => {
      // 创建多个测试项目
      const created = await Promise.all([
        inboxService.create(mockInboxItem, testUserId),
        inboxService.create({
          ...mockInboxItem,
          content: "第二条内容",
        }, testUserId),
        inboxService.create({
          ...mockInboxItem,
          content: "第三条内容",
        }, testUserId),
      ]);

      const ids = created.map((item) => item.id);
      const result = await inboxService.batchDelete(ids, testUserId);
      expect(result).toBe(true);

      // 验证所有项目都被删除
      const items = await inboxService.findAll(testUserId);
      expect(items).toHaveLength(0);
    });

    it("should handle empty array", async () => {
      const result = await inboxService.batchDelete([], "test-user-1");
      expect(result).toBe(false);
    });
  });
});
