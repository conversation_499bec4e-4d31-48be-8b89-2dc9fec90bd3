import type { InboxTypeValue } from '@/db/schema/inbox'
import { TRPCError } from '@trpc/server'
import { and, desc, eq, inArray } from 'drizzle-orm'
import { getDB, schemas } from '@/db'
import { InboxType } from '@/db/schema/inbox'

// 输入类型定义
export interface CreateInboxInput {
  content: string
  remark?: string
  sourceUrl: string
  type?: InboxTypeValue
  tags?: string[]
}

// 创建收集箱内容
async function create(input: CreateInboxInput, userId: string): Promise<{ id: string }> {
  const db = getDB()
  try {
    const [result] = await db
      .insert(schemas.inbox)
      .values({
        ...input,
        userId,
        type: input.type || InboxType.TEXT,
        remark: input.remark || '',
        tags: input.tags || [],
      })
      .$returningId()

    return result
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '创建收集内容失败',
      cause: error,
    })
  }
}

// 获取用户的所有收集内容
async function findAll(userId: string) {
  const db = getDB()
  try {
    const items = await db.query.inbox.findMany({
      where: eq(schemas.inbox.userId, userId),
      orderBy: [desc(schemas.inbox.createdAt)],
    })

    return items
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '获取收集内容失败',
      cause: error,
    })
  }
}

// 获取单个收集内容
async function findOne(id: string, userId: string) {
  const db = getDB()
  try {
    const item = await db.query.inbox.findFirst({
      where: and(eq(schemas.inbox.id, id), eq(schemas.inbox.userId, userId)),
    })

    if (!item) {
      return null
    }

    return item
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '获取收集内容失败',
      cause: error,
    })
  }
}

// 删除单个收集内容
async function deleteOne(id: string, userId: string): Promise<boolean> {
  const db = getDB()
  try {
    const [result] = await db
      .delete(schemas.inbox)
      .where(and(eq(schemas.inbox.id, id), eq(schemas.inbox.userId, userId)))

    return result.affectedRows > 0
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '删除收集内容失败',
      cause: error,
    })
  }
}

// 批量删除收集内容
async function batchDelete(ids: string[], userId: string): Promise<boolean> {
  const db = getDB()
  try {
    const [result] = await db
      .delete(schemas.inbox)
      .where(
        and(inArray(schemas.inbox.id, ids), eq(schemas.inbox.userId, userId)),
      )

    return result.affectedRows > 0
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '批量删除收集内容失败',
      cause: error,
    })
  }
}

// 添加 deleteAll 方法
async function deleteAll(userId: string) {
  const db = getDB()
  try {
    await db.delete(schemas.inbox).where(eq(schemas.inbox.userId, userId))
    return true
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '删除所有收集内容失败',
      cause: error,
    })
  }
}

export const inboxService = {
  create,
  findAll,
  findOne,
  deleteOne,
  batchDelete,
  deleteAll,
}
