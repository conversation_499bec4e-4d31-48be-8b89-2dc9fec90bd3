import type { coursePackCategory } from '../../../db/schema'

export interface CategoryDTO {
  id: string
  label: string
  value: string
  description: string
  sortOrder: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  parentId: string | null
}

export const categoryMapper = {
  toDTO: (categoryData: typeof coursePackCategory.$inferSelect): CategoryDTO => {
    return {
      id: categoryData.id,
      label: categoryData.label,
      value: categoryData.value,
      description: categoryData.description || '',
      sortOrder: categoryData.sortOrder,
      isActive: categoryData.isActive,
      createdAt: categoryData.createdAt,
      updatedAt: categoryData.updatedAt,
      parentId: categoryData.parentId,
    }
  },
}
