import STS from 'qcloud-cos-sts'
import { config } from '@/config/config'

export const cosService = {
  async getCredentials() {
    const appId = config.cos.bucket.substr(
      1 + config.cos.bucket.lastIndexOf('-'),
    )

    const policy = {
      version: '2.0',
      statement: [
        {
          action: [
            // 只需要简单上传的权限
            'name/cos:PutObject',
            'name/cos:PostObject',
          ],
          effect: 'allow',
          principal: { qcs: ['*'] },
          resource: [
            `qcs::cos:${config.cos.region}:uid/${appId}:${config.cos.bucket}/${config.cos.allowPrefix}`,
          ],
          condition: {
            // 限制上传文件最大为 2MB
            numeric_less_than_equal: {
              'cos:content-length': 2097152, // 2MB in bytes
            },
            // 只允许上传图片
            string_like: {
              'cos:content-type': ['image/*'],
            },
          },
        },
      ],
    }

    try {
      const startTime = Math.round(Date.now() / 1000)
      const result = await new Promise<{
        credentials: {
          sessionToken: string
          tmpSecretId: string
          tmpSecretKey: string
        }
        expiredTime: number
      }>((resolve, reject) => {
        STS.getCredential(
          {
            secretId: config.cos.secretId,
            secretKey: config.cos.secretKey,
            proxy: config.cos.proxy,
            durationSeconds: config.cos.durationSeconds || 1800,
            policy,
          },
          (err, data) => {
            if (err) {
              reject(err)
              return
            }
            resolve(data)
          },
        )
      })

      return {
        ...result.credentials,
        startTime,
        expiredTime: result.expiredTime,
        bucket: config.cos.bucket,
        region: config.cos.region,
        allowPrefix: config.cos.allowPrefix,
      }
    } catch (error) {
      if (error instanceof Error) {
        throw new TypeError(`获取 COS 临时密钥失败: ${error.message}`)
      }
      throw new Error('获取 COS 临时密钥失败')
    }
  },
}
