import axios from 'axios'
import { eq, inArray } from 'drizzle-orm'
import { config } from '@/config/config'
import { getDB, schemas } from '@/db'

export enum AdvancedPronunciationType {
  // 英文音色
  Ariana = 'BV503_streaming', // 活力女声-<PERSON>na（美式英语）
  Lily = 'BV506_streaming', // 天真萌娃-Lily (美式英语)
  Anna = 'BV040_streaming', // 亲切女声-<PERSON>(英式英语)
}

// 生成发音人列表
const voices = [
  AdvancedPronunciationType.Ariana,
  AdvancedPronunciationType.Lily,
  AdvancedPronunciationType.Anna,
]

interface TTSResponse {
  audioUrl: string
}

interface TTSRequest {
  text: string
  voice: string
  rate: string
}

// 提取文本中的单词函数
function extractWords(text: string): string[] {
  // 只基于空格分割句子
  return text.split(/\s+/).filter(word => word.length > 0)
}

export const ttsService = {
  async generateTTS(text: string, voice = 'af_bella', rate = '1.0') {
    try {
      await axios.post<TTSResponse>(
        `${config.audioService.url}/api/tts`,
        {
          text,
          voice,
          rate,
        } as TTSRequest,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-api-token': config.audioService.secret,
          },
        },
      )

      return true
    } catch (error) {
      console.error('Error generating TTS:', error)
      throw error
    }
  },

  async generateTTSForCourse(courseId: string) {
    const db = getDB()

    // 获取课程中的所有句子
    const sentences = await db.query.sentence.findMany({
      where: eq(schemas.sentence.courseId, courseId),
    })

    if (sentences.length === 0) {
      throw new Error('No sentences found in course')
    }

    // 获取所有句子相关的元素
    const elements = await db.query.element.findMany({
      where: inArray(
        schemas.element.sentenceId,
        sentences.map(s => s.id),
      ),
    })

    if (elements.length === 0) {
      throw new Error('No elements found in sentences')
    }

    try {
      // 准备批量请求数据
      const sentenceTexts = elements.map(element => element.english)

      // 提取所有单词并去重
      const allWords = new Set<string>()
      elements.forEach((element) => {
        const words = extractWords(element.english)
        words.forEach(word => allWords.add(word.toLowerCase()))
      })

      // 合并句子和单词到一个数组
      const texts = [...sentenceTexts, ...Array.from(allWords)]

      // 调用支持多发音人的批量 TTS 接口
      const response = await axios.post(
        `${config.audioService.url}/api/tts/batch-multi-voices`,
        {
          texts,
          voiceTypes: voices,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'x-api-token': config.audioService.secret,
          },
        },
      )

      return response.data
    } catch (error) {
      console.error('Error generating batch TTS with multiple voices:', error)
      // 如果批量生成失败，返回空的音频 URL
      return elements.map(element => ({
        elementId: element.id,
        audioUrl: '',
      }))
    }
  },
}
