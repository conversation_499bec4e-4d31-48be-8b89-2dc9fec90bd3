import { z } from 'zod'
import { protectedProcedure, router } from '../../trpc'
import { ttsService } from './tts.service'

export const ttsRouter = router({
  generateTTS: protectedProcedure
    .input(
      z.object({
        text: z.string(),
        voice: z.string().optional(),
        rate: z.string().optional(),
      }),
    )
    .mutation(async ({ input }) => {
      const { text, voice, rate } = input
      return ttsService.generateTTS(text, voice, rate)
    }),

  generateTTSForCourse: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { courseId } = input
      return ttsService.generateTTSForCourse(courseId)
    }),
})
