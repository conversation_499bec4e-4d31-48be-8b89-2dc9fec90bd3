import type { InferSelectModel } from 'drizzle-orm'
import type { schemas } from '@/db'

export type ElementEntity = InferSelectModel<typeof schemas.element>

export type ElementEntityWithImage = ElementEntity & {
  image?: {
    userId: string | null
    id: string
    fileKey: string
    mimeType: string
    fileSize: number
    width: number
    height: number
    source: string
    description: string | null
    styleTags: string[] | null
    categoryTags: string[] | null
    createdAt: Date
    updatedAt: string
  } | null
}
