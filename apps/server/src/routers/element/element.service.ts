import type { ElementProps } from '@julebu/shared'
import { TRPCError } from '@trpc/server'
import { and, eq, gt, gte, sql } from 'drizzle-orm'
import { getDB, schemas } from '@/db'
import { translateToChinese } from '@/services/translate'
import { phoneticService } from '../phonetic/phonetic.service'
import { normalizeContent } from '../sentence/content'

export async function movePosition(
  affectedElements: { position: number, id: string }[],
) {
  const db = getDB()

  // 批量更新受影响课程的位置
  for (const element of affectedElements) {
    await db
      .update(schemas.element)
      .set({
        position: element.position,
      })
      .where(and(eq(schemas.element.id, element.id)))
  }

  return true
}

async function deleteOne(elementId: string) {
  const db = getDB()

  // 获取要删除的元素的 position
  const elementToDelete = await db.query.element.findFirst({
    where: eq(schemas.element.id, elementId),
  })

  if (!elementToDelete) {
    throw new TRPCError({ code: 'NOT_FOUND', message: '找不到要删除的元素' })
  }

  return db.transaction(async (tx) => {
    // 删除元素
    await tx.delete(schemas.element).where(eq(schemas.element.id, elementId))

    // 更新后续元素的 position
    await tx
      .update(schemas.element)
      .set({
        position: sql`${schemas.element.position} - 1`,
      })
      .where(
        and(
          eq(schemas.element.sentenceId, elementToDelete.sentenceId),
          gt(schemas.element.position, elementToDelete.position),
        ),
      )

    return true
  })
}

async function insertBelow(sentenceId: string, elementId: string) {
  const db = getDB()

  const targetElement = await db.query.element.findFirst({
    where: eq(schemas.element.id, elementId),
  })

  if (!targetElement) {
    throw new TRPCError({ code: 'NOT_FOUND', message: '找不到目标元素' })
  }

  const insertPosition = targetElement.position + 1

  return db.transaction(async (tx) => {
    // 更新后续元素的position
    await tx
      .update(schemas.element)
      .set({
        position: sql`${schemas.element.position} + 1`,
      })
      .where(
        and(
          eq(schemas.element.sentenceId, sentenceId),
          gt(schemas.element.position, targetElement.position),
        ),
      )

    // 插入新元素
    const [elementEntity] = await tx
      .insert(schemas.element)
      .values({
        sentenceId,
        content: '我是新加入的',
        position: insertPosition,
      })
      .$returningId()

    return { id: elementEntity.id }
  })
}

async function insertAbove(sentenceId: string, elementId: string) {
  const db = getDB()

  const targetElement = await db.query.element.findFirst({
    where: eq(schemas.element.id, elementId),
  })

  if (!targetElement) {
    throw new TRPCError({ code: 'NOT_FOUND', message: '找不到目标元素' })
  }

  const insertPosition = targetElement.position

  return db.transaction(async (tx) => {
    // 更新受影响元素的position
    await tx
      .update(schemas.element)
      .set({
        position: sql`${schemas.element.position} + 1`,
      })
      .where(
        and(
          eq(schemas.element.sentenceId, sentenceId),
          gte(schemas.element.position, insertPosition),
        ),
      )

    // 插入新元素
    const [elementEntity] = await tx
      .insert(schemas.element)
      .values({
        sentenceId,
        content: '我是新加入的',
        position: insertPosition,
      })
      .$returningId()

    return { id: elementEntity.id }
  })
}

async function copyOne(sentenceId: string, elementId: string) {
  const db = getDB()

  // 获取要复制的元素
  const elementToCopy = await db.query.element.findFirst({
    where: eq(schemas.element.id, elementId),
  })

  if (!elementToCopy) {
    throw new TRPCError({ code: 'NOT_FOUND', message: '找不到要复制的元素' })
  }

  return db.transaction(async (tx) => {
    // 更新所有大于等于插入位置的元素的 position
    await tx
      .update(schemas.element)
      .set({
        position: sql`${schemas.element.position} + 1`,
      })
      .where(
        and(
          eq(schemas.element.sentenceId, sentenceId),
          gt(schemas.element.position, elementToCopy.position),
        ),
      )

    // 插入复制的元素
    const [elementEntity] = await tx
      .insert(schemas.element)
      .values({
        sentenceId,
        content: elementToCopy.content,
        english: elementToCopy.english,
        chinese: elementToCopy.chinese,
        phonetic: elementToCopy.phonetic,
        type: elementToCopy.type,
        position: elementToCopy.position + 1,
        imageId: elementToCopy.imageId,
      })
      .$returningId()

    return { id: elementEntity.id }
  })
}

async function updateProp(elementId: string, key: ElementProps, val: string) {
  const db = getDB()

  // 获取要更新的元素
  const elementToUpdate = await db.query.element.findFirst({
    where: eq(schemas.element.id, elementId),
  })

  if (!elementToUpdate) {
    throw new TRPCError({ code: 'NOT_FOUND', message: '找不到要更新的元素' })
  }

  // 如果值没变,直接返回
  if (elementToUpdate[key] === val) {
    return false
  }

  // 构建更新数据
  const updateData: Record<string, any> = {
    [key]: val,
  }

  // 特殊处理 content 字段
  if (key === 'content') {
    updateData.english = normalizeContent(val)
  }

  if (key === 'imageId') {
    updateData.imageId = val || sql`NULL`
  }

  // 更新元素
  await db
    .update(schemas.element)
    .set(updateData)
    .where(eq(schemas.element.id, elementId))

  return true
}

export async function processOne(elementId: string) {
  // 加工 element 的特点是可以强制重新赋值 不用管用之前的有没有值
  const db = getDB()
  const element = await db.query.element.findFirst({
    where: eq(schemas.element.id, elementId),
  })

  if (!element) {
    throw new TRPCError({ code: 'NOT_FOUND', message: '找不到要更新的元素' })
  }

  const chinese = await translateToChinese(element.content)
  const phonetic = await phoneticService.generateSentencePhonetic(
    element.content,
  )
  const english = normalizeContent(element.content)

  await db
    .update(schemas.element)
    .set({
      chinese,
      phonetic,
      english,
    })
    .where(eq(schemas.element.id, elementId))

  return {
    chinese,
    phonetic,
    english,
  }
}

export const elementService = {
  movePosition,
  deleteOne,
  insertAbove,
  insertBelow,
  copyOne,
  processOne,
  updateProp,
}
