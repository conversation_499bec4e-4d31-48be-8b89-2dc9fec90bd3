import type { Element } from '@julebu/shared'
import type { ElementEntityWithImage } from '../entity/element.entity'

export const elementMapper = {
  toDTO: (elementEntity: ElementEntityWithImage): Element => {
    return {
      id: elementEntity.id,
      content: elementEntity.content,
      english: elementEntity.english,
      chinese: elementEntity.chinese,
      phonetic: elementEntity.phonetic,
      image: elementEntity.image
        ? {
            width: elementEntity.image.width,
            height: elementEntity.image.height,
            fileKey: elementEntity.image.fileKey,
            description: elementEntity.image.description || '',
          }
        : null,
    }
  },
}
