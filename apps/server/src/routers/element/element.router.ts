import type { ElementProps } from '@julebu/shared'
import { z } from 'zod'
import { protectedProcedure, router } from '../../trpc'
import { elementService } from './element.service'

export const elementRouter = router({
  movePosition: protectedProcedure
    .input(
      z.object({
        sentenceId: z.string(),
        affectedElements: z.array(
          z.object({
            position: z.number(),
            id: z.string(),
          }),
        ),
      }),
    )
    .mutation(async ({ input }) => {
      const { affectedElements } = input
      return elementService.movePosition(affectedElements)
    }),

  deleteOne: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentenceId: z.string(),
        elementId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { elementId } = input
      return elementService.deleteOne(elementId)
    }),

  insertAbove: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentenceId: z.string(),
        elementId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { sentenceId, elementId } = input
      return elementService.insertAbove(sentenceId, elementId)
    }),

  insertBelow: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentenceId: z.string(),
        elementId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { sentenceId, elementId } = input
      return elementService.insertBelow(sentenceId, elementId)
    }),

  copyOne: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentenceId: z.string(),
        elementId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { sentenceId, elementId } = input
      return elementService.copyOne(sentenceId, elementId)
    }),

  updateProp: protectedProcedure
    .input(
      z.object({
        elementId: z.string(),
        key: z.string(),
        val: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { elementId, key, val } = input
      return elementService.updateProp(
        elementId,
        key as ElementProps,
        val,
      )
    }),

  processOne: protectedProcedure
    .input(
      z.object({
        elementId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      return elementService.processOne(input.elementId)
    }),
})
