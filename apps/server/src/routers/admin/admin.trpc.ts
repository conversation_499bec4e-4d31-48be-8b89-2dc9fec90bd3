import { z } from 'zod'
import { protectedProcedure, router } from '../../trpc'
import { adminService } from './admin.service'

// 定义类型以避免导出问题
export interface DeleteSentencesResult {
  total: number
  successCount: number
  failCount: number
  failedIds: string[]
  results: Array<{
    sentenceId: string
    success: boolean
    error?: string
  }>
}

export const adminCourseRouter = router({
  getCourseDetail: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
        courseId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const { coursePackId, courseId } = input
      const userId = ctx.user.id

      return adminService.getCourseDetailWithStatements(coursePackId, courseId, userId)
    }),

  updateCourseInfo: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        coursePackId: z.string(),
        title: z.string(),
        description: z.string(),
        mediaUrl: z.string(),
        type: z.enum(['normal', 'music']).optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.user.id
      return adminService.updateCourse(input.courseId, {
        title: input.title,
        description: input.description,
        mediaUrl: input.mediaUrl,
      }, userId)
    }),

  updateCourseToGame: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        coursePackId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.user.id
      return adminService.updateCourseToGame(input.courseId, input.coursePackId, userId)
    }),

  publishCourseToGame: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        coursePackId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.user.id
      return adminService.publishCourseToGame(input.courseId, input.coursePackId, userId)
    }),

  // 获取课程包完整信息（包含课程列表）
  getCoursePackWithCourses: protectedProcedure
    .input(z.object({ coursePackId: z.string() }))
    .query(async ({ input, ctx }) => {
      const userId = ctx.user.id
      return adminService.getCoursePackWithCourses(input.coursePackId, userId)
    }),
})

export const adminSentenceRouter = router({
  batchUpsertSentences: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentences: z.array(
          z.object({
            uuid: z.string(),
            sentenceId: z.string().optional(),
            content: z.string(),
            chinese: z.string(),
          }),
        ),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.user.id
      return adminService.batchUpsertSentences(input.courseId, input.sentences, userId)
    }),

  deleteBatchSentences: protectedProcedure
    .input(
      z.object({
        sentenceList: z.array(
          z.object({
            sentenceId: z.string(),
            courseId: z.string(),
          }),
        ),
      }),
    )
    .mutation(async ({ input }): Promise<DeleteSentencesResult> => {
      return adminService.deleteSentences(input.sentenceList)
    }),

  batchProcessSentences: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.user.id
      return adminService.processAllSentences(input.courseId, userId)
    }),

  movePosition: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        affectedSentences: z.array(
          z.object({
            sentenceId: z.string(),
            position: z.number(),
          }),
        ),
      }),
    )
    .mutation(async ({ input }) => {
      return adminService.moveSentencePositions(input.courseId, input.affectedSentences)
    }),

  batchUpdateElementsTime: protectedProcedure
    .input(
      z.object({
        updates: z.array(
          z.object({
            sentenceId: z.string(),
            startTime: z.number(),
            endTime: z.number(),
          }),
        ),
      }),
    )
    .mutation(async ({ input }) => {
      return adminService.updateElementsTime(input.updates)
    }),
})
