import type { FastifyReply, FastifyRequest } from 'fastify'
import { Buff<PERSON> } from 'node:buffer'
import { randomUUID } from 'node:crypto'
import COS from 'cos-nodejs-sdk-v5'
import { config } from '@/config/config'
import { cosService } from '../cos/cos.service'
import { adminService } from './admin.service'

// 文件上传类型已在 types/fastify.d.ts 中定义

// 初始化COS实例（用于服务端直接上传）
const cos = new COS({
  SecretId: config.cos.secretId,
  SecretKey: config.cos.secretKey,
})

// 服务端直接上传文件到COS
async function uploadToCOS(fileBuffer: COS.UploadBody, fileName: string, mimeType: string): Promise<string> {
  const key = `course-packs/${Date.now()}_${fileName}`

  return new Promise((resolve, reject) => {
    cos.putObject({
      Bucket: config.cos.bucket,
      Region: config.cos.region,
      Key: key,
      Body: fileBuffer,
      ContentType: mimeType,
    }, (err) => {
      if (err) {
        reject(err)
        return
      }
      // 返回完整的COS URL
      const url = `https://${config.cos.bucket}.cos.${config.cos.region}.myqcloud.com/${key}`
      resolve(url)
    })
  })
}

export const uploadController = {
  // 获取课程包封面上传凭证和参数
  async getCoverUploadCredentials(
    request: FastifyRequest,
    reply: FastifyReply,
  ) {
    try {
      // 获取COS临时凭证
      const credentials = await cosService.getCredentials()

      // 生成唯一的文件key
      const timestamp = Date.now()
      const uuid = randomUUID()
      const keyPrefix = `course-packs/${timestamp}_${uuid}`

      return await reply.send({
        success: true,
        message: '获取上传凭证成功',
        data: {
          credentials,
          keyPrefix,
          // 提供一些上传指导信息
          uploadInfo: {
            maxSize: 2 * 1024 * 1024, // 2MB
            allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
            bucket: credentials.bucket,
            region: credentials.region,
          },
        },
      })
    } catch (error) {
      console.error('获取上传凭证失败:', error)
      return reply.code(500).send({
        success: false,
        message: '获取上传凭证失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 验证上传的文件信息（可选，用于记录上传结果）
  async validateUploadResult(
    request: FastifyRequest<{
      Body: {
        key: string
        url: string
        size: number
        mimeType: string
        originalName: string
      }
    }>,
    reply: FastifyReply,
  ) {
    try {
      const { key, url, size, mimeType, originalName } = request.body

      // 验证文件类型
      const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
      if (!allowedMimeTypes.includes(mimeType)) {
        return await reply.code(400).send({
          success: false,
          message: '不支持的文件格式',
        })
      }

      // 验证文件大小
      const maxSize = 2 * 1024 * 1024
      if (size > maxSize) {
        return await reply.code(400).send({
          success: false,
          message: '文件大小超过 2MB',
        })
      }

      // 验证key格式
      if (!key.startsWith('course-packs/')) {
        return await reply.code(400).send({
          success: false,
          message: '无效的文件路径',
        })
      }

      return await reply.send({
        success: true,
        message: '文件验证成功',
        data: {
          key,
          url,
          size,
          mimeType,
          originalName,
          validated: true,
        },
      })
    } catch (error) {
      console.error('验证上传结果失败:', error)
      return reply.code(500).send({
        success: false,
        message: '验证上传结果失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 为指定课程包上传封面
  async uploadCover(
    request: FastifyRequest<{
      Body: { coursePackId: string, userId: string }
    }>,
    reply: FastifyReply,
  ) {
    try {
      const data = await request.file()

      if (!data) {
        return await reply.code(400).send({
          success: false,
          message: '没有找到上传的文件',
        })
      }

      // 从表单数据中获取coursePackId和userId
      const fields = data.fields
      const coursePackId = (fields.coursePackId as any)?.value
      const userId = (fields.userId as any)?.value

      if (!coursePackId) {
        return await reply.code(400).send({
          success: false,
          message: 'coursePackId 是必需的',
        })
      }

      if (!userId) {
        return await reply.code(400).send({
          success: false,
          message: 'userId 是必需的',
        })
      }

      // 验证文件类型
      const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
      if (!allowedMimeTypes.includes(data.mimetype)) {
        return await reply.code(400).send({
          success: false,
          message: '只支持 JPEG、PNG、WebP 和 GIF 格式的图片',
        })
      }

      // 读取文件流为Buffer
      const chunks: Buffer[] = []
      for await (const chunk of data.file) {
        chunks.push(chunk as Buffer)
      }
      const fileBuffer = Buffer.concat(chunks)

      // 验证文件大小 (1MB，与COS策略一致)
      const maxSize = 1 * 1024 * 1024
      if (fileBuffer.length > maxSize) {
        return await reply.code(400).send({
          success: false,
          message: '文件大小不能超过 1MB',
        })
      }

      // 生成唯一文件名
      const fileExtension = data.filename ? data.filename.split('.').pop() : 'jpg'
      const fileName = `${randomUUID()}.${fileExtension}`

      // 上传到COS
      const cosUrl = await uploadToCOS(fileBuffer, fileName, data.mimetype)

      // 更新课程包的封面URL
      const updatedCoursePack = await adminService.updateCoursePackCover(
        coursePackId as string,
        userId as string,
        cosUrl,
      )

      return await reply.send({
        success: true,
        message: '封面上传并更新成功',
        data: {
          filename: fileName,
          originalName: data.filename,
          mimetype: data.mimetype,
          url: cosUrl,
          size: fileBuffer.length,
          coursePackId,
          coursePackTitle: updatedCoursePack.title,
        },
      })
    } catch (error) {
      console.error('上传封面失败:', error)
      return reply.code(500).send({
        success: false,
        message: '上传封面失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 批量上传多个封面
  async uploadMultipleCovers(
    request: FastifyRequest,
    reply: FastifyReply,
  ) {
    try {
      const files = request.files()
      const results = []
      const errors = []

      for await (const data of files) {
        try {
          // 验证文件类型
          const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
          if (!allowedMimeTypes.includes(data.mimetype)) {
            errors.push({
              filename: data.filename,
              error: '不支持的文件格式',
            })
            continue
          }

          // 读取文件流为Buffer
          const chunks: Buffer[] = []
          for await (const chunk of data.file) {
            chunks.push(chunk as Buffer)
          }
          const fileBuffer = Buffer.concat(chunks)

          // 验证文件大小
          const maxSize = 2 * 1024 * 1024
          if (fileBuffer.length > maxSize) {
            errors.push({
              filename: data.filename,
              error: '文件大小超过 2MB',
            })
            continue
          }

          // 生成唯一文件名
          const fileExtension = data.filename ? data.filename.split('.').pop() : 'jpg'
          const fileName = `${randomUUID()}.${fileExtension}`

          // 上传到COS
          const cosUrl = await uploadToCOS(fileBuffer, fileName, data.mimetype)

          // 添加到结果
          results.push({
            filename: fileName,
            originalName: data.filename,
            mimetype: data.mimetype,
            url: cosUrl,
            size: fileBuffer.length,
          })
        } catch (error) {
          errors.push({
            filename: data.filename,
            error: error instanceof Error ? error.message : '上传失败',
          })
        }
      }

      return await reply.send({
        success: true,
        message: `成功上传 ${results.length} 个文件${errors.length > 0 ? `，${errors.length} 个文件失败` : ''}`,
        data: {
          uploaded: results,
          errors,
        },
      })
    } catch (error) {
      console.error('批量上传封面失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量上传封面失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },
}
