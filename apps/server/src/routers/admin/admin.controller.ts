import type { CoursePackShareLevel, CourseType, CreateImageWithTextLinkDto, MediaUploadDto } from '@julebu/shared'
import type { FastifyReply, FastifyRequest } from 'fastify'
import type { BatchUpdateCoursePackRequest } from './admin.service'
import { isArray, isEmpty } from 'lodash-es'
import { courseService } from '../course/course.service'
import { s3Service } from '../s3/s3.service'
import { adminService } from './admin.service'

interface BatchCoursePackRequest {
  coursePacks: Array<{
    title: string
    description: string
    cover?: string
    categoryId?: string
    shareLevel?: CoursePackShareLevel
    courses?: Array<{
      title: string
      description: string
      sentences?: Array<{
        content: string
        translation: string
        elements?: Array<{
          content: string
          translation: string
          phonetic?: string
        }>
      }>
    }>
  }>
  userId: string // 临时指定用户ID，后续可以从token中获取
}

interface BatchCourseRequest {
  coursePackId: string
  courses: Array<{
    title: string
    description: string
    sentences?: Array<{
      content: string
      translation: string
      elements?: Array<{
        content: string
        translation: string
        phonetic?: string
      }>
    }>
  }>
  userId: string
}

interface BatchSplitRequest {
  courseId: string
  sentences: Array<{
    content: string
    translation: string
    elements?: Array<{
      content: string
      translation: string
      phonetic?: string
    }>
  }>
  userId: string
}

export interface BatchUpsertSentencesRequest {
  courseId: string
  sentences: Array<{
    uuid?: string // 用于前端判断更新的哪个句子
    sentenceId?: string // 可选，如果提供则更新，否则创建
    content?: string // 英文内容
    english?: string // 标准化英文内容
    chinese?: string // 中文翻译
  }>
  userId: string
}

export const adminController = {
  // 批量创建课程包（包含课程和内容）
  async batchCreateCoursePacks(
    request: FastifyRequest<{ Body: BatchCoursePackRequest }>,
    reply: FastifyReply,
  ) {
    try {
      const { coursePacks, userId } = request.body

      if (isEmpty(coursePacks) || !isArray(coursePacks)) {
        return await reply.code(400).send({
          success: false,
          message: 'coursePacks 必须是非空数组',
        })
      }

      if (!userId?.trim()) {
        return await reply.code(400).send({
          success: false,
          message: 'userId 是必需的',
        })
      }

      const result = await adminService.batchCreateCoursePacks(coursePacks, userId)
      return await reply.send({
        success: true,
        message: `成功创建 ${result.length} 个课程包`,
        data: result,
      })
    } catch (error) {
      console.error('批量创建课程包失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量创建课程包失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  async batchUpdateCoursePack(
    request: FastifyRequest<{ Body: BatchUpdateCoursePackRequest[] }>,
    reply: FastifyReply,
  ) {
    const body = request.body

    try {
      if (isEmpty(body) || !isArray(body)) {
        return await reply.code(400).send({
          success: false,
          message: '请求体必须是非空数组',
        })
      }

      const result = await adminService.batchUpdateCoursePack(body)
      return await reply.send({
        success: true,
        message: `成功更新 ${result.length} 个课程包`,
        data: result,
      })
    } catch (error) {
      console.error('批量更新课程包失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量更新课程包失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  async batchUpdateCoursePacksToGame(
    request: FastifyRequest<{ Body: { coursePackId: string, userId: string }[] }>,
    reply: FastifyReply,
  ) {
    const body = request.body

    try {
      if (isEmpty(body) || !isArray(body)) {
        return await reply.code(400).send({
          success: false,
          message: '请求体必须是非空数组',
        })
      }

      const result = await adminService.batchUpdateCoursePacksToGame(body)
      return await reply.send({
        success: true,
        message: `成功更新 ${result.length} 个课程包到游戏端`,
        data: result,
      })
    } catch (error) {
      console.error('批量更新课程包到游戏端失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量更新课程包到游戏端失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 批量创建课程（在指定课程包下）
  async batchCreateCourses(
    request: FastifyRequest<{ Body: BatchCourseRequest }>,
    reply: FastifyReply,
  ) {
    try {
      const { coursePackId, courses, userId } = request.body

      if (!coursePackId?.trim()) {
        return await reply.code(400).send({
          success: false,
          message: 'coursePackId 是必需的',
        })
      }

      if (isEmpty(courses) || !isArray(courses)) {
        return await reply.code(400).send({
          success: false,
          message: 'courses 必须是非空数组',
        })
      }

      if (!userId?.trim()) {
        return await reply.code(400).send({
          success: false,
          message: 'userId 是必需的',
        })
      }

      const result = await adminService.batchCreateCourses(coursePackId, courses, userId)
      return await reply.send({
        success: true,
        message: `成功创建 ${result.length} 个课程`,
        data: result,
      })
    } catch (error) {
      console.error('批量创建课程失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量创建课程失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 批量拆分课程内容（为指定课程添加句子和元素）
  async batchSplitCourse(
    request: FastifyRequest<{ Body: BatchSplitRequest }>,
    reply: FastifyReply,
  ) {
    try {
      const { courseId, sentences, userId } = request.body

      if (!courseId?.trim()) {
        return await reply.code(400).send({
          success: false,
          message: 'courseId 是必需的',
        })
      }

      if (isEmpty(sentences) || !isArray(sentences)) {
        return await reply.code(400).send({
          success: false,
          message: 'sentences 必须是非空数组',
        })
      }

      if (!userId?.trim()) {
        return await reply.code(400).send({
          success: false,
          message: 'userId 是必需的',
        })
      }

      const result = await adminService.batchSplitCourse(courseId, sentences, userId)
      return await reply.send({
        success: true,
        message: `成功为课程添加 ${result.sentenceCount} 个句子和 ${result.elementCount} 个元素`,
        data: result,
      })
    } catch (error) {
      console.error('批量拆分课程失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量拆分课程失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 批量处理（加工）课程包中所有课程的句子
  async batchProcessAllCourses(
    request: FastifyRequest<{ Body: { coursePackId: string, userId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { coursePackId, userId } = request.body

      if (!coursePackId) {
        return await reply.code(400).send({
          success: false,
          message: 'coursePackId 是必需的',
        })
      }

      if (!userId) {
        return await reply.code(400).send({
          success: false,
          message: 'userId 是必需的',
        })
      }

      const result = await adminService.batchProcessAllCourses(coursePackId, userId)

      return await reply.send({
        success: true,
        message: `成功处理 ${result.totalCourses} 个课程，共加工 ${result.totalProcessedCount} 个句子`,
        data: result,
      })
    } catch (error) {
      console.error('批量处理课程失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量处理课程失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 批量拆分课程包中所有课程的句子
  async batchSplitAllCourses(
    request: FastifyRequest<{ Body: { userId: string, coursePackId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { userId, coursePackId } = request.body
      const result = await adminService.batchSplitAllCourses(coursePackId, userId)

      return await reply.send({
        success: true,
        message: '批量拆分完成',
        data: result,
      })
    } catch (error) {
      console.error('批量拆分失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量拆分失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 生成课程知识点讲解
  async generateLearningContent(
    request: FastifyRequest<{ Body: { userId: string, courseId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { userId, courseId } = request.body
      const result = await adminService.generateLearningContent(courseId, userId)

      return await reply.send({
        success: true,
        message: '知识点讲解生成完成',
        data: result,
      })
    } catch (error) {
      console.error('生成知识点讲解失败:', error)
      return reply.code(500).send({
        success: false,
        message: '生成知识点讲解失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 获取用户的课程包列表
  async getUserCoursePacks(
    request: FastifyRequest<{ Querystring: { userId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { userId } = request.query
      const coursePacks = await adminService.getUserCoursePacks(userId)

      return await reply.send({
        success: true,
        message: '获取课程包列表成功',
        data: coursePacks,
      })
    } catch (error) {
      console.error('获取课程包列表失败:', error)
      return reply.code(500).send({
        success: false,
        message: '获取课程包列表失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 获取指定课程包下课程列表
  async getCoursesByCoursePackId(
    request: FastifyRequest<{ Params: { coursePackId: string }, Querystring: { userId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { coursePackId } = request.params
      const { userId } = request.query
      if (!coursePackId?.trim()) {
        return await reply.code(400).send({ success: false, message: 'coursePackId 是必需的' })
      }
      if (!userId?.trim()) {
        return await reply.code(400).send({ success: false, message: 'userId 是必需的' })
      }
      const result = await adminService.getCoursesByCoursePackId(coursePackId, userId)
      return await reply.send({
        success: true,
        message: '获取课程列表成功',
        data: result,
      })
    } catch (error) {
      console.error('获取课程包下课程列表失败:', error)
      return reply.code(500).send({
        success: false,
        message: '获取课程包下课程列表失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 获取课程包完整信息（包含课程列表）
  async getCoursePackWithCourses(
    request: FastifyRequest<{ Params: { coursePackId: string }, Querystring: { userId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { coursePackId } = request.params
      const { userId } = request.query
      if (!coursePackId?.trim()) {
        return await reply.code(400).send({ success: false, message: 'coursePackId 是必需的' })
      }
      if (!userId?.trim()) {
        return await reply.code(400).send({ success: false, message: 'userId 是必需的' })
      }
      const result = await adminService.getCoursePackWithCourses(coursePackId, userId)
      return await reply.send({
        success: true,
        message: '获取课程包完整信息成功',
        data: result,
      })
    } catch (error) {
      console.error('获取课程包完整信息失败:', error)
      return reply.code(500).send({
        success: false,
        message: '获取课程包完整信息失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 获取课程详情（含 statements）
  async getCourseDetailWithStatements(
    request: FastifyRequest<{
      Params: { coursePackId: string, courseId: string }
      Querystring: { userId: string }
    }>,
    reply: FastifyReply,
  ) {
    try {
      const { coursePackId, courseId } = request.params
      const { userId } = request.query

      if (!coursePackId?.trim()) {
        return await reply.code(400).send({ success: false, message: 'coursePackId 是必需的' })
      }
      if (!courseId?.trim()) {
        return await reply.code(400).send({ success: false, message: 'courseId 是必需的' })
      }
      if (!userId?.trim()) {
        return await reply.code(400).send({ success: false, message: 'userId 是必需的' })
      }

      const result = await adminService.getCourseDetailWithStatements(coursePackId, courseId, userId)
      return await reply.send({
        success: true,
        message: '获取课程详情成功',
        data: result,
      })
    } catch (error) {
      console.error('获取课程详情失败:', error)
      return reply.code(500).send({
        success: false,
        message: '获取课程详情失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 批量创建或更新句子
  async batchUpsertSentences(
    request: FastifyRequest<{ Body: BatchUpsertSentencesRequest }>,
    reply: FastifyReply,
  ) {
    try {
      const { courseId, sentences, userId } = request.body

      if (!courseId?.trim()) {
        return await reply.code(400).send({
          success: false,
          message: 'courseId 是必需的',
        })
      }

      if (isEmpty(sentences)) {
        return await reply.code(400).send({
          success: false,
          message: 'sentences 必须是非空数组',
        })
      }

      if (!userId?.trim()) {
        return await reply.code(400).send({
          success: false,
          message: 'userId 是必需的',
        })
      }

      const result = await adminService.batchUpsertSentences(courseId, sentences, userId)

      return await reply.send({
        success: true,
        message: `成功处理 ${result.created} 个新句子，更新 ${result.updated} 个句子`,
        data: result,
      })
    } catch (error) {
      console.error('批量操作句子失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量操作句子失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 批量加工单个课程的所有句子
  async adminProcessAllSentences(
    request: FastifyRequest<{ Body: { courseId: string, userId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { courseId, userId } = request.body

      if (!courseId?.trim()) {
        return await reply.code(400).send({
          success: false,
          message: 'courseId 是必需的',
        })
      }

      if (!userId?.trim()) {
        return await reply.code(400).send({
          success: false,
          message: 'userId 是必需的',
        })
      }

      const result = await adminService.processAllSentences(courseId, userId)
      return await reply.send({
        success: true,
        message: `成功处理课程中的所有句子`,
        data: result,
      })
    } catch (error) {
      console.error('批量处理句子失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量处理句子失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 批量调整课程内句子顺序
  async adminMoveSentencePositions(
    request: FastifyRequest<{ Body: { courseId: string, affectedSentences: Array<{ position: number, sentenceId: string }> } }>,
    reply: FastifyReply,
  ) {
    try {
      const { courseId, affectedSentences } = request.body

      if (!courseId?.trim()) {
        return await reply.code(400).send({
          success: false,
          message: 'courseId 是必需的',
        })
      }
      if (isEmpty(affectedSentences)) {
        return await reply.code(400).send({
          success: false,
          message: 'affectedSentences 必须是非空数组',
        })
      }

      const result = await adminService.moveSentencePositions(courseId, affectedSentences)
      return await reply.send({
        success: true,
        message: '句子顺序批量调整成功',
        data: result,
      })
    } catch (error) {
      console.error('批量调整句子顺序失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量调整句子顺序失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 批量删除句子
  async adminDeleteSentences(
    request: FastifyRequest<{ Body: { sentenceList: { sentenceId: string, courseId: string }[] } }>,
    reply: FastifyReply,
  ) {
    try {
      const { sentenceList } = request.body
      if (isEmpty(sentenceList)) {
        return await reply.code(400).send({
          success: false,
          message: 'sentenceList 必须是非空数组',
        })
      }
      const result = await adminService.deleteSentences(sentenceList)
      return await reply.send({
        success: true,
        message: '批量删除句子完成',
        data: result,
      })
    } catch (error) {
      console.error('批量删除句子失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量删除句子失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 批量更新句子元素时间
  async adminUpdateElementsTime(
    request: FastifyRequest<{ Body: { updates: { sentenceId: string, startTime: number, endTime: number }[] } }>,
    reply: FastifyReply,
  ) {
    try {
      const { updates } = request.body
      if (isEmpty(updates)) {
        return await reply.code(400).send({
          success: false,
          message: 'updates 必须是非空数组',
        })
      }
      const result = await adminService.updateElementsTime(updates)
      return await reply.send({
        success: true,
        message: '批量更新句子元素时间完成',
        data: result,
      })
    } catch (error) {
      console.error('批量更新句子元素时间失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量更新句子元素时间失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 获取 S3 预签名上传媒体接口（admin专用）
  async getMediaPresignedUrlForAdmin(
    request: FastifyRequest<{
      Body: {
        key: string
        mimeType: string
        contentLength: number
      }
    }>,
    reply: FastifyReply,
  ) {
    try {
      const { key, mimeType, contentLength } = request.body
      if (!key || !mimeType || !contentLength) {
        return await reply.code(400).send({
          success: false,
          message: '参数缺失',
        })
      }
      const presignedUrl = await s3Service.getMediaPresignedUrl({ key, mimeType: mimeType as MediaUploadDto['mimeType'], contentLength })
      return await reply.send({
        success: true,
        data: presignedUrl,
      })
    } catch (error) {
      return reply.code(500).send({
        success: false,
        message: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 管理员发布课程到游戏端
  async publishCourseToGame(
    request: FastifyRequest<{ Body: { courseId: string, coursePackId: string, userId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { courseId, coursePackId, userId } = request.body
      if (!courseId?.trim()) {
        return await reply.code(400).send({ success: false, message: 'courseId 是必需的' })
      }
      if (!coursePackId?.trim()) {
        return await reply.code(400).send({ success: false, message: 'coursePackId 是必需的' })
      }
      if (!userId?.trim()) {
        return await reply.code(400).send({ success: false, message: 'userId 是必需的' })
      }
      const result = await courseService.publishToGame(courseId, coursePackId, userId)
      return await reply.send({
        success: true,
        message: '发布成功',
        data: result,
      })
    } catch (error) {
      console.error('管理员发布课程失败:', error)
      return reply.code(500).send({
        success: false,
        message: '发布失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 管理员更新课程到游戏端
  async updateCourseToGame(
    request: FastifyRequest<{ Body: { courseId: string, coursePackId: string, userId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { courseId, coursePackId, userId } = request.body
      if (!courseId?.trim()) {
        return await reply.code(400).send({ success: false, message: 'courseId 是必需的' })
      }
      if (!userId?.trim()) {
        return await reply.code(400).send({ success: false, message: 'userId 是必需的' })
      }
      const result = await courseService.updateToGame(courseId, coursePackId, userId)
      return await reply.send({
        success: true,
        message: '课程包更新成功',
        data: result,
      })
    } catch (error) {
      console.error('管理员更新课程包失败:', error)
      return reply.code(500).send({
        success: false,
        message: '课程包更新失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 批量记录image 到 images和text_image_links 表
  async batchRecordImage(
    request: FastifyRequest<{ Body: CreateImageWithTextLinkDto[] }>,
    reply: FastifyReply,
  ) {
    try {
      const result = await adminService.batchRecordImage(request.body)

      return await reply.send({
        success: true,
        message: '批量记录image成功',
        data: result,
      })
    } catch (error) {
      console.error('批量记录image失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量记录image失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 获取课程详情
  async getCourse(
    request: FastifyRequest<{ Params: { courseId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { courseId } = request.params
      const course = await adminService.getCourse(courseId)

      return await reply.send({
        success: true,
        message: '获取课程详情成功',
        data: course,
      })
    } catch (error) {
      console.error('获取课程详情失败:', error)
      return reply.code(500).send({
        success: false,
        message: '获取课程详情失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 更新句子翻译
  async updateSentenceTranslation(
    request: FastifyRequest<{ Body: { sentenceId: string, translation: string, userId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { sentenceId, translation, userId } = request.body

      if (!sentenceId || !translation || !userId) {
        return await reply.code(400).send({
          success: false,
          message: 'sentenceId, translation, userId 都是必需的',
        })
      }

      const result = await adminService.updateSentenceTranslation(sentenceId, translation, userId)

      return await reply.send({
        success: true,
        message: '更新句子翻译成功',
        data: result,
      })
    } catch (error) {
      console.error('更新句子翻译失败:', error)
      return reply.code(500).send({
        success: false,
        message: '更新句子翻译失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 更新元素翻译
  async updateElementTranslation(
    request: FastifyRequest<{ Body: { elementId: string, translation: string, userId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { elementId, translation, userId } = request.body

      if (!elementId || !translation || !userId) {
        return await reply.code(400).send({
          success: false,
          message: 'elementId, translation, userId 都是必需的',
        })
      }

      const result = await adminService.updateElementTranslation(elementId, translation, userId)

      return await reply.send({
        success: true,
        message: '更新元素翻译成功',
        data: result,
      })
    } catch (error) {
      console.error('更新元素翻译失败:', error)
      return reply.code(500).send({
        success: false,
        message: '更新元素翻译失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 更新课程包
  async updateCoursePack(
    request: FastifyRequest<{
      Params: { coursePackId: string }
      Body: { title?: string, description?: string, userId: string }
    }>,
    reply: FastifyReply,
  ) {
    try {
      const { coursePackId } = request.params
      const { title, description, userId } = request.body

      if (!userId) {
        return await reply.code(400).send({
          success: false,
          message: 'userId 是必需的',
        })
      }

      const result = await adminService.updateCoursePack(coursePackId, { title, description }, userId)

      return await reply.send({
        success: true,
        message: '更新课程包成功',
        data: result,
      })
    } catch (error) {
      console.error('更新课程包失败:', error)
      return reply.code(500).send({
        success: false,
        message: '更新课程包失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 更新课程
  async updateCourse(
    request: FastifyRequest<{
      Params: { courseId: string }
      Body: { title?: string, description?: string, mediaUrl?: string, type?: CourseType, userId: string }
    }>,
    reply: FastifyReply,
  ) {
    try {
      const { courseId } = request.params
      const { title, description, mediaUrl, type, userId } = request.body

      if (!userId) {
        return await reply.code(400).send({
          success: false,
          message: 'userId 是必需的',
        })
      }

      const result = await adminService.updateCourse(courseId, { title, description, mediaUrl, type }, userId)

      return await reply.send({
        success: true,
        message: '更新课程成功',
        data: result,
      })
    } catch (error) {
      console.error('更新课程失败:', error)
      return reply.code(500).send({
        success: false,
        message: '更新课程失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 根据英文批量获取图片
  async batchGetImagesByEnglish(
    request: FastifyRequest<{ Body: { english: string[] } }>,
    reply: FastifyReply,
  ) {
    try {
      const { english } = request.body
      const result = await adminService.batchGetImagesByEnglish(english)

      return await reply.send({
        success: true,
        message: '批量获取图片成功',
        data: result,
      })
    } catch (error) {
      console.error('批量获取图片失败:', error)
      return reply.code(500).send({
        success: false,
        message: '批量获取图片失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 获取分类列表
  async getCategories(
    request: FastifyRequest,
    reply: FastifyReply,
  ) {
    try {
      const categories = await adminService.getCategories()
      return void reply.send({
        success: true,
        message: '获取分类列表成功',
        data: categories,
      })
    } catch (error) {
      console.error('获取分类列表失败:', error)
      return reply.code(500).send({
        success: false,
        message: '获取分类列表失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 创建分类
  async createCategory(
    request: FastifyRequest<{ Body: { label: string, value: string, description: string, sortOrder: number } }>,
    reply: FastifyReply,
  ) {
    try {
      const { label, value, description, sortOrder } = request.body
      const result = await adminService.createCategory({ label, value, description, sortOrder })
      return void reply.send({
        success: true,
        message: '创建分类成功',
        data: result,
      })
    } catch (error) {
      console.error('创建分类失败:', error)
      return reply.code(500).send({
        success: false,
        message: '创建分类失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 发布课程包
  async publishCoursePack(
    request: FastifyRequest<{ Body: { coursePackId: string, userId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { coursePackId, userId } = request.body
      const result = await adminService.publishCoursePack(coursePackId, userId)
      return void reply.send({
        success: true,
        message: '发布课程包成功',
        data: result,
      })
    } catch (error) {
      console.error('发布课程包失败:', error)
      return reply.code(500).send({
        success: false,
        message: '发布课程包失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  // 发布课程
  async publishCourse(
    request: FastifyRequest<{ Body: { courseId: string, coursePackId: string, userId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { courseId, coursePackId, userId } = request.body
      const result = await adminService.publishCourse(courseId, coursePackId, userId)
      return void reply.send({
        success: true,
        message: '发布课程成功',
        data: result,
      })
    } catch (error) {
      console.error('发布课程失败:', error)
      return reply.code(500).send({
        success: false,
        message: '发布课程失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  async getCoursePackAll(
    request: FastifyRequest<{ Params: { coursePackId: string } }>,
    reply: FastifyReply,
  ) {
    try {
      const { coursePackId } = request.params
      const result = await adminService.getCoursePack(coursePackId)
      return void reply.send({
        success: true,
        message: '获取课程包成功',
        data: result,
      })
    } catch (error) {
      console.error('获取课程包失败:', error)
      return reply.code(500).send({
        success: false,
        message: '获取课程包失败',
        error: error instanceof Error ? error.message : '未知错误',
      })
    }
  },

  async getCoursePacksByShareLevel(
    request: FastifyRequest,
    reply: FastifyReply,
  ) {
    const { shareLevel } = request.params as { shareLevel: CoursePackShareLevel }
    const result = await adminService.getCoursePacksByShareLevel(shareLevel)
    return reply.send({
      success: true,
      message: `获取${shareLevel}课程包成功`,
      data: result,
    })
  },

  async getCoursePackCoursesDetail(
    request: FastifyRequest<{ Params: { coursePackId: string } }>,
    reply: FastifyReply,
  ) {
    const { coursePackId } = request.params
    const result = await adminService.getCoursePackCourses(coursePackId)
    return reply.send({
      success: true,
      message: '获取课程包中所有课程成功',
      data: result,
    })
  },

  async updateElements(
    request: FastifyRequest<{ Body: { id: string, key: string, value: string }[] }>,
    reply: FastifyReply,
  ) {
    const result = await adminService.updateElements(request.body)
    return reply.send({
      success: true,
      message: '更新elements成功',
      data: result,
    })
  },

  async updateWordDetails(
    request: FastifyRequest<{ Body: { id: string, wordDetails: any[] }[] }>,
    reply: FastifyReply,
  ) {
    const result = await adminService.updateWordDetails(request.body)
    return reply.send({
      success: true,
      message: '更新wordDetails成功',
      data: result,
    })
  },

  async batchUpdateCoursesToGame(
    request: FastifyRequest<{ Body: { courseId: string, coursePackId: string, userId: string }[] }>,
    reply: FastifyReply,
  ) {
    const result = await adminService.updateCoursesToGame(request.body)

    return reply.send({
      success: true,
      message: '更新课程到游戏成功',
      data: result,
    })
  },
}
