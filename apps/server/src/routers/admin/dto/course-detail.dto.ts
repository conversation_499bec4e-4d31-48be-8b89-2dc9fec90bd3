import type { AdminCoursePackWithCourses, CourseDetailDTO, CoursePack, CoursePackShareLevel, ElementDTO, SentenceDTO } from '@julebu/shared'
import type {
  Course,
  CoursePackEntity,
  CoursePackWithCoursesEntity,
} from '../entity/course-detail.entity'
import type { CourseWithSentencesEntity } from '@/routers/course/entity/course.entity'

import { isEmpty, map } from 'lodash-es'

export const courseDetailMapper = {
  toDTO: (courseEntity: CourseWithSentencesEntity): CourseDetailDTO => {
    // 收集所有 sentences 并按顺序排列
    const allSentences: SentenceDTO[] = courseEntity.sentences
      .sort((a, b) => a.position - b.position)
      .map((sentence) => {
        // 获取排序后的elements
        const sortedElements = sentence.elements.sort((a, b) => a.position - b.position)

        // 收集该句子的所有elements
        const elements: ElementDTO[] = sortedElements
          .map(element => ({
            id: element.id,
            content: element.content,
            english: element.english,
            chinese: element.chinese,
            phonetic: element.phonetic,
            type: element.type,
            position: element.position,
            gameId: element.gameId,
            sentenceId: element.sentenceId,
            imageId: element.imageId,
          }))

        return {
          id: sentence.id,
          content: sentence.content,
          chinese: sentence.chinese,
          english: sentence.english,
          position: sentence.position,
          courseId: sentence.courseId,
          startTime: sentence.startTime != null ? Number(sentence.startTime) : null,
          endTime: sentence.endTime != null ? Number(sentence.endTime) : null,
          elements,
        }
      })

    return {
      id: courseEntity.id,
      title: courseEntity.title,
      description: courseEntity.description,
      video: courseEntity.video,
      order: courseEntity.position,
      coursePackId: courseEntity.coursePackId,
      type: courseEntity.type,
      mediaUrl: courseEntity.mediaUrl ?? '',
      sentences: allSentences,
      gameId: courseEntity.gameId,
      createdAt: courseEntity.createdAt,
      updatedAt: courseEntity.updatedAt,
    }
  },
}

export const adminCoursePackMapper = {
  toDTO(coursePackEntity: CoursePackEntity): CoursePack {
    return {
      id: coursePackEntity.id,
      title: coursePackEntity.title,
      description: coursePackEntity.description,
      cover: coursePackEntity.cover,
      gameId: coursePackEntity.gameId,
      shareLevel: coursePackEntity.shareLevel as CoursePackShareLevel,
      categoryId: coursePackEntity.categoryId ?? undefined,
      isPinned: coursePackEntity.isPinned,
      position: coursePackEntity.position,
      isFree: coursePackEntity.isFree,
      createdAt: coursePackEntity.createdAt,
      updatedAt: coursePackEntity.updatedAt,
    }
  },
  withCoursesToDTO(
    coursePackEntity: CoursePackWithCoursesEntity,
  ): AdminCoursePackWithCourses {
    return {
      ...this.toDTO(coursePackEntity),
      courses: map(coursePackEntity.courses, (course: Course) => ({
        id: course.id,
        title: course.title,
        description: course.description,
        type: course.type,
        mediaUrl: course.mediaUrl,
        gameId: course.gameId,
        position: course.position,
        createdAt: course.createdAt,
        updatedAt: course.updatedAt,
        video: '',
        coursePackId: '',
        sentences: [],
      })),
    }
  },
}
