import type { CourseType } from '@julebu/shared'

import type { InferSelectModel } from 'drizzle-orm'
import type { schemas } from '@/db'

// 数据库模型类型
export type BaseCoursePackEntity = InferSelectModel<typeof schemas.coursePack>

export type CoursePackEntity = BaseCoursePackEntity

export interface Course {
  id: string
  title: string
  description: string
  gameId: string
  createdAt: string
  updatedAt: string
  position: number
  type: CourseType
  mediaUrl: string
}

export type CoursePackWithCoursesEntity = BaseCoursePackEntity & {
  courses: Course[]
}
