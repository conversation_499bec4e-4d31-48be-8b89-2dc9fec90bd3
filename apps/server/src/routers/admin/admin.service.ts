import type { AdminCoursePackWithCourses, Course, CourseDetailDTO, CoursePackShareLevel, CreateImageWithTextLinkDto, WordDetail } from '@julebu/shared'
import type { SplitSentenceResult } from '../sentence/split'
import type { CoursePackWithCoursesEntity } from './entity/course-detail.entity'
import { CourseType } from '@julebu/shared'
import { TRPCError } from '@trpc/server'
import { eq } from 'drizzle-orm'
import { isEmpty, map } from 'lodash-es'
import { getDB, schemas } from '@/db'
import { categoriesService } from '../category/categories.service'
import { coursePacksService } from '../course-pack/course-packs.service'
import { courseService } from '../course/course.service'
import { courseMapper } from '../course/dto/course.dto'
import { elementService } from '../element/element.service'
import { imageService } from '../image/image.service'
import { sentenceService } from '../sentence/sentence.service'
import { adminCoursePackMapper, courseDetailMapper } from './dto/course-detail.dto'

// 定义清晰的返回类型
interface DeleteResult {
  sentenceId: string
  success: boolean
  error?: string
}

interface DeleteSentencesResult {
  total: number
  successCount: number
  failCount: number
  failedIds: string[]
  results: DeleteResult[]
}
interface CoursePackData {
  title: string
  description: string
  cover?: string
  categoryId?: string
  shareLevel?: CoursePackShareLevel
  courses?: CourseData[]
}

interface CourseData {
  title: string
  description: string
  sentences?: SentenceData[]
  type?: CourseType
}

interface SentenceData {
  content: string
  translation: string
  elements?: ElementData[]
  image?: {
    fileKey: string
    description: string
    textContent: string
    mimeType: string
    fileSize: number
    width: number
    height: number
    linkType: 'image'
    source: 'user_upload' | 'ai_generated'
  }
}

interface ElementData {
  content: string
  translation: string
  phonetic?: string
}

interface UpsertSentenceData {
  uuid?: string // 用于前端判断更新的哪个句子
  sentenceId?: string // 可选，如果提供则更新，否则创建
  content?: string // 英文内容
  chinese?: string // 中文翻译
}

export interface BatchUpdateCoursePackRequest {
  userId: string
  coursePackId: string
  title?: string
  description?: string
  cover?: string
  shareLevel?: CoursePackShareLevel
  categoryId?: string
}

class AdminService {
  // 默认封面图片列表
  private readonly defaultCovers = [
    'https://earthworm-prod-1312884695.cos.ap-beijing.myqcloud.com/course-packs/1720518714550_default_1.avif',
    'https://earthworm-prod-1312884695.cos.ap-beijing.myqcloud.com/course-packs/1720518714550_default_2.avif',
  ]

  // 随机选择默认封面
  private getRandomDefaultCover(): string {
    const randomIndex = Math.floor(Math.random() * this.defaultCovers.length)
    return this.defaultCovers[randomIndex]
  }

  // 批量创建课程包（包含课程和内容）
  async batchCreateCoursePacks(coursePacks: CoursePackData[], userId: string) {
    const results = []

    // 验证用户是否存在
    await this.validateUser(userId)

    for (const coursePackData of coursePacks) {
      // 如果没有提供封面，使用默认封面
      const cover = coursePackData.cover !== undefined && coursePackData.cover.trim()
        ? coursePackData.cover
        : this.getRandomDefaultCover()

      // 使用coursePackService创建课程包
      const coursePackId = await coursePacksService.create(
        userId,
        coursePackData.title,
        coursePackData.description,
        cover,
        coursePackData.categoryId,
        coursePackData.shareLevel,
      )

      // 如果有课程数据，批量创建课程
      const courseResults = []
      if (coursePackData.courses && coursePackData.courses.length > 0) {
        for (const courseData of coursePackData.courses) {
          const courseResult = await this.createCourseWithContent(
            coursePackId,
            courseData,
            userId,
          )
          courseResults.push(courseResult)
        }
      }

      results.push({
        coursePackId,
        title: coursePackData.title,
        cover, // 返回实际使用的封面（可能是默认的）
        usedDefaultCover: coursePackData.cover === undefined || !coursePackData.cover.trim(), // 标识是否使用了默认封面
        courses: courseResults,
      })
    }

    return results
  }

  // 批量更新课程包
  async batchUpdateCoursePack(coursePacksInfos: BatchUpdateCoursePackRequest[]) {
    const results: BatchUpdateCoursePackRequest[] = []

    // 验证用户是否存在
    await this.validateUser(coursePacksInfos[0].userId)

    const db = getDB()

    try {
      await db.transaction(async (tx) => {
        for (const coursePackInfo of coursePacksInfos) {
          // 验证课程包是否存在
          const existingCoursePack = await tx.query.coursePack.findFirst({
            where: eq(schemas.coursePack.id, coursePackInfo.coursePackId),
          })

          if (!existingCoursePack) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: `课程包 ${coursePackInfo.coursePackId} 不存在`,
            })
          }

          // 更新课程包信息
          await tx.update(schemas.coursePack)
            .set(coursePackInfo)
            .where(eq(schemas.coursePack.id, coursePackInfo.coursePackId))

          results.push(
            coursePackInfo,
          )
        }
      })

      return results
    } catch (error) {
      console.error('批量更新课程包失败:', error)
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '批量更新课程包失败，请稍后重试',
      })
    }
  }

  // 批量更新课程包信息到游戏端

  async batchUpdateCoursePacksToGame(coursePackInfos: { userId: string, coursePackId: string }[]) {
    // 验证用户是否存在
    await this.validateUser(coursePackInfos[0].userId)

    const result: string[] = []

    for (const coursePack of coursePackInfos) {
      try {
        await coursePacksService.updateToGame(coursePack.coursePackId, coursePack.userId)
        result.push(coursePack.coursePackId)
      } catch (error) {
        console.error(`更新课程包 ${coursePack.coursePackId} 到游戏端失败:`, error)
      }
    }
    return result
  }

  // 批量创建课程（在指定课程包下）
  async batchCreateCourses(coursePackId: string, courses: CourseData[], userId: string) {
    // 验证用户和课程包权限
    await this.validateCoursePackAccess(userId, coursePackId)

    const results = []

    for (const courseData of courses) {
      const courseResult = await this.createCourseWithContent(coursePackId, courseData, userId)
      results.push(courseResult)
    }

    return results
  }

  // 批量拆分课程内容（为指定课程添加句子和元素）
  async batchSplitCourse(courseId: string, sentences: SentenceData[], userId: string) {
    // 验证课程权限
    await this.validateCourseAccess(userId, courseId)

    // 1. 创建句子
    const createdSentences = await this.createSentencesFromData(courseId, sentences)

    // 2. 构建句子ID到图片的映射
    const sentenceIdToImageMap = this.buildSentenceImageMap(sentences, createdSentences)

    // 3. 处理元素创建（预定义 or 自动拆分）
    const elementCount = await this.processElements(
      courseId,
      sentences,
      createdSentences,
      sentenceIdToImageMap,
      userId,
    )

    return {
      courseId,
      sentenceCount: createdSentences.length,
      elementCount,
    }
  }

  // 私有方法：从句子数据创建句子
  private async createSentencesFromData(courseId: string, sentences: SentenceData[]): Promise<{
    id: string
    wordDetails: WordDetail[]
  }[]> {
    const sentencesToCreate = sentences.map(sentenceData => ({
      content: sentenceData.content,
      chinese: sentenceData.translation,
    }))

    return sentenceService.createSentences(courseId, sentencesToCreate)
  }

  // 私有方法：构建句子ID到图片的映射
  private buildSentenceImageMap(sentences: SentenceData[], createdSentences: {
    id: string
    wordDetails: WordDetail[]
  }[]) {
    const sentenceIdToImageMap = new Map<string, SentenceData['image']>()

    for (let i = 0; i < sentences.length; i++) {
      const sentenceData = sentences[i]
      if (sentenceData.image) {
        sentenceIdToImageMap.set(createdSentences[i].id, sentenceData.image)
      }
    }

    return sentenceIdToImageMap
  }

  // 私有方法：处理元素创建
  private async processElements(
    courseId: string,
    sentences: SentenceData[],
    createdSentences: {
      id: string
      wordDetails: WordDetail[]
    }[],
    sentenceIdToImageMap: Map<string, SentenceData['image']>,
    userId: string,
  ): Promise<number> {
    const hasPreDefinedElements = sentences.some(s => s.elements && s.elements.length > 0)

    if (hasPreDefinedElements) {
      return this.createPreDefinedElements(sentences, createdSentences)
    } else {
      return this.autoSplitAndProcessImages(courseId, sentenceIdToImageMap, userId)
    }
  }

  // 私有方法：创建预定义的元素
  private async createPreDefinedElements(sentences: SentenceData[], createdSentences: {
    id: string
    wordDetails: WordDetail[]
  }[]): Promise<number> {
    const db = getDB()
    let elementCount = 0

    await db.transaction(async (tx) => {
      for (let i = 0; i < sentences.length; i++) {
        const sentenceData = sentences[i]
        if (sentenceData.elements && sentenceData.elements.length > 0) {
          const sentenceId = createdSentences[i].id

          // 先删除可能已经存在的元素
          await tx.delete(schemas.element).where(eq(schemas.element.sentenceId, sentenceId))

          // 创建预定义的元素
          for (let j = 0; j < sentenceData.elements.length; j++) {
            const elementData = sentenceData.elements[j]
            await tx.insert(schemas.element).values({
              sentenceId,
              content: elementData.content,
              chinese: elementData.translation,
              phonetic: elementData.phonetic ?? '',
              position: j + 1,
            })
            elementCount++
          }
        }
      }
    })

    return elementCount
  }

  // 私有方法：自动拆分并处理图片
  private async autoSplitAndProcessImages(
    courseId: string,
    sentenceIdToImageMap: Map<string, SentenceData['image']>,
    userId: string,
  ): Promise<number> {
    try {
      const splitResults = await sentenceService.splitAll(courseId, userId)
      const elementCount = splitResults.reduce((total, result) => total + result.elements.length, 0)

      // 处理图片关联
      await this.processImageAssociations(splitResults, sentenceIdToImageMap)

      return elementCount
    } catch (error) {
      console.warn('自动拆分失败，句子已创建但未拆分:', error)
      return 0
    }
  }

  // 私有方法：处理图片关联
  private async processImageAssociations(
    splitResults: (SplitSentenceResult & {
      sentenceId: string
    })[],
    sentenceIdToImageMap: Map<string, SentenceData['image']>,
  ) {
    const imageWithElementIdList: { elementId: string, image: SentenceData['image'] }[] = []

    // 收集需要关联图片的元素
    for (const { elements, sentenceId } of splitResults) {
      if (elements.length === 0)
        continue

      const lastElement = elements[elements.length - 1]
      const image = sentenceIdToImageMap.get(sentenceId)

      if (image) {
        imageWithElementIdList.push({
          elementId: lastElement.id,
          image,
        })
      }
    }

    // 批量处理图片关联
    if (imageWithElementIdList.length > 0) {
      await this.batchAssociateImages(imageWithElementIdList)
    }
  }

  // 私有方法：批量关联图片
  private async batchAssociateImages(imageWithElementIdList: { elementId: string, image: SentenceData['image'] }[]) {
    const db = getDB()

    const imageIds = await this.batchRecordImage(
      map(imageWithElementIdList, item => ({
        ...item.image!,
        description: item.image!.description.slice(0, 499),
      })),
    )

    await db.transaction(async (tx) => {
      const updatePromises = imageWithElementIdList.map((item, index) =>
        tx.update(schemas.element)
          .set({ imageId: imageIds[index] })
          .where(eq(schemas.element.id, item.elementId)),
      )

      await Promise.all(updatePromises)
    })
  }

  // 批量处理（加工）课程包中所有课程的句子
  async batchProcessAllCourses(coursePackId: string, userId: string) {
    // 验证课程包权限
    await this.validateCoursePackAccess(userId, coursePackId)

    // 获取课程包信息（包含课程列表）
    const coursePack = await coursePacksService.findOne(coursePackId, userId)
    const courses = coursePack.courses

    if (courses.length === 0) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '课程包下没有找到任何课程',
      })
    }

    const results = []
    let totalProcessedCount = 0

    // 逐个处理每个课程
    for (const course of courses) {
      try {
        // 调用sentenceService的processAll方法
        const processResults = await sentenceService.processAll(course.id, userId)

        const processedCount = processResults.length

        results.push({
          courseId: course.id,
          courseTitle: course.title,
          processedCount,
          success: true,
        })

        totalProcessedCount += processedCount
      } catch (error) {
        console.error(`处理课程 ${course.id} 失败:`, error)
        results.push({
          courseId: course.id,
          courseTitle: course.title,
          processedCount: 0,
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
        })
      }
    }

    return {
      coursePackId,
      totalCourses: courses.length,
      totalProcessedCount,
      results,
    }
  }

  // 批量拆分课程包中所有课程的句子
  async batchSplitAllCourses(coursePackId: string, userId: string) {
    // 验证课程包权限
    await this.validateCoursePackAccess(userId, coursePackId)

    // 获取课程包信息（包含课程列表）
    const coursePack = await coursePacksService.findOne(coursePackId, userId)
    const courses = coursePack.courses

    if (courses.length === 0) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '课程包下没有找到任何课程',
      })
    }

    const results = []
    let totalSentenceCount = 0
    let totalElementCount = 0

    // 逐个处理每个课程
    for (const course of courses) {
      try {
        // 调用sentenceService的splitAll方法
        const splitResults = await sentenceService.splitAll(course.id, userId)

        const sentenceCount = splitResults.length
        const elementCount = splitResults.reduce((total, result) => total + result.elements.length, 0)

        results.push({
          courseId: course.id,
          courseTitle: course.title,
          sentenceCount,
          elementCount,
          success: true,
        })

        totalSentenceCount += sentenceCount
        totalElementCount += elementCount
      } catch (error) {
        console.error(`拆分课程 ${course.id} 失败:`, error)
        results.push({
          courseId: course.id,
          courseTitle: course.title,
          sentenceCount: 0,
          elementCount: 0,
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
        })
      }
    }

    return {
      coursePackId,
      totalCourses: courses.length,
      totalSentenceCount,
      totalElementCount,
      results,
    }
  }

  // 获取用户的课程包列表
  async getUserCoursePacks(userId: string) {
    // 验证用户是否存在
    await this.validateUser(userId)

    // 使用coursePackService获取课程包列表
    return coursePacksService.findAll(userId)
  }

  // 私有方法：创建课程并添加内容
  private async createCourseWithContent(
    coursePackId: string,
    courseData: CourseData,
    userId: string,
  ) {
    // 使用courseService创建课程
    const courseId = await courseService.create({
      coursePackId,
      title: courseData.title,
      description: courseData.description,
      type: courseData.type || CourseType.normal,
      userId,
    })

    let sentenceCount = 0
    let elementCount = 0

    // 如果有句子数据，使用sentenceService创建句子（包含LLM处理）
    if (courseData.sentences && courseData.sentences.length > 0) {
      const sentencesToCreate = courseData.sentences.map(sentenceData => ({
        content: sentenceData.content,
        chinese: sentenceData.translation,
      }))

      // 使用现有的sentenceService，它会自动处理LLM调用、翻译等
      const createdSentences = await sentenceService.createSentences(courseId, sentencesToCreate)
      sentenceCount = createdSentences.length

      // 如果有预定义的元素数据，需要手动创建元素
      if (courseData.sentences.some(s => s.elements && s.elements.length > 0)) {
        const db = getDB()
        await db.transaction(async (tx) => {
          for (let i = 0; i < courseData.sentences!.length; i++) {
            const sentenceData = courseData.sentences![i]
            if (sentenceData.elements && sentenceData.elements.length > 0) {
              const sentenceId = createdSentences[i].id

              // 先删除可能已经存在的元素（如果sentenceService自动创建了）
              await tx.delete(schemas.element).where(eq(schemas.element.sentenceId, sentenceId))

              // 创建预定义的元素
              for (let j = 0; j < sentenceData.elements.length; j++) {
                const elementData = sentenceData.elements[j]
                await tx.insert(schemas.element).values({
                  sentenceId,
                  content: elementData.content,
                  chinese: elementData.translation,
                  phonetic: elementData.phonetic ?? '',
                  position: j + 1,
                })
                elementCount++
              }
            }
          }
        })
      }
    }

    return {
      courseId,
      title: courseData.title,
      sentenceCount,
      elementCount,
    }
  }

  // 私有方法：验证用户ID格式
  private async validateUser(userId: string) {
    if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: '用户ID不能为空',
      })
    }
  }

  // 私有方法：验证课程包访问权限
  private async validateCoursePackAccess(userId: string, coursePackId: string) {
    try {
      // 使用coursePackService验证权限
      await coursePacksService.findOne(coursePackId, userId)
    } catch {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '课程包不存在或无权限访问',
      })
    }
  }

  // 私有方法：验证课程访问权限
  private async validateCourseAccess(userId: string, courseId: string) {
    const db = getDB()
    const course = await db.query.course.findFirst({
      where: eq(schemas.course.id, courseId),
      with: {
        coursePack: {
          columns: {
            userId: true,
          },
        },
      },
    })

    if (!course || course.coursePack.userId !== userId) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '课程不存在或无权限访问',
      })
    }
  }

  // 更新课程包封面
  async updateCoursePackCover(coursePackId: string, userId: string, coverUrl: string) {
    const db = getDB()

    // 验证课程包权限
    await this.validateCoursePackAccess(userId, coursePackId)

    // 更新课程包封面（updatedAt会自动更新）
    await db
      .update(schemas.coursePack)
      .set({
        cover: coverUrl,
      })
      .where(eq(schemas.coursePack.id, coursePackId))

    // 获取更新后的课程包
    const updatedCoursePack = await db.query.coursePack.findFirst({
      where: eq(schemas.coursePack.id, coursePackId),
    })

    if (!updatedCoursePack) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '课程包不存在或更新失败',
      })
    }

    return updatedCoursePack
  }

  // 生成课程知识点讲解
  async generateLearningContent(courseId: string, userId: string) {
    // 验证课程权限
    await this.validateCourseAccess(userId, courseId)

    // 调用sentenceService生成知识点讲解
    const results = await sentenceService.generateLearningContentAll(courseId, userId)

    // 统计结果
    const successCount = results.filter(result => result.learningContent !== null).length
    const failedCount = results.filter(result => result.learningContent === null).length
    const failedSentenceIds = results
      .filter(result => result.learningContent === null)
      .map(result => result.id)

    return {
      courseId,
      totalSentences: results.length,
      successCount,
      failedCount,
      failedSentenceIds,
      results,
    }
  }

  // 获取指定课程包下课程列表
  async getCoursesByCoursePackId(coursePackId: string, userId: string): Promise<Course[]> {
    await this.validateCoursePackAccess(userId, coursePackId)
    const coursePack = await coursePacksService.findOne(coursePackId, userId)
    if (isEmpty(coursePack) || isEmpty(coursePack.courses)) {
      return []
    }

    // 按position排序coursePack.courses
    const sortedCourses = coursePack.courses.sort((a, b) => a.position - b.position)

    // 获取每个课程的完整数据
    const fullCourses = []
    for (const course of sortedCourses) {
      const fullCourse = await courseService.findOne(course.id, coursePackId, userId)
      fullCourses.push(courseMapper.toDTO(fullCourse))
    }

    return fullCourses
  }

  // Admin 专用的课程包查询方法
  private async findCoursePackWithFullCourses(coursePackId: string, userId: string): Promise<CoursePackWithCoursesEntity> {
    const db = getDB()

    const coursePack = await db.query.coursePack.findFirst({
      where: (coursePacks, { eq, and }) =>
        and(eq(coursePacks.id, coursePackId), eq(coursePacks.userId, userId)),
      with: {
        courses: {
          columns: {
            id: true,
            title: true,
            description: true,
            cover: true,
            type: true,
            mediaUrl: true,
            gameId: true,
            position: true,
            createdAt: true,
            updatedAt: true,
          },
          orderBy: (courses, { asc }) => [asc(courses.position)],
        },
      },
    })

    if (!coursePack) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '课程包不存在',
      })
    }

    return coursePack as CoursePackWithCoursesEntity
  }

  // 获取课程包完整信息（包含课程列表）
  async getCoursePackWithCourses(coursePackId: string, userId: string): Promise<AdminCoursePackWithCourses> {
    await this.validateCoursePackAccess(userId, coursePackId)
    const coursePack = await this.findCoursePackWithFullCourses(coursePackId, userId)
    return adminCoursePackMapper.withCoursesToDTO(coursePack)
  }

  // 获取课程详情（含 statements）
  async getCourseDetailWithStatements(coursePackId: string, courseId: string, userId: string): Promise<CourseDetailDTO> {
    // 验证课程包访问权限
    await this.validateCoursePackAccess(userId, coursePackId)

    // 使用现有的 courseService.findOne 获取完整课程数据
    const course = await courseService.findOne(courseId, coursePackId, userId)
    // 使用 DTO 映射为目标结构
    const courseDetail = courseDetailMapper.toDTO(course)
    return courseDetail
  }

  // 批量创建或更新句子
  async batchUpsertSentences(courseId: string, sentences: UpsertSentenceData[], userId: string) {
    // 验证课程权限
    await this.validateCourseAccess(userId, courseId)

    return sentenceService.batchUpsertSentences(courseId, sentences, userId)
  }

  // 批量处理单个课程的所有句子
  async processAllSentences(courseId: string, userId: string) {
    // 验证课程权限
    await this.validateCourseAccess(userId, courseId)

    return sentenceService.batchProcessSentences(courseId, userId)
  }

  // 批量调整课程内句子顺序
  async moveSentencePositions(courseId: string, affectedSentences: Array<{ position: number, sentenceId: string }>) {
    return sentenceService.movePosition(courseId, affectedSentences)
  }

  // 批量删除句子
  async deleteSentences(sentenceList: { sentenceId: string, courseId: string }[]): Promise<DeleteSentencesResult> {
    return sentenceService.deleteBatchSentences(sentenceList)
  }

  // 批量更新句子元素时间
  async updateElementsTime(updates: { sentenceId: string, startTime: number, endTime: number }[]) {
    return sentenceService.batchUpdateElementsTime(updates)
  }

  // 批量记录image 到 images和text_image_links 表
  async batchRecordImage(images: CreateImageWithTextLinkDto[]) {
    return imageService.batchCreateImageWithTextLink(images)
  }

  // 获取课程详情
  async getCourse(courseId: string) {
    const db = getDB()

    // 先获取课程的基本信息来得到 coursePackId
    const basicCourse = await db.query.course.findFirst({
      where: eq(schemas.course.id, courseId),
      columns: { coursePackId: true },
      with: {
        coursePack: {
          columns: { userId: true },
        },
      },
    })

    if (!basicCourse) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '课程不存在',
      })
    }

    // 使用现有的 courseService.findOne 方法
    const course = await courseService.findOne(
      courseId,
      basicCourse.coursePackId,
      basicCourse.coursePack.userId,
    )

    // 添加 coursePack 信息（如果需要的话）
    return {
      ...course,
      coursePack: basicCourse.coursePack,
    }
  }

  // 更新句子翻译
  async updateSentenceTranslation(sentenceId: string, translation: string, userId: string) {
    await this.validateUser(userId)

    const db = getDB()

    // 验证句子存在并且用户有权限
    const sentence = await db.query.sentence.findFirst({
      where: eq(schemas.sentence.id, sentenceId),
      with: {
        course: {
          with: {
            coursePack: {
              columns: { userId: true },
            },
          },
        },
      },
    })

    if (!sentence || sentence.course.coursePack.userId !== userId) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '句子不存在或无权限访问',
      })
    }

    // 使用现有的 sentenceService.changeChinese 方法
    await sentenceService.changeChinese(sentenceId, translation)

    return { sentenceId, translation }
  }

  // 更新元素翻译
  async updateElementTranslation(elementId: string, translation: string, userId: string) {
    await this.validateUser(userId)

    const db = getDB()

    // 验证元素存在并且用户有权限
    const element = await db.query.element.findFirst({
      where: eq(schemas.element.id, elementId),
      with: {
        sentence: {
          with: {
            course: {
              with: {
                coursePack: {
                  columns: { userId: true },
                },
              },
            },
          },
        },
      },
    })

    if (!element || element.sentence.course.coursePack.userId !== userId) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '元素不存在或无权限访问',
      })
    }

    // 使用现有的 elementService.updateProp 方法
    await elementService.updateProp(elementId, 'chinese', translation)

    return { elementId, translation }
  }

  // 更新课程包
  async updateCoursePack(coursePackId: string, updates: { title?: string, description?: string }, userId: string) {
    await this.validateUser(userId)
    await this.validateCoursePackAccess(userId, coursePackId)

    // 如果没有需要更新的字段，直接返回
    if (updates.title == null && updates.description == null) {
      return { coursePackId, message: '没有需要更新的字段' }
    }

    // 获取当前课程包信息
    const coursePack = await coursePacksService.findOne(coursePackId, userId)

    // 使用现有的 coursePacksService.update 方法
    await coursePacksService.update(
      userId,
      coursePackId,
      updates.title ?? coursePack.title,
      updates.description ?? coursePack.description,
      coursePack.cover,
      coursePack.shareLevel as CoursePackShareLevel,
      coursePack.categoryId ?? undefined,
    )

    return { coursePackId, updates }
  }

  // 更新课程
  async updateCourse(courseId: string, updates: { title?: string, description?: string, mediaUrl?: string, type?: CourseType }, userId: string) {
    await this.validateUser(userId)
    await this.validateCourseAccess(userId, courseId)

    // 如果没有需要更新的字段，直接返回
    if (updates.title == null && updates.description == null && updates.mediaUrl == null && updates.type == null) {
      return { courseId, message: '没有需要更新的字段' }
    }

    // 获取课程信息
    const db = getDB()
    const course = await db.query.course.findFirst({
      where: eq(schemas.course.id, courseId),
    })

    if (!course) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: '课程不存在',
      })
    }

    // 使用现有的 courseService.update 方法
    await courseService.update({
      courseId,
      coursePackId: course.coursePackId,
      title: updates.title ?? course.title,
      description: updates.description ?? course.description,
      mediaUrl: updates.mediaUrl ?? course.mediaUrl ?? undefined,
      type: updates.type ?? course.type,
      userId,
    })

    return { courseId, updates }
  }

  // 更新课程到游戏端
  async updateCourseToGame(courseId: string, coursePackId: string, userId: string) {
    await this.validateUser(userId)
    await this.validateCourseAccess(userId, courseId)

    return courseService.updateToGame(courseId, coursePackId, userId)
  }

  // 发布课程到游戏端
  async publishCourseToGame(courseId: string, coursePackId: string, userId: string) {
    await this.validateUser(userId)
    await this.validateCourseAccess(userId, courseId)

    return courseService.publishToGame(courseId, coursePackId, userId)
  }

  // 根据英文批量获取图片
  async batchGetImagesByEnglish(english: string[]) {
    return imageService.batchGetImagesByEnglish(english)
  }

  // 获取分类列表
  async getCategories() {
    return categoriesService.findAll()
  }

  // 创建分类
  async createCategory(category: {
    label: string
    value: string
    description: string
    sortOrder: number
  }) {
    return categoriesService.create(category)
  }

  // 发布课程包
  async publishCoursePack(coursePackId: string, userId: string) {
    return coursePacksService.publishToGame(coursePackId, userId)
  }

  // 发布课程
  async publishCourse(courseId: string, coursePackId: string, userId: string) {
    return courseService.publishToGame(courseId, coursePackId, userId)
  }

  // 获取课程包详情
  async getCoursePack(coursePackId: string) {
    const db = getDB()

    const courses = await db.query.course.findMany({
      where: (course, { eq, and }) =>
        and(eq(course.coursePackId, coursePackId)),
      with: {
        sentences: {
          with: {
            elements: {
              with: {
                image: true,
              },
              orderBy: (elements, { asc }) => [asc(elements.position)],
            },
          },
          orderBy: (sentences, { asc }) => [asc(sentences.position)],
        },
      },
    })

    const { userId } = await db.query.coursePack.findFirst({
      where: eq(schemas.coursePack.id, coursePackId),
      columns: { userId: true },
    }) || {}

    if (!courses) {
      throw new TRPCError({ code: 'NOT_FOUND', message: '课程不存在' })
    }

    return {
      courses,
      userId,
    }
  }

  async getCoursePacksByShareLevel(shareLevel: CoursePackShareLevel) {
    const db = getDB()

    const coursePacks = await db.query.coursePack.findMany({
      where: eq(schemas.coursePack.shareLevel, shareLevel),
    })

    return coursePacks
  }

  // 获取课程包中所有课程
  async getCoursePackCourses(coursePackId: string) {
    const db = getDB()
    const courses = await db.query.course.findMany({
      where: eq(schemas.course.coursePackId, coursePackId),
    })

    return courses
  }

  // 更新elements
  async updateElements(elements: { id: string, key: string, value: string }[]) {
    const db = getDB()

    await db.transaction(async (tx) => {
      for (const element of elements) {
        const setObj = {
          [element.key]: element.value,
        }

        // 如果是content，也需要更新english
        if (element.key === 'content') {
          setObj.english = element.value
        }
        await tx.update(schemas.element).set(setObj).where(eq(schemas.element.id, element.id))
      }
    })

    return elements
  }

  async updateWordDetails(wordDetails: { id: string, wordDetails: any[] }[]) {
    const db = getDB()

    await db.transaction(async (tx) => {
      for (const wordDetail of wordDetails) {
        if (wordDetail.wordDetails.length === 0) {
          continue
        }
        await tx.update(schemas.sentence).set({ wordDetails: wordDetail.wordDetails }).where(eq(schemas.sentence.id, wordDetail.id))
      }
    })

    return wordDetails
  }

  async updateCoursesToGame(courses: { courseId: string, coursePackId: string, userId: string }[]) {
    for (const course of courses) {
      await courseService.updateToGame(course.courseId, course.coursePackId, course.userId)
    }
    return courses
  }
}

export const adminService = new AdminService()
