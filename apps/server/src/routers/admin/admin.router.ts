import type { FastifyInstance } from 'fastify'
import { adminController } from './admin.controller'
import { uploadController } from './upload.controller'

export async function adminRoutes(fastify: FastifyInstance) {
  // 文件上传接口
  fastify.post('/admin/upload/cover', uploadController.uploadCover.bind(uploadController))
  fastify.post('/admin/upload/covers', uploadController.uploadMultipleCovers.bind(uploadController))

  // 高级接口（可选）
  fastify.get('/admin/upload/cover/credentials', uploadController.getCoverUploadCredentials.bind(uploadController))
  fastify.post('/admin/upload/cover/validate', uploadController.validateUploadResult.bind(uploadController))

  // 批量创建课程包（包含课程和内容）
  fastify.post('/admin/course-packs/batch', adminController.batchCreateCoursePacks.bind(adminController))

  // 批量更新课程包信息
  fastify.put('/admin/course-packs/batch', adminController.batchUpdateCoursePack.bind(adminController))

  // 批量更新课程包信息到游戏端
  fastify.put('/admin/course-packs/update-to-game/batch', adminController.batchUpdateCoursePacksToGame.bind(adminController))

  // 批量创建课程（在指定课程包下）
  fastify.post('/admin/courses/batch', adminController.batchCreateCourses.bind(adminController))

  // 批量拆分课程内容（为指定课程添加句子和元素）
  fastify.post('/admin/courses/split', adminController.batchSplitCourse.bind(adminController))

  // 批量处理（加工）课程包中所有课程的句子
  fastify.post('/admin/course-packs/process-all', adminController.batchProcessAllCourses.bind(adminController))

  // 批量拆分课程包中所有课程的句子
  fastify.post('/admin/course-packs/split-all', adminController.batchSplitAllCourses.bind(adminController))

  // 生成课程知识点讲解
  fastify.post('/admin/courses/generate-learning-content', adminController.generateLearningContent.bind(adminController))

  // 获取用户的课程包列表
  fastify.get('/admin/course-packs', adminController.getUserCoursePacks.bind(adminController))

  // 获取指定课程包下课程列表
  fastify.get('/admin/course-packs/:coursePackId/courses', adminController.getCoursesByCoursePackId.bind(adminController))

  // 获取课程包完整信息（包含课程列表）
  fastify.get('/admin/course-packs/:coursePackId', adminController.getCoursePackWithCourses.bind(adminController))

  // 获取课程详情（含 statements） - 已迁移到 tRPC
  fastify.get('/admin/course-packs/:coursePackId/courses/:courseId/detail', adminController.getCourseDetailWithStatements.bind(adminController))

  // 批量创建或更新句子
  fastify.post('/admin/sentences/batch', adminController.batchUpsertSentences.bind(adminController))

  // 批量处理单个课程的所有句子
  fastify.post('/admin/sentences/process-all', adminController.adminProcessAllSentences.bind(adminController))

  // 批量调整课程内句子顺序
  fastify.post('/admin/sentences/move-position', adminController.adminMoveSentencePositions.bind(adminController))

  // 批量删除句子
  fastify.post('/admin/sentences/delete-batch', adminController.adminDeleteSentences.bind(adminController))

  // 批量更新句子元素时间
  fastify.post('/admin/sentences/update-elements-time', adminController.adminUpdateElementsTime.bind(adminController))

  // 上传单个媒体文件，返回S3预签名URL
  fastify.post('/admin/upload/media', adminController.getMediaPresignedUrlForAdmin.bind(adminController))

  // 管理员发布课程到游戏端
  fastify.post('/admin/courses/publish-to-game', adminController.publishCourseToGame.bind(adminController))

  // 管理员更新课程到游戏端
  fastify.post('/admin/courses/update-to-game', adminController.updateCourseToGame.bind(adminController))

  // 批量记录image 到 images和text_image_links 表
  fastify.post('/admin/image-record/batch', adminController.batchRecordImage.bind(adminController))

  // 获取课程详情
  fastify.get('/admin/courses/:courseId', adminController.getCourse.bind(adminController))

  // 更新句子翻译
  fastify.post('/admin/sentences/update-translation', adminController.updateSentenceTranslation.bind(adminController))

  // 更新元素翻译
  fastify.post('/admin/elements/update-translation', adminController.updateElementTranslation.bind(adminController))

  // 更新课程包
  fastify.put('/admin/course-packs/:coursePackId', adminController.updateCoursePack.bind(adminController))

  // 获取整个课程包的所有内容
  fastify.get('/admin/course-packs/:coursePackId/all', adminController.getCoursePackAll.bind(adminController))

  // 更新课程
  fastify.put('/admin/courses/:courseId', adminController.updateCourse.bind(adminController))

  // 根据分享级别获取课程包列表
  fastify.get('/admin/course-packs/share-level/:shareLevel', adminController.getCoursePacksByShareLevel.bind(adminController))

  // 获取指定课程包中所有课程详情，包含wordDetails，learnContent等等
  fastify.get('/admin/course-packs/:coursePackId/courses/detail', adminController.getCoursePackCoursesDetail.bind(adminController))

  // 更新elements
  fastify.post('/admin/elements/update', adminController.updateElements.bind(adminController))

  // 更新wordDetails
  fastify.post('/admin/sentences/update-word-details', adminController.updateWordDetails.bind(adminController))

  // 批量更新课程到游戏端
  fastify.post('/admin/courses/update-to-game/batch', adminController.batchUpdateCoursesToGame.bind(adminController))

  fastify.post('/admin/images/batch-by-english', adminController.batchGetImagesByEnglish.bind(adminController))

  // 获取分类列表
  fastify.get('/admin/categories', adminController.getCategories.bind(adminController))

  // 创建分类
  fastify.post('/admin/categories', adminController.createCategory.bind(adminController))

  // 发布课程包
  fastify.post('/admin/courses-packs/publish', adminController.publishCoursePack.bind(adminController))

  // 发布课程
  fastify.post('/admin/courses/publish', adminController.publishCourse.bind(adminController))
}
