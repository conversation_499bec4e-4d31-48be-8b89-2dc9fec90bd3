import zlib from 'node:zlib'
import { CoursePackShareLevel } from '@julebu/shared'
import { TRPCError } from '@trpc/server'
import { and, eq, gt, inArray, sql } from 'drizzle-orm'
import { getDB, schemas } from '@/db'
import {
  publishCoursePackToGameApi,
  unpublishCoursePackToGameApi,
  updateCoursePackToGameApi,
} from '@/services/game/api/coursePack'
import { courseService } from '../course/course.service'

async function unpublishFromGame(coursePackId: string, userId: string) {
  const db = getDB()
  const coursePack = await db.query.coursePack.findFirst({
    where: and(
      eq(schemas.coursePack.id, coursePackId),
      eq(schemas.coursePack.userId, userId),
    ),
  })

  if (!coursePack) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '课程包不存在或不属于当前用户',
    })
  }

  try {
    const result = await unpublishCoursePackToGameApi(
      coursePack.gameId,
      userId,
    )

    if (result) {
      await db
        .update(schemas.coursePack)
        .set({
          gameId: '',
        })
        .where(eq(schemas.coursePack.id, coursePackId))

      return true
    }
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '下架课程包失败',
      cause: error,
    })
  }
}

async function create(
  userId: string,
  title: string,
  description: string,
  cover: string,
  categoryId?: string,
  shareLevel?: CoursePackShareLevel,
) {
  try {
    const db = getDB()

    const maxPositionResult = await db.query.coursePack.findFirst({
      where: (coursePacks, { eq }) => eq(coursePacks.userId, userId),
      orderBy: (coursePacks, { desc }) => [desc(coursePacks.position)],
    })

    const nextPosition = (maxPositionResult?.position ?? 0) + 1

    const [coursePack] = await db
      .insert(schemas.coursePack)
      .values({
        userId,
        title,
        description,
        isFree: true,
        cover,
        categoryId,
        shareLevel: shareLevel || CoursePackShareLevel.Private,
        position: nextPosition,
      })
      .$returningId()

    return coursePack.id
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '创建课程包失败',
      cause: error,
    })
  }
}

async function findAll(userId: string) {
  const db = getDB()

  const coursePacks = await db.query.coursePack.findMany({
    where: (coursePacks, { eq }) => eq(coursePacks.userId, userId),
    orderBy: (coursePacks, { asc }) => [asc(coursePacks.position)],
  })

  return coursePacks
}

async function findOne(coursePackId: string, userId: string) {
  const db = getDB()

  const coursePack = await db.query.coursePack.findFirst({
    where: (coursePacks, { eq, and }) =>
      and(eq(coursePacks.id, coursePackId), eq(coursePacks.userId, userId)),
    with: {
      courses: {
        columns: {
          id: true,
          title: true,
          description: true,
          createdAt: true,
          updatedAt: true,
          gameId: true,
          position: true,
          mediaUrl: true,
          type: true,
        },
        orderBy: (courses, { asc }) => [asc(courses.position)],
      },
    },
  })

  if (!coursePack) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '课程包不存在',
    })
  }

  return coursePack
}

async function deleteOne(coursePackId: string, userId: string) {
  const db = getDB()

  return db.transaction(async (tx) => {
    // 1. 检查课程包是否存在
    const coursePack = await tx.query.coursePack.findFirst({
      where: (coursePacks, { eq, and }) =>
        and(eq(coursePacks.id, coursePackId), eq(coursePacks.userId, userId)),
    })

    if (!coursePack) {
      throw new TRPCError({ code: 'NOT_FOUND', message: '课程包不存在' })
    }

    // 2. 获取所有相关课程的ID
    const courses = await tx.query.course.findMany({
      where: eq(schemas.course.coursePackId, coursePackId),
      columns: { id: true },
    })
    const courseIds = courses.map(course => course.id)

    // 3. 获取所有相关句子的ID
    const sentences = await tx.query.sentence.findMany({
      where: inArray(schemas.sentence.courseId, courseIds),
      columns: { id: true },
    })
    const sentenceIds = sentences.map(sentence => sentence.id)

    // 4. 按照依赖关系顺序删除数据
    // 4.1 删除元素
    await tx
      .delete(schemas.element)
      .where(inArray(schemas.element.sentenceId, sentenceIds))

    // 4.2 删除句子
    await tx
      .delete(schemas.sentence)
      .where(inArray(schemas.sentence.courseId, courseIds))

    // 4.3 删除课程
    await tx
      .delete(schemas.course)
      .where(eq(schemas.course.coursePackId, coursePackId))

    // 4.4 删除课程包
    await tx
      .delete(schemas.coursePack)
      .where(eq(schemas.coursePack.id, coursePackId))

    // 5. 更新被删除课程包后面的所有课程包的 position
    await tx
      .update(schemas.coursePack)
      .set({
        position: sql`${schemas.coursePack.position} - 1`,
      })
      .where(
        and(
          gt(schemas.coursePack.position, coursePack.position),
          eq(schemas.coursePack.userId, userId),
        ),
      )

    return true
  })
}

async function update(
  userId: string,
  coursePackId: string,
  title: string,
  description: string,
  cover: string,
  shareLevel: CoursePackShareLevel,
  categoryId?: string,
) {
  const db = getDB()

  await db
    .update(schemas.coursePack)
    .set({
      title,
      description,
      cover,
      shareLevel,
      categoryId,
    })
    .where(
      and(
        eq(schemas.coursePack.id, coursePackId),
        eq(schemas.coursePack.userId, userId),
      ),
    )

  return true
}

async function pinOne(coursePackId: string, userId: string) {
  const db = getDB()

  await db
    .update(schemas.coursePack)
    .set({ isPinned: true })
    .where(
      and(
        eq(schemas.coursePack.id, coursePackId),
        eq(schemas.coursePack.userId, userId),
      ),
    )
}

async function unpinOne(coursePackId: string, userId: string) {
  const db = getDB()

  await db
    .update(schemas.coursePack)
    .set({ isPinned: false })
    .where(
      and(
        eq(schemas.coursePack.id, coursePackId),
        eq(schemas.coursePack.userId, userId),
      ),
    )
}

/**
 * 检查课程包下所有课程是否需要升级
 * @param coursePackId 课程包ID
 * @param userId 用户ID
 * @returns 所有需要升级的课程信息
 */
async function checkCoursesNeedUpgrade(coursePackId: string, userId: string) {
  const db = getDB()

  // 检查课程包是否存在
  const coursePack = await db.query.coursePack.findFirst({
    where: (coursePacks, { eq, and }) =>
      and(eq(coursePacks.id, coursePackId), eq(coursePacks.userId, userId)),
    with: {
      courses: {
        columns: {
          id: true,
          title: true,
          description: true,
          position: true,
        },
        orderBy: (courses, { asc }) => [asc(courses.position)],
      },
    },
  })

  if (!coursePack) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '课程包不存在',
    })
  }

  // 检查每个课程是否需要升级
  const upgradeChecks = await Promise.all(
    coursePack.courses.map(async (course) => {
      try {
        const checkResult = await courseService.checkCourseNeedsUpgrade(
          course.id,
          coursePackId,
          userId,
        )

        return {
          ...course,
          needsUpgrade: checkResult.needsUpgrade,
          upgradeDetails: checkResult,
        }
      } catch (error) {
        console.error(`检查课程 ${course.id} 升级状态失败:`, error)
        return {
          ...course,
          needsUpgrade: false,
          error: error instanceof Error ? error.message : String(error),
        }
      }
    }),
  )

  // 过滤出需要升级的课程
  const coursesNeedingUpgrade = upgradeChecks.filter(
    course => course.needsUpgrade,
  )

  return {
    totalCourses: coursePack.courses.length,
    coursesNeedingUpgrade: coursesNeedingUpgrade.length,
    courses: upgradeChecks,
  }
}

export const coursePacksService = {
  findOne,
  findAll,
  create,
  deleteOne,
  update,
  pinOne,
  unpinOne,
  unpublishFromGame,
  updateToGame,
  publishToGame,
  checkCoursesNeedUpgrade,
}

async function publishToGame(coursePackId: string, userId: string) {
  try {
    const db = getDB()
    const coursePack = await db.query.coursePack.findFirst({
      where: (coursePacks, { eq, and }) =>
        and(eq(coursePacks.id, coursePackId), eq(coursePacks.userId, userId)),
    })

    if (!coursePack) {
      throw new TRPCError({ code: 'NOT_FOUND', message: '课程包不存在' })
    }

    // 构建发送给游戏端的课程包数据，只包含分类的 value
    const gameCoursePack = {
      ...coursePack,
      category: coursePack.categoryId,
    }

    // 发布当前的课程包
    const responseData = await publishCoursePackToGameApi(gameCoursePack, userId)

    if (responseData.status !== 1) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '发布课程包失败',
      })
    }

    // 更新课程包的发布状态和ID
    await db
      .update(schemas.coursePack)
      .set({
        gameId: responseData.data.coursePackId,
      })
      .where(
        and(
          eq(schemas.coursePack.id, coursePack.id),
          eq(schemas.coursePack.userId, coursePack.userId),
        ),
      )

    return responseData.data
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '发布课程包失败',
      cause: error,
    })
  }
}

async function updateToGame(coursePackId: string, userId: string) {
  const db = getDB()
  const coursePack = await db.query.coursePack.findFirst({
    where: (coursePacks, { eq, and }) =>
      and(eq(coursePacks.id, coursePackId), eq(coursePacks.userId, userId)),
    with: {
      courses: {
        columns: {
          gameId: true,
          position: true,
        },
        orderBy: (courses, { asc }) => [asc(courses.position)],
      },
    },
  })

  if (!coursePack) {
    throw new TRPCError({ code: 'NOT_FOUND', message: '课程包不存在' })
  }

  const toGameCoursePack = {
    title: coursePack.title,
    description: coursePack.description,
    cover: coursePack.cover,
    userId: coursePack.userId,
    shareLevel: coursePack.shareLevel,
    category: coursePack.categoryId,
    courses: coursePack.courses.filter((item) => {
      return item.gameId !== ''
    }),
  }

  try {
    // 压缩数据
    const compressedData = zlib.gzipSync(
      JSON.stringify({ coursePack: toGameCoursePack, userId }),
    )
    const responseData = await updateCoursePackToGameApi(
      coursePack.gameId,
      compressedData,
    )

    if (responseData.status !== 1) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '更新课程包失败',
      })
    }

    if (responseData.data === false) {
      // 课程包没有变化 无需更新
      // 不抛出 trpcError 由客户端自行处理
      return false
    }

    // return responseData.data;
    return true
  } catch (error) {
    // 检查是否已经是 TRPCError
    if (error instanceof TRPCError) {
      throw error
    }

    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '更新课程包失败',
      cause: error,
    })
  }
}
