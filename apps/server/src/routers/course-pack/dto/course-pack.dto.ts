import type {
  CoursePack,
  CoursePackShareLevel,
  CoursePackWithCourses,
} from '@julebu/shared'
import type {
  CoursePackEntity,
  CoursePackWithCoursesEntity,
} from '../entity/course-pack.entity'

export const coursePackMapper = {
  toDTO(coursePackEntity: CoursePackEntity): CoursePack {
    return {
      id: coursePackEntity.id,
      title: coursePackEntity.title,
      description: coursePackEntity.description,
      isFree: coursePackEntity.isFree,
      cover: coursePackEntity.cover,
      createdAt: coursePackEntity.createdAt,
      updatedAt: coursePackEntity.updatedAt,
      gameId: coursePackEntity.gameId,
      shareLevel: coursePackEntity.shareLevel as CoursePackShareLevel,
      categoryId: coursePackEntity.categoryId || undefined,
      isPinned: coursePackEntity.isPinned,
      position: coursePackEntity.position,
    }
  },
  withCoursesToDTO(
    coursePackEntity: CoursePackWithCoursesEntity,
  ): CoursePackWithCourses {
    return {
      ...this.toDTO(coursePackEntity),
      courses: coursePackEntity.courses.map(course => ({
        id: course.id,
        title: course.title,
        description: course.description,
        createdAt: course.createdAt,
        updatedAt: course.updatedAt,
        gameId: course.gameId,
        position: course.position,
        mediaUrl: course.mediaUrl || '',
        type: course.type,
      })),
    }
  },
}
