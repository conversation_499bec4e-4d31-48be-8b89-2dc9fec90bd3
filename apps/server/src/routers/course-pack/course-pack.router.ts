import { CoursePackShareLevel } from '@julebu/shared'
import { z } from 'zod'
import { protectedProcedure, router } from '../../trpc'
import { coursePacksService } from './course-packs.service'
import { coursePackMapper } from './dto/course-pack.dto'

export const coursePackRouter = router({
  delete: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return coursePacksService.deleteOne(
        input.coursePackId,
        ctx.user.id,
      )
    }),

  create: protectedProcedure
    .input(
      z.object({
        title: z.string(),
        description: z.string(),
        cover: z.string(),
        categoryId: z.string().optional(),
        shareLevel: z.nativeEnum(CoursePackShareLevel).optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return coursePacksService.create(
        ctx.user.id,
        input.title,
        input.description,
        input.cover,
        input.categoryId,
        input.shareLevel,
      )
    }),

  edit: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
        title: z.string(),
        description: z.string(),
        cover: z.string(),
        shareLevel: z.nativeEnum(CoursePackShareLevel),
        categoryId: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return coursePacksService.update(
        ctx.user.id,
        input.coursePackId,
        input.title,
        input.description,
        input.cover,
        input.shareLevel,
        input.categoryId,
      )
    }),

  findOne: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      const result = await coursePacksService.findOne(
        input.coursePackId,
        ctx.user.id,
      )
      return coursePackMapper.withCoursesToDTO(result)
    }),

  list: protectedProcedure.query(async ({ ctx }) => {
    const list = await coursePacksService.findAll(ctx.user.id)
    return list.map(coursePackMapper.toDTO.bind(coursePackMapper))
  }),

  pinOne: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return coursePacksService.pinOne(input.coursePackId, ctx.user.id)
    }),

  unpinOne: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return coursePacksService.unpinOne(input.coursePackId, ctx.user.id)
    }),

  publishToGame: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const coursePack = await coursePacksService.publishToGame(
        input.coursePackId,
        ctx.user.id,
      )
      return coursePack
    }),

  unpublishFromGame: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return coursePacksService.unpublishFromGame(
        input.coursePackId,
        ctx.user.id,
      )
    }),

  updateToGame: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return coursePacksService.updateToGame(
        input.coursePackId,
        ctx.user.id,
      )
    }),

  checkCoursesNeedUpgrade: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
      }),
    )
    .query(async ({ input, ctx }) => {
      return coursePacksService.checkCoursesNeedUpgrade(
        input.coursePackId,
        ctx.user.id,
      )
    }),
})
