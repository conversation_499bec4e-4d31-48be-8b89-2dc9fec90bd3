import { CourseType } from '@julebu/shared'
import { z } from 'zod'
import { protectedProcedure, router } from '../../trpc'
import { courseService } from './course.service'
import { courseMapper } from './dto/course.dto'

export const courseRouter = router({
  create: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
        title: z.string(),
        description: z.string(),
        type: z.nativeEnum(CourseType).optional(),
      }),
    )
    .mutation(async ({ input, ctx }: { input: { coursePackId: string, title: string, description: string, type?: CourseType }, ctx: { user: { id: string } } }) => {
      const { coursePackId, title, description, type } = input

      return courseService.create({
        coursePackId,
        title,
        description,
        type: type ?? CourseType.normal,
        userId: ctx.user.id,
      })
    }),
  edit: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
        courseId: z.string(),
        title: z.string(),
        description: z.string(),
        mediaUrl: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }: { input: { courseId: string, coursePackId: string, title: string, description: string, mediaUrl?: string }, ctx: { user: { id: string } } }) => {
      const { courseId, coursePackId, title, description, mediaUrl } = input
      return courseService.update({
        courseId,
        coursePackId,
        title,
        description,
        mediaUrl,
        userId: ctx.user.id,
      })
    }),

  deleteOne: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        coursePackId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }: { input: { courseId: string, coursePackId: string }, ctx: { user: { id: string } } }) => {
      const { courseId, coursePackId } = input
      return courseService.deleteOne(courseId, coursePackId, ctx.user.id)
    }),
  deleteAll: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }: { input: { coursePackId: string }, ctx: { user: { id: string } } }) => {
      const { coursePackId } = input
      return courseService.deleteAll(coursePackId, ctx.user.id)
    }),
  batchDelete: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
        courseIds: z.array(z.string()),
      }),
    )
    .mutation(async ({ input, ctx }: { input: { coursePackId: string, courseIds: string[] }, ctx: { user: { id: string } } }) => {
      const { coursePackId, courseIds } = input
      return courseService.batchDelete(
        coursePackId,
        courseIds,
        ctx.user.id,
      )
    }),

  findOne: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        coursePackId: z.string(),
      }),
    )
    .query(async ({ input, ctx }: { input: { courseId: string, coursePackId: string }, ctx: { user: { id: string } } }) => {
      const course = await courseService.findOne(
        input.courseId,
        input.coursePackId,
        ctx.user.id,
      )
      return courseMapper.toDTO(course)
    }),
  movePosition: protectedProcedure
    .input(
      z.object({
        coursePackId: z.string(),
        affectedCourses: z.array(
          z.object({
            position: z.number(),
            courseId: z.string(),
          }),
        ),
      }),
    )
    .mutation(async ({ input, ctx }: { input: { coursePackId: string, affectedCourses: { position: number, courseId: string }[] }, ctx: { user: { id: string } } }) => {
      const { coursePackId, affectedCourses } = input
      return courseService.movePosition(
        ctx.user.id,
        coursePackId,
        affectedCourses,
      )
    }),

  publishToGame: protectedProcedure
    .input(z.object({ courseId: z.string(), coursePackId: z.string() }))
    .mutation(async ({ input, ctx }: { input: { courseId: string, coursePackId: string }, ctx: { user: { id: string } } }) => {
      return courseService.publishToGame(
        input.courseId,
        input.coursePackId,
        ctx.user.id,
      )
    }),

  unpublishFromGame: protectedProcedure
    .input(z.object({ courseId: z.string(), coursePackId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      return courseService.unpublishFromGame(
        input.courseId,
        input.coursePackId,
        ctx.user.id,
      )
    }),
  updateToGame: protectedProcedure
    .input(z.object({ courseId: z.string(), coursePackId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      return courseService.updateToGame(
        input.courseId,
        input.coursePackId,
        ctx.user.id,
      )
    }),

  upgradeCourse: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        coursePackId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }: { input: { courseId: string, coursePackId: string }, ctx: { user: { id: string } } }) => {
      return courseService.upgradeCourse(
        input.courseId,
        input.coursePackId,
        ctx.user.id,
      )
    }),
})
