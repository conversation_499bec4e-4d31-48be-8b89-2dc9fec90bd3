import { afterAll, beforeEach, describe, expect, it, vi } from "vitest";
import { courseService } from "../course.service";
import { cleanCourseDB } from "#/fixtures/course";
import { coursePacksService } from "@/routers/course-pack/course-packs.service";
import { getDB, schemas } from "@/db";
import { sentenceService } from "@/routers/sentence/sentence.service";
import { eq } from "drizzle-orm";

// 全局 mock sentenceService 的方法，提供默认行为
vi.spyOn(sentenceService, 'generateWordDetails').mockImplementation(async (content) => {
  return [{ 
    word: "test", 
    definition: "测试",
    pos: "NOUN",
    phonetic: {
      uk: "test",
      us: "test"
    }
  }];
});

vi.spyOn(sentenceService, 'generateDependencyAnalysis').mockImplementation(async (content) => {
  return { 
    nodes: [{ 
      id: "1", 
      text: "test", 
      pos: "NN", 
      dep: "root" 
    }], 
    edges: [] 
  };
});

describe("course service", () => {
  beforeEach(async () => {
    // 清空数据库
    await cleanCourseDB();
    // 重置 mock
    vi.clearAllMocks();
    
    // 重置 mock 的实现为默认行为
    vi.mocked(sentenceService.generateWordDetails).mockImplementation(async (content) => {
      return [{ 
        word: "test", 
        definition: "测试",
        pos: "NOUN",
        phonetic: {
          uk: "test",
          us: "test"
        }
      }];
    });
    
    vi.mocked(sentenceService.generateDependencyAnalysis).mockImplementation(async (content) => {
      return { 
        nodes: [{ 
          id: "1", 
          text: "test", 
          pos: "NN", 
          dep: "root" 
        }], 
        edges: [] 
      };
    });
  });

  afterAll(async () => {
    await cleanCourseDB();
  });

  describe("create course", () => {
    it("should create course to add course pack", async () => {
      const userId = "tester";
      const coursePackId = await coursePacksService.create(
        userId,
        "测试课程包",
        "测试课程包描述",
        ""
      );

      const courseId = await courseService.create({
        coursePackId,
        userId,
        title: "测试课程",
        description: "测试课程描述",
      });

      const course = await courseService.findOne(
        courseId,
        coursePackId,
        userId
      );

      expect(course).not.toBeNull();
      expect(course?.title).toBe("测试课程");
      expect(course?.description).toBe("测试课程描述");
    });
  });

  describe("upgrade course", () => {
    it("should check if course needs upgrade", async () => {
      // 创建测试数据
      const userId = "tester";
      const coursePackId = await coursePacksService.create(
        userId,
        "测试课程包",
        "测试课程包描述",
        ""
      );
      const courseId = await courseService.create({
        coursePackId,
        userId,
        title: "测试课程",
        description: "测试课程描述",
      });

      // 创建测试句子
      const db = getDB();
      const sentenceData = [
        {
          courseId: courseId,
          content: "This is a test sentence.",
          position: 1,
          // 缺少 wordDetails, dependencyAnalysis 和 english
          english: "",
          chinese: "",
        },
        {
          courseId: courseId,
          content: "This is another test sentence.",
          position: 2,
          wordDetails: [{ word: "test", definition: "测试" }],
          // 缺少 dependencyAnalysis 和 english
          english: "",
          chinese: "",
        },
        {
          courseId: courseId,
          content: "This is a complete sentence.",
          position: 3,
          wordDetails: [{ word: "complete", definition: "完整的" }],
          dependencyAnalysis: { nodes: [], edges: [] },
          english: "This is a complete sentence.",
          chinese: "",
        },
      ];
      await db.insert(schemas.sentence).values(sentenceData);

      // 检查课程是否需要升级
      const result = await courseService.checkCourseNeedsUpgrade(
        courseId,
        coursePackId,
        userId
      );

      // 验证结果
      expect(result.needsUpgrade).toBe(true);
      expect(result.totalSentences).toBe(3);
      expect(result.sentencesNeedingUpgrade).toBe(2);
      expect(result.details.missingWordDetails).toBe(1);
      expect(result.details.missingDependencyAnalysis).toBe(2);
      expect(result.details.missingEnglish).toBe(2);
      expect(result.sentences.length).toBe(2);
      
      // 验证第一个需要升级的句子
      const firstSentence = result.sentences[0];
      expect(firstSentence.content).toBe("This is a test sentence.");
      expect(firstSentence.position).toBe(1);
      expect(firstSentence.missingFields.wordDetails).toBe(true);
      expect(firstSentence.missingFields.dependencyAnalysis).toBe(true);
      expect(firstSentence.missingFields.english).toBe(true);
      
      // 验证第二个需要升级的句子
      const secondSentence = result.sentences[1];
      expect(secondSentence.content).toBe("This is another test sentence.");
      expect(secondSentence.position).toBe(2);
      expect(secondSentence.missingFields.wordDetails).toBe(false);
      expect(secondSentence.missingFields.dependencyAnalysis).toBe(true);
      expect(secondSentence.missingFields.english).toBe(true);
    });

    it("should upgrade course successfully", async () => {
      // 创建测试数据
      const userId = "tester";
      const coursePackId = await coursePacksService.create(
        userId,
        "测试课程包",
        "测试课程包描述",
        ""
      );
      const courseId = await courseService.create({
        coursePackId,
        userId,
        title: "测试课程",
        description: "测试课程描述",
      });

      // 创建测试句子
      const db = getDB();
      const sentenceData = [
        {
          courseId: courseId,
          content: "This is a test sentence.",
          position: 1,
          // 缺少 wordDetails, dependencyAnalysis 和 english
          english: "",
          chinese: "",
        },
        {
          courseId: courseId,
          content: "This is another test sentence.",
          position: 2,
          wordDetails: [{ word: "test", definition: "测试" }],
          // 缺少 dependencyAnalysis 和 english
          english: "",
          chinese: "",
        },
        {
          courseId: courseId,
          content: "This is a complete sentence.",
          position: 3,
          wordDetails: [{ word: "complete", definition: "完整的" }],
          dependencyAnalysis: { nodes: [], edges: [] },
          english: "This is a complete sentence.",
          chinese: "",
        },
      ];
      await db.insert(schemas.sentence).values(sentenceData);

      // 升级课程
      const result = await courseService.upgradeCourse(
        courseId,
        coursePackId,
        userId
      );

      // 验证结果
      expect(result.totalSentences).toBe(3);
      expect(result.updatedSentences).toBe(2);
      expect(result.skippedSentences).toBe(1);
      expect(result.failedSentences).toBe(0);
      expect(result.errors.length).toBe(0);

      // 验证 sentenceService 的调用
      expect(sentenceService.generateWordDetails).toHaveBeenCalledTimes(1);
      expect(sentenceService.generateDependencyAnalysis).toHaveBeenCalledTimes(2);

      // 验证数据库更新
      const sentences = await db.query.sentence.findMany({
        where: eq(schemas.sentence.courseId, courseId),
        orderBy: (s, { asc }) => [asc(s.position)],
      });

      // 验证第一个句子已完全更新
      expect(sentences[0].wordDetails).not.toBeNull();
      expect(sentences[0].dependencyAnalysis).not.toBeNull();
      expect(sentences[0].english).toBe("This is a test sentence");

      // 验证第二个句子已部分更新
      expect(sentences[1].dependencyAnalysis).not.toBeNull();
      expect(sentences[1].english).toBe("This is another test sentence");

      // 验证第三个句子保持不变
      expect(sentences[2].english).toBe("This is a complete sentence.");
    });

    it("should handle errors during upgrade", async () => {
      // 创建测试数据
      const userId = "tester";
      const coursePackId = await coursePacksService.create(
        userId,
        "测试课程包",
        "测试课程包描述",
        ""
      );
      const courseId = await courseService.create({
        coursePackId,
        userId,
        title: "测试课程",
        description: "测试课程描述",
      });

      // 创建测试句子
      const db = getDB();
      const sentenceData = [
        {
          courseId: courseId,
          content: "This is a test sentence.",
          position: 1,
          english: "",
          chinese: "",
        },
        {
          courseId: courseId,
          content: "This will fail.",
          position: 2,
          english: "",
          chinese: "",
        },
      ];
      await db.insert(schemas.sentence).values(sentenceData);

      // 在这个特定的测试用例中模拟 generateWordDetails 失败
      vi.mocked(sentenceService.generateWordDetails).mockImplementation(async (content) => {
        if (content === "This will fail.") {
          throw new Error("模拟生成失败");
        }
        return [{ 
          word: "test", 
          definition: "测试",
          pos: "NOUN",
          phonetic: {
            uk: "test",
            us: "test"
          }
        }];
      });

      // 升级课程
      const result = await courseService.upgradeCourse(
        courseId,
        coursePackId,
        userId
      );

      // 验证结果
      expect(result.totalSentences).toBe(2);
      expect(result.updatedSentences).toBe(1);
      expect(result.failedSentences).toBe(1);
      expect(result.errors.length).toBe(1);
      
      // 验证错误信息
      const error = result.errors[0];
      expect(error.content).toBe("This will fail.");
      expect(error.position).toBe(2);
      expect(error.error).toContain("模拟生成失败");
    });
  });
});
