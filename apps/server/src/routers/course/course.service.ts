import type { CourseType } from '@julebu/shared'
import type { <PERSON><PERSON><PERSON> } from 'node:buffer'
import zlib from 'node:zlib'
import { TRPCError } from '@trpc/server'
import { and, eq, gt, inArray, sql } from 'drizzle-orm'
import { getDB, schemas } from '@/db'
import { sentenceService } from '@/routers/sentence/sentence.service'
import {
  publishCourseToGameApi,
  unpublishCourseToGameApi,
  updateCourseToGameApi,
} from '@/services/game/api/course'
import { isSingleWord } from '@/utils/word'
import { normalizeContent } from '../sentence/content'

async function update({
  courseId,
  coursePackId,
  title,
  description,
  mediaUrl,
  type,
  userId,
}: {
  courseId: string
  coursePackId: string
  title: string
  description: string
  mediaUrl?: string
  type?: CourseType
  userId: string
}) {
  await checkCoursePackAccess(userId, coursePackId)

  const db = getDB()

  // 构建更新对象，只包含需要更新的字段
  const updateData: {
    title: string
    description: string
    mediaUrl?: string
    type?: CourseType
  } = {
    title,
    description,
  }

  // 如果提供了mediaUrl，则包含在更新中
  if (mediaUrl !== undefined) {
    updateData.mediaUrl = mediaUrl
  }

  if (type !== undefined) {
    updateData.type = type
  }

  const [result] = await db
    .update(schemas.course)
    .set(updateData)
    .where(
      and(
        eq(schemas.course.id, courseId),
        eq(schemas.course.coursePackId, coursePackId),
      ),
    )

  if (!result || result.affectedRows === 0) {
    throw new TRPCError({ code: 'NOT_FOUND', message: '课程不存在' })
  }

  return true
}

async function deleteOne(
  courseId: string,
  coursePackId: string,
  userId: string,
) {
  await checkCoursePackAccess(userId, coursePackId)

  const db = getDB()

  return db.transaction(async (tx) => {
    // 1. 先获取要删除的课程信息，用于后续更新 position
    const courseToDelete = await tx.query.course.findFirst({
      where: (course, { eq, and }) =>
        and(eq(course.id, courseId), eq(course.coursePackId, coursePackId)),
    })

    if (!courseToDelete) {
      throw new TRPCError({ code: 'NOT_FOUND', message: '课程不存在' })
    }

    // 3. 获取所有相关句子的ID
    const sentences = await tx.query.sentence.findMany({
      where: eq(schemas.sentence.courseId, courseId),
      columns: { id: true },
    })
    const sentenceIds = sentences.map(sentence => sentence.id)

    // 4. 按照依赖关系顺序删除数据
    // 4.1 删除元素
    await tx
      .delete(schemas.element)
      .where(inArray(schemas.element.sentenceId, sentenceIds))

    // 4.2 删除句子
    await tx
      .delete(schemas.sentence)
      .where(eq(schemas.sentence.courseId, courseId))

    // 4.3 删除课程
    await tx.delete(schemas.course).where(eq(schemas.course.id, courseId))

    // 5. 更新被删除课程后面的所有课程的 position
    await tx
      .update(schemas.course)
      .set({
        position: sql`${schemas.course.position} - 1`,
      })
      .where(
        and(
          gt(schemas.course.position, courseToDelete.position),
          eq(schemas.course.coursePackId, coursePackId),
        ),
      )

    return true
  })
}

async function deleteAll(coursePackId: string, userId: string) {
  await checkCoursePackAccess(userId, coursePackId)

  const db = getDB()

  return db
    .delete(schemas.course)
    .where(eq(schemas.course.coursePackId, coursePackId))
}

export async function findOne(
  courseId: string,
  coursePackId: string,
  userId: string,
) {
  await checkCoursePackAccess(userId, coursePackId)

  const db = getDB()

  const course = await db.query.course.findFirst({
    where: (course, { eq, and }) =>
      and(eq(course.id, courseId), eq(course.coursePackId, coursePackId)),
    with: {
      sentences: {
        with: {
          elements: {
            with: {
              image: true,
            },
            orderBy: (elements, { asc }) => [asc(elements.position)],
          },
        },
        orderBy: (sentences, { asc }) => [asc(sentences.position)],
      },
    },
  })

  if (!course) {
    throw new TRPCError({ code: 'NOT_FOUND', message: '课程不存在' })
  }

  return course
}

async function fineAll(coursePackId: string, userId: string) {
  await checkCoursePackAccess(userId, coursePackId)

  const db = getDB()

  const courses = await db.query.course.findMany({
    where: (course, { eq, and }) =>
      and(eq(course.coursePackId, coursePackId)),
    with: {
      sentences: {
        with: {
          elements: {
            with: {
              image: true,
            },
            orderBy: (elements, { asc }) => [asc(elements.position)],
          },
        },
        orderBy: (sentences, { asc }) => [asc(sentences.position)],
      },
    },
  })

  if (!courses || courses.length === 0) {
    throw new TRPCError({ code: 'NOT_FOUND', message: '课程不存在' })
  }

  return courses
}

async function create({
  coursePackId,
  title,
  description,
  type,
  userId,
}: {
  coursePackId: string
  title: string
  description: string
  type: CourseType
  userId: string
}) {
  await checkCoursePackAccess(userId, coursePackId)

  const db = getDB()

  const maxPositionResult = await db.query.course.findFirst({
    where: (courses, { eq }) => eq(courses.coursePackId, coursePackId),
    orderBy: (courses, { desc }) => [desc(courses.position)],
  })

  // 默认从 1 开始(找不到的话是 0 ， 那么 + 1 之后就是 1 )
  const nextPosition = (maxPositionResult?.position ?? 0) + 1

  const [course] = await db
    .insert(schemas.course)
    .values({
      title,
      description,
      coursePackId,
      position: nextPosition,
      type,
    })
    .$returningId()

  if (!course) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '创建课程失败',
    })
  }

  return course.id
}

export async function checkCoursePackAccess(
  userId: string,
  coursePackId: string,
) {
  const db = getDB()
  const coursePack = await db.query.coursePack.findFirst({
    where: (coursePacks, { eq, and }) =>
      and(eq(coursePacks.id, coursePackId), eq(coursePacks.userId, userId)),
  })

  if (!coursePack) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: '无权限访问该课程包',
    })
  }

  return coursePack
}

async function movePosition(
  userId: string,
  coursePackId: string,
  affectedCourses: { position: number, courseId: string }[],
) {
  await checkCoursePackAccess(userId, coursePackId)

  await validateAndFixPositions(coursePackId)

  const db = getDB()

  // 使用事务来确保原子性
  return db.transaction(async (tx) => {
    // 使用 SQL CASE 语句进行批量更新
    await tx
      .update(schemas.course)
      .set({
        position: sql`CASE id 
          ${affectedCourses
            .map(
              course => sql`WHEN ${course.courseId} THEN ${course.position}`,
            )
            .reduce((acc, curr) => sql`${acc} ${curr}`)}
          ELSE position 
          END`,
      })
      .where(
        and(
          inArray(
            schemas.course.id,
            affectedCourses.map(c => c.courseId),
          ),
          eq(schemas.course.coursePackId, coursePackId),
        ),
      )

    return true
  })
}

async function batchDelete(
  coursePackId: string,
  courseIds: string[],
  userId: string,
) {
  await checkCoursePackAccess(userId, coursePackId)

  const db = getDB()

  return db
    .delete(schemas.course)
    .where(
      and(
        inArray(schemas.course.id, courseIds),
        eq(schemas.course.coursePackId, coursePackId),
      ),
    )
}

async function publishToGame(
  courseId: string,
  coursePackId: string,
  userId: string,
) {
  const coursePack = await checkCoursePackAccess(userId, coursePackId)

  const course = await getToGameCourseData(courseId)

  if (!course) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '课程不存在',
    })
  }

  validateCourseHasElements(course)
  validateSentencesHaveElements(course)
  validateElementsTranslationAndPhonetic(course)

  try {
    // 压缩数据
    const db = getDB()

    const compressedData = zlib.gzipSync(
      JSON.stringify({ course, coursePackId: coursePack.gameId, userId }),
    )

    validateCompressedDataSize(compressedData)

    const responseData = await publishCourseToGameApi(compressedData)

    if (responseData.status !== 1) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '发布课程失败',
      })
    }

    // 使用事务来确保数据库操作的原子性
    return await db.transaction(async (tx) => {
      // 更新课程的发布ID
      await tx
        .update(schemas.course)
        .set({
          gameId: responseData.data.courseId,
        })
        .where(eq(schemas.course.id, courseId))

      const elements = course.sentences.flatMap(s => s.elements)

      // 批量更新所有元素的 gameId
      await Promise.all(
        elements.map((element, index) =>
          tx
            .update(schemas.element)
            .set({
              gameId: responseData.data.statements[index],
            })
            .where(eq(schemas.element.id, element.id)),
        ),
      )

      return responseData.data
    })
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '发布课程失败',
      cause: process.env.NODE_ENV === 'production' ? undefined : error,
    })
  }
}

async function unpublishFromGame(
  courseId: string,
  coursePackId: string,
  userId: string,
) {
  await checkCoursePackAccess(userId, coursePackId)

  const db = getDB()

  const course = await db.query.course.findFirst({
    where: (courses, { eq, and }) =>
      and(eq(courses.id, courseId), eq(courses.coursePackId, coursePackId)),
  })

  if (!course) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '课程不存在',
    })
  }

  try {
    const responseData = await unpublishCourseToGameApi(course.gameId, userId)

    if (responseData) {
      // 使用事务来确保数据库操作的原子性
      return await db.transaction(async (tx) => {
        // 清空课程的 gameId
        await tx
          .update(schemas.course)
          .set({
            gameId: '',
          })
          .where(eq(schemas.course.id, courseId))

        // 获取所有相关元素的 id
        const course = await tx.query.course.findFirst({
          where: eq(schemas.course.id, courseId),
          with: {
            sentences: {
              with: {
                elements: {
                  columns: {
                    id: true,
                  },
                },
              },
            },
          },
        })

        // 获取所有 elements 的 id
        const elementIds
          = course?.sentences.flatMap(sentence =>
            sentence.elements.map(element => element.id),
          ) || []

        // 批量更新所有相关 elements 的 gameId
        if (elementIds.length > 0) {
          await tx
            .update(schemas.element)
            .set({
              gameId: '',
            })
            .where(inArray(schemas.element.id, elementIds))
        }

        return true
      })
    }
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '下架课程失败',
      cause: error,
    })
  }
}

async function updateToGame(
  courseId: string,
  coursePackId: string,
  userId: string,
) {
  const coursePack = await checkCoursePackAccess(userId, coursePackId)

  const course = await getToGameCourseData(courseId)

  if (!course) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '课程不存在',
    })
  }

  validateCourseHasElements(course)
  validateSentencesHaveElements(course)
  validateElementsTranslationAndPhonetic(course)

  try {
    // 压缩数据
    const db = getDB()

    const compressedData = zlib.gzipSync(
      JSON.stringify({ course, coursePackId: coursePack.gameId, userId }),
    )

    validateCompressedDataSize(compressedData)

    const responseData = await updateCourseToGameApi(
      course.gameId,
      compressedData,
    )

    if (responseData.status !== 1) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: '更新课程失败',
      })
    }

    // 没有变化的话  那么就返回 false
    // 不直接抛出错误 而是返回 false 是为了好让客户端做处理
    if (responseData.data === false) {
      return false
    }

    // 使用事务来确保数据库操作的原子性
    return await db.transaction(async (tx) => {
      // 使用类型断言，告诉 TypeScript 此时 responseData.data 不是 false
      const data = responseData.data as {
        statements: { editorId: string, gameId: string }[]
      }
      for (const { editorId, gameId } of data.statements) {
        await tx
          .update(schemas.element)
          .set({ gameId })
          .where(eq(schemas.element.id, editorId))
      }

      return responseData.data
    })
  } catch (error) {
    // 检查是否已经是 TRPCError
    if (error instanceof TRPCError) {
      throw error
    }

    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '更新课程失败',
      cause: error,
    })
  }
}

async function getToGameCourseData(courseId: string) {
  const db = getDB()

  const course = await db.query.course.findFirst({
    where: (courses, { eq }) => eq(courses.id, courseId),
    columns: {
      id: true,
      title: true,
      description: true,
      cover: true,
      video: true,
      coursePackId: true,
      position: true,
      gameId: true,
      type: true,
      mediaUrl: true,
    },
    with: {
      sentences: {
        columns: {
          id: true,
          content: true,
          english: true,
          chinese: true,
          learningContent: true,
          position: true,
          wordDetails: true,
          dependencyAnalysis: true,
          startTime: true,
          endTime: true,
        },
        orderBy: (sentences, { asc }) => [asc(sentences.position)],
        with: {
          elements: {
            with: {
              image: {
                columns: {
                  fileKey: true,
                  width: true,
                  height: true,
                  description: true,
                },
              },
            },
            columns: {
              id: true,
              english: true,
              chinese: true,
              phonetic: true,
              type: true,
              position: true,
              gameId: true,
            },
            orderBy: (elements, { asc }) => [asc(elements.position)],
          },
        },
      },
    },
  })

  return course
}

// 检查每个句子是否有内容
function validateSentencesHaveElements(course: any) {
  for (let i = 0; i < course.sentences.length; i++) {
    const sentence = course.sentences[i]
    if (sentence.elements.length === 0) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: `课程 "${course.title}" 中的第 ${i + 1} 个句子 "${sentence.content}" 还没有内容，请先添加内容再发布`,
      })
    }
  }
}

// 检查课程是否有内容
function validateCourseHasElements(course: any) {
  const elements = course.sentences.flatMap((s: any) => s.elements)
  if (elements.length === 0) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: `课程 "${course.title}" 还没有内容，请先添加内容再发布（是不是添加完句子后忘记点击加工/拆分啦？）`,
    })
  }
}

// 检查每个元素是否有中文和音标
function validateElementsTranslationAndPhonetic(course: any) {
  for (let i = 0; i < course.sentences.length; i++) {
    const sentence = course.sentences[i]
    for (let j = 0; j < sentence.elements.length; j++) {
      const element = sentence.elements[j]
      if (!element.chinese || !element.phonetic) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: !element.chinese
            ? `课程 "${course.title}" 中的第 ${i + 1} 个句子 "${sentence.content}" 的第 ${j + 1} 个元素 "${element.english}" 缺少中文翻译，请补充完整再发布`
            : `课程 "${course.title}" 中的第 ${i + 1} 个句子 "${sentence.content}" 的第 ${j + 1} 个元素 "${element.english}" 缺少音标，请补充完整再发布`,
        })
      }
    }
  }
}

function validateCompressedDataSize(compressedData: Buffer) {
  const maxSize = 5 * 1024 * 1024 // 5MB in bytes
  if (compressedData.length > maxSize) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: `课程内容过大,已超过5MB限制,请删减课程内容后重试`,
    })
  }
}

/**
 * 检查并修复课程的位置 (以后 position 没问题了 就可以删除掉了)
 * @param coursePackId
 * @returns
 */
async function validateAndFixPositions(coursePackId: string) {
  const db = getDB()

  return db.transaction(async (tx) => {
    const courses = await tx.query.course.findMany({
      where: eq(schemas.course.coursePackId, coursePackId),
      orderBy: (courses, { asc }) => [asc(courses.position)],
      columns: {
        id: true,
        position: true,
      },
    })

    let needsFix = false

    // 检查是否有重复的 position
    const positions = courses.map(c => c.position)
    const uniquePositions = new Set(positions)
    if (positions.length !== uniquePositions.size) {
      needsFix = true
    }

    // 检查是否有缺失的 position
    for (let i = 1; i <= courses.length; i++) {
      if (!positions.includes(i)) {
        needsFix = true
        break
      }
    }

    if (needsFix) {
      console.log(
        `[Position Fix] Fixing positions for coursePackId: ${coursePackId}`,
      )

      // 重新设置所有课程的 position
      for (let i = 0; i < courses.length; i++) {
        await tx
          .update(schemas.course)
          .set({ position: i + 1 })
          .where(eq(schemas.course.id, courses[i].id))
      }
    }

    return needsFix
  })
}

/**
 * 升级单个课程的数据
 * 更新所有句子的 wordDetails、dependencyAnalysis 和 english 字段
 * @param courseId 课程ID
 * @param coursePackId 课程包ID
 * @param userId 用户ID
 * @returns 升级结果统计
 */
async function upgradeCourse(
  courseId: string,
  coursePackId: string,
  userId: string,
) {
  // 检查用户是否有权限访问该课程包
  await checkCoursePackAccess(userId, coursePackId)

  const db = getDB()

  // 获取课程信息
  const course = await db.query.course.findFirst({
    where: and(
      eq(schemas.course.id, courseId),
      eq(schemas.course.coursePackId, coursePackId),
    ),
    columns: {
      id: true,
      title: true,
    },
  })

  if (!course) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '课程不存在',
    })
  }

  // 统计结果
  const result = {
    courseTitle: course.title,
    totalSentences: 0,
    updatedSentences: 0,
    skippedSentences: 0, // 已有数据，跳过的句子数
    failedSentences: 0,
    skippedWords: 0, // 跳过的单词数
    errors: [] as Array<{
      sentenceId: string
      content: string
      position: number
      error: string
    }>,
  }

  try {
    // 获取课程下的所有句子
    const sentences = await db.query.sentence.findMany({
      where: eq(schemas.sentence.courseId, course.id),
      columns: {
        id: true,
        content: true,
        position: true,
        wordDetails: true,
        dependencyAnalysis: true,
        english: true,
      },
      orderBy: (sentences, { asc }) => [asc(sentences.position)],
    })

    result.totalSentences = sentences.length

    // 并发处理所有句子
    const updateTasks = sentences.map(async (sentence) => {
      try {
        // 检查是否是单词
        if (isSingleWord(sentence.content)) {
          return { status: 'word' as const }
        }

        // 检查是否已有所需字段
        const hasWordDetails
          = sentence.wordDetails
            && Array.isArray(sentence.wordDetails)
            && sentence.wordDetails.length > 0
        const hasDependencyAnalysis
          = sentence.dependencyAnalysis
            && typeof sentence.dependencyAnalysis === 'object'
            && ((sentence.dependencyAnalysis as any).nodes
              || (sentence.dependencyAnalysis as any).edges)
        const hasEnglish
          = sentence.english
            && typeof sentence.english === 'string'
            && sentence.english.trim() !== ''

        // 如果所有字段都已存在，则跳过此句子
        if (hasWordDetails && hasDependencyAnalysis && hasEnglish) {
          return { status: 'skipped' as const }
        }

        // 并行生成缺失的字段
        const updates: {
          wordDetails?: any
          dependencyAnalysis?: any
          english?: string
        } = {}
        const updatePromises: Promise<any>[] = []

        if (!hasWordDetails) {
          updatePromises.push(
            sentenceService
              .generateWordDetails(sentence.content)
              .then((result) => {
                updates.wordDetails = result
              }),
          )
        }

        if (!hasDependencyAnalysis) {
          updatePromises.push(
            sentenceService
              .generateDependencyAnalysis(sentence.content)
              .then((result) => {
                updates.dependencyAnalysis = result
              }),
          )
        }

        if (!hasEnglish) {
          updates.english = normalizeContent(sentence.content)
        }

        // 等待所有更新完成
        await Promise.all(updatePromises)

        // 如果没有需要更新的字段，跳过
        if (Object.keys(updates).length === 0) {
          return { status: 'skipped' as const }
        }

        // 更新句子
        await db
          .update(schemas.sentence)
          .set(updates)
          .where(eq(schemas.sentence.id, sentence.id))

        return { status: 'updated' as const }
      } catch (error) {
        const errorMessage
          = error instanceof Error ? error.message : String(error)

        // 返回错误信息
        return {
          status: 'failed' as const,
          error: {
            sentenceId: sentence.id,
            content: sentence.content,
            position: sentence.position,
            error: errorMessage,
          },
        }
      }
    })

    // 等待所有任务完成
    const updateResults = await Promise.all(updateTasks)

    // 统计结果
    updateResults.forEach((res) => {
      if (res.status === 'updated') {
        result.updatedSentences++
      } else if (res.status === 'skipped') {
        result.skippedSentences++
      } else if (res.status === 'word') {
        result.skippedWords++
      } else if (res.status === 'failed') {
        result.failedSentences++
        result.errors.push(res.error)
      }
    })
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    // 对于整体错误，我们仍然使用简单的字符串格式
    result.errors.push({
      sentenceId: '',
      content: '',
      position: 0,
      error: `课程处理失败: ${errorMessage}`,
    })
  }

  return result
}

/**
 * 检查课程是否需要升级
 * 检查课程下的句子是否已有 wordDetails、dependencyAnalysis 和 english 字段
 * @param courseId 课程ID
 * @param coursePackId 课程包ID
 * @param userId 用户ID
 * @returns 检查结果
 */
async function checkCourseNeedsUpgrade(
  courseId: string,
  coursePackId: string,
  userId: string,
) {
  // 检查用户是否有权限访问该课程包
  await checkCoursePackAccess(userId, coursePackId)

  const db = getDB()

  // 获取课程信息
  const course = await db.query.course.findFirst({
    where: and(
      eq(schemas.course.id, courseId),
      eq(schemas.course.coursePackId, coursePackId),
    ),
    columns: {
      id: true,
      title: true,
    },
  })

  if (!course) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '课程不存在',
    })
  }

  // 获取课程下的所有句子
  const sentences = await db.query.sentence.findMany({
    where: eq(schemas.sentence.courseId, course.id),
    columns: {
      id: true,
      content: true,
      position: true,
      wordDetails: true,
      dependencyAnalysis: true,
      english: true,
    },
    orderBy: (sentences, { asc }) => [asc(sentences.position)],
  })

  // 统计结果
  const result = {
    courseTitle: course.title,
    totalSentences: sentences.length,
    needsUpgrade: false,
    sentencesNeedingUpgrade: 0,
    totalWords: 0, // 单词总数
    details: {
      missingWordDetails: 0,
      missingDependencyAnalysis: 0,
      missingEnglish: 0,
    },
    sentences: [] as Array<{
      id: string
      content: string
      position: number
      missingFields: {
        wordDetails: boolean
        dependencyAnalysis: boolean
        english: boolean
      }
    }>,
  }

  // 检查每个句子是否需要升级
  for (const sentence of sentences) {
    // 检查是否是单词
    if (isSingleWord(sentence.content)) {
      result.totalWords++
      continue
    }

    let sentenceNeedsUpgrade = false
    const missingFields = {
      wordDetails: false,
      dependencyAnalysis: false,
      english: false,
    }

    // 检查是否已有所需字段
    const hasWordDetails
      = sentence.wordDetails
        && Array.isArray(sentence.wordDetails)

    if (!hasWordDetails) {
      result.details.missingWordDetails++
      missingFields.wordDetails = true
      sentenceNeedsUpgrade = true
    }

    const hasDependencyAnalysis
      = sentence.dependencyAnalysis
        && typeof sentence.dependencyAnalysis === 'object'
        && ((sentence.dependencyAnalysis as any).nodes
          || (sentence.dependencyAnalysis as any).edges)
    if (!hasDependencyAnalysis) {
      result.details.missingDependencyAnalysis++
      missingFields.dependencyAnalysis = true
      sentenceNeedsUpgrade = true
    }

    const hasEnglish
      = sentence.english
        && typeof sentence.english === 'string'
        && sentence.english.trim() !== ''
    if (!hasEnglish) {
      result.details.missingEnglish++
      missingFields.english = true
      sentenceNeedsUpgrade = true
    }

    if (sentenceNeedsUpgrade) {
      result.sentencesNeedingUpgrade++

      // 添加需要升级的句子详情
      result.sentences.push({
        id: sentence.id,
        content: sentence.content,
        position: sentence.position,
        missingFields,
      })
    }
  }

  // 如果有任何句子需要升级，则整个课程需要升级
  result.needsUpgrade = result.sentencesNeedingUpgrade > 0

  return result
}

export const courseService = {
  findOne,
  fineAll,
  create,
  deleteOne,
  deleteAll,
  update,
  movePosition,
  batchDelete,
  publishToGame,
  unpublishFromGame,
  updateToGame,
  validateAndFixPositions,
  upgradeCourse,
  checkCourseNeedsUpgrade,
}
