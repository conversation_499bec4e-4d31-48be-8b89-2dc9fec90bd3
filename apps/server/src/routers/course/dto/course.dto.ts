import type { Course } from '@julebu/shared'
import type { CourseWithSentencesEntity } from '../entity/course.entity'
import { CourseType } from '@julebu/shared'
import { sentenceMapper } from '@/routers/sentence/dto/sentence.dto'

export const courseMapper = {
  toDTO: (courseEntity: CourseWithSentencesEntity): Course => {
    return {
      id: courseEntity.id,
      title: courseEntity.title,
      description: courseEntity.description,
      video: courseEntity.video,
      coursePackId: courseEntity.coursePackId,
      sentences: courseEntity.sentences.map(sentenceMapper.toDTO.bind(sentenceMapper)),
      gameId: courseEntity.gameId,
      createdAt: courseEntity.createdAt,
      updatedAt: courseEntity.updatedAt,
      type: courseEntity.type || CourseType.normal,
      mediaUrl: courseEntity.mediaUrl || '',
    }
  },
}
