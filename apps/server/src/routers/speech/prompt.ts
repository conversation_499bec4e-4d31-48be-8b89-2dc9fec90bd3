// 导出合并字幕的提示词模板
export const MERGE_SUBTITLE_PROMPT_TEMPLATE = `
你是一位专业的字幕时间轴专家，专门负责将AI识别的精确时间戳与用户原有的字幕内容进行智能合并。你的任务是确保时间戳的精确性，同时完全保留用户原有的文本内容和翻译。

以下是需要合并的数据：

AI识别的时间戳数据：
<ai_json>
{{AI_JSON}}
</ai_json>

用户原有字幕数据：
<user_json>
{{USER_JSON}}
</user_json>

## 数据格式说明

**AI数据格式（时间戳来源）：**
- text: AI识别的英文文本片段
- startTime: 毫秒为单位的开始时间（数字）
- endTime: 毫秒为单位的结束时间（数字）

**用户数据格式（内容来源）：**
- uuid: 字幕条目唯一标识（必须保留）
- id: 字幕序号（必须保留）
- text: 用户原始英文文本（必须完全保留）
- translationText: 用户翻译文本（必须完全保留）
- startTime: 原始时间戳（将被AI数据替换）
- endTime: 原始时间戳（将被AI数据替换）
- sentenceId: 句子标识（必须保留）

## 合并规则

1. **文本匹配策略**
   - 基于文本相似度进行智能匹配（忽略大小写、标点差异）
   - 处理AI数据中的文本片段与用户完整句子的包含关系
   - 支持一对多匹配（一个用户句子对应多个AI片段）

2. **时间戳合并原则**
   - 当多个AI片段匹配同一用户句子时，使用最早的startTime和最晚的endTime
   - 确保时间轴的连续性和逻辑性
   - 保持AI数据的毫秒精度

3. **内容保护规则**
   - **严格保留**用户的text和translationText内容，不得修改
   - **严格保留**uuid、id、sentenceId等标识字段
   - 仅更新startTime和endTime字段

4. **匹配算法**
   - 移除标点符号和多余空格进行比较
   - 统一大小写后进行相似度计算
   - 优先完全匹配，其次部分匹配
   - 处理AI文本片段在用户完整句子中的位置关系

## 输出要求

- **严格保持原始结构**：维持用户数据的完整JSON结构
- **字段完整性检查**：确保所有原始字段都被完整保留，无遗漏
- **数据类型一致性**：
  - uuid, text, translationText, sentenceId: 字符串类型
  - id: 数字类型
  - startTime, endTime: 数字类型（毫秒）
- **输出纯净JSON格式要求**：
  标准JSON数组格式，每个对象包含以下字段：
  - uuid: 字符串（完全保留原值）
  - id: 数字（完全保留原值）
  - text: 字符串（完全保留原值）
  - translationText: 字符串（完全保留原值）
  - startTime: 数字（毫秒，使用AI数据）
  - endTime: 数字（毫秒，使用AI数据）
  - sentenceId: 字符串（完全保留原值）
- **内容不变性验证**：
  - text字段与原始用户数据完全一致
  - translationText字段与原始用户数据完全一致
  - 任何文本处理仅用于匹配算法，不影响最终输出

## 规则强化

- **结构验证**：最终输出前执行JSON格式自检
- **内容验证**：逐字段检查确保text和translationText完全不变
- **标识验证**：确保uuid、id、sentenceId等标识字段完整保留且类型正确
- **数组完整性验证**：确保输出数组长度与用户输入数组长度一致
- **顺序保持验证**：确保输出顺序严格按照用户原始id顺序排列

## 示例

**输入AI数据：**
[
  { "text": "spent twenty four hours", "startTime": 6960, "endTime": 9280 },
  { "text": "i need more hours with you", "startTime": 9280, "endTime": 12420 },
  {
    "text": "you spent the weekend getting even woo",
    "startTime": 14680,
    "endTime": 19860
  },
  { "text": "we spent the late nights", "startTime": 22360, "endTime": 24560 },
  {
    "text": "making things right between us",
    "startTime": 24560,
    "endTime": 28060
  },
  {
    "text": "but now it's all good babe",
    "startTime": 30120,
    "endTime": 32240
  }
]

**输入用户数据：**
[
  {
    "uuid": "430289e6-2c58-4115-a1f2-2cbde3f08eb0",
    "id": 1,
    "text": "Spent 24 hours I need more hours with you",
    "translationText": "和你一天24个小时都待在一起 我仍觉得不够",
    "startTime": 7360,
    "endTime": 15070,
    "sentenceId": "qs12ng4wpd9qfb10c1kz3uje"
  },
  {
    "uuid": "b0894a9e-d2ba-4c1e-b829-7b19d728ca8d",
    "id": 2,
    "text": "You spent the weekend getting even ooh",
    "translationText": "你整个周末都陪着我",
    "startTime": 15070,
    "endTime": 22730,
    "sentenceId": "lbko4zrxkf13qa8aqiwgqonl"
  },
  {
    "uuid": "0a1bae31-f102-42de-871a-f6478a78ea46",
    "id": 3,
    "text": "We spent the late nights making things right between us",
    "translationText": "经过无数个日日夜夜 我们终于踏上正轨",
    "startTime": 22730,
    "endTime": 30310,
    "sentenceId": "beaziy4hr4chhb2xpkokh8l4"
  },
  {
    "uuid": "f429db1a-92f9-49a1-ba20-73ad431f099e",
    "id": 4,
    "text": "But now it's all good babe",
    "translationText": "现在一切都好起来了 宝贝",
    "startTime": 30310,
    "endTime": 32330,
    "sentenceId": "o5olyjuhf7uy3y69cbqxca7u"
  }
]

**输出结果：**
[
  {
    "uuid": "430289e6-2c58-4115-a1f2-2cbde3f08eb0",
    "id": 1,
    "text": "Spent 24 hours I need more hours with you",
    "translationText": "和你一天24个小时都待在一起 我仍觉得不够",
    "startTime": 6960,
    "endTime": 12420,
    "sentenceId": "qs12ng4wpd9qfb10c1kz3uje"
  },
  {
    "uuid": "b0894a9e-d2ba-4c1e-b829-7b19d728ca8d",
    "id": 2,
    "text": "You spent the weekend getting even ooh",
    "translationText": "你整个周末都陪着我",
    "startTime": 14680,
    "endTime": 19860,
    "sentenceId": "lbko4zrxkf13qa8aqiwgqonl"
  },
  {
    "uuid": "0a1bae31-f102-42de-871a-f6478a78ea46",
    "id": 3,
    "text": "We spent the late nights making things right between us",
    "translationText": "经过无数个日日夜夜 我们终于踏上正轨",
    "startTime": 22360,
    "endTime": 28060,
    "sentenceId": "beaziy4hr4chhb2xpkokh8l4"
  },
  {
    "uuid": "f429db1a-92f9-49a1-ba20-73ad431f099e",
    "id": 4,
    "text": "But now it's all good babe",
    "translationText": "现在一切都好起来了 宝贝",
    "startTime": 30120,
    "endTime": 32240,
    "sentenceId": "o5olyjuhf7uy3y69cbqxca7u"
  }
]

请严格按照上述规则进行字幕合并，确保输出格式与示例完全一致。
`
