import { MAX_AUDIO_SIZE, MAX_VIDEO_SIZE } from '@julebu/shared'
import { TRPCError } from '@trpc/server'
import { config } from '@/config/config'

// Submit接口返回类型
export interface SubmitResponse {
  code: number
  message: string
  id?: string // 只有成功时才有id
}

// Query接口返回类型
export interface QueryResponse {
  id: string
  code: number
  message: string
  duration?: number // 音频时长（秒）
  utterances?: Array<{
    text: string // 完整文本段
    start_time: number // 开始时间（毫秒）
    end_time: number // 结束时间（毫秒）
    words?: Array<{
      text: string
      start_time: number
      end_time: number
    }>
  }>
}

interface SubmitParams {
  // 字幕语言类型
  language: string
  // 每行最多展示字数，默认46
  words_per_line?: number
  // 每屏最多展示行数，默认1
  max_lines?: number
  // 字幕识别类型：speech(语音)、singing(歌曲)、auto(自动)，默认auto
  caption_type?: 'speech' | 'singing' | 'auto'
  // 是否使用数字转换功能，默认false
  use_itn?: boolean
  // 是否增加标点，默认false
  use_punc?: boolean
  // 是否使用顺滑标注水词，默认false
  use_ddc?: boolean
  // 是否返回说话人信息，默认false
  with_speaker_info?: boolean
}

interface QueryParams {
  // 任务ID（从submit接口返回）
  id: string
  // 查询结果阻塞模式，默认1（阻塞）
  blocking?: number
}

interface SpeechSubmitInput {
  fileData: Uint8Array
  fileName: string
  contentType: string
  queryParams: SubmitParams
}

export class SpeechService {
  async submitSpeechRecognition(input: SpeechSubmitInput): Promise<SubmitResponse> {
    try {
      const { fileData, fileName, contentType, queryParams } = input
      // 添加文件大小验证
      const fileSizeBytes = fileData.byteLength

      // 根据内容类型判断大小限制
      if (contentType?.startsWith('video/')) {
        if (fileSizeBytes > MAX_VIDEO_SIZE) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: `视频文件大小不能超过 ${MAX_VIDEO_SIZE / 1024 / 1024}MB`,
          })
        }
      } else {
      // 默认按音频处理
        if (fileSizeBytes > MAX_AUDIO_SIZE) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: `音频文件大小不能超过 ${MAX_AUDIO_SIZE / 1024 / 1024}MB`,
          })
        }
      }

      // 从配置获取语音服务配置
      if (!config.speech.appid || !config.speech.accessToken) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Missing speech configuration',
        })
      }

      // 构建火山引擎API的URL
      const apiUrl = new URL('https://openspeech.bytedance.com/api/v1/vc/submit')

      // 添加必需的appid参数
      apiUrl.searchParams.append('appid', config.speech.appid)

      // 添加其他参数
      if (queryParams) {
        Object.entries(queryParams).forEach(([key, value]) => {
          if (value !== undefined && value !== null)
            apiUrl.searchParams.append(key, String(value))
        })
      }

      // 构造FormData发送给火山引擎
      const formData = new FormData()
      const blob = new Blob([fileData], {
        type: contentType,
      })
      formData.append('data', blob, fileName)

      // 转发请求到火山引擎API
      const response = await fetch(apiUrl.toString(), {
        method: 'POST',
        headers: {
          Authorization: `Bearer; ${config.speech.accessToken}`,
        },
        body: formData,
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API Error:', errorText)
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `API request failed: ${response.statusText} - ${errorText}`,
        })
      }

      const result: SubmitResponse = await response.json()
      return result
    } catch (error) {
      console.error('💥 Speech recognition API error:', error)
      if (error instanceof TRPCError) {
        throw error
      }
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      })
    }
  }

  async querySpeechRecognition(queryParams: QueryParams): Promise<QueryResponse> {
    try {
      // 从配置获取语音服务配置
      if (!config.speech.appid || !config.speech.accessToken) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Missing speech configuration',
        })
      }

      // 构建火山引擎API的URL
      const apiUrl = new URL('https://openspeech.bytedance.com/api/v1/vc/query')

      // 添加必需的appid参数
      apiUrl.searchParams.append('appid', config.speech.appid)

      // 添加其他参数
      Object.entries(queryParams).forEach(([key, value]) => {
        if (value !== undefined && value !== null)
          apiUrl.searchParams.append(key, String(value))
      })

      // 转发请求到火山引擎API
      const response = await fetch(apiUrl.toString(), {
        method: 'GET',
        headers: {
          Authorization: `Bearer; ${config.speech.accessToken}`,
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ VolcEngine API Error:', errorText)

        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `API request failed: ${response.statusText} - ${errorText}`,
        })
      }

      const result: QueryResponse = await response.json()
      return result
    } catch (error) {
      console.error('💥 Speech recognition query API error:', error)
      if (error instanceof TRPCError) {
        throw error
      }
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: error instanceof Error ? error.message : 'Internal server error',
      })
    }
  }
}

export const speechService = new SpeechService()
