import { z } from 'zod'
import { publicProcedure, router } from '../../trpc'
import { speechService } from './speech.service'

const submitParamsSchema = z.object({
  // 字幕语言类型
  language: z.string(),
  // 每行最多展示字数，默认46
  words_per_line: z.number().int().optional().default(46),
  // 每屏最多展示行数，默认1
  max_lines: z.number().int().optional().default(1),
  // 字幕识别类型：speech(语音)、singing(歌曲)、auto(自动)，默认auto
  caption_type: z.enum(['speech', 'singing', 'auto']).optional().default('auto'),
  // 是否使用数字转换功能，默认false
  use_itn: z.boolean().optional().default(false),
  // 是否增加标点，默认false
  use_punc: z.boolean().optional().default(false),
  // 是否使用顺滑标注水词，默认false
  use_ddc: z.boolean().optional().default(false),
  // 是否返回说话人信息，默认false
  with_speaker_info: z.boolean().optional().default(false),
})

const queryParamsSchema = z.object({
  // 任务ID（从submit接口返回）
  id: z.string(),
  // 查询结果阻塞模式，默认1（阻塞）
  blocking: z.number().int().optional().default(1),
})

const submitSpeechSchema = z.object({
  fileData: z.instanceof(Uint8Array),
  fileName: z.string(),
  contentType: z.string(),
  queryParams: submitParamsSchema,
})

const querySpeechSchema = z.object({
  queryParams: queryParamsSchema,
})

export const speechRouter = router({
  submit: publicProcedure
    .input(submitSpeechSchema)
    .mutation(async ({ input }) => {
      return speechService.submitSpeechRecognition(input)
    }),

  query: publicProcedure
    .input(querySpeechSchema)
    .query(async ({ input }) => {
      return speechService.querySpeechRecognition(input.queryParams)
    }),
})
