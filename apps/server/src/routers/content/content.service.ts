import { TRPCError } from '@trpc/server'
import {
  checkFormatContentDiamondBalance,
  checkWordStoryDiamondBalance,
} from '../currency/checker'
import { currencyService } from '../currency/currency.service'
import {
  formatSentenceToEnglish,
  formatSentenceToEnglishAndChinese,
  FormatType,
  formatVocabularyToEnglish,
  formatVocabularyToEnglishAndChinese,
} from './format'
import { generateWithWordsLLM } from './generate'

async function format(content: string, formatType: string, userId: string) {
  // 检查用户是否有足够的钻石
  const diamondConsumption = await checkFormatContentDiamondBalance(
    userId,
    content,
  )

  try {
    // 根据格式化类型执行相应的格式化操作
    let formattedContent
    switch (formatType) {
      case FormatType.SentenceToEnglish:
        formattedContent = await formatSentenceToEnglish(content)
        break
      case FormatType.SentenceToEnglishAndChinese:
        formattedContent = await formatSentenceToEnglishAndChinese(content)
        break
      case FormatType.VocabularyToEnglish:
        formattedContent = await formatVocabularyToEnglish(content)
        break
      case FormatType.VocabularyToEnglishAndChinese:
        formattedContent = await formatVocabularyToEnglishAndChinese(content)
        break
      default:
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '不支持的格式化类型',
        })
    }

    // 扣除钻石
    await currencyService.consumeDiamonds(
      userId,
      diamondConsumption,
      `格式化内容 "${content.substring(0, 20)}${content.length > 20 ? '...' : ''}"`,
    )

    return formattedContent
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '格式化内容失败，请稍后重试',
      cause: error,
    })
  }
}

async function generateWithWords(
  words: string,
  englishLevel: string,
  userId: string,
) {
  // 检查用户是否有足够的钻石
  const diamondConsumption = await checkWordStoryDiamondBalance(userId, words)

  try {
    // 生成单词故事
    const content = await generateWithWordsLLM(words, englishLevel)

    // 扣除钻石
    await currencyService.consumeDiamonds(
      userId,
      diamondConsumption,
      `生成单词故事 "${words.substring(0, 20)}${words.length > 20 ? '...' : ''}"`,
    )

    return { content }
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '生成单词故事失败，请稍后重试',
      cause: error,
    })
  }
}

export const contentService = {
  format,
  generateWithWords,
}
