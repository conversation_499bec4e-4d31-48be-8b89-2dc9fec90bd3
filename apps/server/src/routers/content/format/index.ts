import { createFormatInputModelLLM } from '@/llm/task'

export enum FormatType {
  SentenceToEnglish = 'sentenceToEnglish',
  SentenceToEnglishAndChinese = 'sentenceToEnglishAndChinese',
  VocabularyToEnglish = 'vocabularyToEnglish',
  VocabularyToEnglishAndChinese = 'vocabularyToEnglishAndChinese',
}

export async function formatSentenceToEnglish(content: string) {
  const prompt = `
  你是一个专业的文本处理助手，擅长提取和格式化英文句子。

  请将给定的内容识别并格式化为独立的英文句子。
任务要求：
1. 删除无关信息和噪音。
2. 对话形式仅保留实际对话内容。
3. 移除中文翻译或解释。
4. 专注于提取和格式化英文句子。
5. 确保句子完整且有意义。
6. 保持原文顺序。
7. 使用英文标点符号（如. , ? '"）替换非英文符号，并统一引号使用（双引号或单引号）。
8. 句子以大写字母开头，以适当标点结尾。
输出格式：
每个元素为一个独立的英文句子。格式如下：
  Sentence 1.
  Sentence 2.
  Sentence 3.

例子1：
    输入:
        Assistant: Do you like this dress, madam?
        Lady: I like the colour very much. It’s a lovely dress, but it’s too small for me.
    输出：
        Do you like this dress, madam?
        I like the colour very much.
        It's a lovely dress, but it's too small for me.
    原因：因为是对话形式，所以只保留对话的实际内容。

例子2:
  输入：
    Once you arrived the border, you hand over your passport and give them the “yellow paper”. 
    They keep your passport and will call you when it’s your turn.
  输出：
      Once you arrived the border, you hand over your passport and give them the 'yellow paper'.
      They keep your passport and will call you when it's your turn.
  原因：把中文的符号（引号和逗号）替换成英文的符号

例子3：
  输入：How do you spell "intelligent'?
  输出：
    How do you spell 'intelligent'?
  原因：把引号统一成英文的引号(一致) 

注意：
- 不添加额外解释或评论。
请基于以上要求处理以下内容：
${content}
`

  const model = createFormatInputModelLLM()
  const response = await model.invoke(prompt, 'format_input', {
    content,
  })

  return response
}

export async function formatVocabularyToEnglishAndChinese(content: string) {
  const prompt = `
  你是一个专业的文本处理助手，擅长提取和格式化英文句子。

    请按照以下要求处理给定内容，将其格式化为中英文对照的单词或者词组:
  任务要求：
  1. 删除所有无关信息和噪音。
  3. 保留英文单词或者词组及其对应的中文翻译（如果有）。
  4. 专注于提取和格式化英文单词或者词组。
  5. 确保每个单词或者词组都是完整且有意义的。
  6. 保持原文的顺序。
  7. 如果一个英文单词或者词组有对应的中文翻译，请将它们分开成两个独立的条目。
  8. 保持顺序和意思不变。
  9. 确保每个单词或者词组都是正确且有意义的。
  10. 不要遗漏任何重要信息。

  输出格式：
  每个元素可以是独立的英文单词或者词组。格式如下：
    English word 1.
    中文翻译1（如果有）
    English word 2.
    English word 3.
    中文翻译3（如果有）
例子1：
    输入:
    - Dog - 狗
    - Cat - 猫
    - Fish - 鱼
    - Bird
    - Puppy Love - 初恋
    - Dog Days - 三伏天

    输出：
    Dog
    狗
    Cat
    猫
    Fish
    鱼
    Bird
    初恋
    Dog Days
    三伏天
  注意：
  - 不要添加任何额外的解释或评论。
  - 中文翻译应紧跟在对应的英文单词或词组后面。
  - 如果某个单词或词组没有对应的中文翻译，则不需要添加空的中文条目。
  请基于以上要求处理以下内容：
  ${content}
  `

  const model = createFormatInputModelLLM()
  const response = await model.invoke(prompt, 'format_input', {
    content,
  })

  return response
}

export async function formatSentenceToEnglishAndChinese(content: string) {
  const prompt = `
  你是一个专业的文本处理助手，擅长提取和格式化英文句子。

    请按照以下要求处理给定内容，将其格式化为独立的英文句子或中英文对照句:
  任务要求：
  1. 删除所有无关信息和噪音。
  2. 如果是对话形式，只保留对话的实际内容。
  3. 保留英文句子及其对应的中文翻译（如果有）。
  4. 专注于提取和格式化英文句子或中英文对照的句子。
  5. 确保每个句子都是完整且有意义的。
  6. 保持原文的顺序。
  7. 将长句子拆分成更短的独立句子。每个句子应该表达一个完整的意思。
  8. 如果一个英文句子有对应的中文翻译，请将它们分开成两个独立的条目。
  9. 保持原文的顺序和意思不变。
  10. 确保每个句子都是语法正确且有意义的。
  11. 不要遗漏任何重要信息。
例如1：
    "It is eight o'clock. The children go to school by car every day, but today, they are going to school on foot."
    "现在是8点钟。孩子们每天都乘小汽车去上学，而今天，他们正步行上学。"
    格式化为：
    "It is eight o'clock."
    "现在是8点钟。"
    "The children go to school by car every day."
    "孩子们每天都乘小汽车去上学。"
    "But today, they are going to school on foot."
    "而今天，他们正步行上学。"
  输出格式：
  每个元素可以是独立的英文句子或中英文对照的句子对。格式如下：
  English sentence 1.
  中文翻译1（如果有）
  English sentence 2.
  English sentence 3.
  中文翻译3（如果有）
例子2：
    输入:
        Assistant: Do you like this dress, madam?
        店员：夫人，您喜欢这件衣服吗?
        Lady: I like the colour very much. It’s a lovely dress, but it’s too small for me.
        女士：我很喜欢这颜色。这件漂亮的衣服，
    输出：
        Do you like this dress, madam?
        夫人，您喜欢这件衣服吗?
        I like the colour very much.
        我很喜欢这颜色。
        It's a lovely dress, but it's too small for me.
        这件漂亮的衣服，但是它对我来说太小了。
    原因：因为是对话形式，所以只保留对话的实际内容。并且把中文的符号（引号和逗号）替换成英文的符号

  注意：
  - 不要添加任何额外的解释或评论。
  - 中文翻译应紧跟在对应的英文句子后面。
  - 如果某个英文句子没有对应的中文翻译，则不需要添加空的中文条目。
  请基于以上要求处理以下内容：
  ${content}
  `

  const model = createFormatInputModelLLM()
  const response = await model.invoke(prompt, 'format_input', {
    content,
  })

  return response
}

export async function formatVocabularyToEnglish(content: string) {
  const prompt = `
  你是一个专业的文本处理助手，擅长提取和格式化英文句子。

  请将给定的内容识别并格式化为独立的英文单词或者词组。
任务要求：
1. 删除所有无关信息和噪音。
2. 移除所有中文翻译或解释。
3. 专注于提取和格式化英文单词。
4. 确保每个单词都是完整且有意义的。
5. 保持原文的顺序。
6. 使用英文标点符号，如句号(.)、逗号(,)、问号(?)、引号('")替换非英文的符号。
7. 确保每个句子以大写字母开头，以适当的标点符号结尾。
输出格式：
每个元素为一个独立的英文单词。格式如下：
  Word 1
  Word 2
  Word 3

例子1：
    输入:
     - Dog - 狗
     - Cat - 猫
     - Fish - 鱼
     - Bird - 鸟
     - Puppy Love - 初恋
     - Dog Days - 三伏天

    输出：
        Dog
        Cat
        Fish
        Bird
        Puppy Love
        Dog Days
    原因：只保留单词/词组本身

注意：
- 不要添加任何额外的解释或评论。
请基于以上要求处理以下内容：
${content}
`

  const model = createFormatInputModelLLM()
  const response = await model.invoke(prompt, 'format_input', {
    content,
  })

  return response
}
