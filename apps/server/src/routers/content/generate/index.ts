import { createGenerateWordStoryModelLLM } from '@/llm/task'

export async function generateWithWordsLLM(
  words: string,
  englishLevel: string,
) {
  const prompt = `
  你是一位富有创意的英语教育专家，擅长编写适合语言学习的有趣故事。
  基于给定的单词列表，创作一篇符合指定英语水平的短篇故事。

详细要求：
1. 故事必须包含所有给定的目标单词。
2. 每个句子中只能使用一个目标单词。
3. 每个目标单词在整个故事中至少出现7次。
4. 除目标单词外，其他词汇的难度不得超过${englishLevel}级别。
5. 创作内容应为连贯的短篇故事，而非独立的句子集合。
6. 故事应该有明确的开端、发展和结尾。
7. 故事长度应在80-150词之间。
8. 不要提供中文翻译。

输出格式：请返回一个故事内容

注意事项：
- 不要添加任何额外的解释或评论。
- 故事应该有趣且引人入胜，适合语言学习使用。

目标单词列表：${words}
`

  const model = createGenerateWordStoryModelLLM()
  const response = await model.invoke(
    prompt,
    'generate_word_story',
    {
      content: words,
    },
  )

  return response
}
