import { z } from 'zod'
import { protectedProcedure, router } from '../../trpc'
import { contentService } from './content.service'

export const contentRouter = router({
  format: protectedProcedure
    .input(
      z.object({
        content: z.string(),
        type: z.enum([
          'sentenceToEnglish',
          'sentenceToEnglishAndChinese',
          'vocabularyToEnglish',
          'vocabularyToEnglishAndChinese',
        ]),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return contentService.format(
        input.content,
        input.type,
        ctx.user.id,
      )
    }),

  generateWithWords: protectedProcedure
    .input(
      z.object({
        words: z.string(),
        englishLevel: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      return contentService.generateWithWords(
        input.words,
        input.englishLevel,
        ctx.user.id,
      )
    }),
})
