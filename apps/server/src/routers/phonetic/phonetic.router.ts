import { z } from 'zod'
import { protectedProcedure, router } from '../../trpc'
import { phoneticService } from './phonetic.service'

export const phoneticRouter = router({
  generate: protectedProcedure
    .input(
      z.object({
        content: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { content } = input
      return phoneticService.generate(content)
    }),
})
