import { createQueryPhoneticModelLLM } from '@/llm/task'
import { parseJsonFromCodeBlock } from '@/utils/json'

interface PhoneticResult {
  word: string
  uk: string
  us: string
}

async function getCompletion(prompt: string) {
  const modelLLM = createQueryPhoneticModelLLM()
  const response = await modelLLM.invoke(prompt, 'query_phonetic')
  return response
}

export async function queryPhonetic(word: string) {
  const result = await getCompletion(`
    你是一位精确的英语音标生成专家。请为给定的单词生成准确的音标。

    要求：
    1. 必须返回完整的 JSON 格式结果，不要包含任何其他内容。
    2. JSON 结构为：{"word": string, "uk": string, "us": string}
    3. "uk" 表示英式音标，"us" 表示美式音标。
    4. 音标必须使用国际音标符号（IPA）。
    5. 确保 "uk" 和 "us" 字段都不为空，且内容正确。
    6. 如果是复合词或短语，也要提供完整的音标。
    7. 检查并验证你的输出是否准确无误。
    8. 如果不确定某个音标，宁可不填写也不要填写错误信息。

    示例输出：
    {"word": "example", "uk": "ɪɡˈzɑːmpl", "us": "ɪɡˈzæmpl"}

    现在，请为以下单词生成音标：
    word = ${word}
    `)

  return parseJsonFromCodeBlock<PhoneticResult>(result)
}
