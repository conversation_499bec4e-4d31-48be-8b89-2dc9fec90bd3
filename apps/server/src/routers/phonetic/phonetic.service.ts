import { getDB, schemas } from '@/db'
import { getPhoneticByYoudao } from '@/services/youdao'
import { normalizeContent } from '../sentence/content'
import { queryPhonetic } from './queryPhonetic'

// 检查数据库中是否存在单词
async function checkWordExists(word: string) {
  const db = getDB()
  const result = await db.query.phonetic.findFirst({
    where: (phonetic, { eq }) => eq(phonetic.word, word),
  })

  return Boolean(result)
}

// 插入单词数据到数据库
async function insertWordData({
  word,
  uk,
  us,
}: {
  word: string
  uk: string
  us: string
}) {
  try {
    const db = getDB()
    await db.insert(schemas.phonetic).values({ word, uk, us })
    return true
  } catch {
    console.error('Error inserting word data:', word)
  }
}

// 在数据库中查找单词
export async function findWord(word: string) {
  const db = getDB()
  return db.query.phonetic.findFirst({
    where: (phonetic, { eq }) => eq(phonetic.word, word),
  })
}

async function generate(content: string) {
  const words = generateWords(normalizeContent(content))
  const needQueryWords: string[] = []
  for (const word of words) {
    const exist = await checkWordExists(word.toLowerCase())
    if (!exist) {
      needQueryWords.push(word)
    }
  }
  if (needQueryWords.length === 0)
    return true

  const uniqueQueryWords = Array.from(new Set(needQueryWords))

  // 首先并发使用 getPhonetic 查询音标
  const youdaoTasks = uniqueQueryWords.map(async word => getPhoneticByYoudao(word))
  const youdaoResults = await Promise.all(youdaoTasks)

  // 对于 getPhonetic 查询失败的单词，使用 queryPhonetic 再次查询
  const llmTasks = youdaoResults.map(async (result, index) => {
    if (result.us && result.uk) {
      return Promise.resolve(result)
    } else {
      return queryPhonetic(uniqueQueryWords[index])
    }
  })

  const llmResults = await Promise.all(llmTasks)

  const insertTasks = llmResults.map(async (result) => {
    if (result) {
      const { us, uk, word } = result
      const normalizedWord = word.toLowerCase()
      return insertWordData({
        word: normalizedWord,
        uk: uk ? normalizePhoneticSymbols(uk) : normalizedWord, // 没有生成音标的话 就直接用当前的 normalizedWord 作为音标
        us: us ? normalizePhoneticSymbols(us) : normalizedWord,
      })
    }
    return Promise.resolve(false)
  })

  await Promise.all(insertTasks)

  return true
}

function normalizePhoneticSymbols(phonetic: string) {
  // LLM 生成的音标 有时候可能会带上 //  或者 [ ]  的符号
  // 所有需要去除掉
  if (!phonetic)
    return ''
  const regex = /[/[\]]/g
  return phonetic.replace(regex, '')
}

function generateWords(content: string) {
  const isWord = (text: string) => /^[a-z0-9]/i.test(text)
  return [...new Set(content.split(' ').filter(isWord))]
}

function splitToWord(sentence: string) {
  return sentence.match(/\b[\w'-]+\b/g) || []
}

async function generateSentencePhonetic(sentence: string) {
  const words = splitToWord(sentence)

  let usResult = ''
  // const ukResult = "";
  for (const word of words) {
    const wordInfo = await findWord(word.toLowerCase())
    const us = wordInfo ? `/${wordInfo.us}/` : `/${word}/`
    usResult += `${us} `

    // TODO 后续扩展 uk
    // const uk = wordInfo?.uk || word;
    // ukResult += `/${uk}/ `;
  }

  return usResult

  // return {
  //   us: usResult,
  //   uk: ukResult,
  // };
}

export const phoneticService = {
  findWord,
  generate,
  generateSentencePhonetic,
}
