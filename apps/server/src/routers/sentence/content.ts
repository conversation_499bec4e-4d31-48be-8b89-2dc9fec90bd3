import nlp from 'compromise'

export function normalizeContent(inputString: string) {
  if (!inputString)
    return ''

  // 处理英文缩写形式
  // 处理各种情况：can"t/can't, it"s/it's, he"s/he's, he"ve/he've 等
  const contractionRegex = /\b(\w+)(["“’])(\w+)\b/g
  let processedString = inputString.replace(
    contractionRegex,
    (match, word, quote, suffix) => {
      // 将所有形式统一替换为标准的缩写形式，使用单引号
      return `${word}'${suffix}`
    },
  )

  // 先把中文的符号替换成英文的
  processedString = processedString
    .replace(/[“”]/g, '"')
    .replace(/[‘’]/g, '\'')
    .replace(/，/g, ',')
    .replace(/。/g, '.')
    .replace(/！/g, '!')
    .replace(/？/g, '?')
    .replace(/：/g, ':')
    .replace(/——/g, '—')

  const abbreviations = ['Mr', 'Mrs', 'Dr'] // 根据需要添加更多

  const doc = nlp(processedString)
  let str = ''
  const termList = doc.termList()
  termList.forEach((term) => {
    const pre = term.pre.trim()
    const post = term.post.trim()

    if (pre === '\'' || pre === '"') {
      term.pre = `${pre} `
    }

    if (
      post === '\''
      || post === '"'
      || post === '",'
      || post === '".'
      || post === '"?'
      || post === '"!'
      || post === '\'.'
      || post === '" —'
      || post === '\' —'
    ) {
      term.post = ` ${post} `
    }

    // 这是处理所有格的情况
    // 例如： students' pigs'
    if ((term.tags?.has('Possessive') || term.tags?.has('Noun')) && post === '\'') {
      if (term.text.endsWith('s')) {
        term.post = term.post.trimStart()
      }
    }

    str += term.pre + term.text + term.post
  })

  processedString = str

  // 处理破折号，确保破折号前后有空格
  processedString = processedString.replace(/—/g, ' — ')

  // 检测逗号、句号或问号前面是不是字母，如果是的话就加上一个空格
  // 但是要先排除缩写词的情况
  processedString = processedString.replace(
    /([a-z0-9])([,.!?;:\])}])/gi,
    (match, p1: string, p2: string, offset: number) => {
      // 如果前后都是数字，则保持原样
      // 例如是这样的 3.22
      if (/\d/.test(p1) && p2 === '.' && offset + 2 < processedString.length && /\d/.test(processedString[offset + 2])) {
        return match
      }

      // 如果是时间格式（数字:数字），则保持原样
      // 例如是这样的 15:00, 9:30, 23:45
      if (/\d/.test(p1) && p2 === ':' && offset + 2 < processedString.length && /\d/.test(processedString[offset + 2])) {
        return match
      }

      // 从当前位置向前查找，直到找到空格或字符串开始
      let start = offset
      while (start > 0 && processedString[start - 1] !== ' ') {
        start--
      }
      // 获取完整的单词
      const word = processedString.slice(start, offset + 1)
      if (abbreviations.includes(word)) {
        return match // 如果是缩写词，保持原样
      }
      return `${p1} ${p2}` // 如果不是缩写词，添加空格
    },
  )

  // 检测逗号、句号或问号后面是不是字母，如果是的话就加上一个空格
  processedString = processedString.replace(
    /([,.!?;:([{])([a-z])/gi,
    '$1 $2',
  )

  // 处理冒号+双引号的 case
  processedString = processedString.replace(/(:")([a-z])/gi, '$1 $2')

  // 处理省略号后面紧跟字母或数字的情况，添加空格
  processedString = processedString.replace(/(\.\.\.)([a-z0-9])/gi, '$1 $2')

  // 替换多个连续空格为单个空格
  processedString = processedString.replace(/\s+/g, ' ')

  // 如果两个符号相邻，删除它们之间的空格
  processedString = processedString.replace(
    /([,.!?"']) (["',.!?])/g,
    '$1$2',
  )

  // 有可能在上面的处理过程中加上了空格
  processedString = processedString.trim()

  // 这几个后面带 . 的单个出现的单词 不可以去除
  const singleWordAbbreviations = ['Mr.', 'Mrs.', 'Miss', 'Ms.', 'Dr.', 'Prof.']
  if (!singleWordAbbreviations.includes(processedString)) {
    processedString = processedString.replace(/[.,]$/, '')
  }

  return processedString.trim()
}
