import { z } from 'zod'
import { createGenerateLearningContentModelLLM } from '@/llm/task'
import { parseJsonFromCodeBlock } from '@/utils/json'

export interface LearningContent {
  overview: {
    chineseTranslation: string
    englishExplanation: string
    functionAndContext: string
  }
  vocabularyAnalysis: Array<{
    word: string
    chineseTranslation: string
    phonetic: string
    pos: string
    basicMeaning: string
    contextualMeaning: string
    commonPhrases: string
    synonyms: string
    antonyms: string
    memorizationTip: string
    exampleSentence: string
  }>
  grammarAnalysis: {
    sentenceType: string
    sentenceComponents: Array<{
      component: string
      grammaticalExplanation: string
      meaningInSentence: string
    }>
    tenseAndMood: string
    keyGrammarPoints: string
    specialStructures: string
    wordOrder: string
    grammarRulesApplication: string
    commonErrors: string
  }
  relatedExamples: Array<{
    example: string
    explanation: string
  }>
  culturalAndPracticalKnowledge: {
    culturalElements: string
    backgroundInformation: string
    practicalApplication: string
  }
}

// const modelLLM = createModelLLM(0.5);

// 定义各部分的 Schema
const overviewSchema = z.object({
  overview: z.object({
    chineseTranslation: z.string(),
    englishExplanation: z.string(),
    functionAndContext: z.string(),
  }),
})

const vocabularyItemSchema = z.object({
  word: z.string(),
  chineseTranslation: z.string(),
  phonetic: z.string(),
  pos: z.string(),
  basicMeaning: z.string(),
  contextualMeaning: z.string(),
  commonPhrases: z.string(),
  synonyms: z.string(),
  antonyms: z.string(),
  memorizationTip: z.string(),
  exampleSentence: z.string(),
})

const vocabularySchema = z.object({
  vocabularyAnalysis: z.array(vocabularyItemSchema),
})

const grammarSchema = z.object({
  grammarAnalysis: z.object({
    sentenceType: z.string(),
    sentenceComponents: z.array(
      z.object({
        component: z.string(),
        grammaticalExplanation: z.string(),
        meaningInSentence: z.string(),
      }),
    ),
    tenseAndMood: z.string(),
    keyGrammarPoints: z.string(),
    specialStructures: z.string(),
    wordOrder: z.string(),
    grammarRulesApplication: z.string(),
    commonErrors: z.string(),
  }),
})

const examplesSchema = z.object({
  relatedExamples: z.array(
    z.object({
      example: z.string(),
      explanation: z.string(),
    }),
  ),
})

const culturalSchema = z.object({
  culturalAndPracticalKnowledge: z.object({
    culturalElements: z.string(),
    backgroundInformation: z.string(),
    practicalApplication: z.string(),
  }),
})

async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
): Promise<T> {
  let lastError: Error | undefined

  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await operation()
      return result
    } catch (error) {
      lastError = error as Error
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
      }
    }
  }

  throw lastError
}

async function validateAndParseJson<T>(
  jsonString: string,
  schema: z.ZodType<T>,
  retryCount: number = 3,
): Promise<T> {
  try {
    const parsedJson = parseJsonFromCodeBlock(jsonString)
    return schema.parse(parsedJson)
  } catch (error) {
    if (retryCount > 0) {
      await new Promise(resolve => setTimeout(resolve, 1000))
      return validateAndParseJson(jsonString, schema, retryCount - 1)
    }
    throw error
  }
}

async function getCompletion(
  prompt: string,
  systemMessage: string,
  schema: z.ZodType<any>,
  sentence: string,
  maxRetries: number = 3,
) {
  return retryOperation(async () => {
    const fullPrompt = `system: ${systemMessage}\n\nuser: ${prompt}`

    const model = createGenerateLearningContentModelLLM()
    const response = await model.invoke(fullPrompt, 'generate_learning_content', {
      content: sentence,
    })

    return validateAndParseJson(response, schema)
  }, maxRetries)
}

export async function generateLearningContentLLM(
  sentence: string,
): Promise<LearningContent | undefined> {
  const systemMessage = `
你现在是一位经验丰富的英语教授，正在编写一本英语学习教材。请为以下英语句子提供全面的讲解，这个讲解将作为教材中的一个重要部分。请以JSON格式输出你的分析：
`

  const commonInstructions = `
要求：
1. 确保解释专业且易懂，适合各级英语学习者。
2. 注重实际应用，使学习者能在日常交流中灵活运用。
3. 详细分析并填充JSON结构中的每个字段，保证内容的完整性和准确性。
4. 适当考虑学习者的认知水平，循序渐进地介绍复杂概念。
5. 在适当的地方加入助记技巧或有趣的知识点，增强学习兴趣。
6. 保持语言表述的一致性和连贯性，便于学习者理解和记忆。
7. 如某些内容不适用，请返回空字符串。
`

  const overviewPrompt = `
句子：${sentence}

请生成以下JSON格式的概述：
{
  "overview": {
    "chineseTranslation": "中文翻译",
    "englishExplanation": "简明英文解释",
    "functionAndContext": "句子的交际功能和使用场景(中文)"
  }
}

${commonInstructions}
提供深入广泛的讲解，帮助学习者全面掌握句子的语言知识点。
`

  const vocabularyPrompt = `
句子：${sentence}

请分析句子中的关键词或词组，生成以下JSON格式的词汇分析：
{
  "vocabularyAnalysis": [
    {
      "word": "关键词或词组",
      "chineseTranslation": "中文翻译",
      "phonetic": "音标",
      "pos": "词性(中文)",
      "basicMeaning": "基本含义(中文)",
      "contextualMeaning": "在句中的具体含义和作用(中文)",
      "commonPhrases": "常见搭配和固定表达(中文)",
      "synonyms": "同义词(string)",
      "antonyms": "反义词(string)",
      "memorizationTip": "提供一个有趣或有效的方法来记忆这个单词，可以是谐音法、联想法等(中文)",
      "exampleSentence": "使用该词的新造句"
    }
  ]
}

${commonInstructions}
1. 特别注意提供有趣或有效的记忆方法，以增强学习兴趣。
2. 先分析句子的难度然后在基于难度去找到符合当前难度的关键的词和词组，切记不要把不符合当前难度词汇和词组也找出来。
3. 不可以为空
4. 只分析单词和词组 
5. 不分析符号 比如句号 逗号省略号等
6. 不分析人名 比如 Peppa、 Liming
7. 不分析代词 比如 it、this、that、these、those
8. 不分析定冠词 比如 the a an
8. 请基于整个句子上下文去分析里面的缩写形式， 比如  it's stopped raining. 基于整个句子上下文分析 It's 是 it has 的缩写
`

  const grammarPrompt = `
句子：${sentence}

请生成以下JSON格式的语法分析：
{
  "grammarAnalysis": {
    "sentenceType": "句子类型（如简单句、并列句、复合句等）",
    "sentenceComponents": [
      {
        "component": "成分1",
        "grammaticalExplanation": "语法解释1",
        "meaningInSentence": "在句子中的意思1"
      }
    ],
    "tenseAndMood": "时态和语气的使用及其效果",
    "keyGrammarPoints": "关键语法点解释（如副词的位置、介词短语的作用等）",
    "specialStructures": "特殊结构解释（如有）",
    "wordOrder": "词序分析及其重要性",
    "grammarRulesApplication": "语法规则的应用及其在日常交流中的重要性",
    "commonErrors": "常见语法错误及如何避免"
  }
}

${commonInstructions}
请基于整个句子上下文去分析里面的缩写形式， 比如  it's stopped raining. 基于整个句子上下文分析 It's 是 it has 的缩写

请在sentenceComponents部分对句子成分进行详细分析，包括成分、语法解释和在句子中的意思。参考以下例子：

"Every Sunday, my mother brings some clean clothes and fresh fruit to me and takes my dirty dresses and T-shirts away."
这个句子可以分解为以下成分：
"Every Sunday" - 这是一个时间状语短语，表明动作发生的频率是每周日。
"my mother" - 这是主语部分，指代的是说话者的母亲。
"brings" - 这是谓语动词，表示"带来"的动作。
"some clean clothes and fresh fruit" - 这是一个名词短语，作为宾语，具体描述了母亲带来的物品，即一些干净的衣服和新鲜的水果。
"to me" - 这是一个介词短语，说明了这些物品的目的地，即给说话者。
"and takes" - 连接两个并列的谓语动词，第二个动词同样由"takes"构成，表达的是带走的动作。
"my dirty dresses and T-shirts" - 这也是一个名词短语，作为第一个"takes"动词的宾语，指的是说话者需要被拿走的脏衣服（裙子和T恤）。
"away" - 这是副词，与"takes"搭配使用，进一步解释了带走的动作方向或结果。
`

  const examplesPrompt = `
句子：${sentence}

请生成3个相关例句，并以下列JSON格式输出：
{
  "relatedExamples": [
    {
      "example": "使用相似结构或关键词的例句",
      "explanation": "简要说明这个例句如何加深对原句的理解"
    }
  ]
}

${commonInstructions}
确保生成的例句能够帮助学习者更好地理解和应用原句的结构或关键词。
`

  const culturalPrompt = `
句子：${sentence}

请生成以下JSON格式的文化和实践知识：
{
  "culturalAndPracticalKnowledge": {
    "culturalElements": "解释句子中可能涉及的文化元素(中文)",
    "backgroundInformation": "相关的文化知识或背景信息(中文)",
    "practicalApplication": "在日常生活中如何应用这个句子表达的概念(中文)"
  }
}

${commonInstructions}
特别注重提供深入的文化背景和实际应用信息，帮助学习者更好地理解和使用这个句子。
`

  try {
    const results = await Promise.all([
      getCompletion(overviewPrompt, systemMessage, overviewSchema, sentence),
      getCompletion(vocabularyPrompt, systemMessage, vocabularySchema, sentence),
      getCompletion(grammarPrompt, systemMessage, grammarSchema, sentence),
      getCompletion(examplesPrompt, systemMessage, examplesSchema, sentence),
      getCompletion(culturalPrompt, systemMessage, culturalSchema, sentence),
    ])

    return {
      ...results[0],
      ...results[1],
      ...results[2],
      ...results[3],
      ...results[4],
    } as LearningContent
  } catch (error) {
    console.error('Error generating learning content:', error)
    throw error
  }
}
