import type { LearningContent } from '@julebu/shared'
import { schemas as sharedSchemas } from '@julebu/shared'
import { TRPCError } from '@trpc/server'
import { eq } from 'drizzle-orm'
import { getDB, schemas } from '@/db'
import { generateLearningContentLLM } from './generate'

export async function generateLearningContent(sentence: {
  id: string
  chinese: string
  content: string
}): Promise<LearningContent> {
  const db = getDB()
  const result = await generateLearningContentLLM(sentence.content)

  if (!result) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '生成学习内容失败',
    })
  }

  if (!sharedSchemas.validateLearningContent(result)) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '大模型处理失败(偷懒生成的格式缺失)，请稍后再试',
    })
  }

  // 如果用户设置了句子的 chinese 那么就使用用户设置的 chinese 翻译
  if (sentence.chinese) {
    result.overview.chineseTranslation = sentence.chinese
  }

  await db
    .update(schemas.sentence)
    .set({
      learningContent: result,
    })
    .where(eq(schemas.sentence.id, sentence.id))

  return result
}
