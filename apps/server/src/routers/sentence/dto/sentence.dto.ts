import type { Sentence, WordDetail } from '@julebu/shared'
import type { SentenceWithElementEntity } from '../entity/sentence.entity'
import { elementMapper } from '@/routers/element/dto/element.dto'

export const sentenceMapper = {
  toDTO(sentenceWithElementEntity: SentenceWithElementEntity): Sentence {
    return {
      id: sentenceWithElementEntity.id,
      content: sentenceWithElementEntity.content,
      chinese: sentenceWithElementEntity.chinese,
      learningContent: sentenceWithElementEntity.learningContent as any,
      courseId: sentenceWithElementEntity.courseId,
      elements: sentenceWithElementEntity.elements.map(elementMapper.toDTO),
      position: sentenceWithElementEntity.position,
      wordDetails: sentenceWithElementEntity.wordDetails as WordDetail[],
      startTime: sentenceWithElementEntity.startTime != null ? Number(sentenceWithElementEntity.startTime) : null,
      endTime: sentenceWithElementEntity.endTime != null ? Number(sentenceWithElementEntity.endTime) : null,
    }
  },
}
