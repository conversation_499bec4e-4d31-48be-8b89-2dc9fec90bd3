import { describe, expect, it } from 'vitest'
import { sentenceMapper } from '../sentence.dto'

describe('Sentence DTO - Time Fields Migration', () => {
  describe('sentenceMapper', () => {
    it('应该从sentence对象的时间字段映射时间信息', () => {
      // 测试数据：模拟带时间字段的sentence实体
      const sentenceWithElementEntity = {
        id: 'sentence-1',
        content: 'Hello world',
        english: 'Hello world',
        chinese: '你好世界',
        position: 1,
        startTime: '12.500',
        endTime: '15.750',
        learningContent: null,
        wordDetails: [],
        dependencyAnalysis: {},
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        courseId: 'course-1',
        elements: [
          {
            id: 'element-1',
            english: 'Hello',
            chinese: '你好',
            phonetic: '/həˈloʊ/',
            type: 'word',
            position: 1,
            sentenceId: 'sentence-1',
            gameId: '',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
            image: null
          },
          {
            id: 'element-2', 
            english: 'world',
            chinese: '世界',
            phonetic: '/wɜːrld/',
            type: 'word',
            position: 2,
            sentenceId: 'sentence-1',
            gameId: '',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
            image: null
          }
        ]
      }

      const result = sentenceMapper.toDTO(sentenceWithElementEntity)

      // 验证时间字段正确映射
      expect(result.startTime).toBe(12.5)
      expect(result.endTime).toBe(15.75)
      expect(result.id).toBe('sentence-1')
      expect(result.content).toBe('Hello world')
    })

    it('应该处理没有时间字段的sentence对象', () => {
      const sentenceWithoutTime = {
        id: 'sentence-2',
        content: 'Test sentence',
        english: 'Test sentence', 
        chinese: '测试句子',
        position: 1,
        startTime: null,
        endTime: null,
        learningContent: null,
        wordDetails: [],
        dependencyAnalysis: {},
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        courseId: 'course-1',
        elements: []
      }

      const result = sentenceMapper.toDTO(sentenceWithoutTime)

      expect(result.startTime).toBeNull()
      expect(result.endTime).toBeNull()
      expect(result.id).toBe('sentence-2')
    })

    it('应该正确处理只有startTime的情况', () => {
      const sentenceWithPartialTime = {
        id: 'sentence-3',
        content: 'Partial time',
        english: 'Partial time',
        chinese: '部分时间',
        position: 1,
        startTime: '5.250',
        endTime: null,
        learningContent: null,
        wordDetails: [],
        dependencyAnalysis: {},
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        courseId: 'course-1',
        elements: []
      }

      const result = sentenceMapper.toDTO(sentenceWithPartialTime)

      expect(result.startTime).toBe(5.25)
      expect(result.endTime).toBeNull()
    })

    it('应该正确处理decimal类型的时间字段', () => {
      const sentenceWithDecimalTime = {
        id: 'sentence-4',
        content: 'Decimal time test',
        english: 'Decimal time test',
        chinese: '十进制时间测试',
        position: 1,
        startTime: '123.456',
        endTime: '789.012',
        learningContent: null,
        wordDetails: [],
        dependencyAnalysis: {},
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        courseId: 'course-1',
        elements: []
      }

      const result = sentenceMapper.toDTO(sentenceWithDecimalTime)

      expect(result.startTime).toBe(123.456)
      expect(result.endTime).toBe(789.012)
    })

    it('应该正确映射elements数组，不包含时间字段', () => {
      const sentenceWithElements = {
        id: 'sentence-5',
        content: 'Element test',
        english: 'Element test',
        chinese: '元素测试',
        position: 1,
        startTime: '10.000',
        endTime: '20.000',
        learningContent: null,
        wordDetails: [],
        dependencyAnalysis: {},
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        courseId: 'course-1',
        elements: [
          {
            id: 'element-1',
            english: 'Element',
            chinese: '元素',
            phonetic: '/ˈelɪmənt/',
            type: 'word',
            position: 1,
            sentenceId: 'sentence-5',
            gameId: '',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
            image: {
              fileKey: 'test-image.jpg',
              width: 100,
              height: 100,
              description: 'Test image'
            }
          }
        ]
      }

      const result = sentenceMapper.toDTO(sentenceWithElements)

      expect(result.elements).toHaveLength(1)
      expect(result.elements[0].id).toBe('element-1')
      expect(result.elements[0].english).toBe('Element')
      expect(result.elements[0].image).toEqual({
        fileKey: 'test-image.jpg',
        width: 100,
        height: 100,
        description: 'Test image'
      })
      // 确认element不包含时间字段
      expect(result.elements[0]).not.toHaveProperty('startTime')
      expect(result.elements[0]).not.toHaveProperty('endTime')
    })
  })
})