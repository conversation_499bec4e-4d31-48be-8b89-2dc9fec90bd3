import type { Element, WordDetail } from '@julebu/shared'
import { StatementType } from '@julebu/shared'
import { getDB, schemas } from '@/db'
import { phoneticService } from '@/routers/phonetic/phonetic.service'
import { translateToChinese } from '@/services/translate'
import { translateChunkCombine } from '@/services/translate/translateLLM'
import { normalizeChinese } from '../chinese'
import { normalizeContent } from '../content'
import { chunkSplit } from './chunkSplit'

export interface SplitSentenceResult {
  elements: Array<Element>
  chinese: string
}

/**
 * 拆分句子
 * @param sentence 要拆分的句子
 * @returns 拆分结果
 */
export async function splitSentence(sentence: {
  content: string
  id: string
  chinese: string
  wordDetails: WordDetail[]
}): Promise<SplitSentenceResult> {
  // 翻译&生成词性&拆分
  const [sentenceChinese, splitResult] = await Promise.all([
    sentence.chinese || translateToChinese(sentence.content),
    chunkSplit(sentence.content),
  ])

  let nodes: {
    content: string
    chinese: string
    context: string
    type: StatementType
  }[] = []
  splitResult.forEach((content, index) => {
    // 添加 phraseAndWord 节点
    // 如果 phraseAndWord 的长度等于 1 那么就说明 phraseAndWord 就等于 chunk 本身了 所以不需要添加
    if (content.phraseAndWord.length > 1) {
      content.phraseAndWord.forEach((item) => {
        // 添加词组的单词
        // if (item.type === "phrase") {
        //   const words = item.words.map((word: string) => {
        //     return {
        //       content: word,
        //       chinese: "",
        //       context: content.chunk,
        //     };
        //   });
        //   nodes.push(...words);
        // }

        // 添加词组/word本身
        nodes.push({
          content: item.content,
          chinese: item.chinese,
          context: content.chunk,
          type: item.type as StatementType,
        })
      })
    }

    // 添加 chunk 节点
    // 单个的 chunk 不需要有符号 只有连接起来的时候才需要加上符号
    // 如果 chunk 是个符号的话 我们不应该添加 比如 Oh, look at that jacket - it looks nice! , 这里的 - 就是一个 chunk
    if (!/^\W+$/.test(content.chunk)) {
      nodes.push({
        content: content.chunk,
        chinese: content.chinese || '',
        context: sentence.content,
        type: StatementType.Chunk,
      })

      // 添加 connect
      // 假如上一个 chunk 是符号的话 那么也就没必要添加 connect
      // 因为必然是 xxx + "-"
      // 所以 connect 也需要在 if 判断里面
      if (content.connect) {
        nodes.push({
          content: content.connect || '',
          chinese: '',
          context: sentence.content,
          type:
            index === splitResult.length - 1
              ? StatementType.Sentence
              : StatementType.CombinedChunks,
        })
      }
    }
  })

  // 基于 node.content 属性给 nodes 去重，保留原有的所有属性
  const uniqueNodes = Array.from(
    new Map(nodes.map(node => [node.content, node])).values(),
  )

  // 更新 nodes 为去重后的结果
  nodes = uniqueNodes

  // 1. 先基于 word details 的翻译来纠正只有一个单词的翻译
  // 解决的问题是一个句子里面有可能有多个重复的单词
  // 给单个 node 赋值上 word detail 生成的中文翻译
  // 这个算法是：
  //  1. 先找到所有的 word 和 phrase 类型的node
  //  2. 基于消消乐的方式  先把 phrase 转成单词，然后从 word details 里面开始消除
  //     这里的细节是只要是 phrase 的话 那么先转成单词 然后从 word details 消除  这里可以是从上到下的找 也就是 find
  //  3. 当是 word 的时候 这时候就会找到正确的那个 word detail 了， 给 word 赋值即可
  const wordDetails = [...sentence.wordDetails] // 创建一个副本，避免修改原始数据

  // 筛选出所有 word 和 phrase 类型的节点
  const wordAndPhraseNodes = nodes.filter(
    node =>
      node.type === StatementType.Word || node.type === StatementType.Phrase,
  )

  function isWord(text: string): boolean {
    if (!text)
      return false

    // 简化判断：检查第一个字符是否为字母或数字
    return /^[a-z0-9]/i.test(text)
  }

  function extractWords(text: string): string[] {
    if (!text)
      return []
    return text.split(' ').filter(isWord)
  }

  // 遍历所有 word 和 phrase 类型的节点进行处理
  wordAndPhraseNodes.forEach((node) => {
    if (node.type === StatementType.Word) {
      // 对于单词类型，直接查找匹配的 wordDetail
      const wordDetailIndex = wordDetails.findIndex(
        detail => detail.word === node.content,
      )

      if (wordDetailIndex !== -1) {
        // 找到匹配项，更新翻译
        node.chinese = wordDetails[wordDetailIndex].definition || ''
        // 消除已处理的单词
        wordDetails.splice(wordDetailIndex, 1)
      }
    } else if (node.type === StatementType.Phrase) {
      // 对于短语类型，先拆分成单词
      const words = extractWords(node.content)

      // 尝试匹配每个单词并从 wordDetails 中消除
      words.forEach((word) => {
        // 清理单词（去除标点符号等）
        const wordDetailIndex = wordDetails.findIndex(
          detail => detail.word === word,
        )

        if (wordDetailIndex !== -1) {
          // 消除已匹配的单词
          wordDetails.splice(wordDetailIndex, 1)
        }
      })

      // 注意：这里我们不更新 phrase 的翻译，因为它可能需要整体翻译
      // 如果需要基于单词组合生成短语翻译，可以在这里添加逻辑
    }
  })

  // 这个动作必须是在 wordDetails 处理之后 因为 wordDetails 是需要依赖顺序的
  // 如果第一个 node 是 do/did/does 的话 因为没有实际的意义 所以需要删除
  // 如果只有一个节点 并且这个节点就是 do/did/does 本身 那么就不删除了
  if (
    nodes.length > 1
    && ['do', 'did', 'does'].includes(nodes[0].content.toLowerCase())
  ) {
    nodes.shift()
  }

  // 2. chunk 合并起来的时候是需要翻译的
  // 使用 Promise.all 处理所有需要翻译的节点
  const translatePromises = nodes
    .filter(node => node.type === StatementType.CombinedChunks)
    .map(async (node) => {
      const translateResult = await translateChunkCombine(
        sentence.content,
        node.content,
      )
      return {
        node,
        chinese: translateResult?.chinese || '',
      }
    })

  // 等待所有翻译完成
  const translatedResults = await Promise.all(translatePromises)

  // 更新节点的中文翻译
  translatedResults.forEach(({ node, chinese }) => {
    node.chinese = chinese
  })

  // 最后一个节点 也就是句子本身 直接用上面已经翻译好的内容
  nodes[nodes.length - 1].chinese = sentenceChinese
  nodes[nodes.length - 1].type = StatementType.Sentence

  // 有时候大模型会出现幻觉 导致某个 node 没有进行翻译
  // 这里检测如果没有翻译的话 那么给翻译一下
  // 随机给一些节点添加空的中文翻译，用于测试后续翻译逻辑
  const missingTranslationPromises = nodes
    .filter((node, index) =>
      // 排除最后一个节点（句子本身），因为它会在下面单独处理
      index < nodes.length - 1
      // 检查是否缺少中文翻译
      && (!node.chinese || node.chinese.trim() === ''),
    )
    .map(async (node) => {
      const translated = await translateToChinese(node.content)
      return {
        node,
        chinese: translated,
      }
    })

  // 等待所有缺失翻译的节点翻译完成
  const missingTranslationResults = await Promise.all(missingTranslationPromises)

  // 更新缺失翻译的节点
  missingTranslationResults.forEach(({ node, chinese }) => {
    node.chinese = chinese
  })

  // 6. 基于 nodes 生成 elements
  const elements = await createElementsByNodes(nodes)

  const db = getDB()
  const elementEntities = await db
    .insert(schemas.element)
    .values(
      elements.map((element, index) => ({
        sentenceId: sentence.id,
        content: element.content,
        chinese: element.chinese,
        english: element.english,
        phonetic: element.phonetic,
        type: element.type,
        position: index + 1,
      })),
    )
    .$returningId()

  const elementInfos = elements.map((element, index) => ({
    ...element,
    id: elementEntities[index].id,
  }))

  return {
    elements: elementInfos,
    chinese: sentenceChinese,
  }
}

async function createElementsByNodes(
  nodes: { content: string, chinese: string, type: StatementType }[],
) {
  const elementTasks = nodes.map(async (node) => {
    return {
      content: node.content,
      chinese: normalizeChinese(node.chinese),
      english: normalizeContent(node.content),
      phonetic: await phoneticService.generateSentencePhonetic(node.content),
      type: node.type,
      image: null,
    }
  })
  const elements = await Promise.all(elementTasks)

  return elements
}
