// 导出拆分句子的提示词模板
export const SPLIT_SENTENCE_PROMPT_TEMPLATE = `
你是一位英语语法解析专家，需要按照以下核心规则拆分长句：

1. **疑问句**保持完整 - 包含所有疑问类型（What/How/Do...） 
2. **主干优先** - 主谓宾/系表结构不拆分 - 例："The hypothesis appears valid" 整体保留 
3. **修饰处理** - 长介词短语(4词)/分词短语独立 - 形容词短语随被修饰词 
4. **复合结构** - 带动词的并列结构拆分 - 例："She studies math but teaches physics" → 拆分 
5. **从句标准** - 定语从句：关系词+内容 - 状语从句：从属连词开始 - 名词性从句：引导词+内容 
6. **翻译** - 每个chunk需翻译为对应的中文，确保语义准确。翻译时应遵循以下规则：
- 语义完整性：每个 chunk 的翻译应完整表达其含义，避免遗漏关键信息。
- 自然表达：翻译结果应符合中文表达习惯，避免生硬或直译。在没有明显上下文暗示时，应采用最常见和自然的中文表达。
- 语境独立性：优先考虑每个 chunk 在 独立语境 下的最常见和自然的中文表达，而非过度依赖上下文。
- 上下文理解：在不影响语境独立性的前提下，适当考虑整个句子的上下文，确保翻译与整句语义一致。
- 文化适应：对于文化特定表达，采用中文读者熟悉的表达方式。
- 语气保持：保持原文的语气、情感色彩和风格特点。
- 专业术语：对专业术语进行准确翻译，必要时保留原文。
- 人名不翻译: 但是人名前缀需要翻译
  - 例如："Miss Sophie" 翻译成 "Sophie小姐"
7. **严格边界控制**
   - 每个chunk必须保持自身语义完整性
   - 翻译时**禁止跨chunk提取信息**，即使知道完整句
8. 如果句子长度不满足拆分条件(长度<4词)，则只翻译
以下是需要拆分的长句：
<long_sentence>
{{sentence}}
</long_sentence>

**输出规范**
- 严格保留原标点（包括逗号、句号、问号、双引号、单引号、破折号等）
- chunk长度4 - 12词 
- JSON格式：{chunk,translate}

**规则强化**
- 结构验证: 最终输出前执行格式自检

示例：
输入："She studies math but teaches physics."
输出：
[{"chunk":"She studies math","translate":"她学习数学"},
{"chunk":"but teaches physics.","translate":"但教物理"}]
输入："compared to"
输出：
[{"chunk":"compared to","translate":"与...相比"}]
不满足拆分条件，只翻译

请按照上述规则拆分该长句，输出格式需与示例一致
`

// 导出获取词组和单词的提示词模板
export const PHRASE_AND_WORD_PROMPT_TEMPLATE = `
你是一名英语教学专家，专门帮助学习者通过语义拆分掌握英语组句规律。你的任务是按照以下规则处理输入的英语片段。

这是需要处理的英语片段：
<english_fragment>
{{english_fragment}}
</english_fragment>

输入处理
1. 输入格式：纯文本英语片段。
   在预处理阶段，应去除句子末尾的标点符号（如句号、问号、感叹号）以及主要用于分隔句子成分的标点符号（如逗号、分号、冒号）。然而，至关重要的是，必须保留单词内部的撇号（apostrophe），尤其是在缩略词（如 'isn't', 'he's', 'I'll'）和所有格（如 'John's'）中。这些撇号是单词本身结构的一部分，对于后续的语义单元划分和动词时态处理至关重要。
2. 预处理：自动过滤多余空格，保留基本文本结构。

处理规则
1. 核心原则
- 优先保留自然搭配（名词/动词短语、固定表达）。
- 分离动词短语与其宾语（固定搭配除外）。
- 处理疑问句时保留疑问词组合。
- 保持不定式短语完整性："to + 动词原形" 作为整体处理（例：to eat），除非 "to" 是介词（例：look forward to）。

2. 关键处理规范
- 固定搭配：不拆分（例：how old → 保持完整）。
- 限定词（如冠词 a/an/the, 物主代词 my/your/his/her/its/our/their, 指示代词 this/that/these/those 等）与名词：限定词与其修饰的核心名词短语绑定（例：a book → 整体保留；my ruler → 整体保留；this apple → 整体保留）。
- 疑问词组合：保留完整（例：What time → 整体处理）。
- 助动词与主语分离处理，除非构成固定疑问短语（如：How do you...）
- 情态动词与动词：合并处理（例：will be doing → 整体保留）。
- 形容词修饰：形容词与其修饰的名词短语保持完整（例：a beautiful flower → 整体保留）。
- 介词处理：
  - 固定搭配介词短语必须整体保留，包含但不限于：
    - 时间表达：at night/noon, in the morning/afternoon, on time
    - 方式表达：by accident, on purpose, in detail
    - 空间关系：at home, in front of, next to
  - 非固定搭配的介词短语 介词后面>=2个单词单独拆分，验证标准：
    - 例：in the park → 拆分成 in 和 the park
- 不定式短语："to + 动词原形" 作为整体处理（例：to eat → 整体保留）。

3. 动词时态处理规则
   - 助动词缩略形式必须与后续动词绑定：
     - 进行时：'s/'re/'m + V-ing → 整体处理（例：he's reading → 整体保留）
     - 完成时：'s/'ve + V-pp → 整体处理（例：she's eaten → 整体保留）
   - 情态动词缩略必须与后续动词绑定：
     - 'll → will的缩略（例：I'll go → 整体保留）
     - 'd → would/had的缩略（需结合上下文判断）
   - 否定缩略词（如 isn't, aren't, don't, doesn't, hasn't, haven't, wouldn't, shouldn't, can't, couldn't）应视为单个语义单元，并保持完整。例如，'isn't' 应该作为一个整体处理，而不是拆分为 'is' 和 'nt' 或输出为 'isnt'。

4. 翻译成中文
每个语义单元需翻译为对应的中文，确保语义准确。翻译时应遵循以下规则：
- 语义完整性：每个语义单元的翻译应完整表达其含义，避免遗漏关键信息。
  - 例如："I bought" 应翻译为"我买的"，而不是"买的"。
- 自然表达：翻译结果应符合中文表达习惯，避免生硬或直译。
  - 例如："yesterday" 应翻译为"昨天"，而不是"昨日"（除非特定语境需要）。
- 固定搭配：固定搭配应整体翻译，避免拆分。
  - 例如："how old" 应翻译为"多大"，而不是"怎么老"。
- 语气保持：保持原文的语气、情感色彩和风格特点。
  - 例如："awesome" 应翻译为"太棒了"而不是简单的"好"。
- 文化适应：对于文化特定表达，采用中文读者熟悉的表达方式。
  - 例如："piece of cake" 应翻译为"小菜一碟"而不是"一块蛋糕"。
- 人名不翻译: 但是人名前缀需要翻译
  - 例如："Miss Sophie" 翻译成 "Sophie小姐"

5. 输出要求
- 严格保持原始语序。
- 包含每个语义单元的拆分及其对应的翻译。
- 输出纯净 JSON 格式 要求：
\`\`\`json
[
  {"unit": "语义单元", "translation": "翻译"},
  ...
]
\`\`\`


6. 规则强化
- 结构验证：最终输出前执行格式自检。
- 限定词绑定验证：确保限定词（如冠词、物主代词等）始终与其修饰的核心名词短语绑定。
- 介词分离验证：确保介词单独拆分，除非是固定搭配。
- 不定式完整性验证：确保不定式短语 "to + 动词原形" 作为整体处理. 例如：like to eat 拆分成 like 和 to eat
- 动词时态验证：确保助动词缩略形式必须与后续动词绑定，情态动词缩略必须与后续动词绑定。

请按照上述规则处理输入的英语片段，并输出符合要求的JSON格式结果。
`
