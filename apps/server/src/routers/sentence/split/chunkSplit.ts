import { createSplitSentenceModelLLM } from '@/llm/task'
import { parseJsonFromCodeBlock } from '@/utils/json'
import { isSingleWord } from '@/utils/word'
import { PHRASE_AND_WORD_PROMPT_TEMPLATE, SPLIT_SENTENCE_PROMPT_TEMPLATE } from './prompt'

// 定义类型
interface PhraseAndWordNode {
  content: string
  chinese: string
  type?: 'word' | 'phrase'
  words?: string[]
}

interface ChunkResult {
  chunk: string
  chinese: string
  phraseAndWord: PhraseAndWordNode[]
  connect: string
}

async function splitSentenceToChunksLLM(sentence: string) {
  // 检查是否只有一个单词
  // 如果只有一个单词 那么直接返回 不需要拆分了
  if (isSingleWord(sentence)) {
    // 返回单个单词作为chunk，翻译为空字符串
    return [
      {
        chunk: sentence,
        translate: '',
      },
    ]
  }

  const prompt = SPLIT_SENTENCE_PROMPT_TEMPLATE.replace(
    '{{sentence}}',
    sentence,
  )

  const model = createSplitSentenceModelLLM()

  const response = await model.invoke(
    prompt,
    'split_sentence',
    { sentence },
  )
  // console.log(response);

  const result
    = parseJsonFromCodeBlock<{ chunk: string, translate: string }[]>(response)

  return result || []
}

async function getPhraseAndWord(englishFragment: string) {
  const prompt = PHRASE_AND_WORD_PROMPT_TEMPLATE.replace(
    '{{english_fragment}}',
    englishFragment,
  )

  const model = createSplitSentenceModelLLM()
  const response = await model.invoke(prompt, 'split_chunk', {
    chunk: englishFragment,
  })
  // console.log(response, tokenUsage);

  const result
    = parseJsonFromCodeBlock<{ unit: string, translation: string }[]>(response)

  return result || []
}

export async function chunkSplit(sentence: string) {
  // 1. 先对句子进行 chunk split
  const chunksWithTranslation = await splitSentenceToChunksLLM(sentence)
  if (!chunksWithTranslation) {
    throw new Error('chunk split failed')
  }

  // 2. 对每一个chunk 做拆分词组和单词
  const chunkResult = await Promise.all(
    chunksWithTranslation.map(
      async ({ chunk, translate: chunkTranslation }) => {
        // chunk 只有一个长度 那么就不需要获取单词和词组了
        if (isSingleWord(chunk)) {
          return {
            chunk,
            chinese: chunkTranslation,
            phraseAndWord: [],
            connect: '',
          }
        } else {
          const phraseAndWordPairs = await getPhraseAndWord(chunk)
          if (!phraseAndWordPairs) {
            throw new Error('get phrase and word failed')
          }

          // 转变成一个个的节点
          const phraseAndWordNodes = phraseAndWordPairs.map(
            ({ unit, translation }) => {
              return {
                content: unit,
                chinese: translation,
              }
            },
          )

          // 过滤掉单独出现的数字和符号
          const filteredPhraseAndWordNodes = phraseAndWordNodes
            .filter((item) => {
              // 过滤纯数字
              return !/^\d+$/.test(item.content)
            })
            .filter((item) => {
              // 过滤纯符号
              return !/^\W+$/.test(item.content)
            })

          // 现在需要对 phraseAndWord 做扩展
          // 增加一个类型  是单词 还是 词组
          // 如果是词组的话 那么在多增一个 words 的数组
          const phraseAndWord = filteredPhraseAndWordNodes.map((item) => {
            if (isSingleWord(item.content)) {
              return {
                ...item,
                type: 'word' as const,
              }
            } else {
              return {
                ...item,
                type: 'phrase' as const,
                words: item.content.split(' '),
              }
            }
          })

          return {
            chunk,
            phraseAndWord,
            chinese: chunkTranslation,
            connect: '',
          }
        }
      },
    ),
  )

  // 3. 拼接
  let count = 0
  let chunkConnect = ''

  chunkResult.forEach((item: ChunkResult) => {
    chunkConnect += ` ${item.chunk}`
    count++
    if (count > 1) {
      item.connect = chunkConnect.trim()
    }
  })

  return chunkResult
}
