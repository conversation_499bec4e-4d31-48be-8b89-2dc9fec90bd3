import type { ElementImage, LearningContent, WordDetail } from '@julebu/shared'
import type { DeleteSentencesResult } from '../admin/admin.trpc'
import type { ElementEntityWithImage } from '../element/entity/element.entity'
import type { SentenceEntity } from './entity/sentence.entity'
import type { SplitSentenceResult } from './split'
import { TRPCError } from '@trpc/server'
import { and, eq, gt, inArray, sql } from 'drizzle-orm'
import { filter, isEmpty } from 'lodash-es'
import { getDB, schemas } from '@/db'
import { currencyService } from '@/routers/currency/currency.service'
import { analyzeDependency } from '@/services/nlp'
import { translateToChinese } from '@/services/translate'
import { isSingleWord } from '@/utils/word'
import { checkGenerateLearningContentDiamondBalance, checkProcessSentenceDiamondBalance, checkSplitSentenceDiamondBalance } from '../currency/checker'
import { elementMapper } from '../element/dto/element.dto'
import { phoneticService } from '../phonetic/phonetic.service'
import { analyzeWordDetails } from './analyze'
import { normalizeContent } from './content'
import { generateLearningContent } from './learning-content'
import { splitSentence } from './split'

// 批量操作相关类型定义
interface DeleteResult {
  sentenceId: string
  success: boolean
  error?: string
}

interface UpsertSentenceData {
  uuid?: string // 用于前端判断更新的哪个句子
  sentenceId?: string // 可选，如果提供则更新，否则创建
  content?: string // 英文内容
  chinese?: string // 中文翻译
}

async function createSentences(
  courseId: string,
  sentences: { content: string, chinese: string }[],
): Promise<{ id: string, wordDetails: WordDetail[] }[]> {
  const db = getDB()

  // 检查当前课程中已有的句子数量
  const existingSentencesCount = await db.query.sentence
    .findMany({
      where: (sentences, { eq }) => eq(sentences.courseId, courseId),
      columns: { id: true },
    })
    .then(results => results.length)

  // 计算添加新句子后的总数
  const totalSentencesCount = existingSentencesCount + sentences.length

  // 如果总数超过1000，抛出错误
  if (totalSentencesCount > 1000) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: '一个课程最多只能添加1000个句子，请拆分多个课程来录入',
    })
  }

  const maxPositionResult = await db.query.sentence.findFirst({
    where: (sentences, { eq }) => eq(sentences.courseId, courseId),
    orderBy: (sentences, { desc }) => [desc(sentences.position)],
  })

  const startPosition = (maxPositionResult?.position ?? 0) + 1

  const wordDetails = await Promise.all(
    sentences.map(async ({ content }) => {
      return generateWordDetails(content)
    }),
  )

  const dependencyAnalysis = await Promise.all(
    sentences.map(async ({ content }) => {
      return generateDependencyAnalysis(content)
    }),
  )

  const sentenceIds = await db.transaction(async (tx) => {
    // 插入句子，将 wordDetails 和 nlpAnalysis 存储在数据库中
    const newSentenceId = await tx
      .insert(schemas.sentence)
      .values(
        sentences.map(({ content, chinese }, index) => ({
          courseId,
          content,
          english: normalizeContent(content), // 存一个格式化之后的  前端需要用
          chinese,
          position: startPosition + index,
          wordDetails: wordDetails[index] || [],
          dependencyAnalysis: !isEmpty(dependencyAnalysis[index])
            ? dependencyAnalysis[index]
            : {
                nodes: [],
                edges: [],
              },
        })),
      )
      .$returningId()

    return newSentenceId
  })

  return sentenceIds.map(({ id }, index) => ({
    id,
    wordDetails: wordDetails[index] || [],
  }))
}

async function deleteOne(sentenceId: string, courseId: string) {
  const db = getDB()

  return db.transaction(async (tx) => {
    // 1. 先删除句子相关的元素
    await tx
      .delete(schemas.element)
      .where(eq(schemas.element.sentenceId, sentenceId))

    // 2. 获取要删除的句子信息，用于后续更新 position
    const sentenceToDelete = await tx.query.sentence.findFirst({
      where: (sentence, { eq }) => eq(sentence.id, sentenceId),
    })

    if (!sentenceToDelete) {
      throw new TRPCError({ code: 'NOT_FOUND', message: '句子不存在' })
    }

    // 3. 删除句子
    await tx
      .delete(schemas.sentence)
      .where(eq(schemas.sentence.id, sentenceId))

    // 4. 更新被删除句子后面的所有句子的 position
    await tx
      .update(schemas.sentence)
      .set({
        position: sql`${schemas.sentence.position} - 1`,
      })
      .where(
        and(
          eq(schemas.sentence.courseId, courseId),
          gt(schemas.sentence.position, sentenceToDelete.position),
        ),
      )

    return true
  })
}

async function deleteAll(courseId: string) {
  const db = getDB()

  return db.transaction(async (tx) => {
    // 1. 获取所有相关句子的ID
    const sentences = await tx.query.sentence.findMany({
      where: eq(schemas.sentence.courseId, courseId),
      columns: { id: true },
    })
    const sentenceIds = sentences.map(sentence => sentence.id)

    // 2. 删除所有相关的元素
    await tx
      .delete(schemas.element)
      .where(inArray(schemas.element.sentenceId, sentenceIds))

    // 3. 删除所有句子
    await tx
      .delete(schemas.sentence)
      .where(eq(schemas.sentence.courseId, courseId))

    return true
  })
}

async function movePosition(
  _courseId: string,
  affectedSentences: { position: number, sentenceId: string }[],
) {
  const db = getDB()

  // 批量更新受影响课程的位置
  for (const sentence of affectedSentences) {
    await db
      .update(schemas.sentence)
      .set({
        position: sentence.position,
      })
      .where(and(eq(schemas.sentence.id, sentence.sentenceId)))
  }

  return true
}

async function splitOne(sentenceId: string, userId: string) {
  const db = getDB()
  const sentence = await db.query.sentence.findFirst({
    where: eq(schemas.sentence.id, sentenceId),
  })

  if (!sentence) {
    throw new TRPCError({ code: 'NOT_FOUND', message: '句子不存在' })
  }

  // 确保 wordDetails 是 WordDetails[] 类型
  const sentenceWithWordDetails = {
    ...sentence,
    wordDetails: !isEmpty(sentence.wordDetails)
      ? sentence.wordDetails as WordDetail[]
      : [],
  }

  // 检查钻石余额
  const diamondConsumption = await checkSplitSentenceDiamondBalance(userId, [sentenceWithWordDetails])

  try {
    // 执行拆分
    const result = await splitSentence(sentenceWithWordDetails)

    // 生成音标（为拆分后的元素生成音标）
    await phoneticService.generate(sentence.content)

    // 拆分成功后，扣除钻石
    await currencyService.consumeDiamonds(
      userId,
      diamondConsumption,
      `拆分句子 ${sentence.content}`,
    )

    // 更新中文翻译
    await _updateSentenceChinese(result.chinese, sentenceId)

    return result
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '句子拆分失败，句子内容非法或不符合正常英文句子结构',
      cause: process.env.NODE_ENV === 'production' ? undefined : error,
    })
  }
}

async function splitAll(
  courseId: string,
  userId: string,
): Promise<Array<SplitSentenceResult & { sentenceId: string }>> {
  const db = getDB()

  // 一次性获取所有句子
  const sentences = await db.query.sentence.findMany({
    where: eq(schemas.sentence.courseId, courseId),
  })

  if (isEmpty(sentences)) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '没有找到任何句子',
    })
  }

  // 一次性获取所有元素
  const elements = await db.query.element.findMany({
    where: inArray(
      schemas.element.sentenceId,
      sentences.map(s => s.id),
    ),
    with: {
      image: true,
    },
  })

  // 按 sentenceId 对元素进行分组
  const elementsBySentenceId = elements.reduce(
    (acc, element) => {
      if (isEmpty(acc[element.sentenceId])) {
        acc[element.sentenceId] = []
      }
      acc[element.sentenceId].push(element)
      return acc
    },
    {} as Record<string, typeof elements>,
  )

  // 1. 收集需要拆分的句子
  const sentencesToSplit = sentences.filter(
    sentence => isEmpty(elementsBySentenceId[sentence.id]),
  )

  if (sentencesToSplit.length === 0) {
    return []
  }

  // 2. 检查钻石余额
  const diamondConsumption = await checkSplitSentenceDiamondBalance(userId, sentencesToSplit)

  // 3. 批量执行拆分
  const splitTasks = sentencesToSplit.map(async (sentence) => {
    // 确保 wordDetails 是 WordDetails[] 类型
    const sentenceWithWordDetails = {
      ...sentence,
      wordDetails: !isEmpty(sentence.wordDetails)
        ? sentence.wordDetails as WordDetail[]
        : [],
    }

    const result = await splitSentence(sentenceWithWordDetails)

    // 同步更新 chinese 字段
    await _updateSentenceChinese(result.chinese, sentence.id)

    return {
      ...result,
      sentenceId: sentence.id,
    }
  })

  try {
    // 执行所有拆分任务
    const results = await Promise.all(splitTasks)

    // 批量生成音标
    const phoneticTasks = sentencesToSplit.map(async sentence =>
      phoneticService.generate(sentence.content),
    )
    await Promise.all(phoneticTasks)

    // 拆分成功后，扣除钻石
    await currencyService.consumeDiamonds(
      userId,
      diamondConsumption,
      `批量拆分 ${sentencesToSplit.length} 个句子`,
    )

    return results.filter(Boolean) as Array<
      SplitSentenceResult & { sentenceId: string }
    >
  } catch (error) {
    // 如果拆分过程中出现错误，不扣除钻石
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '句子拆分失败，句子内容非法或不符合正常英文句子结构',
      cause: process.env.NODE_ENV === 'production' ? undefined : error,
    })
  }
}

// 首先定义一些类型
type ProcessResult = {
  sentenceId: string
  elements: Array<{
    id: string
    content: string
    chinese: string
    phonetic: string
    english: string
    image: ElementImage | null
  }>
  chinese: string
} | null

// 抽取处理单个句子的核心逻辑
async function processSentence(
  sentence: SentenceEntity,
  elements: ElementEntityWithImage[],
  db: typeof getDB extends () => infer R ? R : never,
  inTransaction = false,
): Promise<ProcessResult> {
  const executor = inTransaction ? db : getDB()

  if (elements.length === 0) {
    const content = sentence.content
    const chinese = sentence.chinese || (await translateToChinese(content))

    // 生成音标
    await phoneticService.generate(content)

    const element = {
      content: sentence.content,
      chinese,
      phonetic: await phoneticService.generateSentencePhonetic(content),
      english: normalizeContent(content),
      sentenceId: sentence.id,
      position: 1,
      image: null,
    }

    const [elementEntity] = await executor
      .insert(schemas.element)
      .values(element)
      .$returningId()

    await _updateSentenceChinese(chinese, sentence.id, db)

    return {
      sentenceId: sentence.id,
      elements: [{ id: elementEntity.id, ...element }],
      chinese,
    }
  }

  const sortedElements = [...elements].sort((a, b) => a.position - b.position)

  // 先为所有元素生成音标
  await phoneticService.generate(sentence.content)

  const updatedElements = await Promise.all(
    sortedElements.map(async (element) => {
      const content = element.content
      const updates: Partial<typeof element> = {}

      if (!element.chinese) {
        updates.chinese = await translateToChinese(content)
      }
      if (!element.phonetic) {
        updates.phonetic
          = await phoneticService.generateSentencePhonetic(content)
      }
      if (!element.english) {
        updates.english = normalizeContent(content)
      }

      if (Object.keys(updates).length > 0) {
        await executor
          .update(schemas.element)
          .set(updates)
          .where(eq(schemas.element.id, element.id))

        return elementMapper.toDTO({ ...element, ...updates })
      }
      return null
    }),
  )

  const changedElements = updatedElements.filter(
    (element): element is NonNullable<typeof element> => element !== null,
  )
  return changedElements.length > 0
    ? {
        sentenceId: sentence.id,
        elements: changedElements,
        chinese: sentence.chinese || '',
      }
    : null
}

// 重构后的 processOne
async function processOne(sentenceId: string, userId: string) {
  const db = getDB()
  const sentence = await db.query.sentence.findFirst({
    where: eq(schemas.sentence.id, sentenceId),
  })

  if (!sentence) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '句子不存在',
    })
  }

  // 获取已有的元素
  const elements = await db.query.element.findMany({
    where: eq(schemas.element.sentenceId, sentenceId),
    with: {
      image: true,
    },
  })

  // 检查钻石余额
  const diamondConsumption = await checkProcessSentenceDiamondBalance(userId, [sentence])

  try {
    // 执行加工，传入已有的 elements
    const result = await processSentence(
      sentence,
      elements,
      db,
      false,
    )

    // 加工成功后，扣除钻石
    await currencyService.consumeDiamonds(
      userId,
      diamondConsumption,
      `加工句子 ${sentence.content}`,
    )

    return result
  } catch {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '句子加工失败，请稍后重试',
    })
  }
}

// 重构后的 processAll
async function processAll(courseId: string, userId: string) {
  const db = getDB()

  // 一次性获取所有句子
  const sentences = await db.query.sentence.findMany({
    where: eq(schemas.sentence.courseId, courseId),
  })

  if (sentences.length === 0) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '没有找到任何句子',
    })
  }

  // 一次性获取所有元素
  const elements = await db.query.element.findMany({
    where: inArray(
      schemas.element.sentenceId,
      sentences.map(s => s.id),
    ),
    with: {
      image: true,
    },
  })

  // 按 sentenceId 对元素进行分组
  const elementsBySentenceId = elements.reduce(
    (acc, element) => {
      if (isEmpty(acc[element.sentenceId])) {
        acc[element.sentenceId] = []
      }
      acc[element.sentenceId].push(element)
      return acc
    },
    {} as Record<string, typeof elements>,
  )

  // 1. 收集需要加工的句子（没有elements字段的句子）
  const sentencesToProcess = filter(sentences, (sentence) => {
    const sentenceElements = elementsBySentenceId[sentence.id] ?? []

    // 如果没有elements，需要加工
    return isEmpty(sentenceElements)
  })

  if (sentencesToProcess.length === 0) {
    return []
  }

  // 2. 检查钻石余额
  const diamondConsumption = await checkProcessSentenceDiamondBalance(userId, sentencesToProcess)

  // 3. 批量执行加工
  const processTasks = sentencesToProcess.map(async (sentence) => {
    try {
      const result = await processSentence(
        sentence,
        elementsBySentenceId[sentence.id] ?? [],
        db,
        false,
      )
      return result
    } catch (error) {
      console.error(`处理句子 ${sentence.id} 失败:`, error)
      return null
    }
  })

  try {
    // 执行所有加工任务
    const results = await Promise.all(processTasks)

    // 加工成功后，扣除钻石
    await currencyService.consumeDiamonds(
      userId,
      diamondConsumption,
      `批量加工 ${sentencesToProcess.length} 个句子`,
    )

    return results.filter(Boolean)
  } catch {
    // 如果加工过程中出现错误，不扣除钻石
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '句子加工失败，请稍后重试',
    })
  }
}

async function clearOne(courseId: string, sentenceId: string) {
  const db = getDB()

  // 检查句子是否存在
  const sentence = await db.query.sentence.findFirst({
    where: and(
      eq(schemas.sentence.id, sentenceId),
      eq(schemas.sentence.courseId, courseId),
    ),
  })

  if (!sentence) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '句子不存在',
    })
  }

  await db.transaction(async (tx) => {
    // 删除所有相关的元素
    await tx
      .delete(schemas.element)
      .where(eq(schemas.element.sentenceId, sentenceId))
  })

  return true
}

async function clearAll(courseId: string) {
  const db = getDB()

  // 检查课程下是否有句子
  const sentences = await db.query.sentence.findMany({
    where: eq(schemas.sentence.courseId, courseId),
    columns: { id: true },
  })

  if (sentences.length === 0) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '该课程下没有句子',
    })
  }

  await db.transaction(async (tx) => {
    // 删除所有相关的元素
    await tx.delete(schemas.element).where(
      inArray(
        schemas.element.sentenceId,
        sentences.map(s => s.id),
      ),
    )
  })

  return true
}

export async function changeContent(sentenceId: string, content: string) {
  const db = getDB()

  await db.transaction(async (tx) => {
    // 删除所有相关的元素
    await tx
      .delete(schemas.element)
      .where(eq(schemas.element.sentenceId, sentenceId))

    // 更新句子的内容
    await tx
      .update(schemas.sentence)
      .set({
        content,
        english: normalizeContent(content),
      })
      .where(eq(schemas.sentence.id, sentenceId))

    // 重新生成 wordDetails & dependencyAnalysis
    const wordDetails = await generateWordDetails(content)
    const dependencyAnalysis = await generateDependencyAnalysis(content)
    await tx
      .update(schemas.sentence)
      .set({
        wordDetails,
        dependencyAnalysis,
      })
      .where(eq(schemas.sentence.id, sentenceId))
  })

  return true
}

async function changeChinese(sentenceId: string, chinese: string) {
  const db = getDB()

  await db.transaction(async (tx) => {
    // 删除所有相关的元素
    await tx
      .delete(schemas.element)
      .where(eq(schemas.element.sentenceId, sentenceId))

    // 更新句子的翻译
    await _updateSentenceChinese(chinese, sentenceId, tx)
  })

  return true
}

async function _updateSentenceChinese(
  chinese: string,
  sentenceId: string,
  db?: ReturnType<typeof getDB>,
) {
  const executor = db || getDB()
  await executor
    .update(schemas.sentence)
    .set({
      chinese,
    })
    .where(eq(schemas.sentence.id, sentenceId))
}

async function generateLearningContentOne(sentenceId: string, userId: string) {
  const db = getDB()
  const sentence = await db.query.sentence.findFirst({
    where: eq(schemas.sentence.id, sentenceId),
  })

  if (!sentence) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '句子不存在',
    })
  }

  // 检查钻石余额
  const diamondConsumption = await checkGenerateLearningContentDiamondBalance(userId, [sentence])

  try {
    // 生成学习内容
    const learningContent = await generateLearningContent(sentence)

    // 扣除钻石
    await currencyService.consumeDiamonds(
      userId,
      diamondConsumption,
      `生成学习内容 句子 "${sentence.content.substring(0, 20)}${sentence.content.length > 20 ? '...' : ''}"`,
    )

    return learningContent
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '生成学习内容失败，请稍后重试',
      cause: error,
    })
  }
}

async function deleteLearningContent(sentenceId: string) {
  const db = getDB()
  const sentence = await db.query.sentence.findFirst({
    where: eq(schemas.sentence.id, sentenceId),
  })

  if (!sentence) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '句子不存在',
    })
  }

  await db
    .update(schemas.sentence)
    .set({
      learningContent: null,
    })
    .where(eq(schemas.sentence.id, sentenceId))

  return true
}

async function generateLearningContentAll(courseId: string, userId: string): Promise<
  Array<{
    id: string
    learningContent: LearningContent | null
  }>
> {
  const db = getDB()
  const sentences = await db.query.sentence.findMany({
    where: eq(schemas.sentence.courseId, courseId),
  })

  if (sentences.length === 0) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '没有找到任何句子',
    })
  }

  // 过滤掉已经生成学习内容的句子
  const sentencesToProcess = sentences.filter(
    sentence => sentence.learningContent === null,
  )

  if (sentencesToProcess.length === 0) {
    return []
  }

  // 检查钻石余额
  const diamondConsumption = await checkGenerateLearningContentDiamondBalance(
    userId,
    sentencesToProcess,
  )

  try {
    // 批量生成学习内容
    const results = await Promise.all(
      sentencesToProcess.map(async (sentence) => {
        try {
          const learningContent = await generateLearningContent(sentence)
          return {
            id: sentence.id,
            learningContent,
          }
        } catch (error) {
          console.error(`生成句子 ${sentence.id} 的学习内容失败:`, error)
          return {
            id: sentence.id,
            learningContent: null,
          }
        }
      }),
    )

    // 扣除钻石
    await currencyService.consumeDiamonds(
      userId,
      diamondConsumption,
      `批量生成 ${sentencesToProcess.length} 个句子的学习内容`,
    )

    return results
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: '批量生成学习内容失败，请稍后重试',
      cause: error,
    })
  }
}

// 解析句子 生成 wordDetails & dependencyAnalysis & 格式化后的 english
async function analyze(sentenceId: string) {
  const db = getDB()

  // 检查句子是否存在
  const sentence = await db.query.sentence.findFirst({
    where: eq(schemas.sentence.id, sentenceId),
  })

  if (!sentence) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '句子不存在',
    })
  }

  // 并行获取单词详情和依存分析
  const [wordDetails, dependencyAnalysis] = await Promise.all([
    generateWordDetails(sentence.content),
    generateDependencyAnalysis(sentence.content),
  ])

  // 更新数据库
  await db
    .update(schemas.sentence)
    .set({
      wordDetails,
      dependencyAnalysis,
      english: normalizeContent(sentence.content),
    })
    .where(eq(schemas.sentence.id, sentenceId))

  return {
    wordDetails,
  }
}

async function generateWordDetails(sentenceContent: string) {
  try {
    // 检查是否为单个单词
    if (isSingleWord(sentenceContent)) {
      return null
    }

    // 并行获取单词详情和词性分析
    const normalizedSentenceContent = normalizeContent(sentenceContent)
    const [details] = await Promise.all([
      analyzeWordDetails(normalizedSentenceContent),
      phoneticService.generate(normalizedSentenceContent), // 生成音标
    ])

    // 为每个单词添加音标信息
    const detailsWithPhonetic = await Promise.all(
      details.map(
        async (info: { word: string, definition: string, pos: string }) => {
          // 查询单词的音标
          const normalizedWord = info.word.toLowerCase()
          const phoneticInfo = await phoneticService.findWord(normalizedWord)

          return {
            ...info,
            phonetic: {
              uk: phoneticInfo?.uk ?? '',
              us: phoneticInfo?.us ?? '',
            },
          }
        },
      ),
    )

    return detailsWithPhonetic
  } catch (error) {
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: `句子解析失败: "${sentenceContent.substring(0, 30)}${sentenceContent.length > 30 ? '...' : ''}"`,
      cause: error,
    })
  }
}

/**
 * 生成句子的依存分析结果
 * @param sentenceContent 句子内容
 * @returns 依存分析结果，如果分析失败则返回空结果
 */
async function generateDependencyAnalysis(sentenceContent: string) {
  try {
    // 检查是否为单个单词
    if (isSingleWord(sentenceContent)) {
      return {
        nodes: [],
        edges: [],
      }
    }

    return await analyzeDependency(sentenceContent)
  } catch (error) {
    console.error('Error analyzing dependency:', error)
    console.warn(
      `句子依存分析失败: "${sentenceContent.substring(0, 30)}${sentenceContent.length > 30 ? '...' : ''}"`,
      error,
    )
    // 返回一个空的依赖分析结果，而不是抛出错误中断整个流程
    return { nodes: [], edges: [] }
  }
}

async function updateWordDetail(
  sentenceId: string,
  word: string,
  updates: {
    pos?: string
    definition?: string
  },
) {
  const db = getDB()
  const sentence = await db.query.sentence.findFirst({
    where: eq(schemas.sentence.id, sentenceId),
  })

  if (!sentence) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '句子不存在',
    })
  }

  // 确保 wordDetails 是数组
  const wordDetails = !isEmpty(sentence.wordDetails)
    ? sentence.wordDetails as WordDetail[]
    : []

  // 查找要更新的单词详情
  const wordDetailIndex = wordDetails.findIndex(
    detail => detail.word.toLowerCase() === word.toLowerCase(),
  )

  if (wordDetailIndex === -1) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '未找到指定的单词',
    })
  }

  // 更新单词详情
  const updatedWordDetail = {
    ...wordDetails[wordDetailIndex],
    ...(!isEmpty(updates.pos) && { pos: updates.pos }),
    ...(!isEmpty(updates.definition) && { definition: updates.definition }),
  }

  wordDetails[wordDetailIndex] = updatedWordDetail

  // 更新数据库
  await db
    .update(schemas.sentence)
    .set({
      wordDetails,
    })
    .where(eq(schemas.sentence.id, sentenceId))

  return true
}

async function insertAbove(
  courseId: string,
  targetSentenceId: string,
  content: string,
  chinese: string = '',
) {
  const db = getDB()

  // 获取目标句子的信息
  const targetSentence = await db.query.sentence.findFirst({
    where: eq(schemas.sentence.id, targetSentenceId),
  })

  if (!targetSentence) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '目标句子不存在',
    })
  }

  // 更新所有大于等于目标位置的句子的 position
  await db
    .update(schemas.sentence)
    .set({
      position: sql`${schemas.sentence.position} + 1`,
    })
    .where(
      and(
        eq(schemas.sentence.courseId, courseId),
        gt(schemas.sentence.position, targetSentence.position - 1),
      ),
    )

  // 创建新句子
  const [newSentence] = await db
    .insert(schemas.sentence)
    .values({
      courseId,
      content,
      english: normalizeContent(content),
      chinese,
      position: targetSentence.position,
    })
    .$returningId()

  return newSentence
}

async function insertBelow(
  courseId: string,
  targetSentenceId: string,
  content: string,
  chinese: string = '',
) {
  const db = getDB()

  // 获取目标句子的信息
  const targetSentence = await db.query.sentence.findFirst({
    where: eq(schemas.sentence.id, targetSentenceId),
  })

  if (!targetSentence) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '目标句子不存在',
    })
  }

  // 更新所有大于目标位置的句子的 position
  await db
    .update(schemas.sentence)
    .set({
      position: sql`${schemas.sentence.position} + 1`,
    })
    .where(
      and(
        eq(schemas.sentence.courseId, courseId),
        gt(schemas.sentence.position, targetSentence.position),
      ),
    )

  // 创建新句子
  const [newSentence] = await db
    .insert(schemas.sentence)
    .values({
      courseId,
      content,
      english: normalizeContent(content),
      chinese,
      position: targetSentence.position + 1,
    })
    .$returningId()

  return newSentence
}

async function moveToTop(courseId: string, sentenceId: string) {
  const db = getDB()

  // 获取目标句子的信息
  const targetSentence = await db.query.sentence.findFirst({
    where: eq(schemas.sentence.id, sentenceId),
  })

  if (!targetSentence) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '目标句子不存在',
    })
  }

  // 如果已经在顶部，不需要操作
  if (targetSentence.position === 1) {
    return true
  }

  // 更新所有位置小于目标句子的句子的 position (向下移动一位)
  await db
    .update(schemas.sentence)
    .set({
      position: sql`${schemas.sentence.position} + 1`,
    })
    .where(
      and(
        eq(schemas.sentence.courseId, courseId),
        sql`${schemas.sentence.position} < ${targetSentence.position}`,
      ),
    )

  // 将目标句子移动到顶部
  await db
    .update(schemas.sentence)
    .set({
      position: 1,
    })
    .where(eq(schemas.sentence.id, sentenceId))

  return true
}

async function moveToBottom(courseId: string, sentenceId: string) {
  const db = getDB()

  // 获取目标句子的信息
  const targetSentence = await db.query.sentence.findFirst({
    where: eq(schemas.sentence.id, sentenceId),
  })

  if (!targetSentence) {
    throw new TRPCError({
      code: 'NOT_FOUND',
      message: '目标句子不存在',
    })
  }

  // 获取当前课程的句子总数
  const totalCount = await db.query.sentence
    .findMany({
      where: eq(schemas.sentence.courseId, courseId),
    })
    .then(sentences => sentences.length)

  // 如果已经在底部，不需要操作
  if (targetSentence.position === totalCount) {
    return true
  }

  // 更新所有位置大于目标句子的句子的 position (向上移动一位)
  await db
    .update(schemas.sentence)
    .set({
      position: sql`${schemas.sentence.position} - 1`,
    })
    .where(
      and(
        eq(schemas.sentence.courseId, courseId),
        gt(schemas.sentence.position, targetSentence.position),
      ),
    )

  // 将目标句子移动到底部
  await db
    .update(schemas.sentence)
    .set({
      position: totalCount,
    })
    .where(eq(schemas.sentence.id, sentenceId))

  return true
}

// 批量创建或更新句子
async function batchUpsertSentences(courseId: string, sentences: UpsertSentenceData[], _userId: string) {
  const db = getDB()
  const updates: UpsertSentenceData[] = []
  const creates: UpsertSentenceData[] = []

  // 分离创建和更新操作
  for (const sentenceData of sentences) {
    if (!isEmpty(sentenceData.sentenceId)) {
      // 验证句子是否存在且属于该课程
      const existingSentence = await db.query.sentence.findFirst({
        where: eq(schemas.sentence.id, sentenceData.sentenceId!),
      })

      if (!existingSentence || existingSentence.courseId !== courseId) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `句子 ${sentenceData.sentenceId} 不存在或不属于该课程`,
        })
      }

      updates.push(sentenceData)
    } else {
      // 新建句子必须有content
      if (isEmpty(sentenceData.content)) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '新建句子必须提供content',
        })
      }
      creates.push(sentenceData)
    }
  }

  // 执行批量操作
  const results = { created: 0, updated: 0, sentences: [] as Array<{ id: string, operation: 'created' | 'updated' }> }

  // 1. 批量创建新句子
  if (creates.length > 0) {
    const sentencesToCreate = creates.map(sentenceData => ({
      content: sentenceData.content ?? '',
      chinese: sentenceData.chinese ?? '',
    }))

    const createdSentences = await createSentences(courseId, sentencesToCreate)
    results.created = createdSentences.length
    results.sentences.push(...createdSentences.map(s => ({ id: s.id, operation: 'created' as const })))
  }

  // 2. 批量更新现有句子（分情况处理 elements）
  if (updates.length > 0) {
    await db.transaction(async (tx) => {
      for (const update of updates) {
        const { sentenceId, content, chinese } = update

        // 判断是否更新了英文内容
        const hasContentUpdate = content != null
        const hasChinese = chinese != null

        if (hasContentUpdate) {
          // 删除所有相关的元素（与 changeContent 行为一致）
          await tx
            .delete(schemas.element)
            .where(eq(schemas.element.sentenceId, sentenceId!))

          // 重新生成相关字段
          const wordDetails = await generateWordDetails(content)
          const dependencyAnalysis = await generateDependencyAnalysis(content)

          // 更新句子
          await tx
            .update(schemas.sentence)
            .set({
              content,
              english: normalizeContent(content),
              chinese: hasChinese ? chinese : undefined, // 如果同时更新中文，也一起更新
              wordDetails,
              dependencyAnalysis,
            })
            .where(eq(schemas.sentence.id, sentenceId!))
        } else if (hasChinese) {
          // 只更新中文翻译：保留 elements
          await tx
            .update(schemas.sentence)
            .set({
              chinese,
            })
            .where(eq(schemas.sentence.id, sentenceId!))
        }
      }
    })

    results.updated = updates.length
    results.sentences.push(...updates.map(u => ({ id: u.sentenceId!, operation: 'updated' as const })))
  }

  // 构建返回结果
  const response = sentences.map((sentence, idx: number) => {
    return {
      ...sentence,
      sentenceId: results.sentences[idx]?.id || sentence.sentenceId,
    }
  })

  return {
    sentences: response,
    created: results.created,
    updated: results.updated,
  }
}

// 批量删除句子
async function deleteBatchSentences(sentenceList: { sentenceId: string, courseId: string }[]): Promise<DeleteSentencesResult> {
  const db = getDB()

  const results: DeleteResult[] = []
  let successCount = 0
  let failCount = 0
  const failedIds: string[] = []

  // 先尝试批量删除，提升性能
  const sentenceIds = sentenceList.map(item => item.sentenceId)

  try {
    // 使用事务进行批量删除
    await db.transaction(async (tx) => {
      // 1. 批量删除相关的元素
      await tx.delete(schemas.element)
        .where(inArray(schemas.element.sentenceId, sentenceIds))

      // 2. 批量删除句子
      await tx.delete(schemas.sentence)
        .where(inArray(schemas.sentence.id, sentenceIds))
    })

    // 批量删除成功，标记所有为成功
    successCount = sentenceList.length
    results.push(...sentenceList.map(({ sentenceId }) => ({
      sentenceId,
      success: true,
    })))
  } catch (error) {
    // 批量删除失败，回退到逐个删除以获得详细错误信息
    console.warn('批量删除失败，回退到逐个删除:', error)

    for (const { sentenceId, courseId } of sentenceList) {
      try {
        await deleteOne(sentenceId, courseId)
        successCount++
        results.push({ sentenceId, success: true })
      } catch (individualError) {
        failCount++
        failedIds.push(sentenceId)
        results.push({
          sentenceId,
          success: false,
          error: individualError instanceof Error
            ? individualError.message
            : process.env.NODE_ENV === 'production'
              ? '删除失败'
              : String(individualError),
        })
      }
    }
  }

  const result: DeleteSentencesResult = {
    total: sentenceList.length,
    successCount,
    failCount,
    failedIds,
    results,
  }

  return result
}

// 批量处理句子（包装现有的 processAll）
async function batchProcessSentences(courseId: string, userId: string) {
  return processAll(courseId, userId)
}

// 批量更新句子时间 - 由 element 迁移到 sentence
async function batchUpdateElementsTime(updates: { sentenceId: string, startTime: number, endTime: number }[]) {
  const db = getDB()
  let successCount = 0
  let failCount = 0
  const failed: Array<{ sentenceId: string, error: string }> = []

  for (const { sentenceId, startTime, endTime } of updates) {
    try {
      await db.transaction(async (tx) => {
        await tx
          .update(schemas.sentence)
          .set({ startTime: String(startTime), endTime: String(endTime) })
          .where(eq(schemas.sentence.id, sentenceId))
      })
      successCount++
    } catch (error) {
      failCount++
      failed.push({ sentenceId, error: error instanceof Error ? error.message : '未知错误' })
    }
  }

  return {
    total: updates.length,
    successCount,
    failCount,
    failed,
  }
}

export const sentenceService = {
  createSentences,
  deleteOne,
  deleteAll,
  movePosition,
  processOne,
  processAll,
  clearOne,
  clearAll,
  splitOne,
  splitAll,
  changeContent,
  changeChinese,
  generateWordDetails,
  generateDependencyAnalysis,
  generateLearningContentOne,
  deleteLearningContent,
  generateLearningContentAll,
  updateWordDetail,
  analyze,
  insertAbove,
  insertBelow,
  moveToTop,
  moveToBottom,
  // 新增的批量操作方法
  batchUpsertSentences,
  deleteBatchSentences,
  batchProcessSentences,
  batchUpdateElementsTime,
}
