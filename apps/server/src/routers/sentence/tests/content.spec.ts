import { it, expect, describe } from "vitest";
import { normalizeContent } from "../content";

describe("normalizeContent", () => {
  describe("Handling double quotes", () => {
    it("should separate words and quotes with spaces", () => {
      expect(normalizeContent('I like "my hero"')).toBe('I like " my hero "');
    });

    it("should handle different types of quotes", () => {
      expect(normalizeContent("i can“t find it")).toBe("i can't find it");
      expect(normalizeContent("i can’t find it")).toBe("i can't find it");
      expect(normalizeContent('i can"t find it')).toBe("i can't find it");
      expect(normalizeContent('i can"t find it. he have“ve big apple')).toBe(
        "i can't find it . he have've big apple"
      );
    });

    it("去掉末尾的句号", () => {
      expect(normalizeContent("In addition .")).toBe("In addition");
    });

    it("should convert chinese quotes to english quotes", () => {
      expect(normalizeContent("I like “my hero“")).toBe('I like " my hero "');
    });

    it("should normalizeContent multiple replacements correctly", () => {
      expect(normalizeContent("“Hello“, she said")).toBe('" Hello ", she said');
    });

    it("should convert other chinese symbols to english symbols", () => {
      expect(normalizeContent("‘Hello‘")).toBe("' Hello '");
      expect(normalizeContent("“Hello” and ‘world’")).toBe(
        `" Hello " and ' world '`
      );
    });

    it("should not change", () => {
      expect(normalizeContent("i don't like you")).toBe("i don't like you");
      expect(normalizeContent('I like " my hero "')).toBe('I like " my hero "');
    });
  });

  it("把中文的破折号替换成英文的破折号", () => {
    expect(normalizeContent("——Future Life")).toBe("— Future Life");
  });

  it("处理破折号", () => {
    expect(
      normalizeContent(
        "God made two great lights—the greater light to govern the day and the lesser light to govern the night"
      )
    ).toBe(
      "God made two great lights — the greater light to govern the day and the lesser light to govern the night"
    );
  });

  it("自动添加空格 ", () => {
    expect(normalizeContent("I am Li Hua, a student from China.")).toBe(
      "I am Li Hua , a student from China"
    );

    expect(normalizeContent("I am Li Hua ,a student from China .")).toBe(
      "I am Li Hua , a student from China"
    );
  });

  it("如果前面已经有空格的话 那么不会添加多余的空格", () => {
    expect(normalizeContent("our students' union ")).toBe(
      "our students' union"
    );

    expect(
      normalizeContent(
        "Is it possible for you and me to have a private meeting afterward ?"
      )
    ).toBe(
      "Is it possible for you and me to have a private meeting afterward ?"
    );
  });

  it("名词所有格应该不被分割", () => {
    expect(
      normalizeContent(
        "Born in a pigs' nest, Lina led a poor life in her childhood."
      )
    ).toBe("Born in a pigs' nest , Lina led a poor life in her childhood");
  });

  it("替换中文符号为英文符号", () => {
    expect(normalizeContent("In addition ！")).toBe("In addition !");
    expect(normalizeContent("In addition ？")).toBe("In addition ?");
    expect(normalizeContent("In addition ：")).toBe("In addition :");
  });

  it("如果是2个空格（或者以上）， 那么需要合并成一个空格", () => {
    expect(
      normalizeContent(
        "would be something about customs ,  holidays and festivals in America"
      )
    ).toBe(
      "would be something about customs , holidays and festivals in America"
    );
  });

  it("如果是两个符号相邻的话 需要去掉中间的空格 ", () => {
    expect(
      normalizeContent(
        "“ That's Happy ,” his parents answered and burst into laughter ,“ Poppy's new friend !”"
      )
    ).toBe(
      `" That's Happy ," his parents answered and burst into laughter ," Poppy's new friend !"`
    );
  });

  it("如果是 Mr 的话 后面的句号不去除", () => {
    expect(normalizeContent("Mr. Dinosaur")).toBe("Mr. Dinosaur");
  });

  it("后面是分号的话 需要拆分开", () => {
    expect(normalizeContent("Her response was quite amusing;")).toBe(
      `Her response was quite amusing ;`
    );
  });

  it("前面是分号的话 需要拆分开", () => {
    expect(
      normalizeContent("Her response was quite amusing;my name is xiaohong")
    ).toBe(`Her response was quite amusing ; my name is xiaohong`);
  });

  it("对话里面单引号的处理", () => {
    expect(
      normalizeContent(
        "I teased my daughter, saying, 'From now on, we can't spend the whole day out. You've got a parrot waiting to be fed.'"
      )
    ).toBe(
      "I teased my daughter , saying ,' From now on , we can't spend the whole day out . You've got a parrot waiting to be fed .'"
    );
  });

  it("对话里面双引号的处理", () => {
    expect(
      normalizeContent(
        `I teased my daughter, saying, "From now on, we can't spend the whole day out. You've got a parrot waiting to be fed."`
      )
    ).toBe(
      `I teased my daughter , saying ," From now on , we can't spend the whole day out . You've got a parrot waiting to be fed ."`
    );
  });

  it("后面是冒号", () => {
    expect(normalizeContent(`i say: you are best`)).toBe(
      `i say : you are best`
    );
  });

  it("前面是冒号", () => {
    expect(normalizeContent(`i say :you are best`)).toBe(
      `i say : you are best`
    );
  });

  it("处理对话的句子(冒号加双引号的形式)", () => {
    expect(normalizeContent(`i say:"you are best"`)).toBe(
      `i say :" you are best "`
    );
  });

  it("双引号后面是逗号", () => {
    expect(normalizeContent(`the “yellow paper”.`)).toBe(
      `the " yellow paper "`
    );
  });

  it("处理小括号", () => {
    expect(normalizeContent(`(like name , email , age)`)).toBe(
      `( like name , email , age )`
    );
  });

  it("处理中括号", () => {
    expect(normalizeContent(`[like name , email , age]`)).toBe(
      `[ like name , email , age ]`
    );
  });

  it("处理大括号", () => {
    expect(normalizeContent(`{like name , email , age}`)).toBe(
      `{ like name , email , age }`
    );
  });

  it("处理双引号后面带符号的情况", () => {
    expect(
      normalizeContent(`Can we play that computer game " Happy Mrs. Chicken"?`)
    ).toBe(`Can we play that computer game " Happy Mrs. Chicken "?`);
    expect(
      normalizeContent(`Can we play that computer game " Happy Mrs. Chicken"!`)
    ).toBe(`Can we play that computer game " Happy Mrs. Chicken "!`);
  });

  it("处理 Mrs 后面不是句号的情况 ", () => {
    expect(normalizeContent("Yes, please. Mrs Young.")).toBe(
      `Yes , please . Mrs Young`
    );
    expect(normalizeContent("Do you like coffee, Mrs Price?")).toBe(
      `Do you like coffee , Mrs Price ?`
    );
  });

  it("数字后面是逗号", () => {
    expect(
      normalizeContent("The Codable protocol, introduced in Swift 4, enables")
    ).toBe("The Codable protocol , introduced in Swift 4 , enables");
  });

  it("Mr. 这种情况 需要保留末尾的句号", () => {
    expect(normalizeContent("Mr.")).toBe("Mr.");
  });

  it("should ", () => {
    expect(normalizeContent("You've typed it with only one 'L'.")).toBe(
      "You've typed it with only one ' L '"
    );
  });

  it("数字小数点", () => {
    expect(normalizeContent("it's $3.99 per carton")).toBe(
      "it's $3.99 per carton"
    );
  });

  it("处理引号和破折号", () => {
    expect(
      normalizeContent(
        `Researchers looked at people's "niceness" — how warm`
      )
    ).toBe(
      `Researchers looked at people's " niceness " — how warm`
    );

    expect(
      normalizeContent(
        `Researchers looked at people's 'niceness' — how warm`
      )
    ).toBe(
      `Researchers looked at people's ' niceness ' — how warm`
    );
  });

  it("处理 ... 的情况", async () => {
    expect(
      normalizeContent(
        "Well , you would be too if you found Joan and David boots on sale ...50 percent off"
      )
    ).toBe(
      "Well , you would be too if you found Joan and David boots on sale ... 50 percent off"
    );
  });

  it("时间格式不应该被拆分", () => {
    expect(normalizeContent("The meeting starts at 15:00 today")).toBe(
      "The meeting starts at 15:00 today"
    );
    expect(normalizeContent("I will arrive at 9:30 AM")).toBe(
      "I will arrive at 9:30 AM"
    );
    expect(normalizeContent("The train departs at 23:45")).toBe(
      "The train departs at 23:45"
    );
    expect(normalizeContent("Let's meet at 12:00 for lunch")).toBe(
      "Let's meet at 12:00 for lunch"
    );
  });
});
