import { describe, it, expect } from 'vitest'
import { sentenceService } from '../sentence.service'

describe('Sentence Service', () => {
  describe('Exported Methods', () => {
    it('should export all required batch operation methods', () => {
      // 验证新增的批量操作方法都已导出
      expect(sentenceService.batchUpsertSentences).toBeDefined()
      expect(sentenceService.deleteBatchSentences).toBeDefined()
      expect(sentenceService.batchProcessSentences).toBeDefined()
      expect(sentenceService.batchUpdateElementsTime).toBeDefined()
      
      // 验证方法类型
      expect(typeof sentenceService.batchUpsertSentences).toBe('function')
      expect(typeof sentenceService.deleteBatchSentences).toBe('function')
      expect(typeof sentenceService.batchProcessSentences).toBe('function')
      expect(typeof sentenceService.batchUpdateElementsTime).toBe('function')
    })

    it('should maintain all existing methods', () => {
      // 验证原有方法仍然存在
      expect(sentenceService.createSentences).toBeDefined()
      expect(sentenceService.deleteOne).toBeDefined()
      expect(sentenceService.deleteAll).toBeDefined()
      expect(sentenceService.movePosition).toBeDefined()
      expect(sentenceService.processOne).toBeDefined()
      expect(sentenceService.processAll).toBeDefined()
      expect(sentenceService.clearOne).toBeDefined()
      expect(sentenceService.clearAll).toBeDefined()
      expect(sentenceService.splitOne).toBeDefined()
      expect(sentenceService.splitAll).toBeDefined()
      expect(sentenceService.changeContent).toBeDefined()
      expect(sentenceService.changeChinese).toBeDefined()
      expect(sentenceService.generateWordDetails).toBeDefined()
      expect(sentenceService.generateDependencyAnalysis).toBeDefined()
      expect(sentenceService.generateLearningContentOne).toBeDefined()
      expect(sentenceService.deleteLearningContent).toBeDefined()
      expect(sentenceService.generateLearningContentAll).toBeDefined()
      expect(sentenceService.updateWordDetail).toBeDefined()
      expect(sentenceService.analyze).toBeDefined()
      expect(sentenceService.insertAbove).toBeDefined()
      expect(sentenceService.insertBelow).toBeDefined()
      expect(sentenceService.moveToTop).toBeDefined()
      expect(sentenceService.moveToBottom).toBeDefined()
    })

    it('should have proper method signatures for batch operations', () => {
      // 验证方法签名（通过检查length属性来验证参数数量）
      expect(sentenceService.batchUpsertSentences.length).toBe(3) // courseId, sentences, userId
      expect(sentenceService.deleteBatchSentences.length).toBe(1) // sentenceList
      expect(sentenceService.batchProcessSentences.length).toBe(2) // courseId, userId
      expect(sentenceService.batchUpdateElementsTime.length).toBe(1) // updates
    })
  })

  describe('Service Structure', () => {
    it('should be an object with methods', () => {
      expect(typeof sentenceService).toBe('object')
      expect(sentenceService).not.toBeNull()
    })

    it('should have a reasonable number of methods', () => {
      const methodCount = Object.keys(sentenceService).length
      // 应该有原来的方法 + 4个新的批量操作方法
      expect(methodCount).toBeGreaterThan(25) // 原来就有很多方法
      expect(methodCount).toBeLessThan(35) // 不应该过多
    })
  })

  describe('Method Consistency', () => {
    it('should follow naming conventions', () => {
      const batchMethods = [
        'batchUpsertSentences',
        'batchProcessSentences',
        'batchUpdateElementsTime',
      ]

      batchMethods.forEach(methodName => {
        expect(methodName).toMatch(/^batch[A-Z]/) // 批量方法应该以 'batch' 开头
        expect(sentenceService[methodName as keyof typeof sentenceService]).toBeDefined()
      })
    })

    it('should have delete method following existing pattern', () => {
      // deleteBatchSentences 应该与 deleteOne, deleteAll 保持一致的命名模式
      expect(sentenceService.deleteBatchSentences).toBeDefined()
      expect(sentenceService.deleteOne).toBeDefined()
      expect(sentenceService.deleteAll).toBeDefined()
    })
  })

  describe('Integration Points', () => {
    it('should maintain compatibility with existing service methods', () => {
      // 确保新方法不会与现有方法冲突
      const allMethods = Object.keys(sentenceService)
      const uniqueMethods = new Set(allMethods)
      
      expect(allMethods.length).toBe(uniqueMethods.size) // 没有重复的方法名
    })

    it('should have proper async method signatures', () => {
      // 所有新增的批量操作方法都应该是异步的
      const batchMethods = [
        'batchUpsertSentences',
        'deleteBatchSentences',
        'batchProcessSentences',
        'batchUpdateElementsTime',
      ]

      batchMethods.forEach(methodName => {
        const method = sentenceService[methodName as keyof typeof sentenceService]
        expect(method.constructor.name).toBe('AsyncFunction')
      })
    })
  })

  describe('Migration Verification', () => {
    it('should provide all functionality previously available in adminSentence', () => {
      // 验证从 adminSentence 迁移的所有功能都可用
      const migratedFunctionality = [
        'batchUpsertSentences',    // 批量创建或更新句子
        'deleteBatchSentences',    // 批量删除句子
        'batchProcessSentences',   // 批量处理句子
        'movePosition',            // 移动句子位置（已存在）
        'batchUpdateElementsTime', // 批量更新元素时间
      ]

      migratedFunctionality.forEach(functionality => {
        expect(sentenceService).toHaveProperty(functionality)
        expect(typeof sentenceService[functionality as keyof typeof sentenceService]).toBe('function')
      })
    })
  })
})