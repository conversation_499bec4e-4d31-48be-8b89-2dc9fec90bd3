import { describe, it, expect } from 'vitest'
import { sentenceRouter } from '../sentence.router'

describe('Sentence Router', () => {
  it('should have all required batch operation procedures', () => {
    // 检查路由是否包含所有必需的批量操作接口
    expect(sentenceRouter._def.procedures).toHaveProperty('batchUpsertSentences')
    expect(sentenceRouter._def.procedures).toHaveProperty('deleteBatchSentences')
    expect(sentenceRouter._def.procedures).toHaveProperty('batchProcessSentences')
    expect(sentenceRouter._def.procedures).toHaveProperty('batchUpdateElementsTime')
  })

  it('should have existing procedures unchanged', () => {
    // 确保原有的接口仍然存在
    expect(sentenceRouter._def.procedures).toHaveProperty('createSentences')
    expect(sentenceRouter._def.procedures).toHaveProperty('deleteOne')
    expect(sentenceRouter._def.procedures).toHaveProperty('deleteAll')
    expect(sentenceRouter._def.procedures).toHaveProperty('movePosition')
    expect(sentenceRouter._def.procedures).toHaveProperty('processOne')
    expect(sentenceRouter._def.procedures).toHaveProperty('processAll')
    expect(sentenceRouter._def.procedures).toHaveProperty('clearOne')
    expect(sentenceRouter._def.procedures).toHaveProperty('clearAll')
    expect(sentenceRouter._def.procedures).toHaveProperty('splitOne')
    expect(sentenceRouter._def.procedures).toHaveProperty('splitAll')
    expect(sentenceRouter._def.procedures).toHaveProperty('changeContent')
    expect(sentenceRouter._def.procedures).toHaveProperty('changeChinese')
  })

  describe('Procedure Types', () => {
    it('should have all batch operations defined', () => {
      const procedures = sentenceRouter._def.procedures
      
      // 批量操作应该都有定义
      expect(procedures.batchUpsertSentences._def).toBeDefined()
      expect(procedures.deleteBatchSentences._def).toBeDefined()
      expect(procedures.batchProcessSentences._def).toBeDefined()
      expect(procedures.batchUpdateElementsTime._def).toBeDefined()
    })

    it('should have proper input validation for batchUpsertSentences', () => {
      const procedure = sentenceRouter._def.procedures.batchUpsertSentences
      expect(procedure._def.inputs).toHaveLength(1)
      
      // 测试输入结构（这里主要验证输入验证器存在）
      const inputValidator = procedure._def.inputs[0]
      expect(inputValidator).toBeDefined()
    })

    it('should have proper input validation for deleteBatchSentences', () => {
      const procedure = sentenceRouter._def.procedures.deleteBatchSentences
      expect(procedure._def.inputs).toHaveLength(1)
      
      const inputValidator = procedure._def.inputs[0]
      expect(inputValidator).toBeDefined()
    })

    it('should have proper input validation for batchProcessSentences', () => {
      const procedure = sentenceRouter._def.procedures.batchProcessSentences
      expect(procedure._def.inputs).toHaveLength(1)
      
      const inputValidator = procedure._def.inputs[0]
      expect(inputValidator).toBeDefined()
    })

    it('should have proper input validation for batchUpdateElementsTime', () => {
      const procedure = sentenceRouter._def.procedures.batchUpdateElementsTime
      expect(procedure._def.inputs).toHaveLength(1)
      
      const inputValidator = procedure._def.inputs[0]
      expect(inputValidator).toBeDefined()
    })
  })

  describe('Router Structure', () => {
    it('should be a valid tRPC router', () => {
      expect(sentenceRouter._def).toBeDefined()
      expect(sentenceRouter._def.procedures).toBeDefined()
      expect(typeof sentenceRouter._def.procedures).toBe('object')
    })

    it('should have a reasonable number of procedures', () => {
      const procedureCount = Object.keys(sentenceRouter._def.procedures).length
      // 应该有原来的接口 + 4个新的批量操作接口
      expect(procedureCount).toBeGreaterThan(20) // 原来就有很多接口
      expect(procedureCount).toBeLessThan(50) // 不应该过多
    })
  })

  describe('Batch Operations Coverage', () => {
    it('should cover all migrated adminSentence operations', () => {
      const procedures = sentenceRouter._def.procedures
      
      // 验证所有从 adminSentence 迁移的操作都已包含
      const migratedOperations = [
        'batchUpsertSentences',    // 原 adminSentence.batchUpsertSentences
        'deleteBatchSentences',    // 原 adminSentence.deleteBatchSentences
        'batchProcessSentences',   // 原 adminSentence.batchProcessSentences
        'movePosition',            // 原 adminSentence.movePosition (已存在)
        'batchUpdateElementsTime', // 原 adminSentence.batchUpdateElementsTime
      ]

      migratedOperations.forEach(operation => {
        expect(procedures).toHaveProperty(operation)
      })
    })
  })
})