import { z } from 'zod'
import { protectedProcedure, router } from '../../trpc'
import { sentenceService } from './sentence.service'

export const sentenceRouter = router({
  createSentences: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentences: z.array(
          z.object({
            content: z.string(),
            chinese: z.string(),
          }),
        ),
      }),
    )
    .mutation(async ({ input }) => {
      const { courseId, sentences } = input
      return sentenceService.createSentences(courseId, sentences)
    }),
  deleteOne: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentenceId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { courseId, sentenceId } = input
      return sentenceService.deleteOne(sentenceId, courseId)
    }),
  deleteAll: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { courseId } = input
      return sentenceService.deleteAll(courseId)
    }),

  clearOne: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentenceId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { courseId, sentenceId } = input
      return sentenceService.clearOne(courseId, sentenceId)
    }),
  clearAll: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { courseId } = input
      return sentenceService.clearAll(courseId)
    }),

  movePosition: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        affectedSentences: z.array(
          z.object({
            position: z.number(),
            sentenceId: z.string(),
          }),
        ),
      }),
    )
    .mutation(async ({ input }) => {
      const { courseId, affectedSentences } = input
      return sentenceService.movePosition(courseId, affectedSentences)
    }),

  splitOne: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentenceId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }: { input: { courseId: string, sentenceId: string }, ctx: { user: { id: string } } }) => {
      const { sentenceId } = input
      return sentenceService.splitOne(sentenceId, ctx.user.id)
    }),

  splitAll: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }: { input: { courseId: string }, ctx: { user: { id: string } } }) => {
      const { courseId } = input
      return sentenceService.splitAll(courseId, ctx.user.id)
    }),

  processOne: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentenceId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }: { input: { courseId: string, sentenceId: string }, ctx: { user: { id: string } } }) => {
      const { sentenceId } = input
      return sentenceService.processOne(sentenceId, ctx.user.id)
    }),

  processAll: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }: { input: { courseId: string }, ctx: { user: { id: string } } }) => {
      return sentenceService.processAll(input.courseId, ctx.user.id)
    }),

  changeContent: protectedProcedure
    .input(
      z.object({
        sentenceId: z.string(),
        content: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      return sentenceService.changeContent(
        input.sentenceId,
        input.content,
      )
    }),

  changeChinese: protectedProcedure
    .input(
      z.object({
        sentenceId: z.string(),
        chinese: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      return sentenceService.changeChinese(
        input.sentenceId,
        input.chinese,
      )
    }),

  generateLearningContentOne: protectedProcedure
    .input(
      z.object({
        sentenceId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }: { input: { sentenceId: string }, ctx: { user: { id: string } } }) => {
      return sentenceService.generateLearningContentOne(input.sentenceId, ctx.user.id)
    }),

  deleteLearningContent: protectedProcedure
    .input(
      z.object({
        sentenceId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      return sentenceService.deleteLearningContent(input.sentenceId)
    }),

  generateLearningContentAll: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }: { input: { courseId: string }, ctx: { user: { id: string } } }) => {
      return sentenceService.generateLearningContentAll(input.courseId, ctx.user.id)
    }),

  updateWordDetail: protectedProcedure
    .input(
      z.object({
        sentenceId: z.string(),
        word: z.string(),
        pos: z.string().optional(),
        definition: z.string().optional(),
      }),
    )
    .mutation(async ({ input }) => {
      const { sentenceId, word, pos, definition } = input
      return sentenceService.updateWordDetail(sentenceId, word, {
        pos,
        definition,
      })
    }),

  analyze: protectedProcedure
    .input(
      z.object({
        sentenceId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { sentenceId } = input
      return sentenceService.analyze(sentenceId)
    }),

  insertAbove: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentenceId: z.string(),
        content: z.string(),
        chinese: z.string().optional(),
      }),
    )
    .mutation(async ({ input }) => {
      const { courseId, sentenceId, content, chinese } = input
      return sentenceService.insertAbove(courseId, sentenceId, content, chinese)
    }),

  insertBelow: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentenceId: z.string(),
        content: z.string(),
        chinese: z.string().optional(),
      }),
    )
    .mutation(async ({ input }) => {
      const { courseId, sentenceId, content, chinese } = input
      return sentenceService.insertBelow(courseId, sentenceId, content, chinese)
    }),

  moveToTop: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentenceId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { courseId, sentenceId } = input
      return sentenceService.moveToTop(courseId, sentenceId)
    }),

  moveToBottom: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentenceId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      const { courseId, sentenceId } = input
      return sentenceService.moveToBottom(courseId, sentenceId)
    }),

  // 批量操作接口
  batchUpsertSentences: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
        sentences: z.array(
          z.object({
            uuid: z.string(),
            sentenceId: z.string().optional(),
            content: z.string(),
            chinese: z.string(),
          }),
        ),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { courseId, sentences } = input
      const userId = ctx.user.id
      return sentenceService.batchUpsertSentences(courseId, sentences, userId)
    }),

  deleteBatchSentences: protectedProcedure
    .input(
      z.object({
        sentenceList: z.array(
          z.object({
            sentenceId: z.string(),
            courseId: z.string(),
          }),
        ),
      }),
    )
    .mutation(async ({ input }) => {
      return sentenceService.deleteBatchSentences(input.sentenceList)
    }),

  batchProcessSentences: protectedProcedure
    .input(
      z.object({
        courseId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const { courseId } = input
      const userId = ctx.user.id
      return sentenceService.batchProcessSentences(courseId, userId)
    }),

  batchUpdateElementsTime: protectedProcedure
    .input(
      z.object({
        updates: z.array(
          z.object({
            sentenceId: z.string(),
            startTime: z.number(),
            endTime: z.number(),
          }),
        ),
      }),
    )
    .mutation(async ({ input }) => {
      return sentenceService.batchUpdateElementsTime(input.updates)
    }),
})
