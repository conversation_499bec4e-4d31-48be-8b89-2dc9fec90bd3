import { createAnalyzeSentenceModelLLM } from '@/llm/task'
import { parseJsonFromCodeBlock } from '@/utils/json'

/**
 * 分析给定句子中每个单词的翻译和词性，并以JSON格式返回结果
 * 每个单词的信息包括其在句子中的位置、单词本身、词性以及中文释义
 * @param sentence 需要分析的英文句子
 * @returns 返回格式为 [["单词", "词性", "释义"]...] 的JSON数组
 */
export async function analyzeWordDetails(sentence: string) {
  const prompt = `
你的任务是分析给定句子中每个单词的翻译、词性、形态和功能，并以 JSON 格式返回结果。每个单词的信息应包括其在句子中的位置（索引）、单词本身、词性（使用 Universal Part-of-Speech Tags）、形态说明（如适用）以及中文释义或功能说明。

以下是要分析的句子：
<sentence>
${sentence}
</sentence>

分析规则如下：
### 名词处理
- 单数名词：直接提供中文释义，无需标注“单数”。
- 复数名词：需要提供中文释义，并注明“复数”。

### 动词处理
- 实义动词：
    - 如果动词是原型，直接提供中文释义，无需标注形态。例如：eat → 吃
    - 如果动词有变形（如过去式、现在分词等），标注变形类型和中文释义，格式为 动词（变形类型）: 中文释义。例如：jumped → jump（过去式）: 跳跃
- 助动词：
    - 如果是原型，直接提供中文释义，无需标注变形类型。例如：have → 有
    - 如果有变形，标注变形类型和中文释义，格式为 原型（变形类型）: 中文释义。例如：had → have（过去分词）: 构成过去完成时
    - 如果有缩写，只翻译不解释

### 短语动词处理
- 如果动词与后续介词/副词构成固定搭配（如 "figure out", "put off"），按以下规则处理：
  1. **动词部分**：
     - 词性标注为 "VERB"。
     - 释义格式："短语动词原型: 中文释义"。
     - 示例："give" → "give up（短语动词）: 放弃"。
  2. **介词/副词部分**：
     - 词性按实际标注（通常为 "ADP" 或 "PART"）。
     - 释义标注为 "（与 [动词] 搭配）"。
     - 示例："up" → "（与 give 搭配）"。
- 常见短语动词列表（供参考）："figure out", "look into", "give up", "put off", "carry on"。

### 冠词处理：
- 不定冠词（a、an）：
    - 如果后面接的是可数名词单数，翻译为“一个”。
    - 如果后面接的是不可数名词或固定搭配，仅标注为“不定冠词”。
- 定冠词（the）：直接标注为“定冠词”。

### 缩写形式：
- 当作一个单词来处理，例：He's 应该作为一个整体
- 需要先还原完整然后翻译
- 词性取第一个单词的词性 例如 Where's 的词性是 ADV

### 其他词性处理
- 介词、连词等：直接提供中文释义，无需标注原型或变形类型。例如：over → 在...之上
- 代词：直接翻译,不要解释。例如：They → 他们
- 关系代词：翻译的同时加上说明 例如：翻译结果（关系代词的作用说明）
- to:
  - 作为介词时，基于原文,直接翻译。
  - 作为不定式标记时，标注为“引导不定式”。
- 标点符号（PUNCT）：忽略任何标点符号，不输出。
- 所有格形式：任何带有所有格符号（'s 或 '）的单词都必须作为一个整体处理，不能拆分
  - 例: China's -> {"w": "China's", ...} 应该作为一个整体，不能拆分成 "China" 和 "'s"
  - 例: pigs' -> {"w": "pigs'", ...} 应该作为一个整体
  - 所有格形式的词性根据原词确定，释义格式为 "原词的"，例如：China's → 中国的
- 数字需要包含小数点 例: $4.22 -> {"w": "$4.22"} 应该作为一个整体

### 特殊处理
- 人名（PROPN）：
    - 仅当明确指代人时使用
    - 词性为 PROPN_PERSON, 释义直接留空 ''
    - 当人名分成2个单词时 拆分成单个单词处理 例：Lin Qiaozhi 应拆分为 "Lin" 和 "Qiaozhi" 两个单词，每个单词的词性都为 PROPN_PERSON，并且不翻译
    - 人名前面的 Mr. Mrs. Miss 等等 词性是名词 并且翻译

6. 规则强化
- 不输出任何标点符号
- 基于空格来拆分单词
- 不可以合并两个单词

### 格式要求
- 保留单词原始形态（如缩写、首字母大写等）。
- 输出 JSON 格式为：
  {
    "w": "单词",
    "p": "词性",
    "d": "中文释义或功能说明",
  }
    `

  try {
    const model = createAnalyzeSentenceModelLLM()
    const response = await model.invoke(
      prompt,
      'analyze_sentence',
      {
        sentence,
      },
    )

    const result
      = parseJsonFromCodeBlock<{ w: string, d: string, p: string }[]>(response)

    // 定义补丁类型
    interface WordData { w: string, d: string, p: string }

    // 处理人名并过滤标点符号
    function handlePersonNamesAndPunctuation(words: WordData[]): WordData[] {
      return words
        ?.map(({ w, d, p }) => {
          if (p === 'PROPN_PERSON') {
            return { w, d: w, p }
          }
          return { w, d, p }
        })
        .filter(({ p }) => {
          return p !== 'PUNCT'
        }) || []
    }

    // 合并分离的时间格式（如 15 + :00 -> 15:00）
    function mergeTimeFormat(words: WordData[]): WordData[] {
      const result = []
      let i = 0

      while (i < words.length) {
        // 检测时间格式：数字 + :数字 的模式
        if (
          i < words.length - 1
          && words[i].p === 'NUM'
          && words[i + 1].w.startsWith(':')
          && words[i + 1].p === 'NUM'
          && /^\d+$/.test(words[i].w) // 第一个是纯数字
          && /^:\d+$/.test(words[i + 1].w) // 第二个是冒号+数字
        ) {
          // 合并时间格式
          result.push({
            w: words[i].w + words[i + 1].w,
            d: `${words[i].w}点${words[i + 1].w.substring(1)}分`,
            p: 'NUM',
          })
          i += 2 // 跳过下一个已合并的词
        } else {
          result.push(words[i])
          i++
        }
      }

      return result
    }

    // 补丁执行顺序
    const patches = [
      handlePersonNamesAndPunctuation,
      mergeTimeFormat,
    ]

    // 依次执行所有补丁
    let processedResult = result || []

    for (const patch of patches) {
      processedResult = patch(processedResult)
    }

    return (
      processedResult?.map(({ w, d, p }) => ({
        word: w,
        definition: d,
        pos: p,
      })) || []
    )
  } catch (error) {
    console.error('Error analyzing word details:', error)
    throw new Error('Failed to analyze word details')
  }
}
