import { describe, expect, it, vi, beforeEach } from 'vitest'
import { getDB, schemas } from '@/db'
import { sentenceService } from '../sentence.service'

// Mock dependencies
vi.mock('@/db')
vi.mock('@/services/translate')

const mockDB = {
  transaction: vi.fn(),
  update: vi.fn(),
  query: {
    sentence: {
      findFirst: vi.fn(),
      findMany: vi.fn()
    }
  }
}

const mockTx = {
  update: vi.fn()
}

vi.mocked(getDB).mockReturnValue(mockDB as any)

describe('Sentence Service - Time Fields Migration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // 设置常见的mock返回值
    mockDB.transaction.mockImplementation(async (callback) => {
      return callback(mockTx)
    })
    
    mockTx.update.mockReturnValue({
      set: vi.fn().mockReturnValue({
        where: vi.fn().mockResolvedValue([{ affectedRows: 1 }])
      })
    })
    
    mockDB.update.mockReturnValue({
      set: vi.fn().mockReturnValue({
        where: vi.fn().mockResolvedValue([{ affectedRows: 1 }])
      })
    })
  })

  describe('batchUpdateElementsTime', () => {
    it('应该批量更新sentence表的时间字段', async () => {
      const updates = [
        { sentenceId: 'sentence-1', startTime: 12.5, endTime: 15.75 },
        { sentenceId: 'sentence-2', startTime: 20.0, endTime: 23.25 },
        { sentenceId: 'sentence-3', startTime: 30.125, endTime: 35.875 }
      ]

      const result = await sentenceService.batchUpdateElementsTime(updates)

      // 验证所有更新都成功
      expect(result.total).toBe(3)
      expect(result.successCount).toBe(3)
      expect(result.failCount).toBe(0)
      expect(result.failed).toHaveLength(0)

      // 验证数据库事务被调用了3次
      expect(mockDB.transaction).toHaveBeenCalledTimes(3)
    })

    it('应该将数字时间转换为字符串存储', async () => {
      const updates = [
        { sentenceId: 'sentence-1', startTime: 12.500, endTime: 15.750 }
      ]

      await sentenceService.batchUpdateElementsTime(updates)

      // 验证update被调用时使用字符串格式
      expect(mockTx.update).toHaveBeenCalledWith(schemas.sentence)
      const setCall = mockTx.update.mock.results[0].value.set
      expect(setCall).toHaveBeenCalledWith({
        startTime: '12.5',
        endTime: '15.75'
      })
    })

    it('应该正确处理部分更新失败的情况', async () => {
      const updates = [
        { sentenceId: 'sentence-1', startTime: 12.5, endTime: 15.75 },
        { sentenceId: 'sentence-2', startTime: 20.0, endTime: 23.25 },
        { sentenceId: 'sentence-3', startTime: 30.0, endTime: 35.0 }
      ]

      // 模拟第二个更新失败
      let callCount = 0
      mockDB.transaction.mockImplementation(async (callback) => {
        callCount++
        if (callCount === 2) {
          throw new Error('数据库连接失败')
        }
        return callback(mockTx)
      })

      const result = await sentenceService.batchUpdateElementsTime(updates)

      expect(result.total).toBe(3)
      expect(result.successCount).toBe(2)
      expect(result.failCount).toBe(1)
      expect(result.failed).toHaveLength(1)
      expect(result.failed[0]).toEqual({
        sentenceId: 'sentence-2',
        error: '数据库连接失败'
      })
    })

    it('应该处理空更新数组', async () => {
      const result = await sentenceService.batchUpdateElementsTime([])

      expect(result.total).toBe(0)
      expect(result.successCount).toBe(0)
      expect(result.failCount).toBe(0)
      expect(result.failed).toHaveLength(0)
      expect(mockDB.transaction).not.toHaveBeenCalled()
    })

    it('应该正确处理decimal精度的时间值', async () => {
      const updates = [
        { sentenceId: 'sentence-1', startTime: 123.456789, endTime: 987.654321 }
      ]

      await sentenceService.batchUpdateElementsTime(updates)

      const setCall = mockTx.update.mock.results[0].value.set
      expect(setCall).toHaveBeenCalledWith({
        startTime: '123.456789',
        endTime: '987.654321'
      })
    })

    it('应该正确处理零值时间', async () => {
      const updates = [
        { sentenceId: 'sentence-1', startTime: 0, endTime: 0 }
      ]

      await sentenceService.batchUpdateElementsTime(updates)

      const setCall = mockTx.update.mock.results[0].value.set
      expect(setCall).toHaveBeenCalledWith({
        startTime: '0',
        endTime: '0'
      })
    })

    it('应该确保使用正确的sentence表和字段', async () => {
      const updates = [
        { sentenceId: 'sentence-test', startTime: 10.0, endTime: 20.0 }
      ]

      await sentenceService.batchUpdateElementsTime(updates)

      // 验证更新的是sentence表而不是element表
      expect(mockTx.update).toHaveBeenCalledWith(schemas.sentence)
      
      // 验证where条件使用了sentenceId
      const whereCall = mockTx.update.mock.results[0].value.set.mock.results[0].value.where
      expect(whereCall).toHaveBeenCalled()
    })
  })

  describe('时间字段数据迁移验证', () => {
    it('应该验证sentence表包含时间字段', () => {
      // 这个测试验证schema定义是否正确
      expect(schemas.sentence).toBeDefined()
      // 在实际环境中，可以验证schema.sentence的字段定义
    })

    it('应该确保不再使用element表的时间字段', async () => {
      // 验证不会尝试更新element表的时间字段
      const updates = [
        { sentenceId: 'sentence-1', startTime: 10.0, endTime: 20.0 }
      ]

      await sentenceService.batchUpdateElementsTime(updates)

      // 确保没有尝试更新element表
      expect(mockTx.update).not.toHaveBeenCalledWith(schemas.element)
    })
  })

  describe('错误处理和边界情况', () => {
    it('应该处理非常大的时间值', async () => {
      const updates = [
        { sentenceId: 'sentence-1', startTime: 99999.999, endTime: 999999.999 }
      ]

      const result = await sentenceService.batchUpdateElementsTime(updates)

      expect(result.successCount).toBe(1)
      expect(result.failCount).toBe(0)
    })

    it('应该处理负数时间值（虽然不常见）', async () => {
      const updates = [
        { sentenceId: 'sentence-1', startTime: -1.0, endTime: -0.5 }
      ]

      const result = await sentenceService.batchUpdateElementsTime(updates)

      expect(result.successCount).toBe(1)
      const setCall = mockTx.update.mock.results[0].value.set
      expect(setCall).toHaveBeenCalledWith({
        startTime: '-1',
        endTime: '-0.5'
      })
    })

    it('应该记录详细的错误信息', async () => {
      const updates = [
        { sentenceId: 'invalid-sentence', startTime: 10.0, endTime: 20.0 }
      ]

      const customError = new Error('Sentence not found')
      mockDB.transaction.mockRejectedValue(customError)

      const result = await sentenceService.batchUpdateElementsTime(updates)

      expect(result.failCount).toBe(1)
      expect(result.failed[0].error).toBe('Sentence not found')
      expect(result.failed[0].sentenceId).toBe('invalid-sentence')
    })
  })
})