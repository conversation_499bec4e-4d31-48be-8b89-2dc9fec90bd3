import type { GetPresignedUrlDto, MediaUploadDto } from '@julebu/shared'
import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import {
  ALLOWED_AUDIO_MIME_TYPES,
  ALLOWED_IMAGE_MIME_TYPES,
  ALLOWED_VIDEO_MIME_TYPES,
  getMediaTypeFromMimeType,
  getStoragePrefix,
  MAX_AUDIO_SIZE,
  MAX_IMAGE_SIZE,
  MAX_VIDEO_SIZE,
  MediaType,
} from '@julebu/shared'
import { TRPCError } from '@trpc/server'
import { includes } from 'lodash-es'
import { config } from '@/config/config'

const s3Client = new S3Client({
  endpoint: config.s3.endpoint,
  region: config.s3.region,
  credentials: {
    accessKeyId: config.s3.accessKeyId,
    secretAccessKey: config.s3.secretAccessKey,
  },
  forcePathStyle: true,
})

// Validation functions
function validateAudioFile(mimeType: string, contentLength: number) {
  if (!(ALLOWED_AUDIO_MIME_TYPES as readonly string[]).includes(mimeType)) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: `不支持的音频文件类型: ${mimeType}`,
    })
  }

  if (contentLength && contentLength > MAX_AUDIO_SIZE) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: `音频文件大小不能超过 ${MAX_AUDIO_SIZE / 1024 / 1024}MB`,
    })
  }
}

function validateVideoFile(mimeType: string, contentLength: number) {
  if (!(ALLOWED_VIDEO_MIME_TYPES as readonly string[]).includes(mimeType)) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: `不支持的视频文件类型: ${mimeType}`,
    })
  }

  if (contentLength && contentLength > MAX_VIDEO_SIZE) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: `视频文件大小不能超过 ${MAX_VIDEO_SIZE / 1024 / 1024}MB`,
    })
  }
}

export const s3Service = {
  // Original method for backward compatibility
  async getPresignedUrl(input: GetPresignedUrlDto) {
    const { key, contentLength, mimeType } = input

    if (!ALLOWED_IMAGE_MIME_TYPES.includes(mimeType)) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: `不支持的文件类型: ${mimeType}`,
      })
    }

    if (contentLength && contentLength > MAX_IMAGE_SIZE) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: `文件大小不能超过 ${MAX_IMAGE_SIZE / 1024 / 1024}MB`,
      })
    }

    const putCommand = new PutObjectCommand({
      Bucket: config.s3.bucketImages,
      Key: key,
      ContentLength: contentLength,
      ContentType: mimeType,
    })

    const presignedUrl = await getSignedUrl(s3Client, putCommand, {
      expiresIn: 60 * 1,
      // eslint-disable-next-line ts/ban-ts-comment
      // @ts-expect-error
      unsignedBody: true,
    })

    return presignedUrl
  },

  // New smart media upload method
  async getMediaPresignedUrl(input: MediaUploadDto) {
    const { key, contentLength, mimeType } = input

    // Auto-detect media type
    const mediaType = getMediaTypeFromMimeType(mimeType)

    // Apply validation based on media type
    switch (mediaType) {
      case MediaType.IMAGE:
        if (!includes(ALLOWED_IMAGE_MIME_TYPES, mimeType)) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: `不支持的图片文件类型: ${mimeType}`,
          })
        }
        if (contentLength && contentLength > MAX_IMAGE_SIZE) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: `图片文件大小不能超过 ${MAX_IMAGE_SIZE / 1024 / 1024}MB`,
          })
        }
        break
      case MediaType.AUDIO:
        validateAudioFile(mimeType, contentLength)
        break
      case MediaType.VIDEO:
        validateVideoFile(mimeType, contentLength)
        break
      default:
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `不支持的媒体类型`,
        })
    }

    // Get storage prefix and construct full key
    const storagePrefix = getStoragePrefix(mediaType)
    const fullKey = storagePrefix + key

    const putCommand = new PutObjectCommand({
      Bucket: config.s3.bucketGame,
      Key: fullKey,
      ContentLength: contentLength,
      ContentType: mimeType,
    })

    const presignedUrl = await getSignedUrl(s3Client, putCommand, {
      expiresIn: 60 * 1,
      // eslint-disable-next-line ts/ban-ts-comment
      // @ts-expect-error
      unsignedBody: true,
    })

    return presignedUrl
  },
}
