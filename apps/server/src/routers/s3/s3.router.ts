import { getPresignedUrlDto, mediaUploadDto } from '@julebu/shared'
import { protectedProcedure, router } from '../../trpc'
import { s3Service } from './s3.service'

export const s3Router = router({
  getPresignedUrl: protectedProcedure
    .input(getPresignedUrlDto)
    .mutation(async ({ input }) => {
      return s3Service.getPresignedUrl(input)
    }),

  getMediaPresignedUrl: protectedProcedure
    .input(mediaUploadDto)
    .mutation(async ({ input }) => {
      return s3Service.getMediaPresignedUrl(input)
    }),
})
