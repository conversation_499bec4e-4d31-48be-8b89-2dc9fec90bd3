import type { FastifyInstance, FastifyPluginAsync } from 'fastify'
import { validateAdminSecret, validateToken } from '@/middleware/auth'
import { adminRoutes } from '../admin/admin.router'
import { cosRoutes } from '../cos/cos.routers'
import { inboxApiRoutes } from '../inbox/inbox.router'

// 普通 API 路由插件（使用 JWT 验证）
const apiRoutesPlugin: FastifyPluginAsync = async (apiInstance) => {
  // 在此插件级别添加 JWT 验证钩子
  apiInstance.addHook('preHandler', validateToken)

  // 注册非管理员路由
  apiInstance.register(cosRoutes)
  apiInstance.register(inboxApiRoutes)
}

// 管理员 API 路由插件（使用密钥验证）
const adminApiRoutesPlugin: FastifyPluginAsync = async (adminInstance) => {
  // 在此插件级别添加管理员密钥验证钩子
  adminInstance.addHook('preHandler', validateAdminSecret)

  // 注册管理员路由
  adminInstance.register(adminRoutes)
}

// 这些都是 api 路由
export function setupAPIRoutes(server: FastifyInstance) {
  // 注册普通 API 路由插件，统一添加 /api 前缀
  server.register(apiRoutesPlugin, { prefix: '/api' })

  // 注册管理员 API 路由插件，统一添加 /api 前缀
  server.register(adminApiRoutesPlugin, { prefix: '/api' })
}
