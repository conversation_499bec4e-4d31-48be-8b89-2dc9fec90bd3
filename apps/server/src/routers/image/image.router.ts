import { aiGenerateImageDto, createImageWithTextLinkDto } from '@julebu/shared'
import { z } from 'zod'
import { protectedProcedure, router } from '../../trpc'
import { imageService } from './image.service'

export const imageRouter = router({
  generateImage: protectedProcedure.input(aiGenerateImageDto).mutation(async ({ input, ctx }) => {
    return imageService.generateImage(input, ctx.user.id)
  }),

  createImageWithTextLink: protectedProcedure.input(createImageWithTextLinkDto).mutation(async ({ input, ctx }) => {
    return imageService.createImageWithTextLink({
      ...input,
      userId: ctx.user.id,
    })
  }),

  batchCreateImageWithTextLink: protectedProcedure.input(z.array(createImageWithTextLinkDto)).mutation(async ({ input, ctx }) => {
    return imageService.batchCreateImageWithTextLink(input.map(item => ({ ...item, userId: ctx.user.id })))
  }),
})
