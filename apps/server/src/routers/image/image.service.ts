import type { AiGenerateImageDto, CreateImageWithTextLinkDto } from '@julebu/shared'
import { ALLOWED_IMAGE_MIME_TYPES } from '@julebu/shared'
import { TRPCError } from '@trpc/server'
import { eq, or, sql } from 'drizzle-orm'
import { includes } from 'lodash-es'
import { getDB, schemas } from '@/db'
import { image } from '@/db/schema/image'
import { textImageLink } from '@/db/schema/textImageLink'
import { createModelIG } from '@/image-generation/task'

export const imageService = {
  generateImage: async ({
    elementId,
    style,
    size,
  }: AiGenerateImageDto, userId: string) => {
    const db = getDB()
    const element = await db.query.element.findFirst({
      where: eq(schemas.element.id, elementId),
    })

    if (!element) {
      throw new TRPCError({ code: 'NOT_FOUND', message: '找不到要生成的元素' })
    }

    const { image, prompt } = await createModelIG().generateImage({
      englishText: element.english,
      chineseText: element.chinese,
      size,
      style,
      userId,
    })

    return {
      base64: image.data?.[0].b64_json,
      description: prompt,
    }
  },

  createImageWithTextLink: async (input: CreateImageWithTextLinkDto) => {
    const { textContent, linkType, userId, ...imageInput } = input

    if (!includes(ALLOWED_IMAGE_MIME_TYPES, imageInput.mimeType)) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: `不支持的文件类型: ${imageInput.mimeType}`,
      })
    }

    const textImageLinkInput = {
      textContent,
      linkType,
      userId,
    }

    // 如果这里采用 事务，textImageLink 生成失败的话，image也会回滚，但是这里期望的是即使 textImageLink 生成失败，image 也要生成成功
    const db = getDB()
    const [{ id: imageId }] = await db
      .insert(image)
      .values({
        ...imageInput,
        userId,
      })
      .$returningId()

    await db.insert(textImageLink).values({
      ...textImageLinkInput,
      imageId,
    })
    return imageId
  },

  batchCreateImageWithTextLink: async (inputs: CreateImageWithTextLinkDto[]) => {
    if (!inputs || inputs.length === 0) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: '输入数据不能为空',
      })
    }

    // 预先验证所有数据
    for (const input of inputs) {
      if (!includes(ALLOWED_IMAGE_MIME_TYPES, input.mimeType)) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `不支持的文件类型: ${input.mimeType}`,
        })
      }
    }

    const db = getDB()

    return db.transaction(async (tx) => {
      const results: string[] = []

      for (const input of inputs) {
        const { textContent, linkType, userId, ...imageInput } = input

        // 检查是否已存在相同的图片（通过fileKey判断）
        const existingImage = await tx.query.image.findFirst({
          where: eq(schemas.image.fileKey, imageInput.fileKey),
        })

        let imageId: string
        const normalizedInput = textContent.toLowerCase().trim()

        if (existingImage) {
          imageId = existingImage.id

          // 检查是否已存在相同的文本链接（使用简单文本比较）

          const existingTextLink = await tx.query.textImageLink.findFirst({
            where: sql`${schemas.textImageLink.imageId} = ${imageId} AND ${schemas.textImageLink.textContent} = ${normalizedInput} AND ${schemas.textImageLink.linkType} = ${linkType}`,
          })

          if (!existingTextLink) {
            // 图片存在但文本链接不存在，只插入文本链接
            await tx.insert(textImageLink).values({
              textContent: normalizedInput,
              linkType,
              userId,
              imageId,
            })
          }
        } else {
          // 图片不存在，插入新图片
          const [{ id }] = await tx
            .insert(image)
            .values({
              ...imageInput,
              userId,
            })
            .$returningId()

          imageId = id

          // 插入文本链接
          await tx.insert(textImageLink).values({
            textContent: normalizedInput,
            linkType,
            userId,
            imageId,
          })
        }

        results.push(imageId)
      }

      return results
    })
  },

  batchGetImagesByEnglish: async (english: string[]) => {
    const db = getDB()

    if (!english || english.length === 0) {
      return []
    }

    // 文本标准化函数：只忽略大小写
    const normalizeText = (text: string): string => {
      return text.toLowerCase().trim()
    }

    // 预处理输入文本
    const normalizedInputs = english.map(text => normalizeText(text)).filter(text => text)

    if (normalizedInputs.length === 0) {
      return english.map(originalText => ({
        originalText,
        normalizedText: normalizeText(originalText),
        matchedImages: [],
        matchCount: 0,
      }))
    }

    // 构建OR条件查询所有文本（数据库层面精确匹配）
    const conditions = normalizedInputs.map(normalizedInput =>
      eq(schemas.textImageLink.textContent, normalizedInput),
    )

    // 一次查询获取所有匹配的图片
    const allMatchedImages = await db
      .select({
        fileKey: schemas.image.fileKey,
        description: schemas.image.description,
        mimeType: schemas.image.mimeType,
        fileSize: schemas.image.fileSize,
        width: schemas.image.width,
        height: schemas.image.height,
        source: schemas.image.source,
        textContent: schemas.textImageLink.textContent,
        linkType: schemas.textImageLink.linkType,
      })
      .from(schemas.image)
      .innerJoin(schemas.textImageLink, eq(schemas.image.id, schemas.textImageLink.imageId))
      .where(or(...conditions))

    // 按原始输入文本分组结果
    const results = english.map((originalText) => {
      const normalizedInput = normalizeText(originalText)

      if (!normalizedInput) {
        return null
      }

      // 筛选属于当前输入文本的匹配项
      const filteredImages = allMatchedImages.filter((item) => {
        const normalizedTextContent = normalizeText(item.textContent)
        return normalizedTextContent === normalizedInput
      })

      if (filteredImages.length === 0) {
        return null
      }

      const { fileKey, description, mimeType, fileSize, width, height, source, textContent, linkType } = filteredImages[0]

      return {
        fileKey,
        description,
        mimeType,
        fileSize,
        width,
        height,
        source,
        textContent,
        linkType,
      }
    })

    return results
  },
}
