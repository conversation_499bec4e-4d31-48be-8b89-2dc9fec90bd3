import { and, eq, sql } from 'drizzle-orm'
import { getDB, schemas } from '@/db'

async function recordUsage(data: {
  userId: string
  model: string
  promptTokens: number
  completionTokens: number
  totalTokens: number
}) {
  const now = new Date()
  const db = getDB()
  const year = now.getFullYear()
  const month = now.getMonth() + 1

  return db
    .insert(schemas.tokenUsage)
    .values({
      ...data,
      year,
      month,
    })
    .onDuplicateKeyUpdate({
      set: {
        promptTokens: sql`${schemas.tokenUsage.promptTokens} + ${data.promptTokens}`,
        completionTokens: sql`${schemas.tokenUsage.completionTokens} + ${data.completionTokens}`,
        totalTokens: sql`${schemas.tokenUsage.totalTokens} + ${data.totalTokens}`,
      },
    })
}

async function getUserMonthlyUsage(
  userId: string,
  year: number,
  month: number,
) {
  const db = getDB()
  const result = await db
    .select({
      totalTokens: sql<number>`CAST(sum(${schemas.tokenUsage.totalTokens}) AS SIGNED)`,
      totalPromptTokens: sql<number>`CAST(sum(${schemas.tokenUsage.promptTokens}) AS SIGNED)`,
      totalCompletionTokens: sql<number>`CAST(sum(${schemas.tokenUsage.completionTokens}) AS SIGNED)`,
    })
    .from(schemas.tokenUsage)
    .where(
      and(
        eq(schemas.tokenUsage.userId, userId),
        eq(schemas.tokenUsage.year, year),
        eq(schemas.tokenUsage.month, month),
      ),
    )

  const usage = result[0]
  return {
    totalTokens: usage.totalTokens || 0,
    totalPromptTokens: usage.totalPromptTokens || 0,
    totalCompletionTokens: usage.totalCompletionTokens || 0,
  }
}

async function getUserAllMonthlyUsage(userId: string) {
  const db = getDB()
  const result = await db
    .select({
      year: schemas.tokenUsage.year,
      month: schemas.tokenUsage.month,
      totalTokens: sql<number>`CAST(sum(${schemas.tokenUsage.totalTokens}) AS SIGNED)`,
      totalPromptTokens: sql<number>`CAST(sum(${schemas.tokenUsage.promptTokens}) AS SIGNED)`,
      totalCompletionTokens: sql<number>`CAST(sum(${schemas.tokenUsage.completionTokens}) AS SIGNED)`,
    })
    .from(schemas.tokenUsage)
    .where(eq(schemas.tokenUsage.userId, userId))
    .groupBy(schemas.tokenUsage.year, schemas.tokenUsage.month)
    .orderBy(schemas.tokenUsage.year, schemas.tokenUsage.month)

  return result.map(usage => ({
    year: usage.year,
    month: usage.month,
    totalTokens: usage.totalTokens || 0,
    totalPromptTokens: usage.totalPromptTokens || 0,
    totalCompletionTokens: usage.totalCompletionTokens || 0,
  }))
}

async function getUserModelUsage(userId: string, model: string) {
  const db = getDB()
  const result = await db
    .select({
      totalTokens: sql<number>`CAST(sum(${schemas.tokenUsage.totalTokens}) AS SIGNED)`,
      totalPromptTokens: sql<number>`CAST(sum(${schemas.tokenUsage.promptTokens}) AS SIGNED)`,
      totalCompletionTokens: sql<number>`CAST(sum(${schemas.tokenUsage.completionTokens}) AS SIGNED)`,
    })
    .from(schemas.tokenUsage)
    .where(
      and(
        eq(schemas.tokenUsage.userId, userId),
        eq(schemas.tokenUsage.model, model),
      ),
    )

  const usage = result[0]
  return {
    totalTokens: usage.totalTokens || 0,
    totalPromptTokens: usage.totalPromptTokens || 0,
    totalCompletionTokens: usage.totalCompletionTokens || 0,
  }
}

export const tokenUsageService = {
  recordUsage,
  getUserMonthlyUsage,
  getUserAllMonthlyUsage,
  getUserModelUsage,
}
