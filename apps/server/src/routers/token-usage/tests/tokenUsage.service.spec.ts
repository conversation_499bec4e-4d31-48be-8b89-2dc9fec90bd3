import { afterAll, beforeEach, describe, expect, it } from "vitest";
import { tokenUsageService } from "../tokenUsage.service";
import { getDB, schemas } from "@/db";

// 清理数据库函数
async function cleanTokenUsageDB() {
  const db = getDB();
  await db.delete(schemas.tokenUsage);
}

describe("token usage service", () => {
  beforeEach(async () => {
    // 每个测试前清空数据库
    await cleanTokenUsageDB();
  });

  afterAll(async () => {
    await cleanTokenUsageDB();
  });

  describe("record usage", () => {
    it("should record new token usage", async () => {
      const userId = "tester";
      const model = "gpt-3.5-turbo";
      const usageData = {
        userId,
        model,
        promptTokens: 100,
        completionTokens: 50,
        totalTokens: 150,
      };

      await tokenUsageService.recordUsage(usageData);

      const db = getDB();
      const result = await db.query.tokenUsage.findFirst({
        where: (tokenUsage, { eq, and }) =>
          and(
            eq(tokenUsage.userId, userId),
            eq(tokenUsage.model, model)
          ),
      });

      expect(result).not.toBeNull();
      expect(result?.promptTokens).toBe(100);
      expect(result?.completionTokens).toBe(50);
      expect(result?.totalTokens).toBe(150);
    });

    it("should update existing token usage for same user, model and month", async () => {
      const userId = "tester";
      const model = "gpt-3.5-turbo";
      const initialUsage = {
        userId,
        model,
        promptTokens: 100,
        completionTokens: 50,
        totalTokens: 150,
      };

      // 记录初始使用量
      await tokenUsageService.recordUsage(initialUsage);

      // 记录额外使用量
      const additionalUsage = {
        userId,
        model,
        promptTokens: 50,
        completionTokens: 25,
        totalTokens: 75,
      };
      await tokenUsageService.recordUsage(additionalUsage);

      const db = getDB();
      const result = await db.query.tokenUsage.findFirst({
        where: (tokenUsage, { eq, and }) =>
          and(
            eq(tokenUsage.userId, userId),
            eq(tokenUsage.model, model)
          ),
      });

      expect(result).not.toBeNull();
      expect(result?.promptTokens).toBe(150); // 100 + 50
      expect(result?.completionTokens).toBe(75); // 50 + 25
      expect(result?.totalTokens).toBe(225); // 150 + 75
    });
  });

  describe("get user monthly usage", () => {
    it("should get user monthly usage statistics", async () => {
      const userId = "tester";
      const model = "gpt-3.5-turbo";
      const usageData = {
        userId,
        model,
        promptTokens: 100,
        completionTokens: 50,
        totalTokens: 150,
      };

      await tokenUsageService.recordUsage(usageData);

      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;

      const usage = await tokenUsageService.getUserMonthlyUsage(
        userId,
        year,
        month
      );

      expect(usage.totalPromptTokens).toBe(100);
      expect(usage.totalCompletionTokens).toBe(50);
      expect(usage.totalTokens).toBe(150);
    });

    it("should return zero values when no usage records exist", async () => {
      const userId = "tester";
      const year = 2023;
      const month = 1;

      const usage = await tokenUsageService.getUserMonthlyUsage(
        userId,
        year,
        month
      );

      expect(usage.totalPromptTokens).toBe(0);
      expect(usage.totalCompletionTokens).toBe(0);
      expect(usage.totalTokens).toBe(0);
    });
  });

  describe("get user all monthly usage", () => {
    it("should get all monthly usage statistics ordered by year and month", async () => {
      const userId = "tester";
      const model = "gpt-3.5-turbo";
      
      // 创建测试数据
      const db = getDB();
      await db.insert(schemas.tokenUsage).values([
        {
          userId,
          model,
          promptTokens: 100,
          completionTokens: 50,
          totalTokens: 150,
          year: 2023,
          month: 1,
        },
        {
          userId,
          model,
          promptTokens: 200,
          completionTokens: 100,
          totalTokens: 300,
          year: 2023,
          month: 2,
        },
      ]);

      const usage = await tokenUsageService.getUserAllMonthlyUsage(userId);

      expect(usage).toHaveLength(2);
      expect(usage[0].year).toBe(2023);
      expect(usage[0].month).toBe(1);
      expect(usage[0].totalTokens).toBe(150);
      expect(usage[1].year).toBe(2023);
      expect(usage[1].month).toBe(2);
      expect(usage[1].totalTokens).toBe(300);
    });

    it("should return empty array when no usage records exist", async () => {
      const userId = "tester";
      const usage = await tokenUsageService.getUserAllMonthlyUsage(userId);
      expect(usage).toHaveLength(0);
    });
  });

  describe("get user model usage", () => {
    it("should get total usage statistics for specific model", async () => {
      const userId = "tester";
      const model = "gpt-3.5-turbo";
      
      // 创建测试数据
      const db = getDB();
      await db.insert(schemas.tokenUsage).values([
        {
          userId,
          model,
          promptTokens: 100,
          completionTokens: 50,
          totalTokens: 150,
          year: 2023,
          month: 1,
        },
        {
          userId,
          model,
          promptTokens: 200,
          completionTokens: 100,
          totalTokens: 300,
          year: 2023,
          month: 2,
        },
      ]);

      const usage = await tokenUsageService.getUserModelUsage(userId, model);

      expect(usage.totalPromptTokens).toBe(300); // 100 + 200
      expect(usage.totalCompletionTokens).toBe(150); // 50 + 100
      expect(usage.totalTokens).toBe(450); // 150 + 300
    });

    it("should return zero values when no usage records exist for the model", async () => {
      const userId = "tester";
      const model = "gpt-4";

      const usage = await tokenUsageService.getUserModelUsage(userId, model);

      expect(usage.totalPromptTokens).toBe(0);
      expect(usage.totalCompletionTokens).toBe(0);
      expect(usage.totalTokens).toBe(0);
    });
  });
});
