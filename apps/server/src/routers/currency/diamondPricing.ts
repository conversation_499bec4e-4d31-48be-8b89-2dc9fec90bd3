// 任务消耗计算
import { countWords, isSingleWord } from '@/utils/word'

// 一个阶梯是 10 个钻石的定价
export const SPLIT_SENTENCE_DIAMOND_PRICE = 10
// 单个单词的钻石消耗
export const SINGLE_WORD_DIAMOND_PRICE = 1
// 单词阶梯大小
export const WORD_STEP_SIZE = 10
// 加工句子时每个单词的钻石消耗
export const PROCESS_WORD_DIAMOND_PRICE = 0.1
// 格式化用户输入时每700个字符消耗1个钻石
export const FORMAT_CONTENT_CHAR_STEP_SIZE = 700

/**
 * 计算句子拆分任务的钻石消耗
 * 如果句子只有一个单词，直接扣除1个钻石
 * 否则按照10个单词为一个阶梯计算，价格为10
 * @param sentence 要拆分的句子
 * @returns 钻石消耗数量
 */
export function calculateSplitSentenceDiamondConsumption(
  sentence: string,
): number {
  // 检查是否为单个单词
  if (isSingleWord(sentence)) {
    return SINGLE_WORD_DIAMOND_PRICE
  }

  // 计算单词数量
  const words = countWords(sentence)
  // 按照阶梯计算钻石消耗
  return Math.ceil(words / WORD_STEP_SIZE) * SPLIT_SENTENCE_DIAMOND_PRICE
}

/**
 * 计算句子加工任务的钻石消耗
 * 每个单词消耗 PROCESS_WORD_DIAMOND_PRICE 个钻石
 * @param sentence 要加工的句子
 * @returns 钻石消耗数量
 */
export function calculateProcessSentenceDiamondConsumption(
  sentence: string,
): number {
  // 计算单词数量
  const words = countWords(sentence)
  // 每个单词消耗 PROCESS_WORD_DIAMOND_PRICE 个钻石
  // 这里最少也是消耗 1 个钻石
  return Math.ceil(words * PROCESS_WORD_DIAMOND_PRICE)
}

/**
 * 计算格式化用户输入任务的钻石消耗
 * 每700个字符消耗1个钻石，向上取整
 * @param content 要格式化的内容
 * @returns 钻石消耗数量
 */
export function calculateFormatContentDiamondConsumption(
  content: string,
): number {
  // 计算字符数量
  const charCount = content.length
  // 按照每700个字符为一个阶梯计算，向上取整
  return Math.ceil(charCount / FORMAT_CONTENT_CHAR_STEP_SIZE)
}

/**
 * 计算句子学习内容生成任务的钻石消耗
 * 固定 一个句子是 50 钻石
 * @param sentence 要加工的句子
 * @returns 钻石消耗数量
 */
const GENERATE_LEARNING_CONTENT_DIAMOND_PRICE = 50
export function calculateGenerateLearningContentDiamondConsumption(
  _sentence: string,
): number {
  return GENERATE_LEARNING_CONTENT_DIAMOND_PRICE
}

/**
 * 计算单词故事生成任务的钻石消耗
 * 固定 一个单词故事是 5 钻石
 * @param sentence 要加工的句子
 * @returns 钻石消耗数量
 */
const WORD_STORY_DIAMOND_PRICE = 5
export function calculateWordStoryDiamondConsumption(
  _sentence: string,
): number {
  return WORD_STORY_DIAMOND_PRICE
}
