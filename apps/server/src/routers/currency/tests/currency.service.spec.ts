import {
  it,
  expect,
  describe,
  vi,
  beforeEach,
  afterEach,
  afterAll,
} from "vitest";
import { currencyService } from "../currency.service";
import { getDB } from "@/db";
import { userWallet, currencyTransaction, TRANSACTION_TYPE } from "@/db/schema";
import { DIAMOND_CURRENCY } from "../constants";
import * as memberships from "@/services/game/api/memberships";
import { TRPCError } from "@trpc/server";

// 创建一个清理数据库的帮助函数
async function cleanCurrencyDB() {
  const db = getDB();
  await db.delete(currencyTransaction);
  await db.delete(userWallet);
}

describe("currency service", () => {
  beforeEach(async () => {
    // 清空数据库
    await cleanCurrencyDB();
    // 清除所有mock
    vi.clearAllMocks();
    // 复原所有mock
    vi.restoreAllMocks();
  });

  afterAll(async () => {
    await cleanCurrencyDB();
  });

  describe("resetDiamonds", () => {
    // 所有测试共享的常量
    const INITIAL_DIAMOND_BALANCE = 10000;
    const TEST_DATE = "2024-03-15";

    beforeEach(() => {
      // 设置固定的测试日期为 2024-03-15
      const testDate = new Date(TEST_DATE);
      vi.useFakeTimers();
      vi.setSystemTime(testDate);
    });

    afterEach(() => {
      // 恢复真实时间
      vi.useRealTimers();
    });

    it("应成功重置达到重置日的会员用户的钻石余额", async () => {
      // 1. 准备测试数据
      const db = getDB();
      const userId = "resetUser";
      const initialBalance = 500;

      // 创建测试钱包
      await db.insert(userWallet).values({
        userId,
        currencyType: DIAMOND_CURRENCY,
        balance: initialBalance,
      });

      // 2. Mock会员信息，确保是重置日
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        return {
          isActive: true,
          // 2024-01-15开始，3月15日是其重置日
          startDate: "2024-01-15",
          endDate: "2025-01-15",
          type: "regular",
        } as any;
      });

      // 3. 执行测试
      await currencyService.resetDiamonds();

      // 4. 验证结果
      // 验证钱包已重置
      const wallet = await db.query.userWallet.findFirst({
        where: (wallet, { eq, and }) =>
          and(
            eq(wallet.userId, userId),
            eq(wallet.currencyType, DIAMOND_CURRENCY)
          ),
      });
      expect(wallet?.balance).toBe(INITIAL_DIAMOND_BALANCE);

      // 验证交易记录
      const transactions = await db.query.currencyTransaction.findMany({
        where: (tx, { eq }) => eq(tx.transactionType, TRANSACTION_TYPE.SYSTEM),
      });
      expect(transactions.length).toBe(1);
      expect(transactions[0].userId).toBe(userId);
      expect(transactions[0].amount).toBe(
        INITIAL_DIAMOND_BALANCE - initialBalance
      );
      expect(transactions[0].description).toContain("月度会员钻石重置");
    });

    it("不应重置非会员用户的钻石余额", async () => {
      // 1. 准备测试数据
      const db = getDB();
      const userId = "nonMemberUser";
      const initialBalance = 5000;

      // 创建测试钱包
      await db.insert(userWallet).values({
        userId,
        currencyType: DIAMOND_CURRENCY,
        balance: initialBalance,
      });

      // 2. Mock会员信息，设为非会员
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        return {
          status: 200,
          data: {
            isActive: false,
            startDate: "2024-01-15",
            endDate: "2025-01-15",
            type: "regular",
          },
        } as any;
      });

      // 3. 执行测试
      await currencyService.resetDiamonds();

      // 4. 验证结果
      // 验证钱包未变化
      const wallet = await db.query.userWallet.findFirst({
        where: (wallet, { eq, and }) =>
          and(
            eq(wallet.userId, userId),
            eq(wallet.currencyType, DIAMOND_CURRENCY)
          ),
      });
      expect(wallet?.balance).toBe(initialBalance);

      // 验证没有交易记录
      const transactions = await db.query.currencyTransaction.findMany();
      expect(transactions.length).toBe(0);
    });

    it("不应重置非重置日的会员用户的钻石余额", async () => {
      // 1. 准备测试数据
      const db = getDB();
      const userId = "nonResetDayUser";
      const initialBalance = 8000;

      // 创建测试钱包
      await db.insert(userWallet).values({
        userId,
        currencyType: DIAMOND_CURRENCY,
        balance: initialBalance,
      });

      // 2. Mock会员信息，确保今天不是重置日
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        return {
          status: 200,
          data: {
            isActive: true,
            // 2024-03-10开始，3月15日不是其重置日
            startDate: "2024-03-10",
            endDate: "2025-03-10",
            type: "regular",
          },
        } as any;
      });

      // 3. 执行测试
      await currencyService.resetDiamonds();

      // 4. 验证结果
      // 验证钱包未变化
      const wallet = await db.query.userWallet.findFirst({
        where: (wallet, { eq, and }) =>
          and(
            eq(wallet.userId, userId),
            eq(wallet.currencyType, DIAMOND_CURRENCY)
          ),
      });
      expect(wallet?.balance).toBe(initialBalance);

      // 验证没有交易记录
      const transactions = await db.query.currencyTransaction.findMany();
      expect(transactions.length).toBe(0);
    });

    it("应处理会员信息获取错误的情况", async () => {
      // 1. 准备测试数据
      const db = getDB();
      await db.insert(userWallet).values({
        userId: "errorUser",
        currencyType: DIAMOND_CURRENCY,
        balance: 500,
      });

      // 2. Mock getMembershipDetailsFromGameApi 方法抛出错误
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        throw new Error("获取会员信息失败");
      });

      // 3. 执行测试
      await currencyService.resetDiamonds();

      // 4. 验证钱包状态未变
      const wallet = await db.query.userWallet.findFirst({
        where: (wallet, { eq, and }) =>
          and(
            eq(wallet.userId, "errorUser"),
            eq(wallet.currencyType, DIAMOND_CURRENCY)
          ),
      });
      expect(wallet?.balance).toBe(500);

      // 5. 确认没有交易记录
      const transactions = await db.query.currencyTransaction.findMany();
      expect(transactions.length).toBe(0);
    });

    it("应处理没有符合条件用户的情况", async () => {
      // 1. 准备测试数据
      const db = getDB();
      await db.insert(userWallet).values({
        userId: "nonResetUser",
        currencyType: DIAMOND_CURRENCY,
        balance: 500,
      });

      // 2. Mock会员信息，确保今天不是重置日
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        return {
          status: 200,
          data: {
            isActive: true,
            startDate: "2024-03-10", // 3月10日注册，3月15日不是重置日
            endDate: "2025-03-10",
            type: "premium",
          },
        } as any;
      });

      // 3. 执行测试
      const result = await currencyService.resetDiamonds();

      // 4. 验证钱包状态
      const wallet = await db.query.userWallet.findFirst({
        where: (wallet, { eq, and }) =>
          and(
            eq(wallet.userId, "nonResetUser"),
            eq(wallet.currencyType, DIAMOND_CURRENCY)
          ),
      });
      expect(wallet?.balance).toBe(500);

      // 5. 确认没有交易记录
      const transactions = await db.query.currencyTransaction.findMany();
      expect(transactions.length).toBe(0);
    });

    it("应处理钱包更新失败的情况", async () => {
      // 1. 准备测试数据
      const db = getDB();
      await db.insert(userWallet).values({
        userId: "updateErrorUser",
        currencyType: DIAMOND_CURRENCY,
        balance: 500,
      });

      // 2. Mock会员信息，确保是重置日
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        return {
          status: 200,
          data: {
            isActive: true,
            startDate: "2024-02-15", // 2月15日注册，3月15日是重置日
            endDate: "2025-02-15",
            type: "premium",
          },
        } as any;
      });

      // 3. Mock数据库更新失败
      vi.spyOn(db, "update").mockImplementation(() => {
        throw new Error("数据库更新失败");
      });

      // 4. 执行测试
      await currencyService.resetDiamonds();

      // 5. 验证钱包状态未变
      const wallet = await db.query.userWallet.findFirst({
        where: (wallet, { eq, and }) =>
          and(
            eq(wallet.userId, "updateErrorUser"),
            eq(wallet.currencyType, DIAMOND_CURRENCY)
          ),
      });
      expect(wallet?.balance).toBe(500);

      // 6. 确认没有交易记录
      const transactions = await db.query.currencyTransaction.findMany();
      expect(transactions.length).toBe(0);

      // 恢复原始函数
      vi.mocked(db.update).mockRestore();
    });

    // 月末特殊情况测试
    it("应正确处理月末的重置日逻辑", async () => {
      // 1. 准备测试数据
      const db = getDB();

      // 设置测试执行日期为2024-02-29（闰年2月最后一天）
      vi.useRealTimers(); // 先恢复真实时间
      const monthEndDate = new Date("2024-02-29");
      vi.useFakeTimers();
      vi.setSystemTime(monthEndDate);

      // 创建一个用户，注册日期是1月31日（对应的2月重置日应该是2月29日）
      await db.insert(userWallet).values({
        userId: "monthEndUser",
        currencyType: DIAMOND_CURRENCY,
        balance: 2000,
      });

      // 2. Mock会员信息
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        return {
          isActive: true,
          startDate: "2024-01-31", // 1月31日注册，2月29日应为重置日
          endDate: "2025-01-31",
          type: "regular",
        };
      });

      // 3. 执行测试
      await currencyService.resetDiamonds();

      // 4. 验证结果
      // 验证钱包已重置
      const wallet = await db.query.userWallet.findFirst({
        where: (wallet, { eq, and }) =>
          and(
            eq(wallet.userId, "monthEndUser"),
            eq(wallet.currencyType, DIAMOND_CURRENCY)
          ),
      });
      expect(wallet?.balance).toBe(INITIAL_DIAMOND_BALANCE);

      // 验证交易记录
      const transactions = await db.query.currencyTransaction.findMany({
        where: (tx, { eq }) => eq(tx.transactionType, TRANSACTION_TYPE.SYSTEM),
      });
      expect(transactions.length).toBe(1);
      expect(transactions[0].amount).toBe(INITIAL_DIAMOND_BALANCE - 2000);
      expect(transactions[0].description).toContain("月度会员钻石重置");
    });

    it("当用户钻石余额高于初始值时，应将其重置回初始值", async () => {
      // 1. 准备测试数据
      const db = getDB();
      const userId = "highBalanceUser";
      const initialBalance = 15000; // 高于初始值的余额

      // 创建测试钱包
      await db.insert(userWallet).values({
        userId,
        currencyType: DIAMOND_CURRENCY,
        balance: initialBalance,
      });

      // 2. Mock会员信息，确保是重置日
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        return {
          isActive: true,
          // 2024-01-15开始，3月15日是其重置日
          startDate: "2024-01-15",
          endDate: "2025-01-15",
          type: "regular",
        } as any;
      });

      // 3. 执行测试
      await currencyService.resetDiamonds();

      // 4. 验证结果
      // 验证钱包已重置为初始值（即使原来余额更高）
      const wallet = await db.query.userWallet.findFirst({
        where: (wallet, { eq, and }) =>
          and(
            eq(wallet.userId, userId),
            eq(wallet.currencyType, DIAMOND_CURRENCY)
          ),
      });
      expect(wallet?.balance).toBe(INITIAL_DIAMOND_BALANCE); // 应该被重置为10000

      // 验证交易记录
      const transactions = await db.query.currencyTransaction.findMany({
        where: (tx, { eq }) => eq(tx.userId, userId),
      });
      expect(transactions.length).toBe(1);
      // 对于高于初始值的情况，应该使用SYSTEM类型
      expect(transactions[0].amount).toBe(initialBalance - INITIAL_DIAMOND_BALANCE); // 应该是5000（减少了5000个钻石）
      expect(transactions[0].transactionType).toBe(TRANSACTION_TYPE.SYSTEM); // 使用SYSTEM类型，对用户不可见
      expect(transactions[0].description).toContain("月度会员钻石重置");
    });
  });

  describe("getUserCurrentCycleTransactions", () => {
    // 在每个测试前都恢复真实时间
    beforeEach(() => {
      vi.useRealTimers();
      // 设置固定的测试日期为 2024-06-20
      vi.useFakeTimers();
      vi.setSystemTime(new Date("2024-06-20"));
    });

    afterEach(() => {
      // 恢复真实时间
      vi.useRealTimers();
    });

    it("应返回活跃会员当前周期的交易记录和周期信息", async () => {
      // 1. 准备测试数据
      const db = getDB();
      const userId = "testUser";

      // 创建测试钱包
      await db.insert(userWallet).values({
        userId,
        currencyType: DIAMOND_CURRENCY,
        balance: 5000,
      });

      // 当前测试日期设置为2024-06-20
      // 对于5月15日注册的会员，当前周期应为6月15日-7月15日

      // 此测试只关注周期信息的计算，不关心交易记录的筛选
      // 所以不创建在当前周期内的交易记录，只验证周期日期计算是否准确

      // 2. Mock会员信息
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        return {
          isActive: true,
          startDate: "2024-05-15", // 会员开始日期
          endDate: "2025-05-15", // 会员结束日期
          type: "regular",
        };
      });

      // 3. 执行测试
      const result =
        await currencyService.getUserCurrentCycleTransactions(userId);

      // 4. 验证结果
      // 当前日期是2024-06-20，在5月15日注册的会员的第二个周期内（6月15日-7月15日）
      expect(result.cycleStartDate).not.toBeNull();
      expect(result.cycleEndDate).not.toBeNull();

      // 验证周期日期
      expect(result.cycleStartDate).toBe("2024-06-15");
      expect(result.cycleEndDate).toBe("2024-07-15");

      // 确认没有交易记录（因为没有创建过）
      expect(result.transactions.length).toBe(0);
    });

    it("应正确获取当前周期内的交易记录", async () => {
      // 1. 准备测试数据
      const db = getDB();
      const userId = "currentCycleUser";

      // 创建测试钱包
      await db.insert(userWallet).values({
        userId,
        currencyType: DIAMOND_CURRENCY,
        balance: 5000,
      });

      // 当前测试日期为 2024-06-20
      // 对于5月15日注册的会员，当前周期为 6月15日-7月15日

      // 创建交易记录 - 在当前周期内
      await db.insert(currencyTransaction).values({
        userId,
        currencyType: DIAMOND_CURRENCY,
        amount: -1000,
        transactionType: TRANSACTION_TYPE.CONSUME,
        description: "当前周期内的交易",
        createdAt: "2024-06-16", // 6月16日，在当前周期（6月15日-7月15日）内
      });

      // 创建交易记录 - 在上个周期内
      await db.insert(currencyTransaction).values({
        userId,
        currencyType: DIAMOND_CURRENCY,
        amount: -500,
        transactionType: TRANSACTION_TYPE.CONSUME,
        description: "上个周期的交易",
        createdAt: "2024-06-14", // 6月14日，在上个周期（5月15日-6月15日）内
      });

      // 创建系统交易记录 - 应该被排除（默认不显示给用户）
      await db.insert(currencyTransaction).values({
        userId,
        currencyType: DIAMOND_CURRENCY,
        amount: 5000,
        transactionType: TRANSACTION_TYPE.SYSTEM,
        description: "系统操作记录",
        createdAt: "2024-06-17", // 在当前周期内，但是系统类型
      });

      // 2. Mock会员信息
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        return {
          isActive: true,
          startDate: "2024-05-15", // 会员开始日期
          endDate: "2025-05-15", // 会员结束日期
          type: "regular",
        };
      });

      // 3. 执行测试 - 默认会排除系统交易
      const result =
        await currencyService.getUserCurrentCycleTransactions(userId);

      // 4. 验证结果
      expect(result.cycleStartDate).not.toBeNull();
      expect(result.cycleEndDate).not.toBeNull();

      // 验证周期日期
      expect(result.cycleStartDate).toBe("2024-06-15");
      expect(result.cycleEndDate).toBe("2024-07-15");

      // 确认只返回了当前周期的非系统交易记录（应该是1条）
      expect(result.transactions.length).toBe(1);
      expect(result.transactions[0].description).toBe("当前周期内的交易");
      expect(result.transactions[0].amount).toBe(-1000);
      
      // 3.1 额外测试 - 直接调用getUserTransactionsByDateRange并包含系统交易
      const resultWithSystem = await currencyService.getUserTransactionsByDateRange(
        userId,
        "2024-06-15",
        "2024-07-15",
        false // 不排除系统交易
      );
      
      // 验证结果 - 应该包含系统交易
      expect(resultWithSystem.transactions.length).toBe(2); // 1条普通交易 + 1条系统交易
      const systemTransaction = resultWithSystem.transactions.find(
        tx => tx.transactionType === TRANSACTION_TYPE.SYSTEM
      );
      expect(systemTransaction).toBeDefined();
      expect(systemTransaction?.description).toBe("系统操作记录");
    });

    it("应处理用户不是活跃会员的情况", async () => {
      // 1. 准备测试数据
      const userId = "inactiveUser";

      // 2. Mock会员信息
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        return {
          isActive: false,
          startDate: "2024-01-15",
          endDate: "2024-02-15", // 已过期
          type: "regular",
        };
      });

      // 3. 执行测试并验证抛出TRPCError
      await expect(
        currencyService.getUserCurrentCycleTransactions(userId)
      ).rejects.toThrow(TRPCError);

      // 验证错误代码和消息
      await expect(
        currencyService.getUserCurrentCycleTransactions(userId)
      ).rejects.toMatchObject({
        code: "BAD_REQUEST",
        message: expect.stringContaining("用户不是活跃会员"),
      });
    });

    it("应处理会员信息不完整的情况", async () => {
      // 1. 准备测试数据
      const userId = "incompleteUser";

      // 2. Mock会员信息（缺少结束日期）
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        return {
          isActive: true,
          startDate: "2024-05-15",
          endDate: "", // 空字符串而不是null
          type: "regular",
        };
      });

      // 3. 执行测试并验证抛出TRPCError
      await expect(
        currencyService.getUserCurrentCycleTransactions(userId)
      ).rejects.toThrow(TRPCError);

      // 验证错误代码和消息
      await expect(
        currencyService.getUserCurrentCycleTransactions(userId)
      ).rejects.toMatchObject({
        code: "BAD_REQUEST",
        message: expect.stringContaining("会员信息不完整"),
      });
    });

    it("应处理当前日期不在有效计费周期内的情况", async () => {
      // 1. 准备测试数据
      const userId = "outOfCycleUser";

      // 重置并设置当前日期为会员期结束后
      // 不需要再调用vi.useFakeTimers()，因为beforeEach已经设置了
      vi.setSystemTime(new Date("2025-06-20")); // 会员结束日期后

      // 2. Mock会员信息
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        return {
          isActive: true,
          startDate: "2024-05-15",
          endDate: "2025-05-15", // 已过期
          type: "regular",
        };
      });

      // 3. 执行测试并验证抛出TRPCError
      await expect(
        currencyService.getUserCurrentCycleTransactions(userId)
      ).rejects.toThrow(TRPCError);

      // 验证错误代码和消息
      await expect(
        currencyService.getUserCurrentCycleTransactions(userId)
      ).rejects.toMatchObject({
        code: "BAD_REQUEST",
        message: expect.stringContaining("不在有效的计费周期内"),
      });
    });
  });

  describe("deleteInactiveMembersWallets", () => {
    // 每个测试前设置时间
    beforeEach(() => {
      // 设置固定的测试日期为 2024-03-15
      const testDate = new Date("2024-03-15");
      vi.useFakeTimers();
      vi.setSystemTime(testDate);
    });

    afterEach(() => {
      // 恢复真实时间
      vi.useRealTimers();
    });

    it("应成功删除非活跃会员的钱包并创建交易记录", async () => {
      // 1. 准备测试数据
      const db = getDB();
      const activeUserId = "activeUser";
      const inactiveUserId = "inactiveUser";

      // 创建两个测试钱包：一个活跃会员，一个非活跃会员
      await db.insert(userWallet).values([
        {
          userId: activeUserId,
          currencyType: DIAMOND_CURRENCY,
          balance: 5000,
        },
        {
          userId: inactiveUserId,
          currencyType: DIAMOND_CURRENCY,
          balance: 3000,
        },
      ]);

      // 2. Mock会员信息
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async (userId) => {
        if (userId === activeUserId) {
          return {
            isActive: true,
            startDate: "2024-01-15",
            endDate: "2025-01-15",
            type: "regular",
          };
        } else {
          return {
            isActive: false,
            startDate: "2024-01-15",
            endDate: "2024-02-15", // 已过期
            type: "regular",
          };
        }
      });

      // 3. 执行测试
      const result = await currencyService.deleteInactiveMembersWallets();

      // 4. 验证结果
      // 验证返回结果
      expect(result.success).toBe(true);
      expect(result.processed).toBe(2);
      expect(result.deleted).toBe(1);

      // 验证活跃会员钱包未删除
      const activeWallet = await db.query.userWallet.findFirst({
        where: (wallet, { eq, and }) =>
          and(
            eq(wallet.userId, activeUserId),
            eq(wallet.currencyType, DIAMOND_CURRENCY)
          ),
      });
      expect(activeWallet).not.toBeNull();
      expect(activeWallet?.balance).toBe(5000);

      // 验证非活跃会员钱包已删除
      const inactiveWallet = await db.query.userWallet.findFirst({
        where: (wallet, { eq, and }) =>
          and(
            eq(wallet.userId, inactiveUserId),
            eq(wallet.currencyType, DIAMOND_CURRENCY)
          ),
      });
      expect(inactiveWallet).toBeFalsy();

      // 验证交易记录
      const transactions = await db.query.currencyTransaction.findMany({
        where: (tx, { eq }) => eq(tx.userId, inactiveUserId),
      });
      expect(transactions.length).toBe(1);
      expect(transactions[0].amount).toBe(-3000);
      expect(transactions[0].transactionType).toBe(TRANSACTION_TYPE.SYSTEM); // 验证使用SYSTEM类型
      expect(transactions[0].description).toContain("会员已过期");
    });

    it("当没有非活跃会员时不应删除任何钱包", async () => {
      // 1. 准备测试数据
      const db = getDB();
      const activeUserId1 = "activeUser1";
      const activeUserId2 = "activeUser2";

      // 创建两个活跃会员的钱包
      await db.insert(userWallet).values([
        {
          userId: activeUserId1,
          currencyType: DIAMOND_CURRENCY,
          balance: 6000,
        },
        {
          userId: activeUserId2,
          currencyType: DIAMOND_CURRENCY,
          balance: 7000,
        },
      ]);

      // 2. Mock会员信息
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        return {
          isActive: true,
          startDate: "2024-01-15",
          endDate: "2025-01-15",
          type: "regular",
        };
      });

      // 3. 执行测试
      const result = await currencyService.deleteInactiveMembersWallets();

      // 4. 验证结果
      // 验证返回结果
      expect(result.success).toBe(true);
      expect(result.processed).toBe(2);
      expect(result.deleted).toBe(0);

      // 验证两个钱包都未删除
      const wallets = await db.query.userWallet.findMany();
      expect(wallets.length).toBe(2);

      // 验证没有创建交易记录
      const transactions = await db.query.currencyTransaction.findMany();
      expect(transactions.length).toBe(0);
    });

    it("应处理会员信息获取失败的情况", async () => {
      // 1. 准备测试数据
      const db = getDB();
      const userId = "errorUser";

      // 创建测试钱包
      await db.insert(userWallet).values({
        userId,
        currencyType: DIAMOND_CURRENCY,
        balance: 4000,
      });

      // 2. Mock会员信息获取失败
      vi.spyOn(
        memberships,
        "getMembershipDetailsFromGameApi"
      ).mockImplementation(async () => {
        throw new Error("获取会员信息失败");
      });

      // 3. 执行测试
      const result = await currencyService.deleteInactiveMembersWallets();

      // 4. 验证结果
      // 验证返回结果表明处理过程成功，但有异常处理
      expect(result.success).toBe(true);
      expect(result.processed).toBe(1);
      expect(result.deleted).toBe(0);

      // 验证钱包未被删除
      const wallet = await db.query.userWallet.findFirst({
        where: (wallet, { eq, and }) =>
          and(
            eq(wallet.userId, userId),
            eq(wallet.currencyType, DIAMOND_CURRENCY)
          ),
      });
      expect(wallet).not.toBeNull();
      expect(wallet?.balance).toBe(4000);

      // 验证没有创建交易记录
      const transactions = await db.query.currencyTransaction.findMany();
      expect(transactions.length).toBe(0);
    });

  });
});
