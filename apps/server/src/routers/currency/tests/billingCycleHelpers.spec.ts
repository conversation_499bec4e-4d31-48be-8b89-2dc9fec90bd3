import { it, expect, describe } from "vitest";
import { isResetDay, findCurrentBillingCycle } from "../billingCycleHelpers";

describe("isResetDay", () => {
  describe("基本重置日场景", () => {
    const startDate = "2024-01-15";
    const endDate = "2025-01-15";

    it("应返回 true，当检查日期是订阅期间的月度重置日", () => {
      expect(isResetDay(startDate, endDate, "2024-03-15")).toBe(true);
      expect(isResetDay(startDate, endDate, "2024-02-15")).toBe(true);
      expect(isResetDay(startDate, endDate, "2024-12-15")).toBe(true);
    });

    it("应返回 false，当检查日期不是重置日", () => {
      expect(isResetDay(startDate, endDate, "2024-03-10")).toBe(false);
    });
  });

  describe("边缘情况", () => {
    const startDate = "2024-01-15";
    const endDate = "2025-01-15";

    it("应返回 false，当检查日期是开始日期当天", () => {
      expect(isResetDay(startDate, endDate, "2024-01-15")).toBe(false);
    });

    it("应返回 false，当检查日期是结束日期当天", () => {
      expect(isResetDay(startDate, endDate, "2025-01-15")).toBe(false);
    });

    it("应返回 false，当检查日期在结束日期之后", () => {
      expect(isResetDay(startDate, endDate, "2025-02-15")).toBe(false);
    });

    it("应返回 false，当开始日期不早于结束日期", () => {
      expect(isResetDay("2024-01-01", "2023-01-01", "2024-02-01")).toBe(false);
    });
  });

  describe("月末特殊情况", () => {
    describe("从31号开始的订阅", () => {
      const startDate = "2024-01-31";
      const endDate = "2024-06-30";

      it("应正确处理月末日期，返回 true 当日期是月末", () => {
        expect(isResetDay(startDate, endDate, "2024-02-29")).toBe(true); // 闰年2月最后一天
        expect(isResetDay(startDate, endDate, "2024-03-31")).toBe(true);
        expect(isResetDay(startDate, endDate, "2024-04-30")).toBe(true);
        expect(isResetDay(startDate, endDate, "2024-05-31")).toBe(true);
      });

      it("应返回 false，当日期不是月末但在订阅期内", () => {
        expect(isResetDay(startDate, endDate, "2024-03-30")).toBe(false);
      });

      it("应返回 false，当检查日期是结束日期当天", () => {
        expect(isResetDay(startDate, endDate, "2024-06-30")).toBe(false);
      });
    });

    describe("从30号开始的订阅", () => {
      const startDate = "2024-03-30";
      const endDate = "2024-07-30";

      it("应正确处理30号日期的重置日", () => {
        expect(isResetDay(startDate, endDate, "2024-04-30")).toBe(true);
        expect(isResetDay(startDate, endDate, "2024-05-30")).toBe(true);
        expect(isResetDay(startDate, endDate, "2024-06-30")).toBe(true);
      });
    });
  });

  describe("错误处理", () => {
    it("应处理无效的日期格式", () => {
      expect(() =>
        isResetDay("invalid-date", "2024-02-01", "2024-01-15")
      ).toThrow();
      expect(() =>
        isResetDay("2024-01-01", "invalid-date", "2024-01-15")
      ).toThrow();
      expect(() =>
        isResetDay("2024-01-01", "2024-02-01", "invalid-date")
      ).toThrow();
    });
  });
});

describe("findCurrentBillingCycle", () => {
  // 帮助函数用于调试
  const formatCycle = (
    cycle: { cycleStart: string; cycleEnd: string } | null
  ) => {
    if (!cycle) return "无有效周期";
    return {
      cycleStart: cycle.cycleStart,
      cycleEnd: cycle.cycleEnd,
    };
  };

  // 创建北京时区的日期（上午8点对应UTC的0点）
  function createBeijingDate(dateStr: string): Date {
    const date = new Date(dateStr);
    date.setHours(0, 0, 0, 0);
    return date;
  }

  it("在第一个计费周期内应返回正确的周期范围", () => {
    // 会员开始日期为2024-05-15
    const startDate = createBeijingDate("2024-05-15");
    // 会员结束日期为2025-05-15
    const endDate = createBeijingDate("2025-05-15");
    // 当前日期为2024-06-10（在第一个重置日之前）
    const currentDate = createBeijingDate("2024-06-10");

    const cycle = findCurrentBillingCycle(startDate, endDate, currentDate);

    expect(cycle).not.toBeNull();
    expect(cycle!.cycleStart).toBe("2024-05-15");
    expect(cycle!.cycleEnd).toBe("2024-06-15");
  });

  it("在第一个重置日应返回新的周期范围", () => {
    const startDate = createBeijingDate("2024-05-15");
    const endDate = createBeijingDate("2025-05-15");
    // 当前日期为第一个重置日（2024-06-15）
    const currentDate = createBeijingDate("2024-06-15");

    const cycle = findCurrentBillingCycle(startDate, endDate, currentDate);

    expect(cycle).not.toBeNull();
    // 在重置日当天应该算入新周期
    expect(cycle!.cycleStart).toBe("2024-06-15");
    expect(cycle!.cycleEnd).toBe("2024-07-15");
  });

  it("在后续周期里应返回正确的周期范围", () => {
    const startDate = createBeijingDate("2024-05-15");
    const endDate = createBeijingDate("2025-05-15");
    // 当前日期为2024-08-20（在第三个完整月之内）
    const currentDate = createBeijingDate("2024-08-20");

    const cycle = findCurrentBillingCycle(startDate, endDate, currentDate);

    expect(cycle).not.toBeNull();
    expect(cycle!.cycleStart).toBe("2024-08-15");
    expect(cycle!.cycleEnd).toBe("2024-09-15");
  });

  it("在最后一个周期应返回正确的周期范围，结束日期应为会员结束日期", () => {
    const startDate = createBeijingDate("2024-05-15");
    const endDate = createBeijingDate("2025-05-10"); // 注意：结束日期提前5天
    // 当前日期为2025-04-20（在最后一个完整月之内）
    const currentDate = createBeijingDate("2025-04-20");

    const cycle = findCurrentBillingCycle(startDate, endDate, currentDate);

    expect(cycle).not.toBeNull();
    expect(cycle!.cycleStart).toBe("2025-04-15");
    // 周期结束日期应该是会员结束日期而非下一个重置日
    expect(cycle!.cycleEnd).toBe("2025-05-10");
  });

  it("当前日期在会员期之前应返回null", () => {
    const startDate = createBeijingDate("2024-05-15");
    const endDate = createBeijingDate("2025-05-15");
    // 当前日期在会员开始日期之前
    const currentDate = createBeijingDate("2024-05-14");

    const cycle = findCurrentBillingCycle(startDate, endDate, currentDate);

    expect(cycle).toBeNull();
  });

  it("当前日期在会员期结束或之后应返回null", () => {
    const startDate = createBeijingDate("2024-05-15");
    const endDate = createBeijingDate("2025-05-15");
    // 当前日期为会员结束日期当天
    const currentDate = createBeijingDate("2025-05-15");

    const cycle = findCurrentBillingCycle(startDate, endDate, currentDate);

    expect(cycle).toBeNull();
  });

  it("处理月末特殊情况 - 31日注册在2月的表现", () => {
    // 会员开始日期为2024-01-31（注意是1月31日）
    const startDate = createBeijingDate("2024-01-31");
    const endDate = createBeijingDate("2025-01-31");
    // 2024是闰年，2月有29天
    const currentDate = createBeijingDate("2024-02-15");

    const cycle = findCurrentBillingCycle(startDate, endDate, currentDate);

    expect(cycle).not.toBeNull();
    expect(cycle!.cycleStart).toBe("2024-01-31");
    expect(cycle!.cycleEnd).toBe("2024-02-29");
  });

  it("处理月末特殊情况 - 在闰年2月29日注册的情况", () => {
    // 会员开始日期为闰年2024-02-29
    const startDate = createBeijingDate("2024-02-29");
    const endDate = createBeijingDate("2025-02-28");
    // 当前日期为2024-03-15
    const currentDate = createBeijingDate("2024-03-15");

    const cycle = findCurrentBillingCycle(startDate, endDate, currentDate);

    expect(cycle).not.toBeNull();
    expect(cycle!.cycleStart).toBe("2024-02-29");
    // 3月没有29日，所以应该是3月最后一天
    expect(cycle!.cycleEnd).toBe("2024-03-29");
  });

  it("应接受ISO 8601格式的日期字符串", () => {
    const startDate = "2024-05-15T00:00:00.000Z";
    const endDate = "2025-05-15T00:00:00.000Z";
    const currentDate = "2024-06-10T12:30:45.000Z";

    const cycle = findCurrentBillingCycle(startDate, endDate, currentDate);

    expect(cycle).not.toBeNull();
    expect(cycle!.cycleStart).toBe("2024-05-15");
    expect(cycle!.cycleEnd).toBe("2024-06-15");
  });

  it("在6月20日应返回6月15日至7月15日周期", () => {
    const startDate = createBeijingDate("2024-05-15");
    const endDate = createBeijingDate("2025-05-15");
    // 当前日期为2024-06-20
    const currentDate = createBeijingDate("2024-06-20");

    const cycle = findCurrentBillingCycle(startDate, endDate, currentDate);

    expect(cycle).not.toBeNull();
    expect(cycle!.cycleStart).toBe("2024-06-15");
    expect(cycle!.cycleEnd).toBe("2024-07-15");
  });

  it("应处理无效日期输入并返回null", () => {
    const startDate = "invalid-date";
    const endDate = "2025-05-15";
    const currentDate = "2024-06-10";

    const cycle = findCurrentBillingCycle(startDate, endDate, currentDate);

    expect(cycle).toBeNull();
  });
});
