import {
  addMonths,
  differenceInCalendarMonths,
  format,
  isAfter,
  isBefore,
  isEqual,
  isValid,
  min,
  parseISO,
  startOfDay, // 确保只比较日期，忽略时间
} from 'date-fns'

/**
 * 判断给定的 `checkDate` 是否是 `startDate` 和 `endDate` 定义的会员期内的一个重置日。
 * 重置日定义为：从购买日期的下一个月开始，每个月的同一天（或对应月末），
 * 且该日期必须严格在 `startDate` 之后，并严格在 `endDate` 之前。
 *
 * @param {string | Date} startDateInput - 开始日期 (会员生效日期) (ISO 8601 字符串或 Date 对象)
 * @param {string | Date} endDateInput - 结束日期 (会员过期日期，当天 00:00:00 失效) (ISO 8601 字符串或 Date 对象)
 * @param {string | Date} checkDateInput - 需要检查的日期 (ISO 8601 字符串或 Date 对象)
 * @returns {boolean} 如果 `checkDate` 是一个有效的重置日，则返回 `true`，否则返回 `false`。
 * @throws {Error} 如果输入的日期字符串格式无效。
 */
export function isResetDay(
  startDateInput: string | Date,
  endDateInput: string | Date,
  checkDateInput: string | Date,
): boolean {
  let startDate, endDate, checkDate

  // 1. 解析、验证并将所有日期标准化为当天的开始（忽略时间部分）
  try {
    startDate = startOfDay(
      startDateInput instanceof Date ? startDateInput : parseISO(startDateInput),
    )
    endDate = startOfDay(
      endDateInput instanceof Date ? endDateInput : parseISO(endDateInput),
    )
    checkDate = startOfDay(
      checkDateInput instanceof Date ? checkDateInput : parseISO(checkDateInput),
    )

    if (!isValid(startDate) || !isValid(endDate) || !isValid(checkDate)) {
      throw new Error('Invalid date format provided.')
    }
  } catch (error) {
    console.error('Error parsing dates:', error)
    throw new Error(
      'Could not parse input dates. Ensure they are valid ISO 8601 strings (e.g., "YYYY-MM-DD") or Date objects.',
    )
  }

  // 2. 边界条件检查
  // a) 会员期必须有效 (开始 < 结束)
  if (!isBefore(startDate, endDate)) {
    // console.warn("Warning: Start date is not before end date.");
    return false
  }
  // b) 检查日期必须在会员期内 (开始 < 检查 < 结束)
  //    注意：重置日不能是开始日期当天，所以用 isAfter
  if (!isAfter(checkDate, startDate) || !isBefore(checkDate, endDate)) {
    // console.log(`Debug: checkDate (${format(checkDate, 'yyyy-MM-dd')}) is not strictly between startDate (${format(startDate, 'yyyy-MM-dd')}) and endDate (${format(endDate, 'yyyy-MM-dd')})`);
    return false
  }

  // 3. 核心逻辑：检查 checkDate 是否是由 startDate 按月递增生成的
  //    计算从 startDate 到 checkDate 过去了多少个完整的日历月份
  const monthsDifference = differenceInCalendarMonths(checkDate, startDate)

  //    如果 checkDate 在 startDate 的同一个月或更早的月份， monthsDifference 会是 0 或负数
  //    但由于步骤 2b 的 isAfter(checkDate, startDate) 检查，monthsDifference 肯定 >= 1
  //    (例如 start=Jan 15, check=Feb 10 => diff=1; start=Jan 15, check=Feb 15 => diff=1)
  if (monthsDifference <= 0) {
    // 理论上由于 isAfter 检查，这里不会执行，但作为保险
    return false
  }

  //    计算从 startDate 开始，增加 monthsDifference 个月后的日期
  const potentialResetDate = addMonths(startDate, monthsDifference)

  // 4. 比较 checkDate 和计算出的潜在重置日是否完全相同
  //    isEqual 会比较年月日，由于我们用了 startOfDay，时间部分也是一致的
  //    这能正确处理月末情况（如 start=Jan 31, check=Feb 29，addMonths(Jan 31, 1) = Feb 29）
  const result = isEqual(checkDate, potentialResetDate)
  // console.log(`Debug: checkDate=${format(checkDate, 'yyyy-MM-dd')}, monthsDiff=${monthsDifference}, potentialResetDate=${format(potentialResetDate, 'yyyy-MM-dd')}, isEqual=${result}`);

  return result
}

/**
 * 查找用户当前的计费周期
 * @param startDate 会员开始日期
 * @param endDate 会员结束日期
 * @param currentDate 当前日期
 * @returns 当前计费周期的开始和结束日期，如果不在有效期则返回null
 */
export function findCurrentBillingCycle(
  startDateInput: Date | string,
  endDateInput: Date | string,
  currentDateInput: Date | string,
): { cycleStart: string, cycleEnd: string } | null {
  try {
    // 转换并标准化输入日期
    const startDate = startOfDay(
      startDateInput instanceof Date
        ? startDateInput
        : parseISO(startDateInput),
    )
    const endDate = startOfDay(
      endDateInput instanceof Date
        ? endDateInput
        : parseISO(endDateInput),
    )
    const currentDate = startOfDay(
      currentDateInput instanceof Date
        ? currentDateInput
        : parseISO(currentDateInput),
    )

    // 检查基本有效性
    if (
      !isBefore(startDate, endDate)
      || !isBefore(currentDate, endDate)
      || isBefore(currentDate, startDate)
    ) {
      // 当前日期不在有效的会员期内
      return null
    }

    // 计算第一个重置日
    const firstResetDate = addMonths(startDate, 1)

    let cycleStart: Date
    let cycleEnd: Date

    if (isBefore(currentDate, firstResetDate)) {
      // 当前日期在第一个重置日之前，属于第一个周期
      cycleStart = startDate
      cycleEnd = firstResetDate
    } else {
      // 当前日期在第一个重置日或之后
      const monthsPassed = differenceInCalendarMonths(currentDate, startDate)
      const potentialResetForCurrentMonth = addMonths(startDate, monthsPassed)

      if (isBefore(currentDate, potentialResetForCurrentMonth)) {
        // 当前日期在本月重置日之前 -> 属于上个月开始的周期
        cycleStart = addMonths(startDate, monthsPassed - 1)
        cycleEnd = potentialResetForCurrentMonth
      } else {
        // 当前日期在本月重置日当天或之后 -> 属于本月开始的周期
        cycleStart = potentialResetForCurrentMonth
        cycleEnd = addMonths(startDate, monthsPassed + 1)
      }
    }

    // 确保 cycleEnd 不超过 endDate
    cycleEnd = min([cycleEnd, endDate])

    // 再次校验 cycleStart 是否有效
    if (!isBefore(cycleStart, cycleEnd)) {
      return null
    }

    return {
      cycleStart: format(cycleStart, 'yyyy-MM-dd'),
      cycleEnd: format(cycleEnd, 'yyyy-MM-dd'),
    }
  } catch (error) {
    console.error('Error calculating billing cycle:', error)
    return null
  }
}
