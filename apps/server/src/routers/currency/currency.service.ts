import type { CurrencyTransaction } from './types'
import { TRPCError } from '@trpc/server'
import { format } from 'date-fns'
import { and, desc, eq, gte, lte, ne } from 'drizzle-orm'
import { getMembershipDetailsFromGameApi } from '@/services/game/api/memberships'
import { getDB } from '../../db'
import {
  currencyTransaction,
  TRANSACTION_TYPE,
  userWallet,
} from '../../db/schema'
import { findCurrentBillingCycle, isResetDay } from './billingCycleHelpers'
import { DIAMOND_CURRENCY } from './constants'

const INITIAL_DIAMOND_BALANCE = 10000

export const currencyService = {
  /**
   * 获取用户钻石钱包
   */
  async getUserDiamondWallet(userId: string) {
    const db = getDB()
    return db.query.userWallet.findFirst({
      where: and(
        eq(userWallet.userId, userId),
        eq(userWallet.currencyType, DIAMOND_CURRENCY),
      ),
    })
  },

  /**
   * 检查用户是否有足够的钻石
   */
  async checkDiamondBalance(
    userId: string,
    requiredAmount: number,
  ): Promise<boolean> {
    const userWallet = await this.getUserDiamondWallet(userId)

    if (!userWallet) {
      return false
    }

    return userWallet.balance >= requiredAmount
  },

  /**
   * 获取用户钻石余额
   */
  async getDiamondBalance(userId: string): Promise<number> {
    const userWallet = await this.getUserDiamondWallet(userId)

    if (!userWallet) {
      return 0
    }

    return userWallet.balance
  },

  /**
   * 消费钻石
   * @param userId 用户ID
   * @param amount 消费金额
   * @param description 描述信息
   * @param referenceId 关联ID
   */
  async consumeDiamonds(userId: string, amount: number, description?: string) {
    const db = getDB()

    return db.transaction(async (tx: any) => {
      // 查找钱包
      const wallet = await tx.query.userWallet.findFirst({
        where: and(
          eq(userWallet.userId, userId),
          eq(userWallet.currencyType, DIAMOND_CURRENCY),
        ),
      })

      if (!wallet) {
        throw new Error('用户钻石钱包未初始化')
      }

      // 检查余额是否足够
      if (wallet.balance < amount) {
        throw new Error('钻石余额不足')
      }

      // 计算新余额
      const newBalance = Math.max(0, wallet.balance - amount)

      // 更新钱包余额
      await tx
        .update(userWallet)
        .set({
          balance: newBalance,
        })
        .where(
          and(
            eq(userWallet.userId, userId),
            eq(userWallet.currencyType, DIAMOND_CURRENCY),
          ),
        )

      // 创建交易记录
      await tx.insert(currencyTransaction).values({
        userId,
        currencyType: DIAMOND_CURRENCY,
        amount: -amount,
        transactionType: TRANSACTION_TYPE.CONSUME,
        description: description || `消费${amount}钻石`,
      })

      return {
        newBalance,
        consumed: amount,
      }
    })
  },

  /**
   * 初始化用户钻石钱包
   * 如果钱包不存在则创建，只有有效会员才会创建钱包并给予初始钻石
   * @param userId 用户ID
   * @param initialBalance 初始钻石余额
   * @returns 钱包创建状态和余额信息
   */
  async initializeUserDiamondWallet(userId: string): Promise<{
    success: boolean
    wallet: { exists: boolean, balance: number } | null
    membership: { isActive: boolean, type: string }
  }> {
    // 获取会员信息
    const membershipDetails = await getMembershipDetailsFromGameApi(userId)
    const wallet = await this.getUserDiamondWallet(userId)

    // 检查会员状态
    const isActiveMember = membershipDetails.isActive
    const membershipType = membershipDetails.type

    // 如果钱包已存在，返回当前状态
    if (wallet) {
      return {
        success: true,
        wallet: {
          exists: true,
          balance: wallet.balance,
        },
        membership: {
          isActive: isActiveMember,
          type: membershipType,
        },
      }
    }

    // 只有有效会员才创建钱包并给予初始钻石
    if (isActiveMember) {
      const db = getDB()
      await db.insert(userWallet).values({
        userId,
        currencyType: DIAMOND_CURRENCY,
        balance: INITIAL_DIAMOND_BALANCE,
      })

      return {
        success: true,
        wallet: {
          exists: true,
          balance: INITIAL_DIAMOND_BALANCE,
        },
        membership: {
          isActive: true,
          type: membershipType,
        },
      }
    }

    // 非有效会员不创建钱包
    return {
      success: false,
      wallet: null,
      membership: {
        isActive: false,
        type: membershipType,
      },
    }
  },

  /**
   * 重置用户钻石
   * 检查所有用户钻石钱包，对当天需要重置的会员进行钻石重置
   */
  async resetDiamonds() {
    try {
      const db = getDB()

      // 获取当前日期
      const today = new Date()

      // 获取所有钻石钱包
      const wallets = await db.query.userWallet.findMany({
        where: eq(userWallet.currencyType, DIAMOND_CURRENCY),
      })

      // 对每个钱包进行处理
      for (const wallet of wallets) {
        try {
          // 获取用户会员信息（使用服务间通信方式）
          const membershipInfo = await getMembershipDetailsFromGameApi(
            wallet.userId,
          )

          if (!membershipInfo) {
            continue
          }

          // 如果用户不是会员，跳过
          if (
            !membershipInfo.isActive
            || !membershipInfo.startDate
            || !membershipInfo.endDate
          ) {
            continue
          }

          // 解析会员开始和结束日期
          const startDate = membershipInfo.startDate
          const endDate = membershipInfo.endDate

          // 使用 isResetDay 函数检查今天是否是会员的钻石重置日
          if (isResetDay(startDate, endDate, today)) {
            console.log(
              `用户 ${wallet.userId} 会员注册日期为 ${format(new Date(startDate), 'yyyy-MM-dd')}，符合今日重置条件`,
            )

            // 重置钻石余额为初始值
            await db
              .update(userWallet)
              .set({ balance: INITIAL_DIAMOND_BALANCE })
              .where(
                and(
                  eq(userWallet.userId, wallet.userId),
                  eq(userWallet.currencyType, DIAMOND_CURRENCY),
                ),
              )

            // 记录交易
            const amountChange = INITIAL_DIAMOND_BALANCE - wallet.balance

            await db.insert(currencyTransaction).values({
              userId: wallet.userId,
              currencyType: DIAMOND_CURRENCY,
              amount: Math.abs(amountChange), // 使用绝对值
              transactionType: TRANSACTION_TYPE.SYSTEM, // 使用系统类型，对用户不可见
              description: `月度会员钻石重置 (${format(today, 'yyyy-MM-dd')})`,
            })
          }
        } catch (err) {
          console.error(`处理用户 ${wallet.userId} 钻石重置时出错:`, err)
        }
      }

      return {
        success: true,
        message: '钻石重置任务执行完成',
      }
    } catch (error: any) {
      console.error('执行钻石重置任务时发生错误:', error)
      return {
        success: false,
        message: `执行钻石重置任务时发生错误: ${error}`,
      }
    }
  },

  /**
   * 检查并删除非有效会员的钱包信息
   * 对所有钻石钱包用户，检查其会员状态，如果不再是有效会员则删除其钱包
   */
  async deleteInactiveMembersWallets() {
    try {
      const db = getDB()

      // 获取所有钻石钱包
      const wallets = await db.query.userWallet.findMany({
        where: eq(userWallet.currencyType, DIAMOND_CURRENCY),
      })

      let deletedCount = 0
      let processedCount = 0

      // 对每个钱包进行处理
      for (const wallet of wallets) {
        try {
          processedCount++

          // 获取用户会员信息（使用服务间通信方式）
          const membershipInfo = await getMembershipDetailsFromGameApi(
            wallet.userId,
          )

          // 如果用户不再是有效会员，删除其钱包
          if (!membershipInfo.isActive) {
            console.log(`用户 ${wallet.userId} 不再是有效会员，删除其钱包信息`)

            // 删除钱包记录
            await db
              .delete(userWallet)
              .where(
                and(
                  eq(userWallet.userId, wallet.userId),
                  eq(userWallet.currencyType, DIAMOND_CURRENCY),
                ),
              )

            // 记录交易（钱包删除）
            await db.insert(currencyTransaction).values({
              userId: wallet.userId,
              currencyType: DIAMOND_CURRENCY,
              amount: -wallet.balance,
              transactionType: TRANSACTION_TYPE.SYSTEM,
              description: `会员已过期，钱包已删除`,
            })

            deletedCount++
          }
        } catch (err) {
          console.error(`处理用户 ${wallet.userId} 钱包检查时出错:`, err)
        }
      }

      return {
        success: true,
        message: `已处理 ${processedCount} 个钱包，删除了 ${deletedCount} 个非有效会员钱包`,
        processed: processedCount,
        deleted: deletedCount,
      }
    } catch (error: any) {
      console.error('执行非有效会员钱包删除任务时发生错误:', error)
      return {
        success: false,
        message: `执行非有效会员钱包删除任务时发生错误: ${error}`,
      }
    }
  },

  /**
   * 根据日期范围获取用户消费记录
   * @param userId 用户ID
   * @param startDate 开始日期
   * @param endDateExclusive 结束日期(不包含)
   * @param excludeSystemTransactions 是否排除系统交易记录
   * @returns 消费记录列表
   */
  async getUserTransactionsByDateRange(
    userId: string,
    startDateStr: string,
    endDateStr: string,
    excludeSystemTransactions: boolean = true,
  ): Promise<{
    transactions: CurrencyTransaction[]
  }> {
    const db = getDB()

    // 确保日期格式正确，与数据库北京时区匹配
    // 格式化为 YYYY-MM-DD 格式的字符串

    // 构建查询条件
    let conditions = and(
      eq(currencyTransaction.userId, userId),
      eq(currencyTransaction.currencyType, DIAMOND_CURRENCY),
      gte(currencyTransaction.createdAt, startDateStr),
      lte(currencyTransaction.createdAt, endDateStr),
    )

    // 如果需要排除系统交易记录，添加额外条件
    if (excludeSystemTransactions) {
      conditions = and(
        conditions,
        // 不等于SYSTEM类型的交易
        ne(currencyTransaction.transactionType, TRANSACTION_TYPE.SYSTEM),
      )
    }

    // 获取指定日期范围的记录
    const dbTransactions = await db
      .select()
      .from(currencyTransaction)
      .where(conditions)
      .orderBy(desc(currencyTransaction.createdAt))

    return {
      transactions: dbTransactions,
    }
  },

  /**
   * 获取用户当前计费周期的消费记录
   * @param userId 用户ID
   * @returns 当前计费周期的消费记录及周期信息
   */
  async getUserCurrentCycleTransactions(userId: string): Promise<{
    transactions: CurrencyTransaction[]
    cycleStartDate: string | null
    cycleEndDate: string | null
  }> {
    try {
      // 获取会员信息
      const membershipInfo = await getMembershipDetailsFromGameApi(userId)
      // 如果用户不是会员，返回空结果
      if (
        !membershipInfo.isActive
        || !membershipInfo.startDate
        || !membershipInfo.endDate
      ) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '用户不是活跃会员或会员信息不完整',
        })
      }

      const currentDate = new Date()

      // 计算当前计费周期
      const cycle = findCurrentBillingCycle(
        membershipInfo.startDate,
        membershipInfo.endDate,
        currentDate,
      )

      if (!cycle) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: '用户当前不在有效的计费周期内',
        })
      }

      const { cycleStart, cycleEnd } = cycle

      // 获取当前周期的交易记录
      const { transactions } = await this.getUserTransactionsByDateRange(
        userId,
        cycleStart,
        cycleEnd,
      )

      return {
        transactions,
        cycleStartDate: cycleStart,
        cycleEndDate: cycleEnd,
      }
    } catch (error: any) {
      console.error('获取用户当前周期交易记录时出错:', error)
      // 如果错误已经是TRPCError，直接抛出
      if (error instanceof TRPCError) {
        throw error
      }
      // 否则包装为TRPCError再抛出
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: `获取用户当前周期交易记录时出错: ${error}`,
      })
    }
  },
}
