import { protectedProcedure, router } from '../../trpc'
import { currencyService } from './currency.service'

export const currencyRouter = router({
  /**
   * 获取用户钻石余额
   */
  getBalance: protectedProcedure.query(async ({ ctx }) => {
    const balance = await currencyService.getDiamondBalance(ctx.user.id)
    return { balance }
  }),

  /**
   * 获取用户当前会员周期的消费记录
   */
  getCurrentCycleTransactions: protectedProcedure.query(async ({ ctx }) => {
    const { transactions, cycleStartDate, cycleEndDate }
      = await currencyService.getUserCurrentCycleTransactions(ctx.user.id)
    return {
      transactions,
      cycleStartDate,
      cycleEndDate,
    }
  }),
})
