import { TRPCError } from '@trpc/server'
import { currencyService } from './currency.service'
import {
  calculateFormatContentDiamondConsumption,
  calculateGenerateLearningContentDiamondConsumption,
  calculateProcessSentenceDiamondConsumption,
  calculateSplitSentenceDiamondConsumption,
  calculateWordStoryDiamondConsumption,
} from './diamondPricing'

/**
 * 检查用户钻石余额的基础函数
 * @param userId 用户ID
 * @param sentences 要处理的句子列表
 * @param calculateConsumption 计算钻石消耗的函数
 * @returns 预估的钻石消耗数量
 */
export async function checkDiamondBalance(
  userId: string,
  sentences: { content: string }[],
  calculateConsumption: (sentence: string) => number,
): Promise<number> {
  // 计算需要的钻石总量
  const totalDiamondConsumption = sentences.reduce(
    (total, sentence) => total + calculateConsumption(sentence.content),
    0,
  )

  // 检查用户是否有足够的钻石
  const hasEnoughDiamond = await currencyService.checkDiamondBalance(
    userId,
    totalDiamondConsumption,
  )

  if (!hasEnoughDiamond) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: `您的钻石余额不足，需要 ${totalDiamondConsumption} 个钻石，请充值`,
    })
  }

  return totalDiamondConsumption
}

/**
 * 检查单个内容的钻石消耗
 * @param userId 用户ID
 * @param content 要处理的内容
 * @param calculateConsumption 计算钻石消耗的函数
 * @returns 预估的钻石消耗数量
 */
export async function checkSingleContentDiamondBalance(
  userId: string,
  content: string,
  calculateConsumption: (content: string) => number,
): Promise<number> {
  // 计算需要的钻石总量
  const diamondConsumption = calculateConsumption(content)

  // 检查用户是否有足够的钻石
  const hasEnoughDiamond = await currencyService.checkDiamondBalance(
    userId,
    diamondConsumption,
  )

  if (!hasEnoughDiamond) {
    throw new TRPCError({
      code: 'BAD_REQUEST',
      message: `您的钻石余额不足，需要 ${diamondConsumption} 个钻石，请充值`,
    })
  }

  return diamondConsumption
}

/**
 * 检查用户是否有足够的钻石用于拆分句子
 * @param userId 用户ID
 * @param sentences 要拆分的句子列表
 * @returns 预估的钻石消耗数量
 */
export async function checkSplitSentenceDiamondBalance(
  userId: string,
  sentences: { content: string }[],
): Promise<number> {
  return checkDiamondBalance(
    userId,
    sentences,
    calculateSplitSentenceDiamondConsumption,
  )
}

/**
 * 检查用户是否有足够的钻石用于加工句子
 * @param userId 用户ID
 * @param sentences 要加工的句子列表
 * @returns 预估的钻石消耗数量
 */
export async function checkProcessSentenceDiamondBalance(
  userId: string,
  sentences: { content: string }[],
): Promise<number> {
  return checkDiamondBalance(
    userId,
    sentences,
    calculateProcessSentenceDiamondConsumption,
  )
}

/**
 * 检查用户是否有足够的钻石用于生成学习内容
 * @param userId 用户ID
 * @param sentences 要生成学习内容的句子列表
 * @returns 预估的钻石消耗数量
 */
export async function checkGenerateLearningContentDiamondBalance(
  userId: string,
  sentences: { content: string }[],
): Promise<number> {
  return checkDiamondBalance(
    userId,
    sentences,
    calculateGenerateLearningContentDiamondConsumption,
  )
}

/**
 * 检查用户是否有足够的钻石用于格式化内容
 * @param userId 用户ID
 * @param content 要格式化的内容
 * @returns 预估的钻石消耗数量
 */
export async function checkFormatContentDiamondBalance(
  userId: string,
  content: string,
): Promise<number> {
  return checkSingleContentDiamondBalance(
    userId,
    content,
    calculateFormatContentDiamondConsumption,
  )
}

/**
 * 检查用户是否有足够的钻石用于生成单词故事
 * @param userId 用户ID
 * @param words 要生成故事的单词
 * @returns 预估的钻石消耗数量
 */
export async function checkWordStoryDiamondBalance(
  userId: string,
  words: string,
): Promise<number> {
  return checkSingleContentDiamondBalance(
    userId,
    words,
    calculateWordStoryDiamondConsumption,
  )
}
