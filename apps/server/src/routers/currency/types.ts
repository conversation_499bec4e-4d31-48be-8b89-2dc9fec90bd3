import type { TRANSACTION_TYPE } from '../../db/schema/currencyTransaction'

/**
 * 用户钱包接口
 */
export interface UserWallet {
  userId: string
  currencyType: string
  balance: number
  createdAt: Date
  updatedAt: Date
}

/**
 * 交易记录接口
 */
export interface CurrencyTransaction {
  id: string
  userId: string
  currencyType: string
  amount: number
  transactionType: keyof typeof TRANSACTION_TYPE
  referenceId?: string
  description?: string | null
  createdAt: string
}

/**
 * 令牌使用情况接口
 */
export interface TokenUsage {
  promptTokens: number
  completionTokens: number
  totalTokens: number
}
