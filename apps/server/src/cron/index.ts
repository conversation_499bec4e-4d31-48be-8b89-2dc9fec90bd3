import cron from 'node-cron'
import { currencyService } from '../routers/currency/currency.service'

export function setupCron() {
  // 检查是否为主进程，只有主进程才执行定时任务
  if (isPrimaryInstance()) {
    console.log('当前为主进程，注册定时任务...')
    registerDiamondResetCron()
    registerInactiveMemberWalletCleanupCron()
  } else {
    console.log('当前为从进程，跳过定时任务注册')
  }
}

/**
 * 判断当前是否为主进程
 * 在PM2环境下，使用NODE_APP_INSTANCE环境变量判断
 * 在非PM2环境下，默认为主进程
 */
function isPrimaryInstance(): boolean {
  // PM2 会设置 NODE_APP_INSTANCE 环境变量
  const instanceId = process.env.NODE_APP_INSTANCE

  // 如果未定义 NODE_APP_INSTANCE，则可能不是在PM2中运行
  if (instanceId === undefined) {
    console.log('未检测到PM2环境，视为主进程')
    return true
  }

  // 只有实例ID为0的才是主进程
  return instanceId === '0'
}

/**
 * 注册钻石重置定时任务
 * 每天凌晨五分执行，检查并重置符合条件的会员钻石
 * 会员会在其会员开通日期的下一个月同一天获得钻石重置
 * 例如：3月12日注册的会员，将在4月12日凌晨00:05获得钻石重置
 */
function registerDiamondResetCron() {
  cron.schedule('5 0 * * *', () => {
    console.log('开始执行钻石重置定时任务...')

    void (async () => {
      try {
        const result = await currencyService.resetDiamonds()

        if (result.success) {
          console.log(result.message)
        } else {
          console.error(result.message)
        }
      } catch (error) {
        console.error('执行钻石重置定时任务时发生错误:', error)
      }
    })()
  })
}

/**
 * 注册非有效会员钱包清理定时任务
 * 每天凌晨00:05执行，检查所有用户是否还是有效会员
 * 如果用户不再是有效会员，则删除其钱包信息
 */
function registerInactiveMemberWalletCleanupCron() {
  cron.schedule('5 0 * * *', () => {
    void (async () => {
      console.log('开始执行非有效会员钱包清理定时任务...')

      try {
        // 调用 currencyService 清理非有效会员钱包
        const result = await currencyService.deleteInactiveMembersWallets()

        if (result.success) {
          console.log(result.message)
        } else {
          console.error(result.message)
        }
      } catch (error) {
        console.error('执行非有效会员钱包清理定时任务时发生错误:', error)
      }
    })()
  })
}
