import type { CreateFastifyContextOptions } from '@trpc/server/adapters/fastify'
import { requestContext } from '@fastify/request-context'
import { TRPCError } from '@trpc/server'
import { extractTokenFromHeader, verifyJwtToken } from '@/services/auth'

export async function createContext({ req }: CreateFastifyContextOptions) {
  const authorization = req.headers.authorization
  if (!authorization) {
    return { user: null, token: null }
  }

  const token = extractTokenFromHeader(authorization)

  if (!token) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: '未提供有效的认证令牌',
    })
  }

  try {
    const { payload, isValid } = await verifyJwtToken(token)

    if (!isValid) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: '无效的认证令牌',
      })
    }

    const context = {
      user: {
        id: payload!.sub,
      },
      token,
    }

    requestContext.set('userId', context.user.id)

    return context
  } catch {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: '令牌验证失败',
    })
  }
}

export interface Context {
  user: {
    id: string
  } | null
  token: string | null
}
