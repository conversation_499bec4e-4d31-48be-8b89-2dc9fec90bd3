import { router } from '.'
// import { textImageLinkRouter } from "../routers/textImageLink/textImageLink.router";
import { adminCourseRouter, adminSentenceRouter } from '../routers/admin/admin.trpc'
import { categoryRouter } from '../routers/category/category.router'
import { contentRouter } from '../routers/content/content.router'
import { coursePackRouter } from '../routers/course-pack/course-pack.router'
import { courseRouter } from '../routers/course/course.router'
import { currencyRouter } from '../routers/currency/currency.router'
import { elementRouter } from '../routers/element/element.router'
import { imageRouter } from '../routers/image/image.router'
import { inboxTrpcRouter } from '../routers/inbox/inbox.trpc'
import { phoneticRouter } from '../routers/phonetic/phonetic.router'
import { s3Router } from '../routers/s3/s3.router'
import { sentenceRouter } from '../routers/sentence/sentence.router'
import { speechRouter } from '../routers/speech/speech.router'
import { ttsRouter } from '../routers/tts/tts.router'
import { userRouter } from '../routers/user/user.router'

export const appRouter = router({
  adminCourse: adminCourseRouter,
  adminSentence: adminSentenceRouter,
  coursePack: coursePackRouter,
  course: courseRouter,
  sentence: sentenceRouter,
  element: elementRouter,
  phonetic: phoneticRouter,
  content: contentRouter,
  inbox: inboxTrpcRouter,
  tts: ttsRouter,
  user: userRouter,
  currency: currencyRouter,
  s3: s3Router,
  image: imageRouter,
  category: categoryRouter,
  speech: speechRouter,
})
export type AppRouter = typeof appRouter
