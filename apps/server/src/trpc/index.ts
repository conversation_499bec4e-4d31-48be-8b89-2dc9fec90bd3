import type { Context } from './trpc.context'
import { initTRPC, TRPCError } from '@trpc/server'
import * as superjson from 'superjson'

import { getMembershipDetailsFromGameApi } from '@/services/game/api/memberships'

const t = initTRPC.context<Context>().create({
  transformer: superjson,
  errorFormatter({ shape }) {
    return shape
  },
})

export const protectedProcedure = t.procedure.use(
  async (opts) => {
    const { ctx } = opts
    if (!ctx.user) {
      throw new TRPCError({ code: 'UNAUTHORIZED' })
    }

    // TODO 后面采用 redis 的方式来进行优化
    const membershipDetails = await getMembershipDetailsFromGameApi(ctx.user.id)
    if (!membershipDetails?.isActive) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: '编辑端只对会员开放',
      })
    }

    return opts.next({
      ctx: {
        user: ctx.user,
        token: ctx.token,
      },
    })
  },
)

export const router = t.router
export const publicProcedure = t.procedure
