import axios from 'axios'
import { config } from '@/config/config'

/**
 * 词性标签的中英文映射
 */
const posMapping: Record<string, string> = {
  ADJ: '形容词',
  ADP: '介词',
  ADV: '副词',
  AUX: '助动词',
  CCONJ: '并列连词',
  DET: '限定词',
  INTJ: '感叹词',
  NOUN: '名词',
  NUM: '数词',
  PART: '虚词',
  PRON: '代词',
  PROPN: '专有名词',
  PUNCT: '标点',
  SCONJ: '从属连词',
  SYM: '符号',
  VERB: '动词',
  X: '其他',
  SPACE: '空格',
}

/**
 * 依存关系的中英文映射
 */
export const depMapping: Record<string, string> = {
  acl: '从句修饰语',
  acomp: '形容词补语',
  advcl: '副词从句修饰语',
  advmod: '副词修饰语',
  agent: '动作执行者',
  amod: '形容词修饰语',
  appos: '同位语',
  attr: '属性',
  aux: '助动词',
  auxpass: '被动助动词',
  case: '格标记',
  cc: '并列连词',
  ccomp: '从句补语',
  compound: '复合词',
  conj: '并列',
  cop: '系动词',
  csubj: '从句主语',
  csubjpass: '被动从句主语',
  dative: '与格',
  dep: '依存',
  det: '限定词',
  dobj: '直接宾语',
  expl: '形式主语',
  intj: '感叹词',
  mark: '标记',
  meta: '元信息',
  neg: '否定词',
  nmod: '名词修饰语',
  npadvmod: '名词性副词修饰语',
  nsubj: '名词性主语',
  nsubjpass: '被动名词性主语',
  nummod: '数词修饰语',
  obj: '宾语',
  obl: '斜格名词',
  parataxis: '并列关系',
  pcomp: '介词补语',
  pobj: '介词宾语',
  poss: '所有格',
  preconj: '前置连词',
  predet: '前置限定词',
  prep: '介词',
  prt: '小品词',
  punct: '标点',
  quantmod: '数量修饰语',
  relcl: '关系从句',
  root: '根',
  xcomp: '开放性补语',
}

/**
 * 词性分析结果项的接口
 */
interface POSItem {
  text: string
  pos: string
  [key: string]: any
}

/**
 * 依存关系节点的接口
 */
interface DependencyNode {
  id: number | string
  text: string
  pos: string
  dep: string
  [key: string]: any
}

/**
 * 依存关系边的接口
 */
interface DependencyEdge {
  source: number | string
  target: number | string
  label: string
  label_cn?: string
  [key: string]: any
}

/**
 * 依存关系分析结果的接口
 */
export interface DependencyResult {
  nodes: DependencyNode[]
  edges: DependencyEdge[]
  [key: string]: any
}

/**
 * 分析句子的词性
 * @param sentence 需要分析的句子
 * @returns 词性分析结果，包含中英文词性标签
 */
export async function analyzePOS(sentence: string): Promise<POSItem[]> {
  try {
    const response = await axios.post(`${config.nlp.apiUrl}/api/pos`, { sentence })

    // 添加中文词性映射
    const data = response.data.map((item: POSItem) => ({
      ...item,
      pos_cn: posMapping[item.pos] || item.pos,
    }))

    return data
  } catch (error) {
    console.error('Error analyzing POS:', error)
    throw new Error('Failed to analyze parts of speech')
  }
}

/**
 * 分析句子的依存关系
 * @param sentence 需要分析的句子
 * @returns 依存关系分析结果，包含节点和边的信息
 */
export async function analyzeDependency(sentence: string): Promise<DependencyResult> {
  try {
    const response = await axios.post(`${config.nlp.apiUrl}/api/dependency`, { sentence })

    // 添加中文依存关系映射
    return response.data as DependencyResult
  } catch (error) {
    console.error('Error analyzing dependency:', error)
    throw new Error('Failed to analyze dependency tree')
  }
}

/**
 * 检查NLP服务健康状态
 * @returns 健康状态信息
 */
export async function checkNLPServiceHealth(): Promise<any> {
  try {
    const response = await axios.get(`${config.nlp.apiUrl}/api/health`)
    return response.data
  } catch (error) {
    console.error('NLP service health check failed:', error)
    throw new Error('NLP service is not available')
  }
}
