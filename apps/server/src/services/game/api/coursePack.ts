import type { Buffer } from 'node:buffer'
import { gameServiceApi } from './http'

export interface PublishResponse {
  status: number
  data: {
    coursePackId: string
    courses: {
      id: string
      statements: string[]
    }[]
  }
}

export async function publishCoursePackToGameApi(coursePackData: {
  id: string
  gameId: string
  title: string
  description: string
  cover: string
  userId: string
  shareLevel: string
}, userId: string) {
  const { data } = await gameServiceApi.post<PublishResponse>(
    '/course-packs/publish',
    { coursePack: coursePackData, userId },
    {
      headers: {
        'Content-Type': 'application/json',
      },
    },
  )
  return data
}

export interface UpdateResponse {
  status: number
  data:
    | {
      courses: { editorId: string, gameId: string }[]
      statements: { editorId: string, gameId: string }[]
    }
    | false
}

export async function updateCoursePackToGameApi(
  gameCoursePackId: string,
  updateCoursePack: Buffer,
) {
  const { data } = await gameServiceApi.put<UpdateResponse>(
    `/course-packs/${gameCoursePackId}`,
    updateCoursePack,
    {
      headers: {
        'Content-Type': 'application/gzip',
        'Content-Encoding': 'gzip',
      },
    },
  )
  return data
}

export async function unpublishCoursePackToGameApi(
  gameCoursePackId: string,
  userId: string,
) {
  const { data } = await gameServiceApi.delete(
    `/course-packs/${gameCoursePackId}`,
    {
      data: { userId },
      headers: {
        'Content-Type': 'application/json',
      },
    },
  )
  return data
}
