import axios from 'axios'
import { config } from '@/config/config'

/**
 * 获取服务间通信的认证头
 * 用于服务到服务的API调用，不依赖用户token
 */
export function getServiceAuthHeaders() {
  return {
    'x-service-auth': config.serviceSecret, // 这里必须使用小写
  }
}

// 用于服务间通信的API实例，预配置了服务认证
export const gameServiceApi = axios.create({
  baseURL: `${config.gameURL}/internal`,
  headers: {
    ...getServiceAuthHeaders(),
  },
})

// 添加请求拦截器，智能处理 Content-Type
gameServiceApi.interceptors.request.use((config) => {
  // 如果请求已经设置了 Content-Type，则使用已设置的值
  if (!config.headers['Content-Type']) {
    // 对于 GET, DELETE 等不需要请求体的方法，不设置 Content-Type
    if (['get', 'delete', 'head', 'options'].includes(config.method?.toLowerCase() || '')) {
      // 不设置 Content-Type
    } else {
      // 对于 POST, PUT, PATCH 等方法，默认使用 application/json
      config.headers['Content-Type'] = 'application/json'
    }
  }
  return config
})
