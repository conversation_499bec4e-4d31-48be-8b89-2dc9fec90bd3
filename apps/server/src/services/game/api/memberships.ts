import { gameServiceApi } from './http'

export interface MembershipDetails {
  status: number
  data: {
    startDate: string
    endDate: string
    type: string
    isActive: boolean
  }
}

export async function getMembershipDetailsFromGameApi(userId: string) {
  try {
    const { data } = await gameServiceApi.post<MembershipDetails>(
      `/memberships/getUserInfo`,
      { userId },
    )

    return data.data
  } catch (error) {
    console.error(`获取用户 ${userId} 的会员信息失败:`, error)
    throw error
  }
}
