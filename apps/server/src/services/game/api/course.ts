import type { Buffer } from 'node:buffer'
import { gameServiceApi } from './http'

export interface PublishResponse {
  status: number
  data: {
    courseId: string
    statements: string[]
  }
}

export async function publishCourseToGameApi(compressedData: Buffer) {
  const { data } = await gameServiceApi.post<PublishResponse>(
    `/courses/publish`,
    compressedData,
    {
      headers: {
        'Content-Type': 'application/gzip',
        'Content-Encoding': 'gzip',
      },
    },
  )
  return data
}

export interface UpdateResponse {
  status: number
  data:
    | {
      statements: { editorId: string, gameId: string }[]
    }
    | false
}

export async function updateCourseToGameApi(
  gameCourseId: string,
  compressedData: Buffer,
) {
  const { data } = await gameServiceApi.put<UpdateResponse>(
    `/courses/${gameCourseId}`,
    compressedData,
    {
      headers: {
        'Content-Type': 'application/gzip',
        'Content-Encoding': 'gzip',
      },
    },
  )
  return data
}

export async function unpublishCourseToGame<PERSON><PERSON>(
  gameCourseId: string,
  userId: string,
) {
  const { data } = await gameServiceApi.delete(`/courses/${gameCourseId}`, {
    data: { userId },
  })
  return data
}
