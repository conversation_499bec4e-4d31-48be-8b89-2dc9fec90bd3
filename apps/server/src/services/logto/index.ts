import { Buffer } from 'node:buffer'
import axios from 'axios'

export const logtoApiClient = axios.create({
  baseURL: process.env.LOGTO_ENDPOINT,
  timeout: 5000,
})

logtoApiClient.interceptors.request.use(async (config) => {
  const token = await fetchToken()
  config.headers.Authorization = `Bearer ${token}`
  return config
})

const encodedCredentials = createCredentials()
async function fetchToken(options: { resource?: string, scope?: string } = {}) {
  const { resource = process.env.LOGTO_M2M_API, scope = 'all' } = options

  const { data } = await axios({
    method: 'post',
    baseURL: process.env.LOGTO_ENDPOINT,
    url: 'oidc/token',
    data: {
      grant_type: 'client_credentials',
      resource,
      scope,
    },
    headers: {
      'Authorization': `Basic ${encodedCredentials}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })

  return data.access_token
}

function createCredentials() {
  return Buffer.from(`${process.env.LOGTO_CLIENT_ID}:${process.env.LOGTO_CLIENT_SECRET}`).toString(
    'base64',
  )
}
