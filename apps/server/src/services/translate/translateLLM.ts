import { createTranslateModelLLM } from '@/llm/task'
import { parseJsonFromCodeBlock } from '@/utils/json'

/**
 * 翻译指定的内容
 * @param content
 * @returns
 */
export async function translateContentLLM(content: string) {
  const prompt = `
您是一位资深的英译中翻译专家，具备深厚的语言功底和丰富的翻译经验。您的任务是将英文内容精准、流畅地转换为中文，同时严格遵守翻译规范，确保译文既忠实于原文，又符合中文的表达习惯。

请将以下英文内容翻译为中文，并遵循以下规则：
1. 如果内容是单个字母、数字或符号，保持原样，不做任何改动。
2. 如果内容是英文专有名词（如地名、品牌等），保持原样，无需翻译。
3. 译文应准确无误，语言地道，符合中文的语法和表达习惯。
4. 保持原文的语气、风格和情感色彩，确保译文与原文在语境上高度一致。
5. 对于文化特定表达，采用中文读者熟悉的表达方式，而不是生硬直译。
6. 处理多义词时，根据上下文选择最合适的翻译，避免歧义。
7. 对于习语、俚语和固定搭配，使用中文中对应的表达方式。
8. 对于专业术语或特定领域词汇，确保使用该领域在中文中的标准表达。
9. 翻译应当完整表达原文含义，不遗漏任何关键信息。
10.译文应当自然流畅，如同中文母语者的表达方式。
11.人名不翻译: 但是人名前缀需要翻译
  - 例如："Miss Sophie" 翻译成 "Sophie小姐"

翻译内容：
${content}

输出格式要求：
请以 JSON 格式返回翻译结果，格式如下：
{
  "chinese": "翻译结果"
}

请按照上述规则进行翻译，并输出符合要求的JSON格式结果。
    `

  const model = createTranslateModelLLM()
  const response = await model.invoke(prompt, 'translate_content', {
    content,
  })

  // console.log(response);
  return parseJsonFromCodeBlock<{ chinese: string }>(response)
}

export async function translateChunkCombine(
  sentence: string,
  chunksContent: string,
) {
  const prompt = `
你是一名专业翻译专家，擅长在保持局部上下文连贯性的前提下，将英文内容准确翻译成中文。请基于以下英文片段进行翻译：

需要翻译的 chunk：
${chunksContent}

上下文参考句（仅供消除歧义）：
${sentence}

翻译要求：
1. 严格仅翻译 chunk 内容，不可添加上下文参考句中的额外信息
2. 当 chunk 包含代词等需要上下文理解时，可参考上下文句
3. 保持自然流畅的中文表达，符合语法规范
4. 保留原文的情感色彩和风格特点
5. 处理文化差异时采用中文惯用表达
6. 使用中文标准术语翻译专业词汇

错误示例警示：
原文句：I usually reads his newspaper at night.
需翻译 chunk：I usually reads his newspaper
错误翻译：我通常在晚上读他的报纸 ← 包含未指定的时间
正确翻译：我通常阅读他的报纸

请输出JSON格式：
{ "chinese": "翻译结果" }
`

  const model = createTranslateModelLLM()
  const response = await model.invoke(
    prompt,
    'translate_chunk_combine',
    {
      sentence,
      chunksContent,
    },
  )
  // console.log(response.content);
  return parseJsonFromCodeBlock<{ chinese: string }>(response)
}
