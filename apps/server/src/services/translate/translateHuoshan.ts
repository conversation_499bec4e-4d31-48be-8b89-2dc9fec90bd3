import { Service } from '@volcengine/openapi'

const accessKeyId = 'AKLTZTZiNGJiY2UwZjZmNDBhYzg3NmI4ZjhjMmQzN2I0YWI' // https://console.volcengine.com/iam/keymanage/
const secretKey
  = 'WVdZek1EQmlNalF5TVRZMk5HWmpNV0kyTWpFd1pqRTFaREF4WlRsaFpUZw=='

const service = new Service({
  host: 'open.volcengineapi.com',
  serviceName: 'translate',
  region: 'cn-north-1',
  accessKeyId,
  secretKey,
})

const fetchApi = service.createAPI('TranslateText', {
  Version: '2020-06-01',
  method: 'POST',
  contentType: 'json',
})

export async function translateToChineseHuoshan(text: string) {
  const rr = await fetchApi({
    SourceLanguage: 'en',
    TargetLanguage: 'zh',
    TextList: [text],
  }) as unknown as { TranslationList: { Translation: string }[] }

  return rr.TranslationList[0].Translation
}
