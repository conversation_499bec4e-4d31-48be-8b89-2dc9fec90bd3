import { createRemoteJWKSet, jwtVerify } from 'jose'
import { config } from '@/config/config'

export interface JwtPayload {
  sub: string
  [key: string]: unknown
}

export function extractTokenFromHeader(authorization: string): string | undefined {
  const [type, token] = authorization.split(' ') ?? []
  return type === 'Bearer' ? token : undefined
}

export async function verifyJwtToken(token: string) {
  try {
    const jwks = createRemoteJWKSet(
      new URL('/oidc/jwks', config.logto.endpoint),
    )

    const { payload } = await jwtVerify(token, jwks, {
      issuer: new URL('oidc', config.logto.endpoint).href,
      audience: config.logto.resources[0],
    })

    return {
      isValid: true,
      payload: payload as JwtPayload,
    }
  } catch (error) {
    console.error('Token validation error:', error)
    return {
      isValid: false,
      payload: null,
    }
  }
}
