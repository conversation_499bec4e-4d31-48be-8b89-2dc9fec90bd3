/**
 * 通过有道云api 查新单词 获取音标信息
 *
 */
import crypto from 'node:crypto'
import { ofetch } from 'ofetch'
import { config } from '@/config/config'

async function translateAPI(word: string) {
  const query = word
  const curtime = Math.round(new Date().getTime() / 1000)
  const salt = new Date().getTime()

  function generateSign() {
    const input
      = query.length > 20
        ? query.substr(0, 10) + query.length + query.substr(-10)
        : query

    const str1 = config.youdao.appKey + input + salt + curtime + config.youdao.secretKey
    return crypto.createHash('sha256').update(str1).digest('hex')
  }

  const data = await ofetch<{ translation: string[] }>(
    'https://openapi.youdao.com/api',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Encoding': 'utf8',
      },
      body: new URLSearchParams({
        q: query.toString(),
        appKey: config.youdao.appKey,
        salt: salt.toString(),
        from: config.youdao.from,
        to: config.youdao.to,
        sign: generateSign(),
        signType: 'v3',
        curtime: curtime.toString(),
      }),
    },
  )

  return data
}

export async function translateToChineseYoudao(word: string) {
  const data = await translateAPI(word)

  if (!data.translation) {
    console.log(data)
    console.log(`翻译出现问题： ${word}`)
    data.translation = []
  }

  return data.translation[0]
}

export async function getPhoneticByYoudao(word: string) {
  const data = (await translateAPI(word)) as {
    basic?: { 'us-phonetic'?: string, 'uk-phonetic'?: string }
  }

  if (!data.basic) {
    console.log(data)
    console.log(`获取音标出现问题： ${word}`)
    return { word, us: '', uk: '' }
  }

  return {
    word,
    us: data.basic['us-phonetic'] || '',
    uk: data.basic['uk-phonetic'] || '',
  }
}
