export const loggerConfig = {
  development: {
    transport: {
      target: 'pino-pretty',
      options: {
        translateTime: 'HH:MM:ss Z',
        ignore: 'pid,hostname',
        colorize: true,
      },
    },
    level: 'debug',
  },
  production: {
    transport: {
      targets: [
        // 错误日志 - 使用 pino-roll 实现日志轮转
        {
          level: 'error', // 此 transport 只处理 error 及以上级别的日志
          target: 'pino-roll',
          options: {
            file: './logs/error.log',
            frequency: 'daily', // 按天轮转
            size: '10m', // 或者当文件达到 10MB 时轮转
            mkdir: true, // 自动创建目录
            maxFiles: 10, // 保留最近10个日志文件
          },
        },
        // 应用日志 - 使用 pino-roll 实现日志轮转
        {
          level: 'warn', // 此 transport 处理 warn 及以上级别的日志
          target: 'pino-roll',
          options: {
            file: './logs/app.log',
            frequency: 'daily', // 按天轮转
            size: '50m', // 当文件达到 50MB 时轮转
            mkdir: true, // 自动创建目录
            maxFiles: 14, // 保留最近14个日志文件
          },
        },
        // 控制台输出，方便通过 kubectl logs / docker logs 查看
        {
          level: 'warn',
          target: 'pino-pretty',
          options: {
            colorize: false,
            translateTime: 'SYS:standard',
          },
        },
      ],
    },
    level: 'warn', // 全局日志级别，决定哪些日志进入 pipeline
  },
  test: {
    transport: {
      target: 'pino-roll',
      options: {
        file: './logs/test.log',
        size: '5m', // 当文件达到 5MB 时轮转
        mkdir: true, // 自动创建目录
        maxFiles: 3, // 保留最近3个日志文件
      },
    },
    level: 'debug',
  },
}
