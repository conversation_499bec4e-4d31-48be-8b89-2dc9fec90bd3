// import fastifyRedis from "@fastify/redis";
import type {
  FastifyTRPCPluginOptions,
} from '@trpc/server/adapters/fastify'
import type { AppRouter } from './trpc/trpc.router'

import fastifyCors from '@fastify/cors'
import fastifyMultipart from '@fastify/multipart'
import fastifyMysql from '@fastify/mysql'
import { fastifyRequestContext } from '@fastify/request-context'
import { MAX_FILE_SIZE } from '@julebu/shared'
import {
  fastifyTRPCPlugin,
} from '@trpc/server/adapters/fastify'

import fastify from 'fastify'
import { config } from './config/config' // 配置需要最先被加载
import { setupCron } from './cron'
import { setupDB } from './db/index'
import { setupLogger } from './logger'
import { ensureLogDir } from './logger/ensure-log-dir'
import { loggerConfig } from './logger/logger.config'
import { setupAPIRoutes } from './routers/api'
// import { setupRedis } from "./services/redis";
import { createContext } from './trpc/trpc.context'
import { appRouter } from './trpc/trpc.router'
import './config/alias'

// 导出给前端使用
export type { AppRouter }

const server = fastify({
  maxParamLength: 5000,
  bodyLimit: MAX_FILE_SIZE, // 需要各个业务层再加限制，防止恶意上传大文件
  logger: loggerConfig[config.nodeEnv as keyof typeof loggerConfig],
})
server.register(fastifyRequestContext)

// 注册文件上传支持
server.register(fastifyMultipart, {
  limits: {
    fileSize: MAX_FILE_SIZE, // 需要各个业务层再加限制，防止恶意上传大文件
    files: 10, // 最多10个文件
  },
})

setupLogger(server)

// server.register(fastifyRedis, {
//   url: config.redisUrl,
// });

server.register(fastifyMysql, {
  promise: true,
  connectionString: config.database.url,
  pool: {
    min: 2,
    max: 10,
    acquireTimeout: 30000,
    createTimeoutMillis: 30000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200,
  },
})

server.register(fastifyCors, {
  origin: ['https://editor.julebu.co', 'http://localhost:3200'], // 允许来自julebu.co和本地开发环境的请求
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], // 允许的HTTP方法
  credentials: true, // 允许携带凭证
})

server.register(fastifyTRPCPlugin, {
  prefix: '/trpc',
  trpcOptions: {
    router: appRouter,
    createContext,
    onError({ path, error }) {
      // report to error monitoring
      console.error(`Error in tRPC handler on path '${path}':`, error)
    },
  } satisfies FastifyTRPCPluginOptions<AppRouter>['trpcOptions'],
})

void (async () => {
  try {
    console.log('env:', config.nodeEnv)
    setupAPIRoutes(server)
    await server.ready()
    await ensureLogDir()
    void setupDB(server.mysql.pool)
    // setupRedis(server.redis);
    setupCron()
    await server.listen({ port: config.port, host: '0.0.0.0' })
    server.log.info(`Server is running on port ${config.port}`)
    server.log.info(`Database URL: ${config.database.url}`)
  } catch (err) {
    console.log(err)
    server.log.error(err)
    process.exit(1)
  }
})()
