import path from 'node:path'
import process from 'node:process'
import * as dotenv from 'dotenv'

console.log('当前NODE_ENV:', process.env.NODE_ENV)
const envFile
  = process.env.NODE_ENV === 'test'
    ? '.env.test'
    : process.env.NODE_ENV === 'prod'
      ? '.env.prod'
      : '.env'

console.log('将使用的环境文件:', envFile)

// 检查文件是否存在
const envPath = path.resolve(process.cwd(), envFile)

// 重置之前可能存在的环境变量
const envConfig = dotenv.config({
  path: envPath,
  override: true, // 强制覆盖已经存在的环境变量
})

// 手动设置环境变量
if (envConfig.parsed && envConfig.parsed.DATABASE_URL) {
  process.env.DATABASE_URL = envConfig.parsed.DATABASE_URL
}

export default {
  schema: './src/db/schema/*',
  out: './drizzle',
  dialect: 'mysql',
  dbCredentials: {
    url: process.env.DATABASE_URL,
  },
}
