import { config } from "@/config/config";
// import Redis from "ioredis";
import * as mysql from "mysql2/promise";
import { afterAll, beforeAll } from "vitest";

import { closeDB, setupDB } from "../src/db"; 
// import { setupRedis } from "../src/services/redis"; 

// let redisClient: Redis;

beforeAll(async () => {
  const pool = await mysql.createPool(config.database.url);
  setupDB(pool);

  // 使用 ioredis 创建 Redis 实例
  // redisClient = new Redis(config.redisUrl);
  // setupRedis(redisClient);
});

afterAll(async () => {
  // 在所有测试结束后清理资源
  await closeDB();

  // 关闭 Redis 连接
  // await redisClient.quit();
});
