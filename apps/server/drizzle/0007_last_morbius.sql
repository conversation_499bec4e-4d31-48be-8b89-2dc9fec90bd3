CREATE TABLE `currency_transactions` (
	`id` varchar(128) NOT NULL,
	`user_id` varchar(128) NOT NULL,
	`currency_type` varchar(50) NOT NULL DEFAULT 'diamond',
	`amount` bigint NOT NULL,
	`transaction_type` enum('PURCH<PERSON><PERSON>','R<PERSON>WA<PERSON>','<PERSON><PERSON>UM<PERSON>','T<PERSON><PERSON><PERSON><PERSON>') NOT NULL,
	`description` varchar(255),
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `currency_transactions_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `user_wallets` (
	`user_id` varchar(128) NOT NULL,
	`currency_type` varchar(50) NOT NULL DEFAULT 'diamond',
	`balance` bigint NOT NULL DEFAULT 0,
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `user_wallets_user_id_currency_type_pk` PRIMARY KEY(`user_id`,`currency_type`)
);
--> statement-breakpoint
CREATE TABLE `task_token_usage` (
	`id` varchar(128) NOT NULL,
	`task_type` varchar(50) NOT NULL,
	`model` varchar(50) NOT NULL,
	`prompt_tokens` int NOT NULL,
	`completion_tokens` int NOT NULL,
	`total_tokens` int NOT NULL,
	`metadata` json,
	`year` int NOT NULL,
	`month` int NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `task_token_usage_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE INDEX `model_idx` ON `task_token_usage` (`model`);--> statement-breakpoint
CREATE INDEX `year_month_idx` ON `task_token_usage` (`year`,`month`);