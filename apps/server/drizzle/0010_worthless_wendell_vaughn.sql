CREATE TABLE `course_pack_categories` (
	`id` varchar(128) NOT NULL,
	`label` varchar(100) NOT NULL,
	`value` varchar(50) NOT NULL,
	`description` text,
	`sort_order` int NOT NULL DEFAULT 0,
	`is_active` boolean NOT NULL DEFAULT true,
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `course_pack_categories_id` PRIMARY KEY(`id`),
	CONSTRAINT `course_pack_categories_value_unique` UNIQUE(`value`)
);
--> statement-breakpoint
DROP TABLE `categories`;--> statement-breakpoint
ALTER TABLE `course_packs` DROP FOREIGN KEY `course_packs_category_id_categories_id_fk`;
--> statement-breakpoint
ALTER TABLE `course_packs` ADD `course_pack_category_id` varchar(128);--> statement-breakpoint
CREATE INDEX `value_idx` ON `course_pack_categories` (`value`);--> statement-breakpoint
CREATE INDEX `is_active_idx` ON `course_pack_categories` (`is_active`);--> statement-breakpoint
CREATE INDEX `sort_order_idx` ON `course_pack_categories` (`sort_order`);--> statement-breakpoint
ALTER TABLE `course_packs` ADD CONSTRAINT `course_packs_course_pack_category_id_course_pack_categories_id_fk` FOREIGN KEY (`course_pack_category_id`) REFERENCES `course_pack_categories`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `course_packs` DROP COLUMN `category_id`;