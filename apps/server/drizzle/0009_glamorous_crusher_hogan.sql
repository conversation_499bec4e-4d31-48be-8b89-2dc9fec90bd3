CREATE TABLE `images` (
	`id` varchar(128) NOT NULL,
	`file_key` varchar(255) NOT NULL,
	`mime_type` varchar(50) NOT NULL,
	`file_size` int NOT NULL,
	`width` int NOT NULL,
	`height` int NOT NULL,
	`source` varchar(50) NOT NULL,
	`description` varchar(500),
	`user_id` varchar(128),
	`style_tags` json DEFAULT ('[]'),
	`category_tags` json DEFAULT ('[]'),
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `images_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `text_image_links` (
	`id` varchar(128) NOT NULL,
	`text_content` text NOT NULL,
	`image_id` varchar(128) NOT NULL,
	`link_type` varchar(50) NOT NULL,
	`user_id` varchar(128),
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `text_image_links_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
ALTER TABLE `elements` ADD `image_id` varchar(128);--> statement-breakpoint
CREATE INDEX `source_idx` ON `images` (`source`);--> statement-breakpoint
CREATE INDEX `user_id_idx` ON `images` (`user_id`);--> statement-breakpoint
CREATE INDEX `description_idx` ON `images` (`description`);--> statement-breakpoint
CREATE INDEX `link_type_idx` ON `text_image_links` (`link_type`);--> statement-breakpoint
CREATE INDEX `user_id_idx` ON `text_image_links` (`user_id`);