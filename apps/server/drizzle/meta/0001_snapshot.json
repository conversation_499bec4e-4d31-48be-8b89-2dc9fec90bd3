{"version": "5", "dialect": "mysql", "id": "027e9e79-0b4d-461a-a3d1-d1481c6a3f99", "prevId": "8b157983-2e4b-4062-81eb-cd10dbed2760", "tables": {"courses": {"name": "courses", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "('')"}, "cover": {"name": "cover", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "video": {"name": "video", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "course_pack_id": {"name": "course_pack_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "publish_course_id": {"name": "publish_course_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "published_md5": {"name": "published_md5", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "position": {"name": "position", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"courses_course_pack_id_course_packs_id_fk": {"name": "courses_course_pack_id_course_packs_id_fk", "tableFrom": "courses", "tableTo": "course_packs", "columnsFrom": ["course_pack_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"courses_id": {"name": "courses_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "course_packs": {"name": "course_packs", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "('')"}, "is_free": {"name": "is_free", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false}, "cover": {"name": "cover", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "published_course_pack_id": {"name": "published_course_pack_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "published_md5": {"name": "published_md5", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "share_level": {"name": "share_level", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'private'"}, "position": {"name": "position", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"course_packs_id": {"name": "course_packs_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "elements": {"name": "elements", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "english": {"name": "english", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "('')"}, "chinese": {"name": "chinese", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "('')"}, "phonetic": {"name": "phonetic", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "('')"}, "part_of_speech": {"name": "part_of_speech", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "''"}, "position": {"name": "position", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "sentence_id": {"name": "sentence_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"sentence_id_idx": {"name": "sentence_id_idx", "columns": ["sentence_id"], "isUnique": false}}, "foreignKeys": {"elements_sentence_id_sentences_id_fk": {"name": "elements_sentence_id_sentences_id_fk", "tableFrom": "elements", "tableTo": "sentences", "columnsFrom": ["sentence_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"elements_id": {"name": "elements_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "phonetics": {"name": "phonetics", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "word": {"name": "word", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "uk": {"name": "uk", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "('')"}, "us": {"name": "us", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "('')"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"word_idx": {"name": "word_idx", "columns": ["word"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"phonetics_id": {"name": "phonetics_id", "columns": ["id"]}}, "uniqueConstraints": {"phonetics_word_unique": {"name": "phonetics_word_unique", "columns": ["word"]}}, "checkConstraint": {}}, "sentences": {"name": "sentences", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tree": {"name": "tree", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "granularity": {"name": "granularity", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "learning_content": {"name": "learning_content", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "chinese": {"name": "chinese", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "('')"}, "part_of_speech": {"name": "part_of_speech", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "position": {"name": "position", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "course_id": {"name": "course_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"course_id_idx": {"name": "course_id_idx", "columns": ["course_id"], "isUnique": false}}, "foreignKeys": {"sentences_course_id_courses_id_fk": {"name": "sentences_course_id_courses_id_fk", "tableFrom": "sentences", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"sentences_id": {"name": "sentences_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}