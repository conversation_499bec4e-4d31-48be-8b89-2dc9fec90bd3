{"version": "5", "dialect": "mysql", "id": "ff8b8e7f-1dd8-4ae0-a070-b7079b31fa3a", "prevId": "6d36dc83-6643-4846-96a5-e13acdeac89d", "tables": {"course_pack_categories": {"name": "course_pack_categories", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sort_order": {"name": "sort_order", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"value_idx": {"name": "value_idx", "columns": ["value"], "isUnique": false}, "is_active_idx": {"name": "is_active_idx", "columns": ["is_active"], "isUnique": false}, "sort_order_idx": {"name": "sort_order_idx", "columns": ["sort_order"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"course_pack_categories_id": {"name": "course_pack_categories_id", "columns": ["id"]}}, "uniqueConstraints": {"course_pack_categories_value_unique": {"name": "course_pack_categories_value_unique", "columns": ["value"]}}, "checkConstraint": {}}, "courses": {"name": "courses", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "cover": {"name": "cover", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "video": {"name": "video", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "course_pack_id": {"name": "course_pack_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "game_id": {"name": "game_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "position": {"name": "position", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"courses_course_pack_id_course_packs_id_fk": {"name": "courses_course_pack_id_course_packs_id_fk", "tableFrom": "courses", "tableTo": "course_packs", "columnsFrom": ["course_pack_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"courses_id": {"name": "courses_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "course_packs": {"name": "course_packs", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "is_free": {"name": "is_free", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "cover": {"name": "cover", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "game_id": {"name": "game_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "share_level": {"name": "share_level", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'private'"}, "course_pack_category_id": {"name": "course_pack_category_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false, "autoincrement": false}, "position": {"name": "position", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "is_pinned": {"name": "is_pinned", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"course_packs_course_pack_category_id_course_pack_categories_id_fk": {"name": "course_packs_course_pack_category_id_course_pack_categories_id_fk", "tableFrom": "course_packs", "tableTo": "course_pack_categories", "columnsFrom": ["course_pack_category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"course_packs_id": {"name": "course_packs_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "currency_transactions": {"name": "currency_transactions", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency_type": {"name": "currency_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'diamond'"}, "amount": {"name": "amount", "type": "bigint", "primaryKey": false, "notNull": true, "autoincrement": false}, "transaction_type": {"name": "transaction_type", "type": "enum('PURCHASE','REWARD','CONSUME','TRANSFER','SYSTEM')", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"currency_transactions_id": {"name": "currency_transactions_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "elements": {"name": "elements", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "english": {"name": "english", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "chinese": {"name": "chinese", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "phonetic": {"name": "phonetic", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "position": {"name": "position", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "game_id": {"name": "game_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "''"}, "sentence_id": {"name": "sentence_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"sentence_id_idx": {"name": "sentence_id_idx", "columns": ["sentence_id"], "isUnique": false}}, "foreignKeys": {"elements_sentence_id_sentences_id_fk": {"name": "elements_sentence_id_sentences_id_fk", "tableFrom": "elements", "tableTo": "sentences", "columnsFrom": ["sentence_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"elements_id": {"name": "elements_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "inbox_items": {"name": "inbox_items", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "remark": {"name": "remark", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "('')"}, "source_url": {"name": "source_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'text'"}, "tags": {"name": "tags", "type": "json", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(JSON_ARRAY())"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"user_id_idx": {"name": "user_id_idx", "columns": ["user_id"], "isUnique": false}, "type_idx": {"name": "type_idx", "columns": ["type"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"inbox_items_id": {"name": "inbox_items_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "phonetics": {"name": "phonetics", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "word": {"name": "word", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "uk": {"name": "uk", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "us": {"name": "us", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"word_idx": {"name": "word_idx", "columns": ["word"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"phonetics_id": {"name": "phonetics_id", "columns": ["id"]}}, "uniqueConstraints": {"phonetics_word_unique": {"name": "phonetics_word_unique", "columns": ["word"]}}, "checkConstraint": {}}, "sentences": {"name": "sentences", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "learning_content": {"name": "learning_content", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "word_details": {"name": "word_details", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "dependency_analysis": {"name": "dependency_analysis", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "chinese": {"name": "chinese", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "english": {"name": "english", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "('')"}, "position": {"name": "position", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "course_id": {"name": "course_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"course_id_idx": {"name": "course_id_idx", "columns": ["course_id"], "isUnique": false}}, "foreignKeys": {"sentences_course_id_courses_id_fk": {"name": "sentences_course_id_courses_id_fk", "tableFrom": "sentences", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"sentences_id": {"name": "sentences_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "token_usage": {"name": "token_usage", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "prompt_tokens": {"name": "prompt_tokens", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "completion_tokens": {"name": "completion_tokens", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "total_tokens": {"name": "total_tokens", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "year": {"name": "year", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "month": {"name": "month", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"user_model_month_idx": {"name": "user_model_month_idx", "columns": ["user_id", "model", "year", "month"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {"token_usage_id": {"name": "token_usage_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "user_wallets": {"name": "user_wallets", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "currency_type": {"name": "currency_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'diamond'"}, "balance": {"name": "balance", "type": "bigint", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"user_wallets_user_id_currency_type_pk": {"name": "user_wallets_user_id_currency_type_pk", "columns": ["user_id", "currency_type"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "task_token_usage": {"name": "task_token_usage", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": true, "autoincrement": false}, "task_type": {"name": "task_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "prompt_tokens": {"name": "prompt_tokens", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "completion_tokens": {"name": "completion_tokens", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "total_tokens": {"name": "total_tokens", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "year": {"name": "year", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "month": {"name": "month", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"model_idx": {"name": "model_idx", "columns": ["model"], "isUnique": false}, "year_month_idx": {"name": "year_month_idx", "columns": ["year", "month"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"task_token_usage_id": {"name": "task_token_usage_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}