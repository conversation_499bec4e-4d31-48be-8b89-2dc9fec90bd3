CREATE TABLE `token_usage` (
	`id` varchar(128) NOT NULL,
	`user_id` varchar(255) NOT NULL,
	`model` varchar(50) NOT NULL,
	`prompt_tokens` int NOT NULL,
	`completion_tokens` int NOT NULL,
	`total_tokens` int NOT NULL,
	`year` int NOT NULL,
	`month` int NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `token_usage_id` PRIMARY KEY(`id`),
	CONSTRAINT `user_model_month_idx` UNIQUE(`user_id`,`model`,`year`,`month`)
);
