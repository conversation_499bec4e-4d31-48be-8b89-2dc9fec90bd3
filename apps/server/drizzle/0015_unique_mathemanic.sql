ALTER TABLE `elements` DROP FOREIGN KEY `elements_image_id_images_id_fk`;
--> statement-breakpoint
ALTER TABLE `text_image_links` DROP FOREIGN KEY `text_image_links_image_id_images_id_fk`;
--> statement-breakpoint
ALTER TABLE `elements` MODIFY COLUMN `image_id` varchar(128) DEFAULT NULL;--> statement-breakpoint
ALTER TABLE `elements` ADD CONSTRAINT `elements_image_id_images_id_fk` FOREIGN KEY (`image_id`) REFERENCES `images`(`id`) ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `text_image_links` ADD CONSTRAINT `text_image_links_image_id_images_id_fk` FOREIGN KEY (`image_id`) REFERENCES `images`(`id`) ON DELETE cascade ON UPDATE no action;