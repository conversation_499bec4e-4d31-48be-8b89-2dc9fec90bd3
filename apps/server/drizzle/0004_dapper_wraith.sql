CREATE TABLE `inbox_items` (
	`id` varchar(128) NOT NULL,
	`user_id` varchar(128) NOT NULL,
	`content` text NOT NULL,
	`remark` text DEFAULT (''),
	`source_url` text NOT NULL,
	`type` varchar(32) NOT NULL DEFAULT 'text',
	`tags` json NOT NULL DEFAULT (JSON_ARRAY()),
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `inbox_items_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
ALTER TABLE `courses` ADD `game_id` varchar(128) DEFAULT '' NOT NULL;--> statement-breakpoint
ALTER TABLE `course_packs` ADD `game_id` varchar(128) DEFAULT '' NOT NULL;--> statement-breakpoint
ALTER TABLE `elements` ADD `game_id` varchar(128) DEFAULT '' NOT NULL;--> statement-breakpoint
CREATE INDEX `user_id_idx` ON `inbox_items` (`user_id`);--> statement-breakpoint
CREATE INDEX `type_idx` ON `inbox_items` (`type`);--> statement-breakpoint
ALTER TABLE `courses` DROP COLUMN `publish_course_id`;--> statement-breakpoint
ALTER TABLE `courses` DROP COLUMN `published_md5`;--> statement-breakpoint
ALTER TABLE `course_packs` DROP COLUMN `is_published`;--> statement-breakpoint
ALTER TABLE `course_packs` DROP COLUMN `published_course_pack_id`;--> statement-breakpoint
ALTER TABLE `course_packs` DROP COLUMN `published_md5`;