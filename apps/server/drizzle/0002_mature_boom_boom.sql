ALTER TABLE `courses` MODIFY COLUMN `title` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `courses` MODIFY COLUMN `description` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `courses` MODIFY COLUMN `cover` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `courses` MODIFY COLUMN `video` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `courses` MODIFY COLUMN `publish_course_id` varchar(128) NOT NULL DEFAULT '';--> statement-breakpoint
ALTER TABLE `courses` MODIFY COLUMN `published_md5` varchar(128) NOT NULL DEFAULT '';--> statement-breakpoint
ALTER TABLE `course_packs` MODIFY COLUMN `title` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `course_packs` MODIFY COLUMN `description` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `course_packs` MODIFY COLUMN `is_free` boolean NOT NULL DEFAULT true;--> statement-breakpoint
ALTER TABLE `course_packs` MODIFY COLUMN `cover` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `course_packs` MODIFY COLUMN `is_published` boolean NOT NULL;--> statement-breakpoint
ALTER TABLE `course_packs` MODIFY COLUMN `published_course_pack_id` varchar(128) NOT NULL DEFAULT '';--> statement-breakpoint
ALTER TABLE `course_packs` MODIFY COLUMN `published_md5` varchar(128) NOT NULL DEFAULT '';--> statement-breakpoint
ALTER TABLE `elements` MODIFY COLUMN `english` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `elements` MODIFY COLUMN `chinese` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `elements` MODIFY COLUMN `phonetic` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `elements` MODIFY COLUMN `part_of_speech` varchar(50) NOT NULL DEFAULT '';--> statement-breakpoint
ALTER TABLE `elements` MODIFY COLUMN `type` varchar(50) NOT NULL DEFAULT '';--> statement-breakpoint
ALTER TABLE `phonetics` MODIFY COLUMN `uk` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `phonetics` MODIFY COLUMN `us` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `sentences` MODIFY COLUMN `content` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `sentences` MODIFY COLUMN `chinese` text NOT NULL DEFAULT ('');--> statement-breakpoint
ALTER TABLE `sentences` DROP COLUMN `granularity`;--> statement-breakpoint
ALTER TABLE `sentences` DROP COLUMN `part_of_speech`;