CREATE TABLE `courses` (
	`id` varchar(128) NOT NULL,
	`title` text NOT NULL,
	`description` text DEFAULT (''),
	`cover` text,
	`video` text,
	`course_pack_id` varchar(128) NOT NULL,
	`publish_course_id` varchar(128) DEFAULT '',
	`published_md5` varchar(128) DEFAULT '',
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `courses_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `course_packs` (
	`id` varchar(128) NOT NULL,
	`user_id` varchar(128) NOT NULL,
	`title` text NOT NULL,
	`description` text DEFAULT (''),
	`is_free` boolean NOT NULL,
	`cover` text,
	`is_published` boolean DEFAULT false,
	`published_course_pack_id` varchar(128) DEFAULT '',
	`published_md5` varchar(128) DEFAULT '',
	`share_level` varchar(32) NOT NULL DEFAULT 'private',
	`position` int NOT NULL DEFAULT 0,
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `course_packs_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `elements` (
	`id` varchar(128) NOT NULL,
	`content` text NOT NULL,
	`english` text DEFAULT (''),
	`chinese` text DEFAULT (''),
	`phonetic` text DEFAULT (''),
	`part_of_speech` varchar(50) DEFAULT '',
	`type` varchar(50) DEFAULT '',
	`sentence_id` varchar(128) NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `elements_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `phonetics` (
	`id` varchar(128) NOT NULL,
	`word` varchar(255) NOT NULL,
	`uk` text DEFAULT (''),
	`us` text DEFAULT (''),
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `phonetics_id` PRIMARY KEY(`id`),
	CONSTRAINT `phonetics_word_unique` UNIQUE(`word`)
);
--> statement-breakpoint
CREATE TABLE `sentences` (
	`id` varchar(128) NOT NULL,
	`content` text NOT NULL,
	`tree` json,
	`granularity` int DEFAULT 0,
	`learning_content` json,
	`chinese` text DEFAULT (''),
	`part_of_speech` json,
	`course_id` varchar(128) NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	`updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	CONSTRAINT `sentences_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
ALTER TABLE `courses` ADD CONSTRAINT `courses_course_pack_id_course_packs_id_fk` FOREIGN KEY (`course_pack_id`) REFERENCES `course_packs`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `elements` ADD CONSTRAINT `elements_sentence_id_sentences_id_fk` FOREIGN KEY (`sentence_id`) REFERENCES `sentences`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `sentences` ADD CONSTRAINT `sentences_course_id_courses_id_fk` FOREIGN KEY (`course_id`) REFERENCES `courses`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX `sentence_id_idx` ON `elements` (`sentence_id`);--> statement-breakpoint
CREATE INDEX `word_idx` ON `phonetics` (`word`);--> statement-breakpoint
CREATE INDEX `course_id_idx` ON `sentences` (`course_id`);