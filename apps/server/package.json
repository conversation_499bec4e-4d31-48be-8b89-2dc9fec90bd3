{"name": "server", "type": "module", "version": "1.0.62", "description": "", "author": "", "license": "ISC", "keywords": [], "main": "dist/main.js", "types": "dist/main.d.ts", "scripts": {"test": "vitest --no-file-parallelism ", "dev": "nodemon", "start": "cross-env NODE_ENV=production node dist/main.js", "build": "tsup", "db:init": "cross-env NODE_ENV=dev drizzle-kit push", "db:init:test": "cross-env NODE_ENV=test drizzle-kit push", "db:migrate": "npx drizzle-kit migrate", "db:studio": "npx drizzle-kit studio", "db:push": "npx drizzle-kit push", "db:generate": "npx drizzle-kit generate"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.817.0", "@fastify/cors": "^10.0.1", "@fastify/multipart": "^9.0.3", "@fastify/mysql": "^5.0.1", "@fastify/redis": "^7.0.1", "@fastify/request-context": "^6.0.1", "@langchain/core": "^0.2.6", "@langchain/openai": "^0.1.2", "@paralleldrive/cuid2": "2.2.2", "@trpc/server": "^10.45.2", "@volcengine/openapi": "^1.20.0", "axios": "^1.6.6", "compromise": "^14.13.0", "cos-nodejs-sdk-v5": "^2.14.1", "date-fns": "^4.1.0", "decimal.js": "^10.5.0", "dotenv": "^16.3.1", "drizzle-orm": "^0.35.1", "fastify": "^5.0.0", "ioredis": "^5.3.2", "jose": "^5.2.4", "lodash-es": "^4.17.21", "module-alias": "^2.2.3", "mysql2": "^3.11.3", "node-cron": "^3.0.3", "ofetch": "^1.4.1", "openai": "^5.0.1", "pino": "^9.5.0", "pino-roll": "^3.1.0", "qcloud-cos-sts": "^3.1.1", "superjson": "^2.2.1", "zod": "^3.22.4"}, "devDependencies": {"@julebu/shared": "workspace:*", "@types/lodash-es": "^4.17.12", "@types/module-alias": "^2.0.4", "@types/node": "^22.7.5", "@types/node-cron": "^3.0.11", "cross-env": "^7.0.3", "drizzle-kit": "0.26.2", "nodemon": "^3.1.7", "pino-pretty": "^11.3.0", "ts-node": "^10.9.1", "tsc-watch": "^6.2.0", "tsup": "^8.0.1", "typescript": "^5.6.3", "vite-tsconfig-paths": "^5.0.1", "vitest": "^2.1.3"}}