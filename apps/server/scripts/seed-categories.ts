import mysql from 'mysql2/promise'
import { getDB, setupDB } from '../src/db'
import { coursePackCategory } from '../src/db/schema'

const categories = [
  {
    id: 'ksknbziv9yuv94dwmnoba4jk',
    label: '中小学同步', // name -> label
    value: 'k12-sync', // key -> value
    description: '小学、初中、高中应试与同步辅导',
    sortOrder: 1, // order -> sortOrder
    isActive: true, // status -> isActive (boolean)
  },
  {
    id: 'byhch2xbjmpo7mnxctr3ubba',
    label: '大学四六级',
    value: 'cet',
    description: '大学英语四六级考试备考',
    sortOrder: 2,
    isActive: true,
  },
  {
    id: 'e8onn4oegcesk4qtjcj57ag7',
    label: '考研',
    value: 'postgraduate',
    description: '考研英语一二备考指导',
    sortOrder: 3,
    isActive: true,
  },
  {
    id: 'qjzzlv4dv536073nj9j7jfbe',
    label: '雅思托福备考',
    value: 'ielts-toefl',
    description: '出国留学必备考试准备',
    sortOrder: 4,
    isActive: true,
  },
  {
    id: 'm2c4cmxgim7h69yhmdhaf1zt',
    label: '日常实用口语',
    value: 'daily-speaking',
    description: '发音矫正、生活对话实用技能',
    sortOrder: 5,
    isActive: true,
  },
  {
    id: 'ujl3bsjiw41pr54u3of5iu1k',
    label: '旅行',
    value: 'travel',
    description: '旅行场景专用英语表达',
    sortOrder: 6,
    isActive: true,
  },
  {
    id: 'ntrf0c7tpx9lxvofxdfxsp27',
    label: '商务职场',
    value: 'business',
    description: '面试英语、BEC备考、职场沟通',
    sortOrder: 7,
    isActive: true,
  },
  {
    id: 'mgvlsrr3itpjck16w5c6mota',
    label: '经典教材',
    value: 'classic-textbook',
    description: '新概念、剑桥英语等经典教材',
    sortOrder: 8,
    isActive: true,
  },
  {
    id: 'j763f6hd9xvw8gkborjltnuo',
    label: '电影与故事',
    value: 'media-story',
    description: '通过电影、美剧、故事趣味学习',
    sortOrder: 9,
    isActive: true,
  },
  {
    id: 'k9m2p4n8v7x5y3z1q28dk2is',
    label: '音乐',
    value: 'music',
    description: '听音乐学英语',
    sortOrder: 10,
    isActive: true,
  },
]

async function seedCategories() {
  let pool: mysql.Pool | null = null

  try {
    // 初始化数据库连接
    pool = mysql.createPool({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'password',
      database: process.env.DB_NAME || 'julebu_editor_dev',
      port: Number.parseInt(process.env.DB_PORT || '3408'),
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
    })

    await setupDB(pool)
    const db = getDB()

    console.log('开始插入分类数据...')

    for (const categoryData of categories) {
      await db.insert(coursePackCategory).values(categoryData).onDuplicateKeyUpdate({
        set: {
          label: categoryData.label,
          description: categoryData.description,
          sortOrder: categoryData.sortOrder,
          isActive: categoryData.isActive,
        },
      })
      console.log(`插入分类: ${categoryData.label}`)
    }

    console.log('分类数据插入完成！')
  } catch (error) {
    console.error('插入分类数据失败:', error)
    throw error
  } finally {
    if (pool) {
      await pool.end()
    }
  }
}

// 直接运行脚本
seedCategories()
  .then(() => {
    console.log('种子数据插入成功')
    process.exit(0)
  })
  .catch((error) => {
    console.error('种子数据插入失败:', error)
    process.exit(1)
  })

export { seedCategories }
