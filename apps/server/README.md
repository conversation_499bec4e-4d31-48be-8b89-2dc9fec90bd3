### 目录结构

apps/server/
├── src/
│ ├── main.ts
│ ├── app.module.ts
│ ├── common/
│ │ └── filters/
│ │ └── guards/
│ │ └── interceptors/
│ │ └── pipes/
│ ├── config/
│ │ └── configuration.ts
│ ├── modules/
│ │ ├── users/
│ │ │ ├── dto/
│ │ │ ├── entities/
│ │ │ ├── users.controller.ts
│ │ │ ├── users.module.ts
│ │ │ ├── users.service.ts
│ │ │ └── users.router.ts
│ │ └── other-modules/
│ ├── shared/
│ │ └── interfaces/
│ │ └── utils/
│ └── trpc/
│ ├── trpc.module.ts
│ └── trpc.router.ts
├── test/
├── package.json
└── tsconfig.json

这个目录结构的设计理念主要基于 NestJS 的模块化和分层架构思想，同时结合了 tRPC 的特性。让我为您详细解释一下：
模块化结构：
主要体现在 src/modules 目录中。每个功能模块（如用户模块）都有自己的子目录，包含了该模块的所有相关文件。这种结构使得代码组织更清晰，便于维护和扩展。2. 分层架构：
虽然使用了 tRPC，但仍然保留了类似 MVC 的分层思想。例如，在用户模块中，我们可以看到：
users.router.ts：相当于控制器层，处理请求路由。
users.service.ts：可以添加此文件来处理业务逻辑。
entities 目录：可以用来存放数据模型。
dto 目录：用于定义数据传输对象。
中央配置：
src/config 目录用于存放全局配置文件，使配置管理更加集中和易于维护。
共享资源：
src/shared 目录用于存放可以在多个模块间共享的接口、工具函数等。
全局中间件和过滤器：
src/common 目录用于存放全局使用的中间件、过滤器、拦截器等，这些可以应用于整个应用程序。
tRPC 集成：
src/trpc 目录专门用于 tRPC 相关的配置和路由聚合。这里集中管理了 tRPC 的上下文和主路由器。
入口文件：
src/main.ts 作为应用程序的入口点，负责启动服务器和应用全局配置。
测试友好：
test 目录用于存放测试文件，与源代码分开管理。
这种结构的主要优点包括：
高度模块化，便于团队协作和代码管理。
清晰的关注点分离，每个文件都有明确的职责。
易于扩展，添加新功能只需创建新模块。
结合了 NestJS 的最佳实践和 tRPC 的优势。
在实际应用中，您可以根据 apps/server/src/main.ts 文件（行 1-26）的示例，将 Fastify 和 tRPC 的配置整合到这个新的结构中。同时，可以参考 apps/server/src/router/index.ts（行 1-30）来组织各个模块的路由。
这种结构为大型应用提供了良好的可扩展性和可维护性，同时也适用于较小的项目，因为它提供了一个清晰的组织框架，可以随项目的增长而轻松扩展。

### 开发要点

1. 在 router 里面不可以使用别名，不然 client 共享不到类型


### 容器化

- [ ] 后续需要改成基于 docker 部署

在 docker 部署的时候需要处理 logs
到时候参考 audio-server 的 logs 的做法即可
