import { resolve } from 'node:path'
import { defineConfig } from 'tsup'

export default defineConfig({
  entry: ['src/main.ts'],
  outDir: 'dist',
  splitting: true,
  sourcemap: false,
  clean: true,
  format: ['esm'],
  dts: true,
  noExternal: ['@julebu/shared'],
  tsconfig: resolve(__dirname, './tsconfig.json'),
  esbuildOptions(options) {
    options.alias = {
      '@': resolve(__dirname, './src'),
    }
  },
})
