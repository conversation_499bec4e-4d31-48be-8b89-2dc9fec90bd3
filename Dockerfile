# 基础镜像
FROM node:22.4.0-bookworm-slim AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN npm install -g pnpm@9.3.0 && \
    pnpm config set registry https://registry.npmmirror.com

# 构建阶段
FROM base AS build
WORKDIR /workspace

# 复制package.json和pnpm相关文件以利用缓存层
COPY pnpm-workspace.yaml pnpm-lock.yaml package.json ./
COPY apps/server/package.json ./apps/server/
COPY packages/shared/package.json ./packages/shared/

# 安装所有依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY packages/shared ./packages/shared
COPY apps/server ./apps/server
COPY tsconfig.json ./

# 先构建shared包
RUN cd packages/shared && pnpm build

# 再构建server
RUN cd apps/server && pnpm build

# 使用pnpm deploy创建生产环境部署包
RUN pnpm deploy --filter=server --prod /prod/server

# 生产环境镜像
FROM base AS production
WORKDIR /app

# 安装运行时依赖
RUN apt-get update && apt-get install -y curl && \
    rm -rf /var/lib/apt/lists/*

# 安装pm2并配置
RUN npm install -g pm2@latest && \
    pm2 install pm2-logrotate && \
    pm2 set pm2-logrotate:max_size 50M && \
    pm2 set pm2-logrotate:compress true && \
    pm2 set pm2-logrotate:retain 5

# 复制应用代码
COPY --from=build /prod/server /app

# 暴露端口
EXPOSE 3400

# 健康检查
HEALTHCHECK --interval=30s --timeout=5s --retries=3 \
  CMD curl -f http://localhost:3400/health || exit 1

# 使用pm2启动应用，直接通过命令行参数配置
CMD ["pm2-runtime", "dist/main.js", "--instances", "8", "--exec-mode", "cluster", "--max-memory-restart", "3G"]