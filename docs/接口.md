# 管理员接口文档

## 概述

本文档描述了管理员相关的所有 API 接口，包括课程包管理、课程管理等功能。

## 接口地址

在 .env 文件中配置 admin baseUrl，接口地址就不需要 /api 了

```bash
# admin baseUrl
ADMIN_BASE_URL="http://localhost:3400/api"
```

## 管理员权限校验

所有 /api/admin/ 路径的接口都需要在请求头中携带 `x-admin-secret`，否则无法访问。

- 传递方式：
  - HTTP Header: `x-admin-secret: <你的管理员密钥>`
- 示例：
  ```http
  GET /api/admin/course-packs?userId=xxx HTTP/1.1
  Host: example.com
  x-admin-secret: your-admin-secret
  ```

## 接口概览

| 分类       | 接口名称                     | HTTP方法 | 接口地址                                                         | 描述                                   |
| ---------- | ---------------------------- | -------- | ---------------------------------------------------------------- | -------------------------------------- |
| 课程包管理 | 批量创建课程包               | POST     | `/api/admin/course-packs/batch`                                  | 批量创建课程包（包含课程和内容）       |
| 课程包管理 | 获取用户课程包列表           | GET      | `/api/admin/course-packs`                                        | 获取指定用户的课程包列表               |
| 课程包管理 | 批量处理课程包中所有课程     | POST     | `/api/admin/course-packs/process-all`                            | 批量处理（加工）课程包中所有课程的句子 |
| 课程包管理 | 批量拆分课程包中所有课程     | POST     | `/api/admin/course-packs/split-all`                              | 批量拆分课程包中所有课程的句子         |
| 课程管理   | 批量创建课程                 | POST     | `/api/admin/courses/batch`                                       | 在指定课程包下批量创建课程             |
| 课程管理   | 批量拆分课程内容             | POST     | `/api/admin/courses/split`                                       | 为指定课程添加句子和元素               |
| 课程管理   | 生成课程知识点讲解           | POST     | `/api/admin/courses/generate-learning-content`                   | 为指定课程生成知识点讲解内容           |
| 课程包管理 | 获取指定课程包下课程列表     | GET      | `/api/admin/course-packs/:coursePackId/courses`                  | 获取指定课程包下的课程列表             |
| 课程管理   | 获取课程详情（含statements） | GET      | `/api/admin/course-packs/:coursePackId/courses/:courseId/detail` | 获取课程详情，包含所有statements       |
| 句子管理   | 批量更新句子内容             | tRPC     | `sentence.changeMultipleContents`                                | 批量更新多个句子的英文内容             |
| 句子管理   | 批量更新句子中文翻译         | tRPC     | `sentence.changeMultipleChinese`                                 | 批量更新多个句子的中文翻译             |
| 句子管理   | 批量创建或更新句子           | POST     | `/api/admin/sentences/batch`                                     | 管理端批量创建或更新句子（支持同时传递多个字段） |

## 基础响应格式

所有接口都遵循统一的响应格式：

```typescript
interface BaseResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}
```

## 1. 课程包管理接口

### 1.1 批量创建课程包

**接口地址：** `POST /api/admin/course-packs/batch`

**描述：** 批量创建课程包（包含课程和内容）

**入参：**

```typescript
interface BatchCoursePackRequest {
  coursePacks: Array<{
    title: string
    description: string
    cover?: string
    categoryId?: string
    shareLevel?: CoursePackShareLevel
    courses?: Array<{
      title: string
      description: string
      sentences?: Array<{
        content: string
        translation: string
        elements?: Array<{
          content: string
          translation: string
          phonetic?: string
        }>
      }>
    }>
  }>
  userId: string
}
```

**出参：**

```typescript
interface BatchCreateResponse {
  success: boolean
  message: string // "成功创建 X 个课程包"
  data: Array<{
    coursePackId: string
    title: string
    cover: string
    usedDefaultCover: boolean // 是否使用了默认封面
    courses: Array<{
      courseId: string
      title: string
      sentenceCount: number
      elementCount: number
    }>
  }>
}
```

### 1.2 获取用户课程包列表

**接口地址：** `GET /api/admin/course-packs`

**描述：** 获取指定用户的课程包列表

**入参：** Query 参数

```typescript
interface GetCoursePacksQuery {
  userId: string
}
```

**出参：**

```typescript
interface GetCoursePacksResponse {
  success: boolean
  message: string
  data: Array<{
    id: string
    title: string
    description: string
    isFree: boolean
    cover: string
    createdAt: string
    updatedAt: string
    gameId: string
    shareLevel: CoursePackShareLevel
    categoryId?: string
    isPinned: boolean
    position: number
  }>
}
```

### 1.3 获取指定课程包下课程列表

**接口地址：** `GET /api/admin/course-packs/:coursePackId/courses`

**描述：** 获取指定课程包下的课程包详情及课程列表（返回完整课程包结构，含 courses 字段）

**入参：**

- 路径参数：
  - `coursePackId` (string) 课程包ID
- Query参数：
  - `userId` (string) 用户ID

**出参：**

```typescript
interface CoursePackWithCourses {
  id: string
  title: string
  description: string
  isFree: boolean
  cover: string
  createdAt: string
  updatedAt: string
  gameId: string
  shareLevel: 'private' | 'public' | 'shared'
  categoryId?: string
  isPinned: boolean
  position: number
  courses: Array<{
    id: string
    title: string
    description: string
    createdAt: string
    updatedAt: string
    gameId: string
    position: number
  }>
}
```

**示例：**

```http
GET /api/admin/course-packs/123456/courses?userId=abcde HTTP/1.1
Host: example.com
x-admin-secret: your-admin-secret
```

**返回：**

```json
{
  "success": true,
  "message": "获取课程列表成功",
  "data": {
    "id": "coursePackId",
    "title": "课程包标题",
    "description": "描述",
    "isFree": true,
    "cover": "https://...",
    "createdAt": "2024-06-01T12:00:00.000Z",
    "updatedAt": "2024-06-01T12:00:00.000Z",
    "gameId": "gameId",
    "shareLevel": "public",
    "categoryId": null,
    "isPinned": false,
    "position": 1,
    "courses": [
      {
        "id": "course1",
        "title": "课程1",
        "description": "描述1",
        "createdAt": "2024-06-01T12:00:00.000Z",
        "updatedAt": "2024-06-01T12:00:00.000Z",
        "gameId": "gameId",
        "position": 1
      }
    ]
  }
}
```

### 1.4 批量处理课程包中所有课程

**接口地址：** `POST /api/admin/course-packs/process-all`

**描述：** 批量处理（加工）课程包中所有课程的句子

**入参：**

```typescript
interface ProcessAllCoursesRequest {
  coursePackId: string
  userId: string
}
```

**出参：**

```typescript
interface ProcessAllCoursesResponse {
  success: boolean
  message: string // "成功处理 X 个课程，共加工 Y 个句子"
  data: {
    coursePackId: string
    totalCourses: number
    totalProcessedCount: number
    results: Array<{
      courseId: string
      courseTitle: string
      processedCount: number
      success: boolean
      error?: string
    }>
  }
}
```

### 1.5 批量拆分课程包中所有课程

**接口地址：** `POST /api/admin/course-packs/split-all`

**描述：** 批量拆分课程包中所有课程的句子

**入参：**

```typescript
interface SplitAllCoursesRequest {
  userId: string
  coursePackId: string
}
```

**出参：**

```typescript
interface SplitAllCoursesResponse {
  success: boolean
  message: string // "批量拆分完成"
  data: {
    coursePackId: string
    totalCourses: number
    totalSentenceCount: number
    totalElementCount: number
    results: Array<{
      courseId: string
      courseTitle: string
      sentenceCount: number
      elementCount: number
      success: boolean
      error?: string
    }>
  }
}
```

## 2. 课程管理接口

### 2.1 批量创建课程

**接口地址：** `POST /api/admin/courses/batch`

**描述：** 在指定课程包下批量创建课程

**入参：**

```typescript
interface BatchCourseRequest {
  coursePackId: string
  courses: Array<{
    title: string
    description: string
    sentences?: Array<{
      content: string
      translation: string
      elements?: Array<{
        content: string
        translation: string
        phonetic?: string
      }>
    }>
  }>
  userId: string
}
```

**出参：**

```typescript
interface BatchCreateCoursesResponse {
  success: boolean
  message: string // "成功创建 X 个课程"
  data: Array<{
    courseId: string
    title: string
    sentenceCount: number
    elementCount: number
  }>
}
```

### 2.2 批量拆分课程内容

**接口地址：** `POST /api/admin/courses/split`

**描述：** 为指定课程添加句子和元素

**入参：**

```typescript
interface BatchSplitRequest {
  courseId: string
  sentences: Array<{
    content: string
    translation: string
    elements?: Array<{
      content: string
      translation: string
      phonetic?: string
    }>
  }>
  userId: string
}
```

**出参：**

```typescript
interface BatchSplitResponse {
  success: boolean
  message: string // "成功为课程添加 X 个句子和 Y 个元素"
  data: {
    courseId: string
    sentenceCount: number
    elementCount: number
  }
}
```

### 2.3 生成课程知识点讲解

**接口地址：** `POST /api/admin/courses/generate-learning-content`

**描述：** 为指定课程生成知识点讲解内容

**入参：**

```typescript
interface GenerateLearningContentRequest {
  userId: string
  courseId: string
}
```

**出参：**

```typescript
interface GenerateLearningContentResponse {
  success: boolean
  message: string // "知识点讲解生成完成"
  data: {
    courseId: string
    totalSentences: number
    successCount: number
    failedCount: number
    failedSentenceIds: string[]
    results: Array<{
      id: string
      learningContent: string | null
    }>
  }
}
```

### 2.4 获取课程详情（含 statements）

**接口地址：** `GET /api/admin/course-packs/:coursePackId/courses/:courseId/detail`

**描述：** 根据课程包ID和课程ID获取课程详情，包含所有 statements（即 elements），用于课程内容的详细展示。

**入参：**

- 路径参数：
  - `coursePackId` (string) 课程包ID
  - `courseId` (string) 课程ID
- Query参数：
  - `userId` (string) 用户ID

**出参：**

```typescript
interface GetCourseDetailWithStatementsResponse {
  success: boolean
  message: string
  data: {
    id: string
    title: string
    description: string
    video: string
    order: number
    coursePackId: string
    type: string
    mediaUrl: string
    statements: Array<{
      id: string
      order: number
      chinese: string
      english: string
      soundmark: string
      type: string
      sentenceId: string
      image: {
        width: number
        height: number
        fileKey: string
        description: string
      } | null
      startTime: number
      endTime: number
    }>
    sentences: any[]
    statementId: string
  }
}
```

**示例：**

```http
GET /api/admin/course-packs/b8zvwqps7i1xcpg1djg6xql0/courses/f6fv274y8i0ojst8628o0gj4/detail?userId=user123 HTTP/1.1
Host: example.com
x-admin-secret: your-admin-secret
```

**返回：**

```json
{
  "success": true,
  "message": "获取课程详情成功",
  "data": {
    "id": "f6fv274y8i0ojst8628o0gj4",
    "title": "新青花瓷",
    "description": "",
    "video": "https://julebu-game-dev-assets.s3.bitiful.net/青花瓷%20(英文版)-罗艺恒..mp4",
    "order": 1,
    "coursePackId": "b8zvwqps7i1xcpg1djg6xql0",
    "type": "music",
    "mediaUrl": "https://julebu-game-dev-assets.s3.bitiful.net/青花瓷%20(英文版)-罗艺恒..mp4",
    "statements": [
      {
        "id": "wioyzss3i77mtp3gqvav90ub",
        "order": 1,
        "chinese": "你的爱抚宛如你执手画笔勾勒我肌肤色彩",
        "english": "Your touch like brush strokes you hold colour in my skin",
        "soundmark": "",
        "type": "",
        "sentenceId": "",
        "image": null,
        "startTime": 21.55,
        "endTime": 23.05
      },
      {
        "id": "hvelbdf6fid36iwt876qcsqp",
        "order": 2,
        "chinese": "你如传世的青花瓷自顾自美丽",
        "english": "The deep blue and pearl white glow like porcelain",
        "soundmark": "",
        "type": "",
        "sentenceId": "",
        "image": null,
        "startTime": 23.05,
        "endTime": 24.55
      }
    ],
    "sentences": [],
    "statementId": ""
  }
}
```

## 3. 句子管理接口

### 3.1 批量更新句子内容

**接口地址：** `POST /api/trpc/sentence.changeMultipleContents`

**描述：** 批量更新多个句子的内容

**注意：** 此接口为 tRPC 接口，不是 REST 接口，使用时需要通过 tRPC 客户端调用

**入参：**

```typescript
interface ChangeMultipleContentsRequest {
  updates: Array<{
    sentenceId: string // 句子ID
    content: string // 新的句子内容
  }>
}
```

**出参：**

```typescript
interface ChangeMultipleContentsResponse {
  success: boolean // 操作是否成功
}
```

**功能说明：**

- 批量更新多个句子的英文内容
- 自动重新生成 wordDetails（单词详情）
- 自动重新生成 dependencyAnalysis（依存分析）
- 自动删除相关的元素（elements）
- 自动格式化英文内容

**使用示例：**

```typescript
// 使用 tRPC 客户端调用
const result = await $trpc.sentence.changeMultipleContents.mutate({
  updates: [
    {
      sentenceId: "sentence_id_1",
      content: "Hello, this is the new content for sentence 1"
    },
    {
      sentenceId: "sentence_id_2", 
      content: "This is the updated content for sentence 2"
    }
  ]
})
```

### 3.2 批量更新句子中文翻译

**接口地址：** `POST /api/trpc/sentence.changeMultipleChinese`

**描述：** 批量更新多个句子的中文翻译

**注意：** 此接口为 tRPC 接口，不是 REST 接口，使用时需要通过 tRPC 客户端调用

**入参：**

```typescript
interface ChangeMultipleChineseRequest {
  updates: Array<{
    sentenceId: string // 句子ID
    chinese: string // 新的中文翻译
  }>
}
```

**出参：**

```typescript
interface ChangeMultipleChineseResponse {
  success: boolean // 操作是否成功
}
```

**功能说明：**

- 批量更新多个句子的中文翻译
- 自动删除相关的元素（elements）
- 保持英文内容不变

**使用示例：**

```typescript
// 使用 tRPC 客户端调用
const result = await $trpc.sentence.changeMultipleChinese.mutate({
  updates: [
    {
      sentenceId: "sentence_id_1",
      chinese: "你好，这是句子1的新中文翻译"
    },
    {
      sentenceId: "sentence_id_2",
      chinese: "这是句子2的更新中文翻译"
    }
  ]
})
```

### 3.3 批量创建或更新句子（管理端）

**接口地址：** `POST /api/admin/sentences/batch`

**描述：** 管理端批量创建或更新句子，支持同时传递多个字段（content、english、chinese），智能判断创建或更新操作

**入参：**

```typescript
interface BatchUpsertSentencesRequest {
  courseId: string // 课程ID
  sentences: Array<{
    sentenceId?: string // 可选，如果提供则更新，否则创建
    content?: string    // 英文内容（会触发重新生成wordDetails等）
    english?: string    // 标准化英文内容（不会触发重新生成）
    chinese?: string    // 中文翻译
  }>
  userId: string // 用户ID
}
```

**出参：**

```typescript
interface BatchUpsertSentencesResponse {
  success: boolean
  message: string // "成功处理 X 个新句子，更新 Y 个句子"
  data: {
    created: number // 创建的句子数量
    updated: number // 更新的句子数量
    sentences: Array<{
      id: string
      operation: 'created' | 'updated'
    }>
  }
}
```

**功能说明：**

- **智能判断操作类型**：根据是否提供 `sentenceId` 判断是创建还是更新
- **支持多字段同时更新**：可以同时传递 content、english、chinese 字段
- **自动处理字段优先级**：
  - 创建时：优先使用 `content`，其次使用 `english`
  - 更新时：分别处理内容更新和翻译更新
- **批量操作**：在事务中执行，保证数据一致性
- **权限验证**：验证课程访问权限和句子归属

**字段说明：**

- `content` vs `english`：
  - `content`：触发完整的NLP处理（wordDetails、依存分析等）
  - `english`：仅更新标准化英文内容，不触发额外处理
- `sentenceId`：如果提供，验证句子存在性并进行更新；否则创建新句子

**使用示例：**

```typescript
// 创建新句子
const createResponse = await fetch('/api/admin/sentences/batch', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'x-admin-secret': 'your-secret'
  },
  body: JSON.stringify({
    courseId: "course123",
    userId: "user456",
    sentences: [
      {
        content: "Hello world",
        chinese: "你好世界"
      },
      {
        content: "How are you?",
        chinese: "你好吗？"
      }
    ]
  })
})

// 更新现有句子
const updateResponse = await fetch('/api/admin/sentences/batch', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'x-admin-secret': 'your-secret'
  },
  body: JSON.stringify({
    courseId: "course123",
    userId: "user456", 
    sentences: [
      {
        sentenceId: "sentence123",
        content: "Hello beautiful world",
        chinese: "你好美丽的世界"
      },
      {
        sentenceId: "sentence456",
        chinese: "你好吗，朋友？" // 仅更新中文翻译
      }
    ]
  })
})

// 混合操作（创建+更新）
const mixedResponse = await fetch('/api/admin/sentences/batch', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'x-admin-secret': 'your-secret'
  },
  body: JSON.stringify({
    courseId: "course123",
    userId: "user456",
    sentences: [
      {
        // 创建新句子
        content: "New sentence",
        chinese: "新句子"
      },
      {
        // 更新现有句子
        sentenceId: "existing123",
        content: "Updated sentence",
        chinese: "更新的句子"
      }
    ]
  })
})
```

## 4. 数据类型定义

### 4.1 课程包分享级别

```typescript
enum CoursePackShareLevel {
  PRIVATE = 'private',
  PUBLIC = 'public',
  SHARED = 'shared'
}
```

### 4.2 句子元素结构

```typescript
interface SentenceElement {
  content: string // 元素内容
  translation: string // 翻译
  phonetic?: string // 音标（可选）
}
```

### 4.3 句子结构

```typescript
interface Sentence {
  content: string // 句子内容
  translation: string // 翻译
  elements?: SentenceElement[] // 句子元素（可选）
}
```

### 4.4 课程结构

```typescript
interface Course {
  title: string // 课程标题
  description: string // 课程描述
  sentences?: Sentence[] // 句子列表（可选）
}
```

### 4.5 课程包结构

```typescript
interface CoursePack {
  title: string // 课程包标题
  description: string // 课程包描述
  cover?: string // 封面图片URL（可选）
  categoryId?: string // 分类ID（可选）
  shareLevel?: CoursePackShareLevel // 分享级别（可选）
  courses?: Course[] // 课程列表（可选）
}
```

## 5. 错误处理

所有接口在出现错误时都会返回统一的错误格式：

```typescript
interface ErrorResponse {
  success: false
  message: string
  error?: string
}
```

常见错误状态码：

- `400`: 请求参数错误
- `500`: 服务器内部错误

## 6. 注意事项

1. 所有接口都需要提供 `userId` 参数用于权限验证
2. 批量操作接口会返回详细的处理结果统计信息，包括成功和失败的详细信息
3. 所有接口都采用异步处理，大批量数据操作可能需要较长时间
4. 建议在调用批量操作接口前先验证数据格式的正确性
5. 创建课程包时如果未提供封面，系统会自动分配默认封面
6. 句子和元素的创建支持预定义数据，也支持自动拆分和处理
7. 知识点讲解生成功能依赖于 LLM 服务，可能存在生成失败的情况
8. **句子管理接口说明**：
   - tRPC 接口（`sentence.changeMultipleContents/changeMultipleChinese`）适用于前端直接调用
   - 管理端 REST 接口（`/api/admin/sentences/batch`）适用于后台管理和批量导入场景
   - 管理端接口支持同时传递多个字段，减少接口调用次数
   - `content` 字段会触发完整的 NLP 处理，`english` 字段仅更新标准化内容
