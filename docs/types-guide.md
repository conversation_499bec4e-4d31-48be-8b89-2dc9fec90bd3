# 项目类型处理开发文档 (Nuxt 3 + tRPC + Monorepo)

## 核心原则：端到端类型安全

本项目利用 tRPC 实现了从后端 API 到前端 UI 的端到端类型安全。这意味着后端 API 的输入（Input）和输出（Output）类型定义能够自动同步到前端，显著减少运行时错误，并提供极佳的开发体验（自动补全、编译时检查）。

## 类型流转与处理策略

类型处理遵循以下分层策略，确保代码的解耦、可维护性和类型安全。下文将详细介绍各层职责，并在**开发者指南**部分提供具体操作指导。

### 1. 后端应用 (`apps/server`) - 类型推断

*   **职责**: 定义 API 路由 (tRPC Routers) 和对应的 Resolver 函数。API 的"事实标准"(`AppRouter`) 在此确立。
*   **类型来源**:
    *   **输入 (Input)**: 使用从 `packages/shared` 导入的 Zod Schema 进行验证 (`.input(zodSchema)`)。
    *   **输出 (Output)**: Resolver 函数的**返回值类型**由 tRPC **自动推断**。后端代码实现直接决定输出类型。
*   **核心产物**: `AppRouter` 类型 (`export type AppRouter = typeof appRouter;`)。

### 2. 共享包 (`packages/shared`) - 类型提取、定义与共享

*   **职责**: 作为后端和前端之间的桥梁，共享**通用的代码和类型定义**。这包括：
    *   **核心实体类型** (如 `User`, `Element` 接口)
    *   **输入验证 Schema** (Zod)
    *   **枚举 (Enums)** 和 **常量**
    *   **API 输出类型** (通过 `inferRouterOutputs` 提取)
*   **类型处理**:
    1.  **定义共享内容**: 定义 Zod 输入验证 Schema (`src/schemas/`)、共享的数据结构（Interfaces, Enums, Types - 包括核心实体类型，通常在 `src/types/` 或按模块组织）和常量等。
    2.  **导入 `AppRouter` (构建时)**: 在**构建时**引用 `apps/server` 的 `AppRouter` 类型（通过 `devDependencies` 和 `import type`）。
    3.  **提取基础输出类型**: 使用 tRPC 提供的 `inferRouterOutputs` 工具类型，从 `AppRouter` 中提取出各个 API Procedure 对应的**原始、基础的输出类型**，并导出（通常在 `src/api-types.ts`）。
*   **运行时**: 编译后的 `shared` 包**不包含**任何来自 `apps/server` 的运行时代码。

### 3. 前端应用 (`apps/client`) - 类型消费与扩展

*   **职责**: 消费来自 `shared` 包的类型（包括核心实体类型和 API 输出类型），并在需要时进行扩展和映射。
*   **类型处理地点**:
    *   `types/`: 定义**前端特定**的类型或对从 `shared` 导入的类型进行**扩展**（使用交叉类型 `&`）。
    *   `composables/` (API 层): 进行数据获取和映射，将基础 API 输出类型转换为前端特定类型。
    *   业务逻辑 (Stores, Components): 使用最终的类型（可能是直接从 `shared` 导入的核心实体类型/API 输出类型，或是在 `client/types/` 中定义的扩展类型）。

---

## 开发者指南：如何定义和使用类型

根据类型的性质和用途，请在以下位置进行定义和使用：

### 1. API 输入验证 Schema (Zod Schemas)

*   **定义位置**: `packages/shared/src/schemas/`
*   **目的**: 定义 API 输入结构和验证规则。
*   **使用**: 后端 Router 使用 `.input()` 验证；前端可选用于表单验证。
*   **示例**:
    ```typescript
    // packages/shared/src/schemas/user.ts
    import { z } from 'zod';
    export const CreateUserInputSchema = z.object({...});
    ```

### 2. 共享的核心实体类型 (Interfaces, Types) 和枚举 (Enums)

*   **定义位置**: `packages/shared/src/types/` (或按模块, e.g., `src/types/user.ts`), `packages/shared/src/enums.ts` 等。
*   **目的**: 定义代表通用数据模型（如数据库表的结构）或状态的核心类型，供前后端共同引用。
*   **使用者**: 后端构造返回值或作为 Schema 一部分；前端直接使用这些类型来注解变量、函数参数等。
*   **重要**: **当需要表示一个标准实体（如 'Element'）的通用结构时，应在此处定义并导出。**
*   **示例**:
    ```typescript
    // packages/shared/src/types/element.ts
    export interface Element {
      id: string;
      type: 'text' | 'image';
      content: string;
      position: number;
      // ... 其他标准属性
    }

    // packages/shared/src/enums.ts
    export enum ProcessStatus { ... }
    ```
*   **前端使用**: `import type { Element } from '@julebu/shared';`

### 3. 特定 API 调用的输出类型 (Output Types)

*   **!!! 重要：不要手动定义 !!!**
*   **来源**: 由 tRPC 根据后端 Resolver 函数的**实际返回值自动推断**。
*   **提取与导出位置**: `packages/shared/src/api-types.ts` (使用 `inferRouterOutputs`)
*   **目的**: 获取**某个特定 API Procedure 调用**的**精确返回类型**。这个返回类型可能是共享的核心实体类型（如 `Element`），也可能是 `null`、状态对象、部分数据或其他结构。
*   **示例**:
    ```typescript
    // packages/shared/src/api-types.ts
    type RouterOutput = inferRouterOutputs<AppRouter>;
    export type ElementRouterOutputs = RouterOutput['element'];
    // 获取 'processOne' 操作的精确返回类型
    export type ProcessOneElementResult = ElementRouterOutputs['processOne'];
    ```
*   **前端使用**: `import type { ProcessOneElementResult } from '@julebu/shared';`
*   **选择**: **如果你的目标只是想知道"一个 Element 通常长什么样"，请优先使用上面第 2 点定义的共享核心实体类型 (`import { Element } ...`)。只有当你需要知道特定 API 调用（如 `processOne`）确切返回了什么时，才使用这里导出的 Procedure 输出类型 (`ProcessOneElementResult`)。**

### 4. 前端特定的类型 (UI状态, 映射后的类型)

*   **定义位置**: `apps/client/types/`
*   **目的**: 定义仅用于前端 UI 或特定前端逻辑的状态和数据结构，通常是对从 `shared` 导入的类型（核心实体类型或 API 输出类型）进行扩展。
*   **做法**: 使用交叉类型 (`&`) 组合基础类型和前端特定接口。
*   **映射**: 数据映射逻辑（将 API 返回数据转换为前端扩展类型）应放在 `apps/client/composables/` (API 层) 中。
*   **示例**:
    ```typescript
    // apps/client/types/element.ts
    import type { Element } from '@julebu/shared'; // 导入共享的核心实体类型
    // 或者: import type { ProcessOneElementResult } from '@julebu/shared';

    export interface FrontendElementState { isSelected: boolean; }
    export type DisplayElement = Element & FrontendElementState;
    // 或者: export type DisplayProcessedElement = ProcessOneElementResult & FrontendElementState;
    ```

---

## 工作流程总结

1.  **定义/修改后端 API**:
    *   **优先**: 在 `shared` 中定义或更新**核心实体类型** (`types/`)、**输入 Schema** (`schemas/`) 和**共享枚举/常量** (`enums/`) (如果需要)。
    *   在 `server` 中实现 Resolver，导入并使用 `shared` 的 Schema/类型，返回数据，tRPC 自动推断输出类型。
2.  **更新共享 API 输出类型**: 重新构建 `packages/shared` 包 (`pnpm -F @julebu/shared build`)。`inferRouterOutputs` 会自动提取最新的 API Procedure 输出类型到 `api-types.ts`。
3.  **前端调整**:
    *   如果只需要**通用实体类型**：直接从 `shared` 导入核心实体类型 (e.g., `import type { Element } from '@julebu/shared';`)。
    *   如果需要**特定 API 调用的精确返回类型**：从 `shared/api-types` 导入对应的 Procedure 输出类型 (e.g., `import type { ProcessOneElementResult } from '@julebu/shared';`)。
    *   如果需要**扩展/映射**：
        *   在 `client/types/` 中定义前端特定类型并组合。
        *   在 `client/composables/` (API 层) 中进行数据映射。
        *   在业务逻辑中使用最终的前端特定类型。

遵循此策略，可以最大限度地利用 tRPC 的类型安全优势，同时保持代码的清晰、可维护和开发效率。
