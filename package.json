{"name": "j<PERSON><PERSON>-editor", "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac", "description": "", "author": "", "license": "ISC", "keywords": [], "main": "index.js", "scripts": {"shared:build": "pnpm -F @julebu/shared build", "shared:dev": "pnpm -F @julebu/shared dev", "dev:client": "pnpm -F client dev", "dev:server": "pnpm shared:build && pnpm -F server dev", "build:client": "pnpm -F client generate", "build:server": "pnpm shared:build && pnpm -F server build", "docker:start": "docker-compose up -d", "docker:stop": "docker-compose stop", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f server", "docker:build": "docker-compose build", "docker:rebuild": "docker-compose build --no-cache", "docker:dev:up": "docker-compose -f docker-compose.dev.yml up -d", "docker:dev:down": "docker-compose -f docker-compose.dev.yml down", "lint": "eslint .", "lint:fix": "eslint . --fix", "release": "tsx --env-file=.env scripts/release.ts", "db:init": "pnpm -F server run db:init", "db:init:test": "pnpm -F server run db:init:test", "db:init:ci": "pnpm -F server run db:init:ci", "db:studio": "pnpm -F server run db:studio", "db:migrate": "pnpm -F server run db:migrate", "db:push": "pnpm -F server run db:push", "db:generate": "pnpm -F server run db:generate"}, "devDependencies": {"@antfu/eslint-config": "^4.14.1", "@types/archiver": "^6.0.2", "@types/fs-extra": "^11.0.4", "@types/node": "^24.0.3", "@types/semver": "^7.7.0", "archiver": "^7.0.1", "eslint": "^9.29.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-tailwindcss": "^3.18.0", "fs-extra": "^11.2.0", "inquirer": "^10.1.8", "mongoose": "^8.4.1", "mysql2": "^3.11.3", "semver": "^7.6.3", "tsx": "^4.17.0"}, "pnpm": {"overrides": {"estree-walker": "2.0.2"}}}