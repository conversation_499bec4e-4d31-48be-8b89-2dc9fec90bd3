import type { inferRouterOutputs } from '@trpc/server'
// Adjust the import path if the server's AppRouter location differs during build
import type { AppRouter } from 'server/src/trpc/trpc.router'

// Infer the complete router output type map
type RouterOutput = inferRouterOutputs<AppRouter>

// --- Export specific procedure output types below ---

// Export all outputs for the 'course' router
export type CourseRouterOutputs = RouterOutput['course']

// Export all outputs for the 'coursePack' router
export type CoursePackRouterOutputs = RouterOutput['coursePack']

// Export all outputs for the 'element' router
export type ElementRouterOutputs = RouterOutput['element']

// Export all outputs for the 'sentence' router
export type SentenceRouterOutputs = RouterOutput['sentence']
