// Export the inferred API output types
export * from './api-types'
export * from './contracts/course'
export * from './contracts/coursePack'
export * from './contracts/element'
export * from './contracts/image'
export * from './contracts/learningContent'
export * from './contracts/s3'
export * from './contracts/Sentence'
export * from './contracts/textImageLink'
export * from './enums'
export * as schemas from './schemas/learningContent'

export * from './types'

// Re-export the AppRouter type from the server
export type { AppRouter } from 'server/src/trpc/trpc.router'
