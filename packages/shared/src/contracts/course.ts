import type { Sentence } from './Sentence'

export enum CourseType {
  normal = 'normal',
  music = 'music',
  audio = 'audio',
  video = 'video',
}

export interface Course {
  id: string
  title: string
  description: string
  video: string
  coursePackId: string
  sentences: Sentence[] // 根据上下文，这里应该是句子的类型，但没有看到具体定义
  gameId: string
  createdAt: string
  updatedAt: string
  type: CourseType
  mediaUrl: string
}

export interface ElementDTO {
  id: string
  content: string
  english: string
  chinese: string
  phonetic: string
  type: string
  position: number
  gameId: string
  sentenceId: string
  imageId: string | null
}

export interface SentenceDTO {
  id: string
  content: string
  chinese: string
  english: string
  position: number
  courseId: string
  startTime: number | null
  endTime: number | null
  elements: ElementDTO[]
}

export interface CourseDetailDTO {
  id: string
  title: string
  description: string
  video: string
  order: number
  coursePackId: string
  type: CourseType
  mediaUrl: string
  gameId: string
  createdAt: string
  updatedAt: string
  sentences: SentenceDTO[]
}
