export interface LearningContent {
  overview: {
    chineseTranslation: string
    englishExplanation: string
    functionAndContext: string
  }
  vocabularyAnalysis: Array<{
    word: string
    chineseTranslation: string
    phonetic: string
    pos: string
    basicMeaning: string
    contextualMeaning: string
    commonPhrases: string
    synonyms: string
    antonyms: string
    memorizationTip: string
    exampleSentence: string
  }>
  grammarAnalysis: {
    sentenceType: string
    sentenceComponents: Array<{
      component: string
      grammaticalExplanation: string
      meaningInSentence: string
    }>
    tenseAndMood: string
    keyGrammarPoints: string
    specialStructures: string
    wordOrder: string
    grammarRulesApplication: string
    commonErrors: string
  }
  relatedExamples: Array<{
    example: string
    explanation: string
  }>
  culturalAndPracticalKnowledge: {
    culturalElements: string
    backgroundInformation: string
    practicalApplication: string
  }
}
