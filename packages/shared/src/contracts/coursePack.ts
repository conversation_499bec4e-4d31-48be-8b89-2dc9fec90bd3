import type { CoursePackShareLevel } from '../enums'
import type { Course, CourseType } from './course'

// findAll 返回的类型
export interface CoursePack {
  id: string
  title: string
  description: string
  isFree: boolean
  cover: string
  gameId: string
  shareLevel: CoursePackShareLevel
  categoryId?: string
  isPinned: boolean
  position: number
  createdAt: string
  updatedAt: string
}

// FindOne 返回的类型
export interface CoursePackWithCourses extends CoursePack {
  courses: {
    id: string
    title: string
    description: string
    gameId: string
    createdAt: string
    updatedAt: string
    position: number
    mediaUrl: string
    type: CourseType
  }[]
}

export interface AdminCoursePackWithCourses extends CoursePack {
  courses: Course[]
}
