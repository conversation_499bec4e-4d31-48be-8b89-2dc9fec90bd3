import { TRPCError } from '@trpc/server'
import { z } from 'zod'

// 服务层上传文件大小限制
export const MAX_FILE_SIZE = 500 * 1024 * 1024 // 500MB

// File size limits (bytes)
export const MAX_IMAGE_SIZE = 2 * 1024 * 1024 // 2MB
export const MAX_AUDIO_SIZE = 50 * 1024 * 1024 // 50MB
export const MAX_VIDEO_SIZE = 150 * 1024 * 1024 // 150MB

// 支持的文件类型
export const SUPPORTED_FORMATS = ['mp3', 'wav', 'm4a', 'ogg']

// Allowed MIME types - 只支持最常用的格式
export const ALLOWED_IMAGE_MIME_TYPES = [
  'image/jpeg',
  'image/png',
  'image/webp',
  'image/gif',
] as const

export const ALLOWED_AUDIO_MIME_TYPES = [
  'audio/mpeg', // MP3
  'audio/mp3', // MP3
  'audio/wav', // WAV
  'audio/aac', // AAC
  'audio/m4a', // M4A (Apple)
] as const

export const ALLOWED_VIDEO_MIME_TYPES = [
  'video/mp4', // MP4 (最通用)
  'video/webm', // WebM (现代浏览器)
  'video/avi', // AVI (Windows)
  'video/quicktime', // MOV (Apple)
] as const

// Media type enum
export enum MediaType {
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
}

// MIME type schemas
const imageMimeType = z.enum(
  ALLOWED_IMAGE_MIME_TYPES,
)
const audioMimeType = z.enum(
  ALLOWED_AUDIO_MIME_TYPES,
)
const videoMimeType = z.enum(
  ALLOWED_VIDEO_MIME_TYPES,
)

export type MimeType = z.infer<typeof imageMimeType>
export type AudioMimeType = z.infer<typeof audioMimeType>
export type VideoMimeType = z.infer<typeof videoMimeType>

// Original image DTO (for backward compatibility)
export const getPresignedUrlDto = z.object({
  key: z.string(),
  mimeType: imageMimeType,
  contentLength: z
    .number()
    .max(
      MAX_IMAGE_SIZE,
      `File size cannot exceed ${MAX_IMAGE_SIZE / 1024 / 1024}MB`,
    )
    .positive('File size must be greater than 0'),
})

// Audio upload DTO
export const audioUploadDto = z.object({
  key: z.string(),
  mimeType: audioMimeType,
  contentLength: z
    .number()
    .max(
      MAX_AUDIO_SIZE,
      `File size cannot exceed ${MAX_AUDIO_SIZE / 1024 / 1024}MB`,
    )
    .positive('File size must be greater than 0'),
})

// Video upload DTO
export const videoUploadDto = z.object({
  key: z.string(),
  mimeType: videoMimeType,
  contentLength: z
    .number()
    .max(
      MAX_VIDEO_SIZE,
      `File size cannot exceed ${MAX_VIDEO_SIZE / 1024 / 1024}MB`,
    )
    .positive('File size must be greater than 0'),
})

// Media upload DTO (union type)
export const mediaUploadDto = z.union([
  getPresignedUrlDto,
  audioUploadDto,
  videoUploadDto,
])

export type GetPresignedUrlDto = z.infer<typeof getPresignedUrlDto>
export type AudioUploadDto = z.infer<typeof audioUploadDto>
export type VideoUploadDto = z.infer<typeof videoUploadDto>
export type MediaUploadDto = z.infer<typeof mediaUploadDto>

// Utility function to get media type from MIME type
export function getMediaTypeFromMimeType(mimeType: MediaUploadDto['mimeType']): MediaType {
  if ((ALLOWED_IMAGE_MIME_TYPES as readonly string[]).includes(mimeType)) {
    return MediaType.IMAGE
  }
  if ((ALLOWED_AUDIO_MIME_TYPES as readonly string[]).includes(mimeType)) {
    return MediaType.AUDIO
  }
  if ((ALLOWED_VIDEO_MIME_TYPES as readonly string[]).includes(mimeType)) {
    return MediaType.VIDEO
  }
  throw new Error(`Unsupported MIME type: ${mimeType}`)
}

// Storage path prefix function
export function getStoragePrefix(mediaType: MediaType): string {
  switch (mediaType) {
    case MediaType.IMAGE:
      return ''
    case MediaType.AUDIO:
      return 'audios/'
    case MediaType.VIDEO:
      return 'videos/'
    default:
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: '不支持的媒体类型',
      })
  }
}
