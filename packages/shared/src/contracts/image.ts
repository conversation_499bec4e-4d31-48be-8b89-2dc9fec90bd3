import { z } from 'zod'

export const IMAGE_SIZE_OPTIONS = [
  { value: '512x512', label: '512x512' },
  { value: '1024x1024', label: '1024x1024' },
  { value: '1024x1536', label: '1024x1536' },
  { value: '1024x1792', label: '1024x1792' },
  { value: '1536x1024', label: '1536x1024' },
  { value: '1792x1024', label: '1792x1024' },
] as const

export type GenerateImageSize = typeof IMAGE_SIZE_OPTIONS[number]['value']
const generateImageSize = z.enum(
  IMAGE_SIZE_OPTIONS.map(item => item.value) as [GenerateImageSize, ...GenerateImageSize[]],
)

export const IMAGE_STYLES_OPTIONS = [
  { value: 'cartoon', label: '卡通' },
  { value: 'oil_painting', label: '油画' },
  { value: 'chinese_style', label: '中国风' },
  { value: 'anime', label: '动漫' },
  { value: 'portrait_photography', label: '人像摄影' },
  { value: 'cyberpunk', label: '赛博朋克' },
  { value: 'cinematic_photography', label: '电影写真' },
  { value: '3d_rendering', label: '3D渲染' },
  { value: 'cg_animation', label: 'CG动画' },
  { value: 'ink_painting', label: '水墨画' },
  { value: 'classical', label: '古典' },
  { value: 'watercolor', label: '水彩画' },
  { value: 'flat_illustration', label: '平面插画' },
  { value: 'landscape', label: '风景' },
  { value: 'hong_kong_anime', label: '港风动漫' },
  { value: 'pixel_art', label: '像素风格' },
  { value: 'fluorescent_painting', label: '荧光绘画' },
  { value: 'colored_pencil', label: '彩铅画' },
  { value: 'figure_model', label: '手办' },
  { value: 'game_style', label: '游戏' },
] as const

export type ImageStyle = typeof IMAGE_STYLES_OPTIONS[number]['value']
const imageStyle = z.enum(
  IMAGE_STYLES_OPTIONS.map(item => item.value) as [ImageStyle, ...ImageStyle[]],
)

export const aiGenerateImageDto = z.object({
  elementId: z.string(),
  style: imageStyle,
  size: generateImageSize,
})
export type AiGenerateImageDto = z.infer<typeof aiGenerateImageDto>

const imageSource = z.enum(['ai_generated', 'user_upload'])
export type ImageSource = z.infer<typeof imageSource>

export const createImageWithTextLinkDto = z.object({
  // for image
  userId: z.string().optional(),
  fileKey: z.string(),
  mimeType: z.string(),
  fileSize: z.number(),
  width: z.number(),
  height: z.number(),
  description: z.string().optional(),
  styleTags: z.array(imageStyle).optional(),
  categoryTags: z.array(z.string()).optional(),
  source: imageSource,
  // for text image link
  textContent: z.string(),
  linkType: z.enum(['image']),
})

export type CreateImageWithTextLinkDto = z.infer<typeof createImageWithTextLinkDto>
