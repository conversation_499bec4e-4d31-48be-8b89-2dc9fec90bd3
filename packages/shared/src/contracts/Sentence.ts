import type { Element } from './element'
import type { LearningContent } from './learningContent'

export interface Phonetic {
  uk: string
  us: string
}

export interface WordDetail {
  pos: string // 词性 (part of speech)
  word: string // 单词
  phonetic: Phonetic // 音标信息
  definition: string // 单词的定义/翻译
}

export interface Sentence {
  id: string
  content: string
  chinese: string
  learningContent: LearningContent | null
  courseId: string
  elements: Element[] // 元素的类型定义
  position: number
  wordDetails: WordDetail[]
  startTime: number | null
  endTime: number | null
}
