import type { LearningContent } from '../contracts/learningContent'
import { z } from 'zod'

export const learningContentSchema = z.object({
  overview: z.object({
    chineseTranslation: z.string(),
    englishExplanation: z.string(),
    functionAndContext: z.string(),
  }),
  vocabularyAnalysis: z.array(
    z.object({
      word: z.string(),
      chineseTranslation: z.string(),
      phonetic: z.string(),
      pos: z.string(),
      basicMeaning: z.string(),
      contextualMeaning: z.string(),
      commonPhrases: z.string(),
      synonyms: z.string(),
      antonyms: z.string(),
      memorizationTip: z.string(),
      exampleSentence: z.string(),
    }),
  ),
  grammarAnalysis: z.object({
    sentenceType: z.string(),
    sentenceComponents: z.array(
      z.object({
        component: z.string(),
        grammaticalExplanation: z.string(),
        meaningInSentence: z.string(),
      }),
    ),
    tenseAndMood: z.string(),
    keyGrammarPoints: z.string(),
    specialStructures: z.string(),
    wordOrder: z.string(),
    grammarRulesApplication: z.string(),
    commonErrors: z.string(),
  }),
  relatedExamples: z.array(
    z.object({
      example: z.string(),
      explanation: z.string(),
    }),
  ),
  culturalAndPracticalKnowledge: z.object({
    culturalElements: z.string(),
    backgroundInformation: z.string(),
    practicalApplication: z.string(),
  }),
})

export function validateLearningContent(content: unknown): content is LearningContent {
  return learningContentSchema.safeParse(content).success
}
