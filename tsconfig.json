{"compilerOptions": {"target": "ESNext", "baseUrl": ".", "module": "ESNext", "moduleResolution": "Node", "paths": {"@shared/*": ["libs/shared/src/*"]}, "resolveJsonModule": true, "types": ["node"], "allowJs": true, "strict": true, "noImplicitAny": true, "noUnusedLocals": true, "noEmit": true, "allowSyntheticDefaultImports": true, "esModuleInterop": false, "forceConsistentCasingInFileNames": true}, "exclude": ["**/dist/**", "**/.nuxt/**", "**/nuxt.d.ts", "**/test/**"]}