# Julebu Editor

## Docker 部署说明

本项目支持通过 Docker 进行部署：

### 环境准备

1. 确保已安装 Docker 和 Docker Compose
2. 确保服务器的环境变量配置正确：
   - 所有配置信息已在 `apps/server/.env.prod` 中设置

### 部署命令

```bash
# 构建镜像
pnpm docker:build

# 启动服务
pnpm docker:start

# 查看日志
pnpm docker:logs

# 停止服务
pnpm docker:stop

# 停止并删除容器
pnpm docker:down

# 开发环境数据库
pnpm docker:dev:up
pnpm docker:dev:down
```

### 访问服务

- 后端 API: http://localhost:3400

### 项目结构

本项目使用 monorepo 结构组织代码：

- `apps/server`: 后端服务 (Fastify + Drizzle ORM)
- `apps/client`: 前端应用 (Nuxt 3)
- `packages/shared`: 共享代码和类型定义 