# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Architecture

This is a **monorepo** using pnpm workspaces with three main components:

- **`apps/server`**: Backend API server built with Fastify, tRPC, and Drizzle ORM
- **`apps/client`**: Frontend Nuxt 3 application with Vue 3 and Naive UI
- **`packages/shared`**: Shared TypeScript types and utilities

## Essential Commands

### Development
```bash
# Start development servers
pnpm dev:client    # Nuxt client on port 3200
pnpm dev:server    # Fastify server with auto-reload

# Build for production
pnpm build:client  # Generates static client
pnpm build:server  # Builds server dist
```

### Testing
```bash
# Run tests (from workspace root)
pnpm -F client test     # Client Vitest tests
pnpm -F server test     # Server Vitest tests (no file parallelism)
```

### Database Management
```bash
# Database operations (run from apps/server)
cd apps/server
pnpm db:init      # Initialize dev database
pnpm db:push      # Push schema changes
pnpm db:studio    # Open Drizzle Studio
pnpm db:generate  # Generate migrations
```

### Code Quality
```bash
# Linting (run from root)
pnpm lint         # Check all workspaces
pnpm lint:fix     # Auto-fix issues
```

### Docker Deployment
```bash
pnpm docker:build    # Build images
pnpm docker:start    # Start services
pnpm docker:logs     # View server logs
pnpm docker:down     # Stop and remove containers
```

## Key Architecture Patterns

### Backend (apps/server)
- **tRPC Router Structure**: Organized by domain (course, user, content, etc.) in `src/routers/`
- **Authentication**: JWT-based with Logto integration, protected procedures require membership
- **Database**: MySQL with Drizzle ORM, schemas in `src/db/schema/`
- **Services**: External integrations (Redis, S3, OpenAI, translation) in `src/services/`
- **File Organization**: Each router has service, controller, DTOs, and entities

### Frontend (apps/client)
- **Nuxt 3**: Server-side rendering with Vue 3 composition API
- **UI Framework**: Naive UI components with Tailwind CSS
- **State Management**: Pinia stores for domain-specific state
- **Authentication**: Logto Vue SDK integration
- **API Communication**: tRPC client with type safety

### Environment Configuration
- Use environment-specific configs: `.env`, `.env.prod`, `.env.test`
- Database URL configuration handled in `drizzle.config.ts`
- Server runs on port 3400 (production), client on 3200 (dev)

## Important Notes

- Always build shared package before server: `pnpm shared:build`
- Server tests run with `--no-file-parallelism` flag
- Protected tRPC procedures validate membership status
- Client generates static files for production deployment
- Use Chinese comments in code (existing pattern)

## Language Requirements

- **Chinese Communication**: When users ask questions in Chinese, always respond in Chinese to maintain natural communication flow and better understanding.
