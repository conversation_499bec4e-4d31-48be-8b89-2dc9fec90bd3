version: '3.8'

services:
  db:
    image: mysql:8.3.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: julebu_editor_dev
      TZ: Asia/Shanghai
    command: --default-authentication-plugin=mysql_native_password --default-time-zone='+8:00'
    ports:
      - '3408:3306'
  testdb:
    image: mysql:8.3.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: julebu_editor_test
      TZ: Asia/Shanghai
    command: --default-authentication-plugin=mysql_native_password --default-time-zone='+8:00'
    ports:
      - '3407:3306' # 将宿主机的 3307 端口映射到容器的 3306 端口
volumes:
  data:
    driver: local
